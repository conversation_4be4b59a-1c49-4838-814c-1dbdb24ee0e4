{"name": "rubicon-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "build:prod": "nest build -c nest-cli.prod.json", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch --preserveWatchOutput", "start:debug": "nest start --debug 0.0.0.0:9229 --watch --preserveWatchOutput", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test,migrations,seeders,environments}/**/*.ts\" ", "lint:fix": "eslint \"{src,apps,libs,test,migrations,seeders,environments}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e:s": "jest --config ./jest-e2e-standalone.json", "test:e2e:s:cov": "jest --config ./jest-e2e-standalone.json --coverage", "test:e2e": "jest --config ./jest-e2e.json", "test:e2e:cov": "jest --config ./jest-e2e.json --coverage", "console:dev": "ts-node -r tsconfig-paths/register src/console.ts", "console": "node dist/console.js", "migrate": "mikro-orm database:create && mikro-orm database:import migrations/Initial.sql && mikro-orm migration:up"}, "dependencies": {"@aws-sdk/client-s3": "^3.367.0", "@aws-sdk/credential-providers": "^3.363.0", "@aws-sdk/protocol-http": "^3.357.0", "@aws-sdk/s3-request-presigner": "^3.367.0", "@aws-sdk/url-parser": "^3.357.0", "@liaoliaots/nestjs-redis": "^9.0.4", "@mikro-orm/core": "~6.3.10", "@mikro-orm/nestjs": "^6.0.2", "@mikro-orm/postgresql": "~6.3.10", "@nestjs/axios": "^3.0.3", "@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.0.1", "@nestjs/passport": "^10.0.1", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.4.1", "@nestjs/terminus": "^10.0.0", "@opentelemetry/api": "^1.3.0", "@opentelemetry/auto-instrumentations-node": "^0.50.0", "@opentelemetry/exporter-trace-otlp-http": "^0.53.0", "@opentelemetry/instrumentation-knex": "^0.40.0", "@opentelemetry/sdk-node": "^0.53.0", "@opentelemetry/semantic-conventions": "^1.8.0", "@rubiconcarbon/shared-types": "3.1.1", "@sendgrid/client": "^7.7.0", "@sendgrid/mail": "^7.7.0", "@types/papaparse": "^5.3.14", "@willsoto/nestjs-prometheus": "^6.0.1", "auth0": "^3.3.0", "aws-sdk": "^2.1413.0", "axios": "^1.3.5", "bcrypt": "^5.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "date-fns": "^4.0.0", "date-fns-tz": "^3.1.3", "decimal.js": "^10.4.2", "dotenv": "^16.0.3", "express-prom-bundle": "^7.0.0", "google-auth-library": "^8.7.0", "hbs": "^4.2.0", "ioredis": "^5.2.4", "jwks-rsa": "^3.0.0", "lodash": "^4.17.21", "nestjs-console": "^9.0.0", "nestjs-pino": "^4.1.0", "nodejs-polars": "^0.15.0", "openid-client": "^5.4.0", "papaparse": "^5.4.1", "passport": "^0.7.0", "passport-custom": "^1.1.1", "passport-jwt": "^4.0.1", "permitio": "^1.0.0-rc1", "pg-copy-streams": "^6.0.6", "pino-http": "^10.3.0", "prom-client": "^15.1.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.2.0", "swagger-ui-express": "^5.0.1", "uuid": "^10.0.0"}, "devDependencies": {"@eslint/compat": "^1.1.1", "@golevelup/ts-jest": "^0.3.4", "@mikro-orm/cli": "~6.3.10", "@mikro-orm/migrations": "~6.3.10", "@mikro-orm/seeder": "~6.3.10", "@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@trendyol/jest-testcontainers": "^2.1.1", "@types/auth0": "^3.3.1", "@types/bcrypt": "^5.0.0", "@types/express": "^4.17.13", "@types/jest": "^29.2.3", "@types/lodash": "^4.14.191", "@types/node": "^18.16.15", "@types/passport-jwt": "^4.0.1", "@types/pg-copy-streams": "^1.2.5", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.6.0", "@typescript-eslint/parser": "^8.6.0", "eslint": "^9.10.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.5.3", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^28.8.3", "eslint-plugin-prettier": "^5.2.1", "jest": "^29.3.1", "prettier": "^3.3.3", "rimraf": "^4.1.2", "source-map-support": "^0.5.20", "supertest": "^7.0.0", "ts-jest": "^29.0.3", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "^4.1.0", "typedoc": "^0.26.7", "typescript": "^4.9.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "coveragePathIgnorePatterns": ["app.module.ts"], "testEnvironment": "node", "moduleNameMapper": {"^@app/(.*)$": "<rootDir>/$1", "^@env/environment$": "<rootDir>/../environments/environment.test"}}, "mikro-orm": {"useTsNode": true, "configPaths": ["./src/mikro-orm.config.ts", "./dist/mikro-orm.config.js"]}, "engines": {"node": ">=18", "npm": "use-yarn", "yarn": ">=1.22.0"}}