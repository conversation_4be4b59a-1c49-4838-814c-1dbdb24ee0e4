import { EntityManager } from '@mikro-orm/postgresql';
import { Seeder } from '@mikro-orm/seeder';
import { AssetType, BookType } from '@rubiconcarbon/shared-types';
import { environment } from '@env/environment';

export class AssetsSeeder extends Seeder {
  async run(em: EntityManager): Promise<void> {
    const knex = em.getKnex();

    const count = await knex(`${environment.db.schema.rubicon}.assets`)
      .count({ count: '*' })
      .then((r) => {
        return Number(r[0].count);
      });
    if (count > 0) {
      return;
    }

    await knex.raw(`insert into "${environment.db.schema.rubicon}".assets (id, created_at, updated_at, asset_type) select id, created_at, updated_at, '${AssetType.REGISTRY_VINTAGE}' from "${environment.db.schema.rubicon}".project_vintages;`);
    await knex.raw(`insert into "${environment.db.schema.rubicon}".assets (id, created_at, updated_at, asset_type) select id, created_at, updated_at, asset_type from "${environment.db.schema.rubicon}".books where type = '${BookType.RCT_PUBLIC}';`);
    await knex.raw(`insert into "${environment.db.schema.rubicon}".assets (id, created_at, updated_at, asset_type) select id, created_at, updated_at, asset_type from "${environment.db.schema.rubicon}".books where type = '${BookType.RCT_CUSTOM}';`);
  }
}
