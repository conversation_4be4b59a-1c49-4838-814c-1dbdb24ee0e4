/* third party */
import { EntityManager } from '@mikro-orm/postgresql';
import { Seeder } from '@mikro-orm/seeder';
/* env */
import { environment } from '@env/environment';
import { BookType } from '@rubiconcarbon/shared-types';
import { Book } from '@app/entities';
/* app */

export class GroupingsParentsSeeder extends Seeder {
  async run(em: EntityManager): Promise<void> {
    const knex = em.getKnex();
    const parentsCount = await knex(`${environment.db.schema.rubicon}.grouping_parents`)
      .count({ count: '*' })
      .then((r) => {
        return Number(r[0].count);
      });

    const groupingsCount = await knex(`${environment.db.schema.rubicon}.groupings`)
      .count({ count: '*' })
      .then((r) => {
        return Number(r[0].count);
      });

    if (parentsCount === 0) {
      await knex.raw(`insert into "${environment.db.schema.rubicon}"."grouping_parents" (id, name, description) values 
      ('${environment.rubicon.groupings.bookType.portfolio}', 'RCT', 'groups bookTypes [${BookType.RCT_CUSTOM}, ${BookType.PORTFOLIO_CUSTOMER}, ${BookType.PORTFOLIO_DEFAULT}, ${BookType.RCT_PUBLIC}, ${BookType.PORTFOLIO_RESERVES}]'),
      ('${environment.rubicon.groupings.bookType.opportunistic}', 'Opportunistic', 'groups bookTypes [${BookType.OPPORTUNISTIC_DEFAULT}]'),
      ('${environment.rubicon.groupings.bookType.rehabilitation}', 'Ineligible RCT', 'groups bookTypes [${BookType.REHABILITATION_DEFAULT}]'),
      ('${environment.rubicon.groupings.bookType.compliance}', 'Compliance', 'groups bookTypes [${BookType.COMPLIANCE_DEFAULT}]'),
      ('${environment.rubicon.groupings.bookType.aged}', 'Aged Inventory, LLC', 'groups bookTypes [${BookType.AGED_DEFAULT}]');`);
    }

    if (groupingsCount === 0) {
      await knex.raw(`insert into "${environment.db.schema.rubicon}".groupings (book_id, parent_id) values 
      ('${environment.rubicon.books.compliance}','${environment.rubicon.groupings.bookType.compliance}'),
      ('${environment.rubicon.books.opportunistic}','${environment.rubicon.groupings.bookType.opportunistic}'),
      ('${environment.rubicon.books.portfolio.default}','${environment.rubicon.groupings.bookType.portfolio}'),
      ('${environment.rubicon.books.portfolio.reserves}','${environment.rubicon.groupings.bookType.portfolio}'),
      ('${environment.rubicon.books.rehabilitation}','${environment.rubicon.groupings.bookType.rehabilitation}'), 
      ('${environment.rubicon.books.aged}', '${environment.rubicon.groupings.bookType.aged}');`);

      // insert customer/custom/public portfolios
      const portfolios = await em.find(Book, { type: [BookType.RCT_CUSTOM, BookType.PORTFOLIO_CUSTOMER, BookType.RCT_PUBLIC] });
      const portfolioInsertsArray: string[] = portfolios.map((m) => {
        return `('${m.id}', '${environment.rubicon.groupings.bookType.portfolio}')`;
      });
      if (portfolioInsertsArray.length > 0) {
        await knex.raw(`insert into "${environment.db.schema.rubicon}".groupings (book_id, parent_id) values ${portfolioInsertsArray.join(',')};`);
      }
    }
  }
}
