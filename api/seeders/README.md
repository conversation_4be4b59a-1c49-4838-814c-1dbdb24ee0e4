# MikroORM seeders

## Files

Please make sure TESTONLY files are not added to [DatabaseSeeder.ts](DatabaseSeeder.ts). They should only be added to [TestSeeder.ts](TestSeeder.ts) since they're only used for e2e testing.

## Environment seeder data
To keep your local users and organizations, add your entries inside the csv files in the local folder `api/seeders/csv/local` (the local folder is used for both local and dev).
Copy from the db the organizations, users, organization_users, and customer portfolios into the developments folder. should look something like this
- local
  - counterparties.csv <-- `select * from rubicon.counterparties;`
  - customer-portfolios.csv <-- `select * from rubicon.customer_portfolios;`
  - customer-portfolio-books.csv <-- `select * from rubicon.books where type in('portfolio:customer', 'rct:custom');`
  - organization-users.csv <-- `select * from rubicon.organizations_users;`
  - organizations.csv <-- `select * from rubicon.organizations;`
  - users.csv <-- `select * from rubicon.users;`

## Updating Seeder data

Every so often (once a quarter) we should update the seeder csv files to have the latest general data.

### Notes :

- update the [Seeder CSVs](../seeders/csvs/) with the latest prod data. make sure all headers are in quotes. arrays need to be reformatted (see existing books.csv). null needs to be removed (replace with empty).
  - asset-flows-purchases `select transaction_id, asset_type, asset_id, source_id, status, sum(amount) as amount, coalesce(sum(raw_price), 0) as raw_price, coalesce(sum(service_fee), 0) as service_fee, coalesce(sum(other_fee), 0) as other_fee from rubicon.asset_flows where transaction_type = 'purchase' and status in('pending', 'settled') group by transaction_id, asset_type, asset_id, source_id, status order by status, transaction_id;`
  - asset-flows-retirements `select transaction_id, transaction_subtype, asset_type, asset_id, source_id, status, sum(amount) as amount, coalesce(sum(raw_price), 0) as raw_price, coalesce(sum(service_fee), 0) as service_fee, coalesce(sum(other_fee), 0) as other_fee from rubicon.asset_flows where transaction_type = 'retirement' and status in('pending', 'settled') group by transaction_id, transaction_subtype, asset_type, asset_id, source_id, status order by status, transaction_id;`
  - asset-flows-trades-buy `select status, asset_id, destination_id as book_id, sum(amount) as amount, coalesce(sum(raw_price), 0) as raw_price, coalesce(sum(service_fee), 0) as service_fee, coalesce(sum(other_fee), 0) as other_fee from rubicon.asset_flows where transaction_type = 'trade' and transaction_subtype = 'buy' and status in('pre-pending', 'pending', 'settled') group by status, asset_id, destination_id;`
  - asset-flows-trades-sell `select status, asset_id, source_id as book_id, sum(amount) as amount, coalesce(sum(raw_price), 0) as raw_price, coalesce(sum(service_fee), 0) as service_fee, coalesce(sum(other_fee), 0) as other_fee from rubicon.asset_flows where transaction_type = 'trade' and transaction_subtype = 'sell' and status in('pre-pending', 'pending', 'settled') group by status, asset_id, source_id;`
  - asset-flows-transfers `select transaction_id, created_at, asset_id, source_id, destination_id, sum(amount) as amount from rubicon.asset_flows where transaction_type = 'internal_transfer' and asset_type = 'registry_vintage' group by transaction_id, created_at, asset_id, source_id, destination_id order by created_at;`
  - books `select * from rubicon.books where type not in ('rct:custom', 'portfolio:customer') and is_enabled = true order by type;`
  - buffer-categories `select * from rubicon.buffer_categories order by id;`
  - countries `select * from rubicon.countries order by alpha3;`
  - project-sdgs `select * from rubicon.project_sdgs order by created_at;`
  - project-types `select * from rubicon.project_types order by id;`
  - project-vintages `select * from rubicon.project_vintages where project_id in(select id from rubicon.projects where id in(select id from rubicon.project_flags_v2 where has_vintages = true)) order by created_at;`
  - projects `select * from rubicon.projects where id in(select id from rubicon.project_flags_v2 where has_vintages = true) order by created_at;`
  - registries `select * from rubicon.registries order by name;`
  - sdg-types `select * from rubicon.sdg_types order by id;`
- please run unit tests and e2e tests after updating the csvs to make sure tests still pass. it should be a quick fix if they're failing.
