/* mikro-orm */
import { UniqueConstraintViolationException } from '@mikro-orm/core';
import type { EntityManager } from '@mikro-orm/postgresql';
import { Seeder } from '@mikro-orm/seeder';
/* rubicon */
import { BookType } from '@rubiconcarbon/shared-types';
/* env */
import { environment } from '@env/environment';
/* app */
import { copyToDbFromCSV } from '@app/helpers/csv.helper';
/* seeders */
import { RubiconCarbonOrganizationSeeder } from './entities/001-RubiconOrganizationSeeder';
import { SourcesDestinationsSeeder } from './entities/014-SourcesDestinationsSeeder';
import { ReportingPermissionsSeeder } from './entities/999-ReportingPermissionsSeeder';
import { GroupingsParentsSeeder } from './entities/013-GroupingParentsSeeder';
import { HistoricalsSeeder } from './entities/012-HistoricalsSeeder';
import { AssetsSeeder } from './entities/002-AssetsSeeder';
import { NotFoundException } from '@nestjs/common';
import { BooksProjectTypesSeeder } from './entities/015-BooksProjectTypesSeeder';

export async function runInTransaction(
  em: EntityManager,
  name: string,
  fn: (tx: EntityManager) => void,
  allowNotFoundFailure = false,
  allowUniqueKeyFailure = true,
  quiet = false,
): Promise<void> {
  try {
    await em.transactional(async (tx) => fn(tx));
    if (!quiet)
      // eslint-disable-next-line no-console
      console.log(`\x1b[34mSucceeded applying seeder block "${name}".\x1b[0m`);
  } catch (err: unknown) {
    // Allow the transaction to fail due to unique key errors
    if (allowUniqueKeyFailure && err instanceof UniqueConstraintViolationException) {
      if (!quiet) console.warn(`\x1b[33mSkipping seeder block "${name}".\x1b[0m`);
      return;
    } else if (allowNotFoundFailure && err instanceof NotFoundException) {
      if (!quiet) {
        console.warn(`\x1b[33mSkipping seeder block "${name}".\x1b[0m`);
      }
      return;
    }
    throw err;
  }
}

export class DatabaseSeeder extends Seeder {
  async run(em: EntityManager): Promise<void> {
    await runInTransaction(
      em,
      'copy countries',
      async (tx) =>
        await copyToDbFromCSV(tx, './seeders/csv/countries.csv', `"${environment.db.schema.rubicon}".countries`),
    );
    await runInTransaction(
      em,
      'copy registries',
      async (tx) =>
        await copyToDbFromCSV(tx, './seeders/csv/registries.csv', `"${environment.db.schema.rubicon}".registries`),
    );
    await runInTransaction(
      em,
      'copy buffer-categories',
      async (tx) =>
        await copyToDbFromCSV(
          tx,
          './seeders/csv/buffer-categories.csv',
          `"${environment.db.schema.rubicon}".buffer_categories`,
        ),
    );
    await runInTransaction(
      em,
      'copy sdg-types',
      async (tx) =>
        await copyToDbFromCSV(tx, './seeders/csv/sdg-types.csv', `"${environment.db.schema.rubicon}".sdg_types`),
    );
    await runInTransaction(
      em,
      'copy project-types',
      async (tx) =>
        await copyToDbFromCSV(
          tx,
          './seeders/csv/project-types.csv',
          `"${environment.db.schema.rubicon}".project_types`,
        ),
    );
    await runInTransaction(
      em,
      'copy projects',
      async (tx) =>
        await copyToDbFromCSV(tx, './seeders/csv/projects.csv', `"${environment.db.schema.rubicon}".projects`),
    );
    await runInTransaction(
      em,
      'copy project-sdgs',
      async (tx) =>
        await copyToDbFromCSV(tx, './seeders/csv/project-sdgs.csv', `"${environment.db.schema.rubicon}".project_sdgs`),
    );
    await runInTransaction(
      em,
      'copy project-vintages',
      async (tx) =>
        await copyToDbFromCSV(
          tx,
          './seeders/csv/project-vintages.csv',
          `"${environment.db.schema.rubicon}".project_vintages`,
        ),
    );
    await runInTransaction(
      em,
      'copy books',
      async (tx) => await copyToDbFromCSV(tx, './seeders/csv/books.csv', `"${environment.db.schema.rubicon}".books`),
    );

    if (environment.env !== 'production') {
      await runInTransaction(
        em,
        'copy environment orgs and users',
        async (tx) => {
          await copyToDbFromCSV(
            tx,
            `./seeders/csv/${environment.info.deploymentEnvironment}/users.csv`,
            `"${environment.db.schema.rubicon}".users`,
          );
          await copyToDbFromCSV(
            tx,
            `./seeders/csv/${environment.info.deploymentEnvironment}/organizations.csv`,
            `"${environment.db.schema.rubicon}".organizations`,
          );
          await copyToDbFromCSV(
            tx,
            `./seeders/csv/${environment.info.deploymentEnvironment}/organizations-users.csv`,
            `"${environment.db.schema.rubicon}".organizations_users`,
          );
        },
        true,
      );
      await runInTransaction(
        em,
        'copy environment customer portfolios and counterparties',
        async (tx) => {
          await copyToDbFromCSV(
            tx,
            `./seeders/csv/${environment.info.deploymentEnvironment}/customer-portfolios.csv`,
            `"${environment.db.schema.rubicon}".customer_portfolios`,
          );
          await copyToDbFromCSV(
            tx,
            `./seeders/csv/${environment.info.deploymentEnvironment}/counterparties.csv`,
            `"${environment.db.schema.rubicon}".counterparties`,
          );
          await copyToDbFromCSV(
            tx,
            `./seeders/csv/${environment.info.deploymentEnvironment}/customer-portfolio-books.csv`,
            `"${environment.db.schema.rubicon}".books`,
            ` where type in('${BookType.PORTFOLIO_CUSTOMER}', '${BookType.RCT_CUSTOM}')`,
          );
        },
        true,
      );
      await runInTransaction(
        em,
        'copy api keys',
        async (tx) => {
          await copyToDbFromCSV(
            tx,
            `./seeders/csv/${environment.info.deploymentEnvironment}/api-keys.csv`,
            `"${environment.db.schema.rubicon}".api_keys`,
          );
        },
        true,
      );
      await runInTransaction(
        em,
        'copy model portfolios',
        async (tx) => {
          await copyToDbFromCSV(
            tx,
            `./seeders/csv/${environment.info.deploymentEnvironment}/model-portfolios.csv`,
            `"${environment.db.schema.rubicon}".model_portfolios`,
          );
          await copyToDbFromCSV(
            tx,
            `./seeders/csv/${environment.info.deploymentEnvironment}/model-portfolio-components.csv`,
            `"${environment.db.schema.rubicon}".model_portfolio_components`,
          );
        },
        true,
      );
    }

    await runInTransaction(em, 'Rubicon Master Org data', async (tx) => {
      // note : this will fail if using env users but that is okay
      return await this.call(tx, [RubiconCarbonOrganizationSeeder]);
    });

    await runInTransaction(em, 'Assets data', async (tx) => {
      return await this.call(tx, [AssetsSeeder]);
    });

    await runInTransaction(em, 'Historical Data', async (tx) => {
      return await this.call(tx, [HistoricalsSeeder]);
    });

    await runInTransaction(em, 'Groupings and Parents', async (tx) => {
      return await this.call(tx, [GroupingsParentsSeeder]);
    });

    await runInTransaction(em, 'Transfer Sources Destinations', async (tx) => {
      return await this.call(tx, [SourcesDestinationsSeeder]);
    });

    await runInTransaction(em, 'BooksProjectTypes', async (tx) => {
      return await this.call(tx, [BooksProjectTypesSeeder]);
    });

    // this should always be the last seeder
    await runInTransaction(em, 'Add Reporting Permissions', async (tx) => {
      return await this.call(tx, [ReportingPermissionsSeeder]);
    });
  }
}
