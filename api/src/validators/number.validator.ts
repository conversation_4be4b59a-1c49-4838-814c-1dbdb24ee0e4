import { ValidationArguments, ValidationOptions, isInt, isNumber, registerDecorator } from 'class-validator';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function IsIntGreaterThan(gtValue: number, _validationOptions?: ValidationOptions) {
  return function (obj: object, propertyName: string): void {
    registerDecorator({
      name: 'isGreaterThan',
      target: obj.constructor,
      propertyName: propertyName,
      constraints: [],
      options: { message: `${propertyName} must be an int greater than ${gtValue}` },
      validator: {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        validate(value: any, args: ValidationArguments) {
          return isInt(value) && Number(value) > gtValue;
        },
      },
    });
  };
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function IsIntGreaterThanOrEqualTo(gteValue: number, _validationOptions?: ValidationOptions) {
  return function (obj: object, propertyName: string): void {
    registerDecorator({
      name: 'isGreaterThan',
      target: obj.constructor,
      propertyName: propertyName,
      constraints: [],
      options: { message: `${propertyName} must be an int greater than or equal to ${gteValue}` },
      validator: {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        validate(value: any, args: ValidationArguments) {
          return isInt(value) && Number(value) >= gteValue;
        },
      },
    });
  };
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function IsIntLessThanOrEqualTo(lteValue: number, _validationOptions?: ValidationOptions) {
  return function (obj: object, propertyName: string): void {
    registerDecorator({
      name: 'isGreaterThan',
      target: obj.constructor,
      propertyName: propertyName,
      constraints: [],
      options: { message: `${propertyName} must be an int less than or equal to ${lteValue}` },
      validator: {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        validate(value: any, args: ValidationArguments) {
          return isInt(value) && Number(value) <= lteValue;
        },
      },
    });
  };
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function IsNonZeroInt(_validationOptions?: ValidationOptions) {
  return function (obj: object, propertyName: string): void {
    registerDecorator({
      name: 'isNonZeroInt',
      target: obj.constructor,
      propertyName: propertyName,
      constraints: [],
      options: { message: `${propertyName} must not be 0` },
      validator: {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        validate(value: any, args: ValidationArguments) {
          return isInt(value) && Number(value) !== 0;
        },
      },
    });
  };
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function IsNumberBetween(gteValue: number, lteValue: number, _validationOptions?: ValidationOptions) {
  return function (obj: object, propertyName: string): void {
    registerDecorator({
      name: 'isBetween',
      target: obj.constructor,
      propertyName: propertyName,
      constraints: [],
      options: { message: `${propertyName} must be a number between ${gteValue} and ${lteValue} inclusive` },
      validator: {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        validate(value: any, args: ValidationArguments) {
          return isNumber(value) && value >= gteValue && value <= lteValue;
        },
      },
    });
  };
}
