/* third party */
import { BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { ApiProperty, ApiPropertyOptional, IntersectionType, OmitType, PartialType, PickType } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import { IsArray, IsEnum, IsIn, IsOptional, IsUUID, ValidateNested } from 'class-validator';
/* rubicon */
import {
  AdminAssetFlowResponse,
  AllTransactionSubtypes,
  AssetFlowOrderByOptions,
  AssetFlowRelation,
  AssetFlowStatus,
  AssetType,
  BaseAssetFlowOrderBy,
  MobileAssetFlowResponse,
  MobileAssetFlowStatus,
  OrderByDirection,
  PortalAssetFlowQuery,
  PortalAssetFlowQueryResponse,
  PortalAssetFlowResponse,
  PortalAssetFlowStatus,
  TransactionSubtype,
  TransactionType,
  uuid,
} from '@rubiconcarbon/shared-types';
/* app */
import { BaseQueryDTO, BaseQueryResponseDTO, BaseResponseDTO } from '@app/dtos/base.dto';
import { TrimmedRetirementLinkDTO } from '@app/dtos/retirement.dto';
import { TrimmedTransactionResponseDTO, toTrimmedTransactionResponseDTO } from '@app/dtos/transaction.dto';
import { AssetFlow, Book, ProjectVintage, RetirementLink } from '@app/entities';
import { IsNonEmptyString } from '@app/helpers';
import { TransformDateISO } from '@app/helpers/transforms.helper';
import { InternalAssetFlowResponse } from '@app/interfaces/flow.interface';
import {
  AdminAssetResponseDTO,
  MobileAssetResponseDTO,
  PortalAssetResponseDTO,
  toAdminAssetResponseDTO,
  toMobileAssetResponseDTO,
  toPortalAssetResponseDTO,
} from './asset.dto';
import { TrimmedBookResponseDTO, toTrimmedBookResponseDTO } from './trimmed.dto';
import { TrimmedProjectVintageResponseDTO, toTrimmedProjectVintageResponseDTO } from './project-vintage.dto';

export class BaseAssetFlowOrderByDTO implements BaseAssetFlowOrderBy {
  constructor(orderBy: string, orderByDirection: string) {
    this.orderBy = orderBy as AssetFlowOrderByOptions;
    this.orderByDirection = orderByDirection as OrderByDirection;
  }

  @IsEnum(AssetFlowOrderByOptions)
  @ApiProperty({
    enum: AssetFlowOrderByOptions,
    description: 'order by options',
  })
  orderBy!: AssetFlowOrderByOptions;

  @IsEnum(OrderByDirection)
  @ApiProperty({
    enum: OrderByDirection,
    description: 'orderBy in this direction',
  })
  orderByDirection!: OrderByDirection;
}

export class MobileAssetFlowResponseDTO
  extends IntersectionType(BaseResponseDTO, PickType(AssetFlow, ['amount', 'transactionSubtype', 'transactionType']))
  implements MobileAssetFlowResponse
{
  @Expose()
  @Type(() => MobileAssetResponseDTO)
  @ApiPropertyOptional({ type: () => MobileAssetResponseDTO, readOnly: true, nullable: true })
  asset!: MobileAssetResponseDTO;

  @Expose()
  @TransformDateISO()
  @ApiPropertyOptional({ type: Date, format: 'iso', readOnly: true, nullable: true })
  lastUpdatedDeliveryDate?: Date;

  @Expose()
  @TransformDateISO()
  @ApiPropertyOptional({ type: Date, format: 'iso', nullable: true })
  settledAt?: Date;

  @Expose()
  @ApiProperty({ enum: AssetFlowStatus, readOnly: true })
  status!: MobileAssetFlowStatus;
}

export class PortalAssetFlowResponseDTO
  extends OmitType(MobileAssetFlowResponseDTO, ['asset', 'status'])
  implements PortalAssetFlowResponse
{
  @Expose()
  @Type(() => PortalAssetResponseDTO)
  @ApiPropertyOptional({ type: () => PortalAssetResponseDTO, readOnly: true, nullable: true })
  asset!: PortalAssetResponseDTO;

  @Expose()
  @Type(() => IntersectionType(PartialType(TrimmedProjectVintageResponseDTO), PartialType(TrimmedBookResponseDTO)))
  @ApiProperty({ type: () => TrimmedProjectVintageResponseDTO || TrimmedBookResponseDTO, readOnly: true })
  detailedAsset?: TrimmedProjectVintageResponseDTO | TrimmedBookResponseDTO;

  @Expose()
  @Type(() => TrimmedRetirementLinkDTO)
  @ApiPropertyOptional({
    type: () => [TrimmedRetirementLinkDTO],
    nullable: true,
  })
  links?: TrimmedRetirementLinkDTO[];

  @Expose()
  @Type(() => TrimmedBookResponseDTO)
  @ApiPropertyOptional({ type: () => TrimmedBookResponseDTO, readOnly: true, nullable: true })
  source?: TrimmedBookResponseDTO;

  @Expose()
  @ApiProperty({ enum: AssetFlowStatus, readOnly: true })
  status!: PortalAssetFlowStatus;

  @Expose()
  @Type(() => TrimmedTransactionResponseDTO)
  @ApiPropertyOptional({
    type: () => TrimmedTransactionResponseDTO,
    readOnly: true,
    nullable: true,
  })
  transaction?: TrimmedTransactionResponseDTO;
}

export class AdminAssetFlowResponseDTO
  extends OmitType(PortalAssetFlowResponseDTO, ['asset', 'status'])
  implements AdminAssetFlowResponse
{
  @Expose()
  @Type(() => AdminAssetResponseDTO)
  @ApiPropertyOptional({ type: () => AdminAssetResponseDTO, readOnly: true, nullable: true })
  asset!: AdminAssetResponseDTO;

  @Expose()
  @ApiProperty({ enum: AssetFlowStatus, readOnly: true })
  status!: AssetFlowStatus;
}

export class PortalAssetFlowQueryDTO extends IntersectionType(BaseQueryDTO) implements PortalAssetFlowQuery {
  @IsUUID()
  @IsOptional()
  @ApiPropertyOptional({ type: String, format: 'uuid', nullable: true })
  assetId?: uuid;

  @IsEnum(AssetType)
  @IsOptional()
  @ApiPropertyOptional({
    enum: AssetType,
    nullable: true,
  })
  assetType?: AssetType;

  @IsEnum(AssetFlowStatus)
  @IsOptional()
  @ApiPropertyOptional({
    enum: AssetFlowStatus,
    nullable: true,
  })
  status?: AssetFlowStatus;

  @IsUUID()
  @IsOptional()
  @ApiPropertyOptional({ type: String, format: 'uuid', nullable: true })
  transactionId?: uuid;

  @IsNonEmptyString()
  @IsIn(AllTransactionSubtypes, { each: true })
  @IsOptional()
  @ApiPropertyOptional({
    type: String,
    nullable: true,
  })
  transactionSubtypes?: TransactionSubtype[];

  @IsEnum(TransactionType, { each: true })
  @IsOptional()
  @ApiPropertyOptional({
    enum: TransactionType,
    nullable: true,
  })
  transactionTypes?: TransactionType[];

  @Transform(({ value }) => value.toString().split(','))
  @IsEnum(AssetFlowRelation, { each: true })
  @IsArray()
  @IsOptional()
  @ApiPropertyOptional({
    enum: AssetFlowRelation,
    description: 'relations to include',
    example: [AssetFlowRelation.SOURCE],
  })
  includeRelations: AssetFlowRelation[] = [];

  @Transform(({ value }) => {
    if (!value || value.length === 0) {
      return;
    }
    const orderBys = value.toString().split(',');
    const errors: string[] = [];
    const mappings: BaseAssetFlowOrderByDTO[] = orderBys.map((ob: string) => {
      const split = ob.split(':');
      return new BaseAssetFlowOrderByDTO(split[0], split[1]);
    });
    if (errors.length > 0) {
      throw new BadRequestException(errors);
    }
    return mappings;
  })
  @ValidateNested({ each: true })
  @Type(() => BaseAssetFlowOrderByDTO)
  @IsArray()
  @IsOptional()
  @ApiPropertyOptional({
    type: [String],
    format: '[AssetFlowOrderByOption:OrderByDirection]',
    nullable: true,
    default: [`${AssetFlowOrderByOptions.CREATED_AT}:${OrderByDirection.ASC}`],
    example: [`${AssetFlowOrderByOptions.CREATED_AT}:${OrderByDirection.ASC}`],
  })
  orderBys: BaseAssetFlowOrderByDTO[] = [
    new BaseAssetFlowOrderByDTO(AssetFlowOrderByOptions.CREATED_AT, OrderByDirection.ASC),
  ];
}

export class PortalAssetFlowQueryResponseDTO extends BaseQueryResponseDTO implements PortalAssetFlowQueryResponse {
  @Expose()
  @Type(() => PortalAssetFlowResponseDTO)
  @ApiProperty({ type: () => [PortalAssetFlowResponseDTO], readOnly: true })
  data: PortalAssetFlowResponseDTO[] = [];
}

/* transformation */

export function toAdminAssetFlowResponseDTO(
  afResponse: InternalAssetFlowResponse,
  includeRelations: AssetFlowRelation[],
): AdminAssetFlowResponseDTO {
  if (!afResponse.assetFlow.transaction.details) {
    throw new InternalServerErrorException(`assetFlow.transaction.details must be included in response`);
  }

  const links =
    includeRelations.includes(AssetFlowRelation.LINKS) && afResponse.assetFlow.links.isInitialized()
      ? afResponse.assetFlow.links.getItems().map((m: RetirementLink) => {
          return { ...m };
        })
      : undefined;

  return Object.assign(new AdminAssetFlowResponseDTO(), {
    ...afResponse.assetFlow,
    asset: toAdminAssetResponseDTO(afResponse.assetFlow.asset),
    links,
    transaction: toTrimmedTransactionResponseDTO(true, afResponse.assetFlow.transaction),
  });
}

export function toMobileAssetFlowResponseDTO(assetFlow: AssetFlow): MobileAssetFlowResponseDTO {
  return Object.assign(new MobileAssetFlowResponseDTO(), {
    ...assetFlow,
    asset: toMobileAssetResponseDTO(assetFlow.asset),
  });
}

export function toPortalAssetFlowResponseDTO(
  afResponse: InternalAssetFlowResponse,
  includeRelations: AssetFlowRelation[],
): PortalAssetFlowResponseDTO {
  if (!afResponse.assetFlow.transaction.details) {
    throw new InternalServerErrorException(`assetFlow.transaction.details must be included in response`);
  }
  if (includeRelations.includes(AssetFlowRelation.DETAILED_ASSET) && !afResponse.detailedAsset) {
    throw new InternalServerErrorException(`detailedAsset must be included in response`);
  }
  if (includeRelations.includes(AssetFlowRelation.SOURCE) && !afResponse.assetFlow.source) {
    throw new InternalServerErrorException(`assetFlow.source must be included in response`);
  }

  const links =
    includeRelations.includes(AssetFlowRelation.LINKS) && afResponse.assetFlow.links.isInitialized()
      ? afResponse.assetFlow.links.getItems().map((m: RetirementLink) => {
          return { ...m };
        })
      : undefined;

  return Object.assign(new PortalAssetFlowResponseDTO(), {
    ...afResponse.assetFlow,
    asset: toPortalAssetResponseDTO(afResponse.assetFlow.asset),
    detailedAsset: includeRelations.includes(AssetFlowRelation.DETAILED_ASSET)
      ? afResponse.assetFlow.assetType === AssetType.RCT
        ? toTrimmedBookResponseDTO(afResponse.detailedAsset as Book)
        : afResponse.assetFlow.assetType === AssetType.REGISTRY_VINTAGE
          ? toTrimmedProjectVintageResponseDTO(afResponse.detailedAsset as ProjectVintage)
          : undefined
      : undefined,
    links,
    source: includeRelations.includes(AssetFlowRelation.SOURCE)
      ? toTrimmedBookResponseDTO(afResponse.assetFlow.source)
      : undefined,
    transaction: toTrimmedTransactionResponseDTO(false, afResponse.assetFlow.transaction),
  });
}
