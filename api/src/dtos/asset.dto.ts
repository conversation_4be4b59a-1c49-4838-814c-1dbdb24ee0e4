/* third party */
import { ApiProperty, ApiPropertyOptional, IntersectionType, OmitType, PartialType, PickType } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import { IsEnum, IsOptional, IsUUID } from 'class-validator';
import Decimal from 'decimal.js';
/* rubicon */
import {
  AdminAssetPurchaseRequest,
  AdminAssetPurchaseResponse,
  AdminAssetResponse,
  AdminAssetTradeResponse,
  AdminAssetTransferRequest,
  AdminAssetTransferResponse,
  AdminAssetTypeQuery,
  AdminRctAssetRetirementResponse,
  AdminRrtAssetResponse,
  AdminVintageAssetDetailsResponse,
  AdminVintageAssetRetirementResponse,
  AssetType,
  BaseAssetRetirementRequest,
  BaseAssetTransactionRequest,
  BookType,
  MobileAssetResponse,
  MobileAssetTypeQuery,
  MobileVintageAssetDetailsResponse,
  PortalAssetPurchaseResponse,
  PortalAssetResponse,
  PortalAssetTypeQuery,
  PortalRctAssetRetirementResponse,
  PortalVintageAssetDetailsResponse,
  PortalVintageAssetRetirementResponse,
  ProjectBeZeroRating,
  uuid,
} from '@rubiconcarbon/shared-types';
/* app */
import { TrimmedRetirementLinkDTO } from '@app/dtos/retirement.dto';
import { TrimmedBookResponseDTO } from '@app/dtos/trimmed.dto';
import { TransformDateISO, TransformDecimal } from '@app/helpers/transforms.helper';
import { IsIntGreaterThan, IsIntGreaterThanOrEqualTo } from '@app/validators';
import { AdminProjectVintageResponseDTO, TrimmedProjectVintageResponseDTO } from './project-vintage.dto';
import { Asset, AssetDetail, AssetFlow, Project, RrtAsset } from '@app/entities';
import { InternalServerErrorException } from '@nestjs/common';
import { BaseResponseDTO } from './base.dto';

class BaseAssetRequestDTO implements BaseAssetTransactionRequest {
  @IsIntGreaterThan(0)
  @ApiProperty({ type: 'integer', example: 1500 })
  amount!: number;

  @IsUUID()
  @ApiProperty({ type: String, format: 'uuid' })
  assetId!: uuid;
}

export class AdminRrtVintageAssetResponseDTO
  extends IntersectionType(BaseResponseDTO, PickType(RrtAsset, ['beZeroFactor']))
  implements AdminRrtAssetResponse
{
  @Expose()
  @TransformDecimal()
  @ApiProperty({ type: Number, format: 'decimal', description: 'BeZero percentage of vintage in RRT' })
  calculatedFactor!: Decimal;

  @Expose()
  @ApiProperty({ type: Number, format: 'integer', description: 'net amount of vintage in RRT' })
  grossQuantity!: number;

  @Expose()
  @ApiProperty({ type: Number, format: 'integer', description: 'net amount of vintage in RRT' })
  netQuantity!: number;

  @Expose()
  @Type(() => TrimmedBookResponseDTO)
  @ApiProperty({ type: () => TrimmedBookResponseDTO, readOnly: true })
  portfolio!: TrimmedBookResponseDTO;

  @Expose()
  @TransformDecimal()
  @ApiProperty({ type: Number, format: 'decimal', description: 'BeZero percentage of vintage in RRT' })
  portfolioNetPercentage!: Decimal;

  @Expose()
  @Type(() => TrimmedProjectVintageResponseDTO)
  @ApiProperty({ type: () => TrimmedProjectVintageResponseDTO, readOnly: true })
  projectVintage!: TrimmedProjectVintageResponseDTO;
}

class MobileRctAssetDetailsResponseDTO {
  @Expose()
  @ApiProperty({ type: String, format: 'uuid', readOnly: true })
  portfolioId!: uuid;

  @Expose()
  @ApiProperty({ type: String, readOnly: true })
  portfolioName!: string;

  @Expose()
  @ApiProperty({ enum: BookType, readOnly: true })
  portfolioType!: BookType;
}

class PortalRctAssetDetailsResponseDTO extends MobileRctAssetDetailsResponseDTO {}

class AdminRctAssetDetailsResponseDTO extends PortalRctAssetDetailsResponseDTO {}

class MobileVintageAssetDetailsResponseDTO
  extends PickType(Project, ['isByorctApproved', 'registryProjectId'])
  implements MobileVintageAssetDetailsResponse
{
  @Expose()
  @ApiPropertyOptional({ enum: ProjectBeZeroRating, readOnly: true, nullable: true })
  beZeroRating?: ProjectBeZeroRating;

  @Expose()
  @TransformDateISO()
  @ApiProperty({ type: Date, readOnly: true, nullable: true })
  beZeroUpdatedDate?: Date;

  @Expose()
  @ApiPropertyOptional({ type: String, readOnly: true, nullable: true })
  countryAlpha3?: string;

  @Expose()
  @ApiPropertyOptional({ type: String, readOnly: true, nullable: true })
  countryName?: string;

  @Expose()
  @ApiProperty({ type: String, format: 'uuid', readOnly: true })
  projectId!: uuid;

  @Expose()
  @ApiProperty({ type: String, readOnly: true })
  projectName!: string;

  @Expose()
  @ApiProperty({ type: String, readOnly: true })
  projectTypeCategory!: string;

  @Expose()
  @ApiProperty({ type: Number, format: 'integer', readOnly: true })
  projectTypeId!: number;

  @Expose()
  @ApiProperty({ type: String, readOnly: true })
  projectTypeType!: string;

  @Expose()
  @ApiProperty({ type: String, readOnly: true })
  projectVintageName!: string;

  @Expose()
  @ApiPropertyOptional({ type: String, readOnly: true, nullable: true })
  registryName?: string;
}

class PortalVintageAssetDetailsResponseDTO
  extends MobileVintageAssetDetailsResponseDTO
  implements PortalVintageAssetDetailsResponse {}

class AdminVintageAssetDetailsResponseDTO
  extends IntersectionType(PortalVintageAssetDetailsResponseDTO, PickType(Project, ['isScienceTeamApproved']))
  implements AdminVintageAssetDetailsResponse
{
  @Expose()
  @ApiPropertyOptional({ type: String, readOnly: true, nullable: true })
  bufferCategoryName?: string;

  @Expose()
  @TransformDecimal()
  @ApiPropertyOptional({ type: Number, format: 'decimal', nullable: true })
  highBufferPercentage?: Decimal;

  @Expose()
  @ApiProperty({ type: Boolean })
  isRctEligible!: boolean;

  @Expose()
  @ApiProperty({ type: Boolean })
  isRctStandard!: boolean;

  @Expose()
  @ApiProperty({ type: Boolean })
  isSuspended!: boolean;

  @Expose()
  @TransformDecimal()
  @ApiPropertyOptional({ type: Number, format: 'decimal', nullable: true })
  lowBufferPercentage?: Decimal;

  @Expose()
  @TransformDecimal()
  @ApiPropertyOptional({ type: Number, format: 'decimal', nullable: true })
  riskBufferPercentage?: Decimal;
}

class MobileRrtVintageAssetDetailsResponseDTO extends IntersectionType(
  MobileVintageAssetDetailsResponseDTO,
  MobileRctAssetDetailsResponseDTO,
  PickType(RrtAsset, ['beZeroFactor']),
) {
  @Expose()
  @ApiProperty({ type: Number, format: 'integer', description: 'net amount of vintage in RRT' })
  netQuantity!: number;
}

class PortalRrtVintageAssetDetailsResponseDTO extends IntersectionType(
  PortalVintageAssetDetailsResponseDTO,
  PortalRctAssetDetailsResponseDTO,
  PickType(RrtAsset, ['beZeroFactor']),
) {
  @Expose()
  @ApiProperty({ type: Number, format: 'integer', description: 'net amount of vintage in RRT' })
  netQuantity!: number;
}

class AdminRrtVintageAssetDetailsResponseDTO extends IntersectionType(
  AdminVintageAssetDetailsResponseDTO,
  AdminRctAssetDetailsResponseDTO,
  PickType(RrtAsset, ['beZeroFactor']),
) {
  @Expose()
  @ApiProperty({ type: Number, format: 'integer', description: 'net amount of vintage in RRT' })
  netQuantity!: number;
}

export class MobileAssetResponseDTO
  extends IntersectionType(
    BaseResponseDTO,
    PickType(AssetDetail, ['type', 'name']),
    PartialType(MobileRctAssetDetailsResponseDTO),
    PartialType(MobileRrtVintageAssetDetailsResponseDTO),
    PartialType(MobileVintageAssetDetailsResponseDTO),
  )
  implements MobileAssetResponse {}

export class PortalAssetResponseDTO
  extends IntersectionType(
    BaseResponseDTO,
    PickType(AssetDetail, ['type', 'name']),
    PartialType(PortalRctAssetDetailsResponseDTO),
    PartialType(PortalRrtVintageAssetDetailsResponseDTO),
    PartialType(PortalVintageAssetDetailsResponseDTO),
  )
  implements PortalAssetResponse {}

export class AdminAssetResponseDTO
  extends IntersectionType(
    BaseResponseDTO,
    PickType(AssetDetail, ['type', 'name']),
    PartialType(AdminRctAssetDetailsResponseDTO),
    PartialType(AdminRrtVintageAssetDetailsResponseDTO),
    PartialType(AdminVintageAssetDetailsResponseDTO),
  )
  implements AdminAssetResponse {}

export class AdminAssetPurchaseRequestDTO
  extends IntersectionType(BaseAssetRequestDTO, PickType(AssetFlow, ['otherFee', 'rawPrice', 'serviceFee']))
  implements AdminAssetPurchaseRequest
{
  @IsUUID()
  @ApiProperty({ type: String, format: 'uuid' })
  sourceId!: uuid;
}

export class BaseAssetRetirementRequestDTO
  extends IntersectionType(BaseAssetRequestDTO, PickType(AssetFlow, ['otherFee', 'rawPrice', 'serviceFee']))
  implements BaseAssetRetirementRequest {}

export class AdminAssetTradeRequestDTO extends BaseAssetRequestDTO {}

export class AdminAssetTransferRequestDTO extends BaseAssetRequestDTO implements AdminAssetTransferRequest {
  @IsUUID()
  @ApiProperty({ type: String, format: 'uuid' })
  destinationId!: uuid;

  @IsUUID()
  @ApiProperty({ type: String, format: 'uuid' })
  sourceId!: uuid;
}

export class AdminRrtAssetCompositionRequestDTO extends PickType(RrtAsset, ['beZeroFactor']) {
  @IsIntGreaterThanOrEqualTo(0)
  @ApiProperty({
    type: Number,
    format: 'integer',
    description: 'net quantity of vintage in RRT',
  })
  netQuantity!: number;

  @IsUUID()
  @ApiProperty({ type: String, format: 'uuid' })
  projectVintageId!: uuid;

  @IsIntGreaterThanOrEqualTo(0)
  @ApiProperty({
    type: 'integer',
    example: 1500,
    description: 'gross quantity of vintage in RRT',
  })
  grossQuantity!: number;
}

export class MobileAssetPurchaseResponseDTO extends BaseResponseDTO {
  @Expose()
  @ApiProperty({ type: 'integer', readOnly: true })
  amount!: number;

  @Expose()
  @Type(() => MobileAssetResponseDTO)
  @ApiProperty({ type: () => MobileAssetResponseDTO, readOnly: true })
  asset!: MobileAssetResponseDTO;

  @Expose()
  @TransformDecimal()
  @ApiProperty({ type: 'decimal', minimum: 0, exclusiveMinimum: false, example: 15000 })
  totalPrice!: Decimal;
}

export class PortalAssetPurchaseResponseDTO
  extends OmitType(MobileAssetPurchaseResponseDTO, ['asset'])
  implements PortalAssetPurchaseResponse
{
  @Expose()
  @Type(() => PortalAssetResponseDTO)
  @ApiProperty({ type: () => PortalAssetResponseDTO, readOnly: true })
  asset!: PortalAssetResponseDTO;
}

export class AdminAssetPurchaseResponseDTO
  extends IntersectionType(
    OmitType(PortalAssetPurchaseResponseDTO, ['asset']),
    PickType(AssetFlow, ['amount', 'otherFee', 'rawPrice', 'serviceFee']),
  )
  implements AdminAssetPurchaseResponse
{
  @Expose()
  @ApiProperty({ type: () => AdminAssetResponseDTO, readOnly: true })
  asset!: AdminAssetResponseDTO;

  @Expose()
  @Type(() => TrimmedBookResponseDTO)
  @ApiProperty({ type: () => TrimmedBookResponseDTO, readOnly: true })
  destination!: TrimmedBookResponseDTO;

  @Expose()
  @Type(() => TrimmedBookResponseDTO)
  @ApiProperty({ type: () => TrimmedBookResponseDTO, readOnly: true })
  source!: TrimmedBookResponseDTO;
}

export class AdminAssetTradeResponseDTO
  extends IntersectionType(BaseResponseDTO, PickType(AssetFlow, ['amount', 'otherFee', 'rawPrice', 'serviceFee']))
  implements AdminAssetTradeResponse
{
  @Expose()
  @ApiProperty({ type: () => AdminAssetResponseDTO, readOnly: true })
  asset!: AdminAssetResponseDTO;

  @Expose()
  @Type(() => TrimmedBookResponseDTO)
  @ApiPropertyOptional({ type: () => TrimmedBookResponseDTO, readOnly: true, nullable: true })
  destination?: TrimmedBookResponseDTO;

  @Expose()
  @Type(() => TrimmedBookResponseDTO)
  @ApiPropertyOptional({ type: () => TrimmedBookResponseDTO, readOnly: true, nullable: true })
  source?: TrimmedBookResponseDTO;

  @Expose()
  @TransformDecimal()
  @ApiProperty({ type: Number, format: 'decimal' })
  totalPrice!: Decimal;
}

export class AdminAssetTransferResponseDTO extends BaseResponseDTO implements AdminAssetTransferResponse {
  @Expose()
  @ApiProperty({ type: 'integer', readOnly: true })
  amount!: number;

  @Expose()
  @Type(() => AdminAssetResponseDTO)
  @ApiProperty({ type: () => AdminAssetResponseDTO, readOnly: true })
  asset!: AdminAssetResponseDTO;

  @Expose()
  @Type(() => TrimmedBookResponseDTO)
  @ApiProperty({ type: () => TrimmedBookResponseDTO, readOnly: true })
  destination!: TrimmedBookResponseDTO;

  @Expose()
  @Type(() => TrimmedBookResponseDTO)
  @ApiProperty({ type: () => TrimmedBookResponseDTO, readOnly: true })
  source!: TrimmedBookResponseDTO;
}

export class PortalVintageAssetRetirementResponseDTO
  extends BaseResponseDTO
  implements PortalVintageAssetRetirementResponse
{
  @Expose()
  @ApiProperty({ type: 'integer', readOnly: true, example: 1500 })
  amount!: number;

  @Expose()
  @Type(() => PortalAssetResponseDTO)
  @ApiProperty({ type: () => PortalAssetResponseDTO, readOnly: true })
  asset!: PortalAssetResponseDTO;

  @Expose()
  @Type(() => TrimmedRetirementLinkDTO)
  @ApiPropertyOptional({
    type: () => [TrimmedRetirementLinkDTO],
    nullable: true,
  })
  links?: TrimmedRetirementLinkDTO[];

  @Expose()
  @TransformDecimal()
  @ApiProperty({ type: 'decimal', minimum: 0, exclusiveMinimum: false, example: 15000 })
  totalPrice!: Decimal;
}

export class AdminVintageAssetRetirementResponseDTO
  extends IntersectionType(
    BaseResponseDTO,
    OmitType(PortalVintageAssetRetirementResponseDTO, ['asset']),
    PickType(AssetFlow, ['otherFee', 'rawPrice', 'serviceFee']),
  )
  implements AdminVintageAssetRetirementResponse
{
  @Expose()
  @Type(() => AdminAssetResponseDTO)
  @ApiProperty({ type: () => AdminAssetResponseDTO, readOnly: true })
  asset!: AdminAssetResponseDTO;

  @Expose()
  @Type(() => AdminProjectVintageResponseDTO)
  @ApiProperty({ type: () => AdminProjectVintageResponseDTO, readOnly: true })
  projectVintage!: AdminProjectVintageResponseDTO;

  @Expose()
  @Type(() => TrimmedBookResponseDTO)
  @ApiProperty({ type: () => TrimmedBookResponseDTO, readOnly: true })
  source!: TrimmedBookResponseDTO;
}

export class PortalRctAssetRetirementResponseDTO extends BaseResponseDTO implements PortalRctAssetRetirementResponse {
  @Expose()
  @ApiProperty({ type: 'integer', readOnly: true, example: 1500 })
  amount!: number;

  @Expose()
  @Type(() => PortalAssetResponseDTO)
  @ApiProperty({ type: () => PortalAssetResponseDTO, readOnly: true })
  asset!: PortalAssetResponseDTO;

  @Expose()
  @Type(() => PortalVintageAssetRetirementResponseDTO)
  @ApiProperty({ type: () => [PortalVintageAssetRetirementResponseDTO], readOnly: true, nullable: true })
  associatedVintages?: PortalVintageAssetRetirementResponseDTO[];

  @Expose()
  @TransformDecimal()
  @ApiProperty({ type: 'decimal', minimum: 0, exclusiveMinimum: false, example: 15000 })
  totalPrice!: Decimal;
}

export class AdminRctAssetRetirementResponseDTO
  extends IntersectionType(BaseResponseDTO, PickType(AssetFlow, ['amount', 'otherFee', 'rawPrice', 'serviceFee']))
  implements AdminRctAssetRetirementResponse
{
  @Expose()
  @Type(() => AdminAssetResponseDTO)
  @ApiProperty({ type: () => AdminAssetResponseDTO, readOnly: true })
  asset!: AdminAssetResponseDTO;

  @Expose()
  @Type(() => AdminVintageAssetRetirementResponseDTO)
  @ApiProperty({ type: () => [AdminVintageAssetRetirementResponseDTO], readOnly: true, nullable: true })
  associatedVintages?: AdminVintageAssetRetirementResponseDTO[];

  @Expose()
  @Type(() => TrimmedBookResponseDTO)
  @ApiProperty({ type: () => TrimmedBookResponseDTO, readOnly: true })
  source!: TrimmedBookResponseDTO;

  @Expose()
  @TransformDecimal()
  @ApiProperty({ type: 'decimal', minimum: 0, exclusiveMinimum: false, example: 15000 })
  totalPrice!: Decimal;
}

export class MobileAssetTypeQueryDTO implements MobileAssetTypeQuery {
  @Transform(({ value }) => value.toString().split(','))
  @IsEnum(AssetType, { each: true })
  @IsOptional()
  @ApiProperty({ type: [String], enum: AssetType, example: [AssetType.RCT, AssetType.REGISTRY_VINTAGE] })
  assetTypes?: AssetType[];
}

export class PortalAssetTypeQueryDTO extends MobileAssetTypeQueryDTO implements PortalAssetTypeQuery {}
export class AdminAssetTypeQueryDTO extends PortalAssetTypeQueryDTO implements AdminAssetTypeQuery {}

/* transforms */

function validateRelations(asset: Asset): void {
  if (asset.type === AssetType.REGISTRY_VINTAGE && !asset.details.vintageDetails?.registry_project_id) {
    throw new InternalServerErrorException(`vintageDetails must be included in relation`);
  }
  if ([AssetType.RCT, AssetType.RRT].includes(asset.type) && !asset.details.portfolioDetails?.name) {
    throw new InternalServerErrorException(`portfolioDetails must be included in relation`);
  }
  if (
    asset.type === AssetType.RRT_VINTAGE &&
    !(asset.details.vintageDetails && asset.details.portfolioDetails && asset.details.rrtDetails?.beZeroFactor)
  ) {
    throw new InternalServerErrorException(
      `vintageDetails, portfolioDetails, and rrtDetails must be included in relation`,
    );
  }
}

export function toMobileAssetResponseDTO(asset: Asset): MobileAssetResponseDTO {
  validateRelations(asset);

  return Object.assign(new MobileAssetResponseDTO(), {
    ...asset,
    ...asset.details,
    beZeroFactor: asset.details.rrtDetails?.beZeroFactor,
    beZeroRating: asset.details.vintageDetails?.project_be_zero_rating,
    beZeroUpdatedDate: asset.details.vintageDetails?.project_be_zero_updated_date,
    countryAlpha3: asset.details.vintageDetails?.project_country_alpha3,
    countryName: asset.details.vintageDetails?.project_country_name,
    isByorctApproved: asset.details.vintageDetails?.project_is_byorct_approved,
    netQuantity: asset.details.rrtDetails?.currentNetQuantity,
    portfolioId: asset.details.portfolioDetails?.id,
    portfolioName: asset.details.portfolioDetails?.name,
    portfolioType: asset.details.portfolioDetails?.type,
    projectId: asset.details.vintageDetails?.project.id,
    projectTypeCategory: asset.details.vintageDetails?.project_type_category,
    projectTypeId: asset.details.vintageDetails?.projectType.id,
    projectTypeType: asset.details.vintageDetails?.project_type_type,
    projectVintageName:
      asset.details.vintageDetails?.project_vintage_label ??
      asset.details.vintageDetails?.project_vintage_interval.toString(),
    registryName: asset.details.vintageDetails?.project_registry_name,
  });
}

export function toPortalAssetResponseDTO(asset: Asset): PortalAssetResponseDTO {
  validateRelations(asset);

  const response = Object.assign(new PortalAssetResponseDTO(), {
    ...asset,
    ...asset.details,
    beZeroFactor: asset.details.rrtDetails?.beZeroFactor,
    beZeroRating: asset.details.vintageDetails?.project_be_zero_rating,
    beZeroUpdatedDate: asset.details.vintageDetails?.project_be_zero_updated_date,
    countryAlpha3: asset.details.vintageDetails?.project_country_alpha3,
    countryName: asset.details.vintageDetails?.project_country_name,
    isByorctApproved: asset.details.vintageDetails?.project_is_byorct_approved,
    netQuantity: asset.details.rrtDetails?.currentNetQuantity,
    portfolioId: asset.details.portfolioDetails?.id,
    portfolioName: asset.details.portfolioDetails?.name,
    portfolioType: asset.details.portfolioDetails?.type,
    projectId: asset.details.vintageDetails?.project.id,
    projectTypeCategory: asset.details.vintageDetails?.project_type_category,
    projectTypeId: asset.details.vintageDetails?.projectType.id,
    projectTypeType: asset.details.vintageDetails?.project_type_type,
    projectVintageName:
      asset.details.vintageDetails?.project_vintage_label ??
      asset.details.vintageDetails?.project_vintage_interval.toString(),
    registryName: asset.details.vintageDetails?.project_registry_name,
  });
  return response;
}

export function toAdminAssetResponseDTO(asset: Asset): AdminAssetResponseDTO {
  validateRelations(asset);

  return Object.assign(new AdminAssetResponseDTO(), {
    ...asset,
    ...asset.details,
    beZeroFactor: asset.details.rrtDetails?.beZeroFactor,
    beZeroRating: asset.details.vintageDetails?.project_be_zero_rating,
    beZeroUpdatedDate: asset.details.vintageDetails?.project_be_zero_updated_date,
    bufferCategoryName: asset.details.vintageDetails?.buffer_category_name,
    countryAlpha3: asset.details.vintageDetails?.project_country_alpha3,
    countryName: asset.details.vintageDetails?.project_country_name,
    highBufferPercentage: asset.details.vintageDetails?.project_vintage_high_buffer_percentage,
    isByorctApproved: asset.details.vintageDetails?.project_is_byorct_approved,
    isRctEligible: asset.details.vintageDetails?.is_rct_eligible,
    isRctStandard: asset.details.vintageDetails?.project_is_rct_standard,
    isScienceTeamApproved: asset.details.vintageDetails?.project_is_rct_standard,
    isSuspended: asset.details.vintageDetails?.project_is_suspended,
    lowBufferPercentage: asset.details.vintageDetails?.project_vintage_low_buffer_percentage,
    netQuantity: asset.details.rrtDetails?.currentNetQuantity,
    portfolioId: asset.details.portfolioDetails?.id,
    portfolioName: asset.details.portfolioDetails?.name,
    portfolioType: asset.details.portfolioDetails?.type,
    projectId: asset.details.vintageDetails?.project.id,
    projectTypeCategory: asset.details.vintageDetails?.project_type_category,
    projectTypeId: asset.details.vintageDetails?.projectType.id,
    projectTypeType: asset.details.vintageDetails?.project_type_type,
    projectVintageName:
      asset.details.vintageDetails?.project_vintage_label ??
      asset.details.vintageDetails?.project_vintage_interval.toString(),
    registryName: asset.details.vintageDetails?.project_registry_name,
    riskBufferPercentage: asset.details.vintageDetails?.project_vintage_risk_buffer_percentage,
  });
}
