/* third party */
import {
  Api<PERSON>roperty,
  ApiPropertyOptional,
  IntersectionType,
  OmitType,
  PartialType,
  PickType,
  getSchemaPath,
} from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import { IsArray, IsBoolean, IsEnum, IsIn, IsOptional, IsUUID, ValidateIf, ValidateNested } from 'class-validator';
import Decimal from 'decimal.js';
/* rubicon */
import {
  AdminModelPortfolioComponentRequest,
  AdminModelPortfolioComponentResponse,
  AdminModelPortfolioCreateRequest,
  AdminModelPortfolioQuery,
  AdminModelPortfolioQueryResponse,
  AdminModelPortfolioRelationsQuery,
  AdminModelPortfolioResponse,
  AdminModelPortfolioUpdateRequest,
  AllMobileModelPortfolioRelations,
  AllPortalModelPortfolioRelations,
  BaseModelPortfolioComponentDelete,
  BaseModelPortfolioOrderBy,
  MobileModelPortfolioComponentResponse,
  MobileModelPortfolioQuery,
  MobileModelPortfolioQueryResponse,
  MobileModelPortfolioRelations,
  MobileModelPortfolioRelationsQuery,
  MobileModelPortfolioResponse,
  ModelPortfolioBookComponentCreate,
  ModelPortfolioBookComponentUpdate,
  ModelPortfolioOrderByOption,
  ModelPortfolioStatus,
  ModelPortfolioVintageComponentCreate,
  ModelPortfolioVintageComponentUpdate,
  OrderByDirection,
  PortalModelPortfolioComponentResponse,
  PortalModelPortfolioQuery,
  PortalModelPortfolioQueryResponse,
  PortalModelPortfolioRelations,
  PortalModelPortfolioRelationsQuery,
  PortalModelPortfolioResponse,
  ProjectVintageRelations,
  uuid,
} from '@rubiconcarbon/shared-types';
/* app */
import { ModelPortfolio, ModelPortfolioComponent } from '@app/entities';
import { IsNonEmptyString } from '@app/helpers/validations.helper';
import { InternalModelPortfolio } from '@app/interfaces/model-portfolio.interface';
import { RequireType } from '@app/types';
import { BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { AdminGroupedPortfolioResponse, ModelPortfolioRelations } from '@rubiconcarbon/shared-types';
import { BaseQueryDTO, BaseQueryResponseDTO, BaseResponseDTO } from './base.dto';
import { AdminBufferCategoryResponseDTO } from './buffer-categories.dto';
import {
  AdminProjectVintageResponseDTO,
  TrimmedProjectVintageResponseDTO,
  toAdminProjectVintageResponseDTO,
  toTrimmedProjectVintageResponseDTO,
} from './project-vintage.dto';
import {
  AdminProjectResponseDTO,
  TrimmedProjectResponseDTO,
  toAdminProjectResponseDTO,
  toTrimmedProjectResponseDTO,
} from './project.dto';
import { TrimmedBookResponseDTO, TrimmedOrganizationResponseDTO, TrimmedUserResponseDTO } from './trimmed.dto';

export class BaseModelPortfolioOrderByDTO implements BaseModelPortfolioOrderBy {
  constructor(orderBy: string, orderByDirection: string) {
    this.orderBy = orderBy as ModelPortfolioOrderByOption;
    this.orderByDirection = orderByDirection as OrderByDirection;
  }

  @IsEnum(ModelPortfolioOrderByOption)
  @ApiProperty({
    enum: ModelPortfolioOrderByOption,
    description: 'order by options',
  })
  orderBy!: ModelPortfolioOrderByOption;

  @IsEnum(OrderByDirection)
  @ApiProperty({
    enum: OrderByDirection,
    description: 'orderBy in this direction',
  })
  orderByDirection!: OrderByDirection;
}

export class AdminModelPortfolioCreateRequestDTO
  extends PickType(ModelPortfolio, [
    'includeRiskAdjustment',
    'memo',
    'priceEstimate',
    'rfp',
    'showCustomer',
    'status',
  ] as const)
  implements AdminModelPortfolioCreateRequest
{
  @IsUUID()
  @IsOptional()
  @ApiPropertyOptional({
    type: String,
    format: 'uuid',
  })
  groupingId?: uuid;

  @IsNonEmptyString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true, default: 'generated name' })
  name?: string;

  @IsUUID()
  @IsOptional()
  @ApiPropertyOptional({ nullable: true, type: uuid })
  organizationId?: uuid;
}

export class AdminModelPortfolioUpdateRequestDTO
  extends PartialType(OmitType(AdminModelPortfolioCreateRequestDTO, ['showCustomer', 'includeRiskAdjustment', 'rfp'])) // omitting since they're defaulted
  implements AdminModelPortfolioUpdateRequest
{
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean })
  showCustomer?: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean })
  includeRiskAdjustment?: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean })
  rfp?: boolean;
}

export class ModelPortfolioBookComponentCreateDTO
  extends PickType(ModelPortfolioComponent, ['amountAllocated', 'overrideMTM'])
  implements ModelPortfolioBookComponentCreate
{
  @Expose()
  @IsUUID()
  @ApiProperty({ type: String, format: 'uuid' })
  bookId!: uuid;
}

export class ModelPortfolioVintageComponentCreateDTO
  extends PickType(ModelPortfolioComponent, [
    'amountAllocated',
    'bufferPercentage',
    'costBasis',
    'isBufferComponent',
    'portfolioManagerEstimate',
    'overrideMTM',
    'registryProjectId',
  ])
  implements ModelPortfolioVintageComponentCreate
{
  @Expose()
  @IsUUID()
  @IsOptional()
  @ApiPropertyOptional({ type: String, format: 'uuid', nullable: true })
  bufferCategoryId?: uuid;

  @Expose()
  @IsUUID()
  @IsOptional() // eventually we need to really clean this up
  @ApiProperty({ type: String, format: 'uuid' })
  projectId!: uuid;

  @Expose()
  @ValidateIf((obj: ModelPortfolioVintageComponentCreateDTO) => !obj.vintageInterval || !!obj.vintageId)
  @IsUUID(undefined, { message: 'vintageId must be a valid uuid if vintageInterval is null' })
  @ApiPropertyOptional({ type: String, format: 'uuid', nullable: true })
  vintageId?: uuid;

  @Expose()
  @ValidateIf((obj: ModelPortfolioVintageComponentCreateDTO) => !obj.vintageId || !!obj.vintageInterval)
  @IsNonEmptyString()
  @ApiPropertyOptional({ type: String, nullable: true, format: 'start.toISOString() - end.toISOString()' })
  vintageInterval?: string;
}

export type BaseModelPortfolioComponentCreateDTO =
  | ModelPortfolioBookComponentCreateDTO
  | ModelPortfolioVintageComponentCreateDTO;

export class ModelPortfolioBookComponentUpdateDTO
  extends PartialType(ModelPortfolioBookComponentCreateDTO)
  implements ModelPortfolioBookComponentUpdate
{
  @IsUUID()
  @ApiProperty({ type: String, format: 'uuid' })
  id!: uuid;
}

export class ModelPortfolioVintageComponentUpdateDTO
  extends PartialType(ModelPortfolioVintageComponentCreateDTO)
  implements ModelPortfolioVintageComponentUpdate
{
  @IsUUID()
  @ApiProperty({ type: String, format: 'uuid' })
  id!: uuid;
}

export type BaseModelPortfolioComponentUpdateDTO =
  | ModelPortfolioBookComponentUpdateDTO
  | ModelPortfolioVintageComponentUpdateDTO;

export class BaseModelPortfolioComponentDeleteDTO implements BaseModelPortfolioComponentDelete {
  @IsUUID()
  @ApiProperty({ type: String, format: 'uuid' })
  id!: uuid;
}

export class AdminModelPortfolioComponentRequestCreateDTO
  implements Extract<AdminModelPortfolioComponentRequest, { type: 'create' }>
{
  type!: 'create';

  @Type((options) => {
    const obj = options?.newObject;
    if (obj?.bookId) return ModelPortfolioBookComponentCreateDTO;
    if (obj?.projectId) return ModelPortfolioVintageComponentCreateDTO;
    return ModelPortfolioBookComponentCreateDTO;
  })
  @IsArray()
  @ValidateNested({ each: true })
  @IsOptional()
  @ApiPropertyOptional({
    oneOf: [
      { $ref: getSchemaPath(ModelPortfolioBookComponentCreateDTO) },
      { $ref: getSchemaPath(ModelPortfolioVintageComponentCreateDTO) },
    ],
    isArray: true,
    nullable: true,
  })
  create: BaseModelPortfolioComponentCreateDTO[] = [];
}

export class AdminModelPortfolioComponentRequestDeleteDTO
  implements Extract<AdminModelPortfolioComponentRequest, { type: 'delete' }>
{
  type!: 'delete';

  @Type(() => BaseModelPortfolioComponentDeleteDTO)
  @ValidateNested()
  @IsArray()
  @IsOptional()
  @ApiPropertyOptional({ type: () => [BaseModelPortfolioComponentDeleteDTO], nullable: true })
  delete: BaseModelPortfolioComponentDeleteDTO[] = [];
}

export class AdminModelPortfolioComponentRequestUpdateDTO
  implements Extract<AdminModelPortfolioComponentRequest, { type: 'update' }>
{
  type!: 'update';

  @Type((options) => {
    const obj = options?.newObject;
    if (obj?.bookId) return ModelPortfolioBookComponentUpdateDTO;
    if (obj?.projectId) return ModelPortfolioVintageComponentUpdateDTO;
    return ModelPortfolioBookComponentUpdateDTO;
  })
  @ValidateNested()
  @IsArray()
  @IsOptional()
  @ApiPropertyOptional({
    oneOf: [
      { $ref: getSchemaPath(ModelPortfolioBookComponentUpdateDTO) },
      { $ref: getSchemaPath(ModelPortfolioVintageComponentUpdateDTO) },
    ],
    isArray: true,
    nullable: true,
  })
  update: BaseModelPortfolioComponentUpdateDTO[] = [];
}

export type AdminModelPortfolioComponentRequestDTO =
  | AdminModelPortfolioComponentRequestCreateDTO
  | AdminModelPortfolioComponentRequestUpdateDTO
  | AdminModelPortfolioComponentRequestDeleteDTO;

export class MobileModelPortfolioComponentResponseVintageDTO
  extends IntersectionType(BaseResponseDTO, PickType(ModelPortfolioComponent, ['amountAllocated']))
  implements Extract<MobileModelPortfolioComponentResponse, { type: 'vintage' }>
{
  type!: 'vintage';

  @Expose()
  @Type(() => TrimmedProjectResponseDTO)
  @ApiProperty({ type: () => TrimmedProjectResponseDTO, readOnly: true })
  project!: TrimmedProjectResponseDTO;

  @Expose()
  @ApiProperty({ type: Boolean, readOnly: true })
  projectPipeline!: boolean;

  @Expose()
  @Type(() => TrimmedProjectVintageResponseDTO)
  @ApiPropertyOptional({
    type: () => TrimmedProjectVintageResponseDTO,
    readOnly: true,
  })
  projectVintage!: TrimmedProjectVintageResponseDTO; // todo : confirm this is enough
}

export class MobileModelPortfolioComponentResponseCustomVintageDTO
  extends IntersectionType(
    BaseResponseDTO,
    PickType(RequireType(ModelPortfolioComponent), ['amountAllocated', 'portfolioManagerEstimate']),
  )
  implements Extract<MobileModelPortfolioComponentResponse, { type: 'custom' }>
{
  type!: 'custom';

  @Expose()
  @Type(() => TrimmedProjectResponseDTO)
  @ApiProperty({ type: () => TrimmedProjectResponseDTO, readOnly: true })
  project!: TrimmedProjectResponseDTO;

  @Expose()
  @ApiProperty({ type: Boolean, readOnly: true })
  projectPipeline!: boolean;

  @Expose()
  @ApiPropertyOptional({ type: String, nullable: true, format: 'start.toISOString() - end.toISOString()' })
  vintageInterval!: string;
}

export class MobileModelPortfolioComponentResponseRCTDTO
  extends IntersectionType(BaseResponseDTO, PickType(ModelPortfolioComponent, ['amountAllocated']))
  implements Extract<MobileModelPortfolioComponentResponse, { type: 'rct' }>
{
  type!: 'rct';

  @Expose()
  @Type(() => TrimmedBookResponseDTO)
  @ApiProperty({ type: () => TrimmedBookResponseDTO, readOnly: true })
  book!: TrimmedBookResponseDTO;
}

export class MobileModelPortfolioComponentResponseRRTDTO
  extends IntersectionType(BaseResponseDTO, PickType(ModelPortfolioComponent, ['amountAllocated']))
  implements Extract<MobileModelPortfolioComponentResponse, { type: 'rrt' }>
{
  type!: 'rrt';

  @Expose()
  @Type(() => TrimmedBookResponseDTO)
  @ApiProperty({ type: () => TrimmedBookResponseDTO, readOnly: true })
  book!: TrimmedBookResponseDTO;
}

export type MobileModelPortfolioComponentResponseDTO =
  | MobileModelPortfolioComponentResponseVintageDTO
  | MobileModelPortfolioComponentResponseCustomVintageDTO
  | MobileModelPortfolioComponentResponseRCTDTO
  | MobileModelPortfolioComponentResponseRRTDTO;

export type PortalModelPortfolioComponentResponseDTO = MobileModelPortfolioComponentResponseDTO &
  PortalModelPortfolioComponentResponse;

export class AdminModelPortfolioComponentResponseCommonDTO {
  @Expose()
  @Type(() => TrimmedUserResponseDTO)
  @ApiProperty({ type: () => TrimmedUserResponseDTO, readOnly: true })
  createdBy!: TrimmedUserResponseDTO;

  @Expose()
  @Type(() => TrimmedUserResponseDTO)
  @ApiProperty({ type: () => TrimmedUserResponseDTO, readOnly: true })
  updatedBy!: TrimmedUserResponseDTO;
}
export class AdminModelPortfolioComponentResponseVintageDTO
  extends IntersectionType(
    BaseResponseDTO,
    AdminModelPortfolioComponentResponseCommonDTO,
    PickType(ModelPortfolioComponent, ['amountAllocated', 'bufferPercentage', 'overrideMTM']),
  )
  implements Extract<AdminModelPortfolioComponentResponse, { type: 'vintage' }>
{
  type!: 'vintage';

  @Expose()
  @ApiProperty({ type: Boolean, readOnly: true })
  isBufferComponent!: boolean;

  @Expose()
  @Type(() => AdminProjectResponseDTO)
  @ApiProperty({ type: () => AdminProjectResponseDTO, readOnly: true })
  project!: AdminProjectResponseDTO;

  @Expose()
  @ApiProperty({ type: Boolean, readOnly: true })
  projectPipeline!: boolean;

  @Expose()
  @Type(() => TrimmedProjectVintageResponseDTO)
  @ApiPropertyOptional({
    type: () => TrimmedProjectVintageResponseDTO,
    readOnly: true,
  })
  projectVintage!: AdminProjectVintageResponseDTO; // todo : confirm this is enough
}

export class AdminModelPortfolioComponentResponseCustomVintageDTO
  extends IntersectionType(
    BaseResponseDTO,
    AdminModelPortfolioComponentResponseCommonDTO,
    PickType(RequireType(ModelPortfolioComponent), [
      'amountAllocated',
      'portfolioManagerEstimate',
      'isBufferComponent',
      'bufferPercentage',
      'costBasis',
    ]),
  )
  implements Extract<AdminModelPortfolioComponentResponse, { type: 'custom' }>
{
  type!: 'custom';

  @Expose()
  @Type(() => AdminProjectResponseDTO)
  @ApiProperty({ type: () => AdminProjectResponseDTO, readOnly: true })
  project!: AdminProjectResponseDTO;

  @Expose()
  @ApiProperty({ type: Boolean, readOnly: true })
  projectPipeline!: boolean;

  @Expose()
  @ApiPropertyOptional({ type: String, nullable: true, format: 'start.toISOString() - end.toISOString()' })
  vintageInterval!: string;

  @Expose()
  @Type(() => AdminBufferCategoryResponseDTO)
  @ApiProperty({ type: () => AdminBufferCategoryResponseDTO, readOnly: true })
  bufferCategory!: AdminBufferCategoryResponseDTO;
}

export class AdminModelPortfolioComponentResponseRCTDTO
  extends IntersectionType(
    BaseResponseDTO,
    AdminModelPortfolioComponentResponseCommonDTO,
    PickType(ModelPortfolioComponent, ['amountAllocated']),
  )
  implements Extract<AdminModelPortfolioComponentResponse, { type: 'rct' }>
{
  type!: 'rct';

  @Expose()
  @Type(() => TrimmedBookResponseDTO)
  @ApiProperty({ type: () => TrimmedBookResponseDTO, readOnly: true })
  book!: TrimmedBookResponseDTO;
}

export class AdminModelPortfolioComponentResponseRRTDTO
  extends IntersectionType(
    BaseResponseDTO,
    AdminModelPortfolioComponentResponseCommonDTO,
    PickType(ModelPortfolioComponent, ['amountAllocated']),
  )
  implements Extract<AdminModelPortfolioComponentResponse, { type: 'rrt' }>
{
  type!: 'rrt';

  @Expose()
  @Type(() => TrimmedBookResponseDTO)
  @ApiProperty({ type: () => TrimmedBookResponseDTO, readOnly: true })
  book!: TrimmedBookResponseDTO;
}

export type AdminModelPortfolioComponentResponseDTO =
  | AdminModelPortfolioComponentResponseVintageDTO
  | AdminModelPortfolioComponentResponseCustomVintageDTO
  | AdminModelPortfolioComponentResponseRCTDTO
  | AdminModelPortfolioComponentResponseRRTDTO;

export class MobileModelPortfolioRelationsQueryDTO implements MobileModelPortfolioRelationsQuery {
  @Transform(({ value }) => value.toString().split(','))
  @IsIn(AllMobileModelPortfolioRelations, { each: true })
  @IsArray()
  @IsOptional()
  @ApiProperty({
    enum: ModelPortfolioRelations,
    description: 'relations to include',
    example: [ModelPortfolioRelations.MODEL_PORTFOLIO_COMPONENTS],
  })
  includeRelations?: MobileModelPortfolioRelations[];
}

// todo : cleanup with proper types
export class PortalModelPortfolioRelationsQueryDTO implements PortalModelPortfolioRelationsQuery {
  @Transform(({ value }) => value.toString().split(','))
  @IsIn(AllPortalModelPortfolioRelations, { each: true })
  @IsArray()
  @IsOptional()
  @ApiProperty({
    enum: ModelPortfolioRelations,
    description: 'relations to include',
    example: [ModelPortfolioRelations.MODEL_PORTFOLIO_COMPONENTS],
  })
  includeRelations?: PortalModelPortfolioRelations[];
}

// todo : cleanup with proper types
export class AdminModelPortfolioRelationsQueryDTO implements AdminModelPortfolioRelationsQuery {
  @Transform(({ value }) => value.toString().split(','))
  @IsEnum(ModelPortfolioRelations, { each: true })
  @IsArray()
  @IsOptional()
  @ApiPropertyOptional({
    enum: ModelPortfolioRelations,
    description: 'relations to include',
    example: [ModelPortfolioRelations.MODEL_PORTFOLIO_COMPONENTS],
    isArray: true,
  })
  includeRelations?: ModelPortfolioRelations[];
}

export class MobileModelPortfolioQueryDTO
  extends IntersectionType(BaseQueryDTO, MobileModelPortfolioRelationsQueryDTO)
  implements MobileModelPortfolioQuery
{
  @IsNonEmptyString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  name?: string;

  @IsUUID()
  @IsOptional()
  @ApiPropertyOptional({ type: String, format: 'uuid', nullable: true })
  projectId?: uuid;

  @IsUUID()
  @IsOptional()
  @ApiPropertyOptional({ type: String, format: 'uuid', nullable: true })
  projectVintageId?: uuid;

  @IsNonEmptyString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  registryProjectId?: string;

  @IsEnum(ModelPortfolioStatus)
  @IsOptional()
  @ApiPropertyOptional({
    enum: ModelPortfolioStatus,
    nullable: true,
  })
  status?: ModelPortfolioStatus;

  @IsNonEmptyString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  uiKey?: string;

  @Transform(({ value }) => {
    if (!value || value.length === 0) {
      return;
    }
    const orderBys = value.toString().split(',');
    const errors: string[] = [];
    const mappings: BaseModelPortfolioOrderByDTO[] = orderBys.map((ob: string) => {
      const split = ob.split(':');
      return new BaseModelPortfolioOrderByDTO(split[0], split[1]);
    });
    if (errors.length > 0) {
      throw new BadRequestException(errors);
    }
    return mappings;
  })
  @ValidateNested({ each: true })
  @Type(() => BaseModelPortfolioOrderByDTO)
  @IsArray()
  @IsOptional()
  @ApiPropertyOptional({
    type: [String],
    format: '[TransactionOrderByOptions:OrderByDirection]',
    nullable: true,
    default: [`${ModelPortfolioOrderByOption.CREATED_AT}:${OrderByDirection.ASC}`],
  })
  orderBys: BaseModelPortfolioOrderByDTO[] = [
    new BaseModelPortfolioOrderByDTO(ModelPortfolioOrderByOption.CREATED_AT, OrderByDirection.ASC_NULLS_LAST),
  ];
}

export class PortalModelPortfolioQueryDTO
  extends IntersectionType(
    OmitType(MobileModelPortfolioQueryDTO, ['includeRelations']),
    PortalModelPortfolioRelationsQueryDTO,
  )
  implements PortalModelPortfolioQuery {}

export class AdminModelPortfolioQueryDTO
  extends IntersectionType(
    OmitType(PortalModelPortfolioQueryDTO, ['includeRelations']),
    AdminModelPortfolioRelationsQueryDTO,
  )
  implements AdminModelPortfolioQuery
{
  @IsUUID()
  @IsOptional()
  @ApiPropertyOptional({ type: String, format: 'uuid', nullable: true })
  createdById?: uuid;

  @IsUUID()
  @IsOptional()
  @ApiPropertyOptional({ type: String, format: 'uuid', nullable: true })
  organizationId?: uuid;

  @Transform(({ value }) => value.toLowerCase() === 'true')
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean, nullable: true })
  showCustomer?: boolean;

  @IsUUID()
  @IsOptional()
  @ApiPropertyOptional({ type: String, format: 'uuid', nullable: true })
  updatedById?: uuid;
}

export class MobileModelPortfolioQueryResponseDTO
  extends BaseQueryResponseDTO
  implements MobileModelPortfolioQueryResponse
{
  @Expose()
  @Type(() => MobileModelPortfolioResponseDTO)
  @ApiProperty({ type: () => [MobileModelPortfolioResponseDTO], readOnly: true })
  data: MobileModelPortfolioResponseDTO[] = [];
}

export class PortalModelPortfolioQueryResponseDTO
  extends BaseQueryResponseDTO
  implements PortalModelPortfolioQueryResponse
{
  @Expose()
  @Type(() => PortalModelPortfolioResponseDTO)
  @ApiProperty({ type: () => [PortalModelPortfolioResponseDTO], readOnly: true })
  data: PortalModelPortfolioResponseDTO[] = [];
}

export class AdminModelPortfolioQueryResponseDTO
  extends BaseQueryResponseDTO
  implements AdminModelPortfolioQueryResponse
{
  @Expose()
  @Type(() => AdminModelPortfolioResponseDTO)
  @ApiProperty({ type: () => [AdminModelPortfolioResponseDTO], readOnly: true })
  data: AdminModelPortfolioResponseDTO[] = [];
}

/* transformations */

export function toAdminModelPortfolioComponentResponseDTO(
  modelPortfolioComponent: ModelPortfolioComponent,
  includeRelations: ModelPortfolioRelations[] = [],
): AdminModelPortfolioComponentResponseDTO {
  if (!!modelPortfolioComponent.vintage) {
    return Object.assign(new AdminModelPortfolioComponentResponseVintageDTO(), {
      ...modelPortfolioComponent,
      project:
        includeRelations.includes(ModelPortfolioRelations.PROJECT) && modelPortfolioComponent.project
          ? toAdminProjectResponseDTO(modelPortfolioComponent.project)
          : undefined,
      projectVintage:
        includeRelations.includes(ModelPortfolioRelations.PROJECT_VINTAGE) && modelPortfolioComponent.vintage
          ? toAdminProjectVintageResponseDTO(
              modelPortfolioComponent.vintage,
              includeRelations.includes(ModelPortfolioRelations.PROJECT) ? [ProjectVintageRelations.PROJECT] : [],
            )
          : undefined,
    });
  }

  if (!!modelPortfolioComponent.vintageInterval) {
    // custom vintage
    return Object.assign(new AdminModelPortfolioComponentResponseCustomVintageDTO(), {
      ...modelPortfolioComponent,
      project:
        includeRelations.includes(ModelPortfolioRelations.PROJECT) && modelPortfolioComponent.project
          ? toAdminProjectResponseDTO(modelPortfolioComponent.project)
          : undefined,
      projectVintage:
        includeRelations.includes(ModelPortfolioRelations.PROJECT_VINTAGE) && modelPortfolioComponent.vintage
          ? toAdminProjectVintageResponseDTO(
              modelPortfolioComponent.vintage,
              includeRelations.includes(ModelPortfolioRelations.PROJECT) ? [ProjectVintageRelations.PROJECT] : [],
            )
          : undefined,
    });
  }

  if (!!modelPortfolioComponent.book) {
    // rct or rrt
    return Object.assign(new AdminModelPortfolioComponentResponseRCTDTO(), {
      ...modelPortfolioComponent,
      book: modelPortfolioComponent.book,
    });
  }

  throw new Error('Unknown modelPortfolioComponent type');
}

export function toAdminModelPortfolioResponseDTO(
  modelPortfolio: InternalModelPortfolio,
  includeRelations: ModelPortfolioRelations[] = [],
): AdminModelPortfolioResponseDTO {
  if (
    includeRelations.includes(ModelPortfolioRelations.MODEL_PORTFOLIO_COMPONENTS) &&
    !modelPortfolio.modelPortfolioComponents.isInitialized()
  ) {
    throw new InternalServerErrorException('modelPortfolioComponents must be passed in for relation');
  }

  const dto = Object.assign(new AdminModelPortfolioResponseDTO(), {
    ...modelPortfolio,
    modelPortfolioComponents:
      includeRelations.includes(ModelPortfolioRelations.MODEL_PORTFOLIO_COMPONENTS) &&
      modelPortfolio.modelPortfolioComponents.isInitialized()
        ? modelPortfolio.modelPortfolioComponents
            .getItems()
            .map((f) => toAdminModelPortfolioComponentResponseDTO(f, includeRelations))
        : undefined,
    organizationId: modelPortfolio.organization?.id, // deprecated
    createdBy: {
      id: modelPortfolio.createdBy.id,
      createdAt: modelPortfolio.createdBy.createdAt,
      updatedAt: modelPortfolio.createdBy.updatedAt,
      email: modelPortfolio.createdBy.email,
      name: modelPortfolio.createdBy.name,
    },
    updatedBy: {
      id: modelPortfolio.updatedBy.id,
      createdAt: modelPortfolio.updatedBy.createdAt,
      updatedAt: modelPortfolio.updatedBy.updatedAt,
      email: modelPortfolio.updatedBy.email,
      name: modelPortfolio.updatedBy.name,
    },
  });

  if (includeRelations.includes(ModelPortfolioRelations.GROUPED_PORTFOLIOS)) {
    dto.groupedPortfolios =
      modelPortfolio.groupedPortfolios?.map((m) => {
        if (!m.modelPortfolioComponents.isInitialized()) {
          throw new InternalServerErrorException('grouped modelPortfolioComponents should be initialized');
        }
        return {
          ...m,
          totalAmount: Decimal.sum(
            ...m.modelPortfolioComponents.getItems().map((m) => m.amountAllocated),
            0,
          ).toNumber(),
        };
      }) || [];
  }

  return dto;
}

export function toMobileModelPortfolioComponentResponseDTO(
  modelPortfolioComponent: ModelPortfolioComponent,
  includeRelations: ModelPortfolioRelations[] = [],
): MobileModelPortfolioComponentResponseDTO {
  if (!!modelPortfolioComponent.vintageInterval) {
    // custom vintage
    return Object.assign(new MobileModelPortfolioComponentResponseCustomVintageDTO(), {
      ...modelPortfolioComponent,
      project:
        includeRelations.includes(ModelPortfolioRelations.PROJECT) && modelPortfolioComponent.project
          ? toTrimmedProjectResponseDTO(modelPortfolioComponent.project)
          : undefined,
      projectVintage:
        includeRelations.includes(ModelPortfolioRelations.PROJECT_VINTAGE) && modelPortfolioComponent.vintage
          ? toTrimmedProjectVintageResponseDTO(modelPortfolioComponent.vintage)
          : undefined,
    });
  }

  if (!!modelPortfolioComponent.vintage) {
    return Object.assign(new MobileModelPortfolioComponentResponseVintageDTO(), {
      ...modelPortfolioComponent,
      project:
        includeRelations.includes(ModelPortfolioRelations.PROJECT) && modelPortfolioComponent.project
          ? toTrimmedProjectResponseDTO(modelPortfolioComponent.project)
          : undefined,
      projectVintage:
        includeRelations.includes(ModelPortfolioRelations.PROJECT_VINTAGE) && modelPortfolioComponent.vintage
          ? toTrimmedProjectVintageResponseDTO(modelPortfolioComponent.vintage)
          : undefined,
    });
  }

  // TODO: RCTs and RRTs
  // if (!!modelPortfolioComponent.book) {
  //   // rct or rrt
  //   if (modelPortfolioComponent.bookType === 'rct') {
  //     return Object.assign(new MobileModelPortfolioComponentResponseRCTDTO(), {
  //       ...modelPortfolioComponent,
  //       book: modelPortfolioComponent.book,
  //     });
  //   }
  //   if (modelPortfolioComponent.bookType === 'rrt') {
  //     return Object.assign(new MobileModelPortfolioComponentResponseRRTDTO(), {
  //       ...modelPortfolioComponent,
  //       book: modelPortfolioComponent.book,
  //     });
  //   }
  // }

  throw new Error('Unknown modelPortfolioComponent type');
}

export function toMobileModelPortfolioResponseDTO(
  modelPortfolio: ModelPortfolio,
  includeRelations: ModelPortfolioRelations[] = [],
): MobileModelPortfolioResponseDTO {
  if (
    includeRelations.includes(ModelPortfolioRelations.MODEL_PORTFOLIO_COMPONENTS) &&
    !modelPortfolio.modelPortfolioComponents.isInitialized()
  ) {
    throw new InternalServerErrorException('modelPortfolioComponents must be passed in for relation');
  }

  const dto = Object.assign(new MobileModelPortfolioResponseDTO(), {
    ...modelPortfolio,
    modelPortfolioComponents:
      includeRelations.includes(ModelPortfolioRelations.MODEL_PORTFOLIO_COMPONENTS) &&
      modelPortfolio.modelPortfolioComponents.isInitialized()
        ? modelPortfolio.modelPortfolioComponents
            .getItems()
            .map((f) => toMobileModelPortfolioComponentResponseDTO(f, includeRelations))
        : undefined,
  });

  return dto;
}

export function toPortalModelPortfolioComponentResponseDTO(
  modelPortfolioComponent: ModelPortfolioComponent,
  includeRelations: ModelPortfolioRelations[] = [],
): PortalModelPortfolioComponentResponseDTO {
  return Object.assign<PortalModelPortfolioComponentResponseDTO, any>({} as PortalModelPortfolioComponentResponseDTO, {
    ...modelPortfolioComponent,
    project:
      includeRelations.includes(ModelPortfolioRelations.PROJECT) && modelPortfolioComponent.project
        ? toTrimmedProjectResponseDTO(modelPortfolioComponent.project)
        : undefined,
    projectVintage:
      includeRelations.includes(ModelPortfolioRelations.PROJECT_VINTAGE) && modelPortfolioComponent.vintage
        ? toTrimmedProjectVintageResponseDTO(modelPortfolioComponent.vintage)
        : undefined,
  });
}

export function toPortalModelPortfolioResponseDTO(
  modelPortfolio: ModelPortfolio,
  includeRelations: ModelPortfolioRelations[] = [],
): PortalModelPortfolioResponseDTO {
  if (
    includeRelations.includes(ModelPortfolioRelations.MODEL_PORTFOLIO_COMPONENTS) &&
    !modelPortfolio.modelPortfolioComponents.isInitialized()
  ) {
    throw new InternalServerErrorException('modelPortfolioComponents must be passed in for relation');
  }

  const dto = Object.assign(new PortalModelPortfolioResponseDTO(), {
    ...modelPortfolio,
    modelPortfolioComponents:
      includeRelations.includes(ModelPortfolioRelations.MODEL_PORTFOLIO_COMPONENTS) &&
      modelPortfolio.modelPortfolioComponents.isInitialized()
        ? modelPortfolio.modelPortfolioComponents
            .getItems()
            .map((f) => toPortalModelPortfolioComponentResponseDTO(f, includeRelations))
        : undefined,
  });

  return dto;
}

export class MobileModelPortfolioResponseDTO
  extends IntersectionType(
    BaseResponseDTO,
    PickType(ModelPortfolio, ['includeRiskAdjustment', 'memo', 'name', 'priceEstimate', 'status', 'uiKey']),
  )
  implements MobileModelPortfolioResponse
{
  @Expose()
  @Type(() =>
    IntersectionType(
      PartialType(MobileModelPortfolioComponentResponseVintageDTO),
      PartialType(MobileModelPortfolioComponentResponseCustomVintageDTO),
      PartialType(MobileModelPortfolioComponentResponseRCTDTO),
      PartialType(MobileModelPortfolioComponentResponseRRTDTO),
    ),
  )
  @ApiPropertyOptional({
    oneOf: [
      { $ref: getSchemaPath(MobileModelPortfolioComponentResponseVintageDTO) },
      { $ref: getSchemaPath(MobileModelPortfolioComponentResponseCustomVintageDTO) },
      { $ref: getSchemaPath(MobileModelPortfolioComponentResponseRCTDTO) },
      { $ref: getSchemaPath(MobileModelPortfolioComponentResponseRRTDTO) },
    ],
    isArray: true,
    readOnly: true,
  })
  modelPortfolioComponents?: MobileModelPortfolioComponentResponseDTO[];
}

export class PortalModelPortfolioResponseDTO
  extends OmitType(MobileModelPortfolioResponseDTO, ['modelPortfolioComponents'])
  implements PortalModelPortfolioResponse
{
  @Expose()
  @Type(() =>
    IntersectionType(
      PartialType(MobileModelPortfolioComponentResponseVintageDTO),
      PartialType(MobileModelPortfolioComponentResponseCustomVintageDTO),
      PartialType(MobileModelPortfolioComponentResponseRCTDTO),
      PartialType(MobileModelPortfolioComponentResponseRRTDTO),
    ),
  )
  @ApiPropertyOptional({
    type: () => [
      MobileModelPortfolioComponentResponseVintageDTO,
      MobileModelPortfolioComponentResponseCustomVintageDTO,
      MobileModelPortfolioComponentResponseRCTDTO,
      MobileModelPortfolioComponentResponseRRTDTO,
    ],
    readOnly: true,
  })
  modelPortfolioComponents?: PortalModelPortfolioComponentResponseDTO[];
}

export class AdminModelPortfolioResponseDTO
  extends IntersectionType(
    OmitType(PortalModelPortfolioResponseDTO, ['modelPortfolioComponents']),
    PickType(ModelPortfolio, ['groupingId', 'showCustomer', 'rfp']),
  )
  implements AdminModelPortfolioResponse
{
  @Expose()
  @Type(() => TrimmedUserResponseDTO)
  @ApiProperty({ type: () => TrimmedUserResponseDTO, readOnly: true })
  createdBy!: TrimmedUserResponseDTO;

  @Expose()
  @Type(() => AdminGroupedPortfolioResponseDTO)
  @ApiProperty({ type: () => [AdminGroupedPortfolioResponseDTO], readOnly: true })
  groupedPortfolios!: AdminGroupedPortfolioResponseDTO[]; // required but we just send [] unless asked for I guess

  @Expose()
  @Type(() =>
    IntersectionType(
      PartialType(AdminModelPortfolioComponentResponseVintageDTO),
      PartialType(AdminModelPortfolioComponentResponseCustomVintageDTO),
      PartialType(AdminModelPortfolioComponentResponseRCTDTO),
      PartialType(AdminModelPortfolioComponentResponseRRTDTO),
    ),
  )
  @ApiPropertyOptional({
    oneOf: [
      { $ref: getSchemaPath(AdminModelPortfolioComponentResponseVintageDTO) },
      { $ref: getSchemaPath(AdminModelPortfolioComponentResponseCustomVintageDTO) },
      { $ref: getSchemaPath(AdminModelPortfolioComponentResponseRCTDTO) },
      { $ref: getSchemaPath(AdminModelPortfolioComponentResponseRRTDTO) },
    ],
    isArray: true,
    readOnly: true,
  })
  modelPortfolioComponents?: AdminModelPortfolioComponentResponseDTO[];

  @Expose()
  @Type(() => TrimmedOrganizationResponseDTO)
  @ApiPropertyOptional({ type: () => TrimmedOrganizationResponseDTO, readOnly: true, nullable: true })
  organization?: TrimmedOrganizationResponseDTO;

  @Expose()
  @Type(() => TrimmedUserResponseDTO)
  @ApiProperty({ type: () => TrimmedUserResponseDTO, readOnly: true })
  updatedBy!: TrimmedUserResponseDTO;
}

export class AdminGroupedPortfolioResponseDTO
  extends IntersectionType(
    BaseResponseDTO,
    PickType(AdminModelPortfolioResponseDTO, [
      'createdBy',
      'name',
      'organization',
      'priceEstimate',
      'rfp',
      'showCustomer',
      'status',
      'uiKey',
      'updatedBy',
    ]),
  )
  implements AdminGroupedPortfolioResponse
{
  @Expose()
  @ApiPropertyOptional({ type: Number, format: 'integer', readOnly: true })
  totalAmount!: number;
}
