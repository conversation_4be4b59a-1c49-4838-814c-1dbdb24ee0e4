/* third party */
import { BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { ApiProperty, ApiPropertyOptional, IntersectionType, OmitType, PartialType, PickType } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsEnum,
  IsIn,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsPositive,
  IsString,
  IsUUID,
} from 'class-validator';
/* rubicon */
import {
  AdminBulkProjectUpdateRequest,
  AdminProjectCreateRequest,
  AdminProjectQueryResponse,
  AdminProjectRelationsQuery,
  AdminProjectSearch,
  AdminProjectSearchResponse,
  AdminProjectUpdateRequest,
  AllMobileProjectRelations,
  AllPortalProjectRelations,
  BaseSdgTypeResponse,
  CountryRegions,
  MobileProjectQuery,
  MobileProjectQueryResponse,
  MobileProjectRelations,
  MobileProjectRelationsQuery,
  MobileProjectResponse,
  PortalProjectQuery,
  PortalProjectQueryResponse,
  PortalProjectRelations,
  PortalProjectRelationsQuery,
  PortalProjectResponse,
  PriceRange,
  ProjectEligibilityAccreditation,
  ProjectEmissionsImpactType,
  ProjectOrderByOption,
  ProjectRelations,
  ProjectTypeCategory,
  TrimmedKmlResponse,
  TrimmedProjectResponse,
  TrimmedProjectSdgResponse,
  uuid,
} from '@rubiconcarbon/shared-types';
/* app */
import { Project, ProjectSDG, SDGType } from '@app/entities';
import { ProjectUpdateLog } from '@app/entities/project-update-log.entity';
import { IsNonEmptyString } from '@app/helpers/validations.helper';
import { InternalProjectResponse } from '@app/interfaces/project.interface';
import {
  AdminBookTypeGroupedAllocationResponseDTO,
  AdminGroupedAllocationResponseDTO,
  toAdminGroupedAllocationResponseDTOFromGroupedAllocation,
} from '@app/dtos/allocation.dto';
import { BaseQueryDTO, BaseQueryResponseDTO, BaseResponseDTO } from '@app/dtos/base.dto';
import { AdminBufferCategoryResponseDTO, toAdminBufferCategoryResponseDTO } from '@app/dtos/buffer-categories.dto';
import { TrimmedCountryResponseDTO, toAdminCountryResponseDTO } from '@app/dtos/country.dto';
import { AdminProjectTypeResponseDTO, TrimmedProjectTypeResponseDTO } from '@app/dtos/project-types.dto';
import {
  AdminProjectVintageResponseDTO,
  MobileProjectVintageResponseDTO,
  PortalProjectVintageResponseDTO,
  toAdminProjectVintageResponseDTO,
  toPortalProjectVintageResponseDTO,
} from '@app/dtos/project-vintage.dto';
import { TrimmedRegistryResponseDTO, toTrimmedRegistryResponseDTO } from '@app/dtos/registry.dto';
import { TrimmedUserResponseDTO } from '@app/dtos/trimmed.dto';

export class TrimmedKmlResponseDTO implements TrimmedKmlResponse {
  @Expose()
  @ApiPropertyOptional({ type: Number, format: 'iso latitude', nullable: true })
  latitude?: number;

  @Expose()
  @ApiPropertyOptional({ type: Number, format: 'iso longitude', nullable: true })
  longitude?: number;

  @Expose()
  @ApiPropertyOptional({ type: String, format: 's3 path', nullable: true })
  url?: string;
}

export class AdminProjectCreateRequestDTO
  extends PickType(Project, [
    'additionalityBlurb',
    'additionalityScore',
    'analystInsightsBlurb',
    'analystName',
    'analystRole',
    'beZeroRating',
    'beZeroUpdatedDate',
    'bookChartDisplayGroup',
    'carbonAccountingBlurb',
    'categoryRiskScore',
    'certificationBlurb',
    'certificationScore',
    'climateImpact',
    'climateImpactBlurb',
    'climateImpactRiskAdjusted',
    'countryCode',
    'dateOfLatestVerification',
    'durabilityScore',
    'eligibilityAccreditations',
    'emissionsImpactType',
    'endDate',
    'futureDeliveryBlurb',
    'futureDeliveryRisk',
    'illustrationImageUrl',
    'independentVerifierName',
    'integrityGradeScore',
    'integrityGradeScoreRiskAdjusted',
    'isByorctApproved',
    'isScienceTeamApproved',
    'kmlLatitude',
    'kmlLongitude',
    'kmlUrl',
    'lastReviewDate',
    'mapImageUrl',
    'maxPercentage',
    'maxQuantity',
    'methodologyBlurb',
    'minPercentage',
    'minQuantity',
    'name',
    'otherCoBenefitsBlurb',
    'overallRiskScore',
    'pddReportLink',
    'permanenceBlurb',
    'previewImageUrl',
    'projectDescription',
    'projectDeveloperName',
    'projectLocation',
    'rctStandard',
    'registryCreditsIssued',
    'registryCreditsRetired',
    'registryLink',
    'registryName',
    'registryProjectId',
    'squareImage1Url',
    'squareImage2Url',
    'startDate',
    'suspended',
    'vvbReportLink',
    'waterBlurb',
  ] as const)
  implements AdminProjectCreateRequest
{
  @IsPositive()
  @IsInt()
  @ApiProperty({ type: 'integer', description: 'id for the associated ProjectType' })
  projectTypeId!: number;

  @IsArray()
  @IsInt({ each: true })
  @IsOptional()
  @ApiProperty({ type: 'integer', nullable: true, description: 'ids for the associated sdgs' })
  sdgIds?: number[];

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    type: 'boolean',
    nullable: true,
    description: 'surpress realtime notifications (intended for bulk scripted creation)',
  })
  surpressEmails?: boolean;

  @IsUUID()
  @IsOptional()
  @ApiPropertyOptional({ type: 'uuid', nullable: true, description: 'id for the associated Buffer Category' })
  bufferCategoryId?: uuid;
}

export class AdminProjectUpdateRequestDTO
  extends PartialType(AdminProjectCreateRequestDTO)
  implements AdminProjectUpdateRequest
{
  // need to keep these to avoid defaults
  @IsArray()
  @IsEnum(ProjectEligibilityAccreditation, { each: true })
  @IsOptional()
  @ApiPropertyOptional({ enum: ProjectEligibilityAccreditation, isArray: true, nullable: true })
  override eligibilityAccreditations?: ProjectEligibilityAccreditation[];

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean, nullable: true })
  override isScienceTeamApproved?: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean, nullable: true })
  override rctStandard?: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean, nullable: true })
  override suspended?: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean, nullable: true })
  override isByorctApproved?: boolean;
}

export class AdminBulkProjectUpdateRequestDTO
  extends AdminProjectUpdateRequestDTO
  implements AdminBulkProjectUpdateRequest
{
  @IsUUID()
  @ApiProperty({ type: String, format: 'uuid' })
  id!: uuid;

  @IsString()
  @IsNonEmptyString()
  @ApiProperty({ type: String })
  memo!: string;

  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  bufferCategory?: string;
}

export class AdminProjectUpdateLogResponseDTO
  extends IntersectionType(BaseResponseDTO, PickType(ProjectUpdateLog, ['data', 'memo']))
  implements AdminProjectUpdateLogResponseDTO
{
  @Expose()
  @Type(() => TrimmedUserResponseDTO)
  @ApiProperty({ type: () => TrimmedUserResponseDTO, readOnly: true })
  user!: TrimmedUserResponseDTO;
}

export class BaseSdgTypeResponseDTO
  extends PickType(SDGType, ['id', 'title', 'iconImagePath'])
  implements BaseSdgTypeResponse {}

export class TrimmedProjectSdgResponseDTO
  extends IntersectionType(BaseResponseDTO, PickType(ProjectSDG, ['sdgDescriptionBlurb']))
  implements TrimmedProjectSdgResponse
{
  @Expose()
  @ApiProperty({ type: String, format: 'uuid', readOnly: true })
  projectId!: uuid;

  @Expose()
  @Type(() => BaseSdgTypeResponseDTO)
  @ApiProperty({ type: () => BaseSdgTypeResponseDTO, readOnly: true })
  sdgType!: BaseSdgTypeResponseDTO;

  @Expose()
  @ApiProperty({ type: 'integer', readOnly: true })
  sdgTypeId!: number;
}

export class TrimmedProjectResponseDTO
  extends IntersectionType(
    BaseResponseDTO,
    PickType(Project, [
      'beZeroRating',
      'beZeroUpdatedDate',
      'eligibilityAccreditations',
      'emissionsImpactType',
      'integrityGradeScore',
      'isScienceTeamApproved',
      'name',
      'overallRiskScore',
      'projectDescription',
      'registryName',
      'registryProjectId',
    ]),
  )
  implements TrimmedProjectResponse
{
  @Expose()
  @Type(() => TrimmedCountryResponseDTO)
  @ApiProperty({ type: () => TrimmedCountryResponseDTO, readOnly: true })
  country?: TrimmedCountryResponseDTO;

  @Expose()
  @ApiProperty({ type: Boolean })
  hasBalance!: boolean;

  @Expose()
  @Type(() => TrimmedProjectSdgResponseDTO)
  @ApiPropertyOptional({ type: () => [TrimmedProjectSdgResponseDTO], readOnly: true, nullable: true })
  projectSDGs?: TrimmedProjectSdgResponseDTO[];

  @Expose()
  @Type(() => TrimmedProjectTypeResponseDTO)
  @ApiProperty({ type: () => TrimmedProjectTypeResponseDTO, readOnly: true })
  projectType!: TrimmedProjectTypeResponseDTO;

  @Expose()
  @Type(() => TrimmedRegistryResponseDTO)
  @ApiProperty({ type: () => TrimmedRegistryResponseDTO, readOnly: true })
  registry?: TrimmedRegistryResponseDTO;
}

export class MobileProjectResponseDTO
  extends IntersectionType(
    BaseResponseDTO,
    TrimmedProjectResponseDTO,
    PickType(Project, [
      'additionalityBlurb',
      'additionalityScore',
      'analystInsightsBlurb',
      'analystName',
      'analystRole',
      'carbonAccountingBlurb',
      'certificationBlurb',
      'certificationScore',
      'climateImpact',
      'climateImpactBlurb',
      'climateImpactRiskAdjusted',
      'durabilityScore',
      'endDate',
      'futureDeliveryBlurb',
      'futureDeliveryRisk',
      'illustrationImageUrl',
      'integrityGradeScoreRiskAdjusted',
      'isByorctApproved',
      'lastReviewDate',
      'mapImageUrl',
      'methodologyBlurb',
      'otherCoBenefitsBlurb',
      'permanenceBlurb',
      'previewImageUrl',
      'projectDeveloperName',
      'projectLocation',
      'registryLink',
      'squareImage1Url',
      'squareImage2Url',
      'startDate',
      'suspended',
      'waterBlurb',
    ]),
  )
  implements MobileProjectResponse
{
  @Expose()
  @Type(() => TrimmedKmlResponseDTO)
  @ApiPropertyOptional({ type: () => TrimmedKmlResponseDTO })
  kml?: TrimmedKmlResponseDTO;

  @Expose()
  @ApiPropertyOptional({ readOnly: true, nullable: true })
  minQuantityBYO?: number;

  @Expose()
  @ApiPropertyOptional({ type: Boolean, nullable: true, default: false })
  pdfReady?: boolean;

  @Expose()
  @ApiPropertyOptional({
    enum: PriceRange,
    readOnly: true,
    nullable: true,
    description: `only returned if includeRelations has ${ProjectRelations.PRICE_RANGE}`,
  })
  priceRange?: PriceRange;

  @Expose()
  @Type(() => MobileProjectVintageResponseDTO)
  @ApiPropertyOptional({ type: () => [MobileProjectVintageResponseDTO], readOnly: true, nullable: true })
  projectVintages?: MobileProjectVintageResponseDTO[];

  @Expose()
  @ApiPropertyOptional({ type: 'decimal', readOnly: true, nullable: true })
  proportionOfBook?: number;

  @Expose()
  productIds?: uuid[];
}

export class PortalProjectResponseDTO
  extends OmitType(MobileProjectResponseDTO, ['projectVintages'])
  implements PortalProjectResponse
{
  @Expose()
  @Type(() => PortalProjectVintageResponseDTO)
  @ApiPropertyOptional({ type: () => [PortalProjectVintageResponseDTO], readOnly: true, nullable: true })
  projectVintages?: PortalProjectVintageResponseDTO[];
}

export class AdminProjectResponseDTO extends IntersectionType(
  BaseResponseDTO,
  TrimmedProjectResponseDTO,
  PickType(Project, [
    'additionalityBlurb',
    'additionalityScore',
    'analystInsightsBlurb',
    'analystName',
    'analystRole',
    'beZeroRating',
    'beZeroUpdatedDate',
    'bookChartDisplayGroup',
    'carbonAccountingBlurb',
    'categoryRiskScore',
    'certificationBlurb',
    'certificationScore',
    'climateImpact',
    'climateImpactBlurb',
    'climateImpactRiskAdjusted',
    'countryRegion',
    'dateOfLatestVerification',
    'durabilityScore',
    'endDate',
    'futureDeliveryBlurb',
    'futureDeliveryRisk',
    'illustrationImageUrl',
    'independentVerifierName',
    'integrityGradeScoreRiskAdjusted',
    'isByorctApproved',
    'lastReviewDate',
    'mapImageUrl',
    'maxPercentage',
    'maxQuantity',
    'methodologyBlurb',
    'minPercentage',
    'minQuantity',
    'otherCoBenefitsBlurb',
    'pddReportLink',
    'permanenceBlurb',
    'previewImageUrl',
    'projectDeveloperName',
    'projectLocation',
    'rctStandard',
    'registryCreditsIssued',
    'registryCreditsRetired',
    'registryLink',
    'squareImage1Url',
    'squareImage2Url',
    'startDate',
    'suspended',
    'vvbReportLink',
    'waterBlurb',
  ]),
) {
  @Expose()
  @Type(() => AdminGroupedAllocationResponseDTO)
  @ApiProperty({ type: () => AdminGroupedAllocationResponseDTO, readOnly: true, nullable: true })
  assetAllocations?: AdminGroupedAllocationResponseDTO;

  @Expose()
  @Type(() => AdminBookTypeGroupedAllocationResponseDTO)
  @ApiProperty({ type: () => [AdminBookTypeGroupedAllocationResponseDTO], readOnly: true, nullable: true })
  assetAllocationsByBookType?: AdminBookTypeGroupedAllocationResponseDTO[];

  @Expose()
  @Type(() => AdminBufferCategoryResponseDTO)
  @ApiPropertyOptional({ type: () => AdminBufferCategoryResponseDTO, readOnly: true, nullable: true })
  bufferCategory?: AdminBufferCategoryResponseDTO;

  @Expose()
  @Type(() => TrimmedKmlResponseDTO)
  @ApiPropertyOptional({ type: () => TrimmedKmlResponseDTO })
  kml?: TrimmedKmlResponseDTO;

  @Expose()
  @ApiPropertyOptional({ type: Boolean, nullable: true, default: false })
  pdfReady?: boolean;

  @Expose()
  @ApiPropertyOptional({
    enum: PriceRange,
    readOnly: true,
    nullable: true,
    description: `only returned if includeRelations has ${ProjectRelations.PRICE_RANGE}`,
  })
  priceRange?: PriceRange;

  @Expose()
  @ApiPropertyOptional({ type: [String], description: 'portfolios that have this project' })
  productIds?: uuid[];

  @Expose()
  @Type(() => AdminProjectVintageResponseDTO)
  @ApiPropertyOptional({ type: () => [AdminProjectVintageResponseDTO], readOnly: true, nullable: true })
  projectVintages?: AdminProjectVintageResponseDTO[];
}

export class MobileProjectRelationsQueryDTO implements MobileProjectRelationsQuery {
  @Transform(({ value }) => value.toString().split(','))
  @IsIn(AllMobileProjectRelations, { each: true })
  @IsArray()
  @IsOptional()
  @ApiProperty({
    type: [String],
    description: 'relations to include',
    example: ProjectRelations.MINIMUM_BYO,
  })
  includeRelations: MobileProjectRelations[] = [];
}

export class PortalProjectRelationsQueryDTO implements PortalProjectRelationsQuery {
  @Transform(({ value }) => value.toString().split(','))
  @IsIn(AllPortalProjectRelations, { each: true })
  @IsArray()
  @IsOptional()
  @ApiProperty({
    type: [String],
    description: 'relations to include',
    example: ProjectRelations.MINIMUM_BYO,
  })
  includeRelations: PortalProjectRelations[] = [];
}

export class AdminProjectRelationsQueryDTO implements AdminProjectRelationsQuery {
  @Transform(({ value }) => value.toString().split(','))
  @IsEnum(ProjectRelations, { each: true })
  @IsArray()
  @IsOptional()
  @ApiProperty({
    type: [ProjectRelations],
    enum: ProjectRelations,
    description: 'relations to include',
    example: ProjectRelations.ASSET_ALLOCATIONS,
  })
  includeRelations: ProjectRelations[] = [];
}

export class MobileProjectQueryDTO
  extends IntersectionType(BaseQueryDTO, MobileProjectRelationsQueryDTO)
  implements MobileProjectQuery
{
  @Transform(({ value }) => value.toLowerCase() === 'true')
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean, nullable: true, default: false })
  byoBufferEligible = true;

  @Transform(({ value }) => value.toLowerCase() === 'true')
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean, nullable: true, default: false })
  hasAmount = true;

  @Transform(({ value }) => value.toLowerCase() === 'true')
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean, nullable: true, default: false })
  isByorctEligible = true;

  /* defaults */
  hasTrades = false;

  @Transform(({ value }) => {
    try {
      return value.toString().split(',').map(Number);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {
      throw new BadRequestException('assetIds must be an integer array');
    }
  })

  // todo (TD-20) : make order bys
  @IsEnum(ProjectOrderByOption)
  @IsOptional()
  @ApiPropertyOptional({
    enum: ProjectOrderByOption,
    nullable: true,
    default: ProjectOrderByOption.CREATED_AT,
  })
  orderBy: ProjectOrderByOption = ProjectOrderByOption.CREATED_AT;
}

// todo (TD-8) : see what FE actually uses for portal vs admin
export class PortalProjectQueryDTO
  extends IntersectionType(BaseQueryDTO, PortalProjectRelationsQueryDTO)
  implements PortalProjectQuery
{
  @Transform(({ value }) => value.toLowerCase() === 'true')
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean, nullable: true, default: false })
  byoBufferEligible = false;

  @Transform(({ value }) => value.toLowerCase() === 'true')
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean, nullable: true, default: false })
  hasAmount = false;

  @Transform(({ value }) => value.toLowerCase() === 'true')
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean, nullable: true, default: false })
  hasTrades = false;

  @Transform(({ value }) => {
    try {
      return value.toString().split(',').map(uuid);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {
      throw new BadRequestException('assetIds must be a uuid array');
    }
  })
  @IsArray()
  @IsUUID(undefined, { each: true })
  @ArrayMinSize(1)
  @IsOptional()
  @ApiPropertyOptional({
    type: [String],
    format: 'uuid',
    description: 'Project Ids to get specific set, invalid IDs will be ignored',
  })
  ids?: uuid[];

  @Transform(({ value }) => value.toLowerCase() === 'true')
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean, nullable: true, default: false })
  isByorctEligible = false;

  @Transform(({ value }) => {
    try {
      return value.toString().split(',').map(Number);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {
      throw new BadRequestException('assetIds must be an integer array');
    }
  })
  @IsArray()
  @ArrayMinSize(1)
  @IsOptional()
  @ApiPropertyOptional({
    type: [Number],
    format: 'integer',
    description: 'projectType ids to filter by',
  })
  projectTypeIds?: number[];

  @IsEnum(ProjectOrderByOption)
  @IsOptional()
  @ApiPropertyOptional({
    enum: ProjectOrderByOption,
    nullable: true,
    default: ProjectOrderByOption.CREATED_AT,
  })
  orderBy: ProjectOrderByOption = ProjectOrderByOption.CREATED_AT;
}

export class AdminProjectQueryDTO extends IntersectionType(
  OmitType(PortalProjectQueryDTO, ['includeRelations']),
  AdminProjectRelationsQueryDTO,
) {}

export class MobileProjectQueryResponseDTO extends BaseQueryResponseDTO implements MobileProjectQueryResponse {
  @Expose()
  @Type(() => MobileProjectResponseDTO)
  @ApiProperty({ type: () => [MobileProjectResponseDTO], readOnly: true })
  data: MobileProjectResponseDTO[] = [];
}

export class PortalProjectQueryResponseDTO extends BaseQueryResponseDTO implements PortalProjectQueryResponse {
  @Expose()
  @Type(() => PortalProjectResponseDTO)
  @ApiProperty({ type: () => [PortalProjectResponseDTO], readOnly: true })
  data: PortalProjectResponseDTO[] = [];
}

export class AdminProjectQueryResponseDTO extends BaseQueryResponseDTO implements AdminProjectQueryResponse {
  @Expose()
  @Type(() => AdminProjectResponseDTO)
  @ApiProperty({ type: () => [AdminProjectResponseDTO], readOnly: true })
  data: AdminProjectResponseDTO[] = [];
}

export class AdminProjectSearchDTO extends PickType(BaseQueryDTO, ['limit']) implements AdminProjectSearch {
  @Transform(({ value }) => value.toLowerCase() === 'true')
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean, nullable: true })
  hasBalance?: boolean;

  @IsNotEmpty()
  @ApiProperty()
  q!: string;

  @Transform(({ value }) => value.toLowerCase() === 'true')
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean, nullable: true, default: true })
  name = true;

  @Transform(({ value }) => value.toLowerCase() === 'true')
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean, nullable: true, default: true })
  id = true;

  @Transform(({ value }) => value.toLowerCase() === 'true')
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean, nullable: true, default: false })
  fuzzy = false;

  @Transform(({ value }) => value.toString().split(','))
  @IsOptional()
  @IsEnum(ProjectEmissionsImpactType, { each: true })
  @ApiPropertyOptional({ type: [String], nullable: true })
  emissionsImpactTypes?: ProjectEmissionsImpactType[];

  @Transform(({ value }) => value.toString().split(','))
  @IsArray()
  @IsOptional()
  @IsEnum(CountryRegions, { each: true })
  @ApiPropertyOptional({ type: [String], nullable: true })
  regions?: CountryRegions[];

  @Transform(({ value }) => value.toString().split(','))
  @IsOptional()
  @IsArray()
  @IsEnum(ProjectTypeCategory, { each: true })
  @ApiPropertyOptional({ type: [String], nullable: true })
  projectTypesCategories?: ProjectTypeCategory[];

  @Transform(({ value }) => value.toString().split(','))
  @IsArray()
  @IsEnum(ProjectEligibilityAccreditation, { each: true })
  @IsOptional()
  @ApiPropertyOptional({
    type: [ProjectEligibilityAccreditation],
    enum: ProjectEligibilityAccreditation,
    nullable: true,
  })
  eligibilityAccreditations?: ProjectEligibilityAccreditation[];

  @Transform(({ value }) => value.toString().split(','))
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ApiPropertyOptional({ type: [String], nullable: true })
  integrityGrades?: string[];
}

export class AdminProjectSearchResponseDTO
  extends PickType(AdminProjectResponseDTO, ['id', 'isScienceTeamApproved', 'name', 'registryProjectId'])
  implements AdminProjectSearchResponse
{
  @Expose()
  @Type(() => AdminBufferCategoryResponseDTO)
  @ApiPropertyOptional({ type: () => AdminBufferCategoryResponseDTO, readOnly: true, nullable: true })
  bufferCategory?: AdminBufferCategoryResponseDTO;

  @Expose()
  @Type(() => TrimmedCountryResponseDTO)
  @ApiProperty({ type: () => TrimmedCountryResponseDTO, readOnly: true })
  country?: TrimmedCountryResponseDTO;

  @Expose()
  @ApiPropertyOptional({ type: Number, readOnly: true, nullable: true })
  integrityGradeScore?: number;

  @Expose()
  @Type(() => AdminProjectTypeResponseDTO)
  @ApiPropertyOptional({ type: () => AdminProjectTypeResponseDTO, readOnly: true, nullable: true })
  projectType?: AdminProjectTypeResponseDTO;

  @Expose()
  @ApiPropertyOptional({ type: Boolean, readOnly: true, nullable: true })
  rctStandard?: boolean;

  @Expose()
  @ApiPropertyOptional({ type: Boolean, readOnly: true, nullable: true })
  suspended?: boolean;
}

/* transformations */

export function toAdminProjectUpdateLogDTO(raw: ProjectUpdateLog): AdminProjectUpdateLogResponseDTO {
  return Object.assign(new AdminProjectUpdateLogResponseDTO(), {
    ...raw,
    user: raw.user,
  });
}

export function toTrimmedProjectSdgResponseDTO(projectSdg: ProjectSDG): TrimmedProjectSdgResponseDTO {
  if (!projectSdg.sdgType) {
    throw new InternalServerErrorException('sdgType must be returned with ProjectSdgResponseDTO');
  }
  return Object.assign(new TrimmedProjectSdgResponseDTO(), {
    ...projectSdg,
    sdgType: Object.assign(new BaseSdgTypeResponseDTO(), { ...projectSdg.sdgType }),
    sdgTypeId: projectSdg.sdgType.id,
  });
}

export function toAdminProjectResponseDTO(
  project: InternalProjectResponse,
  includeRelations: ProjectRelations[] = [],
): AdminProjectResponseDTO {
  if (includeRelations.includes(ProjectRelations.ASSET_ALLOCATIONS) && !project.assetAllocations) {
    throw new InternalServerErrorException('project.assetAllocations must be included for relation');
  }
  if (
    includeRelations.includes(ProjectRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE) &&
    !project.assetAllocationsByBookType
  ) {
    throw new InternalServerErrorException('project.assetAllocationsByBookType must be included for relation');
  }
  if (includeRelations.includes(ProjectRelations.SDGS) && !project.projectSDGs.isInitialized()) {
    throw new InternalServerErrorException('project.projectSDGs must be included for relation');
  }

  const assetAllocations =
    includeRelations.includes(ProjectRelations.ASSET_ALLOCATIONS) && project.assetAllocations
      ? toAdminGroupedAllocationResponseDTOFromGroupedAllocation(
          project.assetAllocations, // it would have thrown an error above if it didn't exist
        )
      : undefined;

  const assetAllocationsByBookType =
    includeRelations.includes(ProjectRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE) && project.assetAllocationsByBookType
      ? project.assetAllocationsByBookType.map((m) => {
          return { ...toAdminGroupedAllocationResponseDTOFromGroupedAllocation(m), bookType: m.bookType };
        })
      : undefined;

  const projectSDGs = project.projectSDGs.isInitialized()
    ? project.projectSDGs.getItems().map((m) => toTrimmedProjectSdgResponseDTO(m))
    : undefined;
  const projectVintages = includeRelations.includes(ProjectRelations.PROJECT_VINTAGES)
    ? project.projectVintages.getItems().map((m) => toAdminProjectVintageResponseDTO(m))
    : undefined;

  const pdfReady = project.isScienceTeamApproved && !!project.mapImageUrl;

  const country = project.country ? toAdminCountryResponseDTO(project.country) : undefined;
  const kml =
    project.kmlLatitude || project.kmlLongitude || project.kmlUrl
      ? {
          latitude: project.kmlLatitude,
          longitude: project.kmlLongitude,
          url: project.kmlUrl ? project.kmlUrl : undefined,
        }
      : undefined;

  const bufferCategory = project.bufferCategory ?? project.projectType.bufferCategory;

  return Object.assign(new AdminProjectResponseDTO(), {
    ...project,
    hasBalance: project.flags?.hasBalance,
    assetAllocations,
    assetAllocationsByBookType,
    country,
    pdfReady,
    kml,
    projectLocation: project.country?.name,
    projectSDGs,
    projectVintages,
    registry: project.registry ? toTrimmedRegistryResponseDTO(project.registry) : undefined,
    bufferCategory: bufferCategory ? toAdminBufferCategoryResponseDTO(bufferCategory) : undefined,
  });
}

export function toPortalProjectResponseDTO(
  project: InternalProjectResponse,
  includeRelations: ProjectRelations[] = [],
): PortalProjectResponseDTO {
  const projectSDGs = includeRelations.includes(ProjectRelations.SDGS)
    ? project.projectSDGs.getItems().map((m) => toTrimmedProjectSdgResponseDTO(m))
    : undefined;
  const country = project.country ? toAdminCountryResponseDTO(project.country) : undefined;
  const kml =
    project.kmlLatitude || project.kmlLongitude || project.kmlUrl
      ? {
          latitude: project.kmlLatitude,
          longitude: project.kmlLongitude,
          url: project.kmlUrl,
        }
      : undefined;
  const pdfReady = project.isScienceTeamApproved && !!project.mapImageUrl;
  const projectVintages =
    includeRelations.includes(ProjectRelations.PROJECT_VINTAGES) && project.projectVintages.isInitialized()
      ? project.projectVintages.getItems().map((m) => toPortalProjectVintageResponseDTO(m))
      : undefined;

  const response = Object.assign(new PortalProjectResponseDTO(), {
    ...project,
    projectVintages,
    country,
    hasBalance: project.flags?.hasBalance || false,
    kml,
    pdfReady,
    projectSDGs,
    projectLocation: project.country?.name,
  });

  if (includeRelations.includes(ProjectRelations.MINIMUM_BYO)) {
    response.minQuantityBYO = Math.max(
      project.minQuantity ?? 0,
      Math.ceil((project.assetAllocations?.totalAmountAllocated ?? 0) * (project.minPercentage ?? 0)),
    );
  }

  return response;
}

export function toMobileProjectResponseDTO(
  project: InternalProjectResponse,
  includeRelations: ProjectRelations[] = [],
): MobileProjectResponseDTO {
  const obj = Object.assign(new MobileProjectResponseDTO(), toPortalProjectResponseDTO(project, includeRelations));
  return obj;
}

export function toTrimmedProjectResponseDTO(project: Project): TrimmedProjectResponseDTO {
  const response: TrimmedProjectResponseDTO = Object.assign(new TrimmedProjectResponseDTO(), {
    ...project,
    bufferCategory: project.bufferCategory ?? project.projectType?.bufferCategory,
    hasBalance: project.flags?.hasBalance || false,
    country: project.country,
    projectSDGs:
      project.projectSDGs && project.projectSDGs.isInitialized()
        ? project.projectSDGs.getItems().map((m) => toTrimmedProjectSdgResponseDTO(m))
        : undefined,
    projectType: { ...project.projectType },
  });

  // if (!isAdmin) {
  //   delete response.bufferCategory;
  //   delete response.bookChartDisplayGroup;
  //   delete response.rctStandard;
  //   delete response.suspended;
  // }
  return response;
}
