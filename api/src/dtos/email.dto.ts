/* third party */
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsBoolean, IsOptional, IsUUID } from 'class-validator';
/* rubicon */
import {
  MobileByorctEmailRequest,
  MobileByorctEmailResponse,
  MobilePurchaseEmailRequest,
  PortalByorctEmailRequest,
  PortalByorctEmailResponse,
  PortalPurchaseEmailRequest,
  uuid,
} from '@rubiconcarbon/shared-types';
/* app */
import { IsIntGreaterThan } from '@app/validators';
import { IsNonEmptyString } from '@app/helpers';
import { UuidType } from '@mikro-orm/core';
import { TrimmedUserResponseDTO } from './trimmed.dto';

export class MobileByorctEmailRequestDTO implements MobileByorctEmailRequest {
  @IsNonEmptyString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  modelPortfolioName?: string;

  @Expose()
  @IsUUID()
  @ApiProperty({ type: UuidType })
  cachedEstimateId!: uuid;
}

export class PortalByorctEmailRequestDTO extends MobileByorctEmailRequestDTO implements PortalByorctEmailRequest {}

export class MobileByorctEmailResponseDTO implements MobileByorctEmailResponse {
  @Expose()
  @IsUUID()
  @ApiProperty({ type: String, format: 'uuid' })
  modelPortfolioId!: uuid;

  @Expose()
  @Type(() => TrimmedUserResponseDTO)
  @ApiProperty({ type: () => TrimmedUserResponseDTO, readOnly: true })
  rubiconManager!: TrimmedUserResponseDTO;
}

export class PortalByorctEmailResponseDTO extends MobileByorctEmailResponseDTO implements PortalByorctEmailResponse {}

export class MobilePurchaseEmailRequestDTO implements MobilePurchaseEmailRequest {
  @Expose()
  @IsIntGreaterThan(0)
  @ApiProperty({ type: 'integer', minimum: 0 })
  amount!: number;

  @IsBoolean()
  @ApiProperty({ type: Boolean })
  isPurchaseToRetire!: boolean;

  @IsBoolean()
  @ApiProperty({ type: Boolean })
  isRiskAdjusted!: boolean;

  @IsNonEmptyString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  modelPortfolioName?: string;

  @IsUUID()
  @ApiProperty({ type: String, format: 'uuid' })
  rctId!: uuid;
}

export class PortalPurchaseEmailRequestDTO
  extends MobilePurchaseEmailRequestDTO
  implements PortalPurchaseEmailRequest {}
