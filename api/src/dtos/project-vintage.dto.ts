/* third party */
import { BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { ApiProperty, ApiPropertyOptional, IntersectionType, OmitType, PickType } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import { ArrayMinSize, IsArray, IsEnum, IsNotEmpty, IsOptional, IsUUID } from 'class-validator';
import Decimal from 'decimal.js';
/* rubicon */
import {
  AdminHistoricalBufferQuery,
  AdminHistoricalBufferQueryResponse,
  AdminHistoricalBufferResponse,
  AdminProjectVintageQuery,
  AdminProjectVintageQueryResponse,
  AdminProjectVintageRelationsQuery,
  AdminProjectVintageRequest,
  AdminProjectVintageResponse,
  AdminProjectVintageRiskBufferRequest,
  GroupedAllocationRelations,
  HistoricalBufferOrderByOption,
  MobileProjectVintageResponse,
  PortalProjectVintageResponse,
  ProjectVintageBufferType,
  ProjectVintageOrderByOption,
  ProjectVintageRelations,
  TrimmedProjectVintageResponse,
  uuid,
} from '@rubiconcarbon/shared-types';
/* app */
import {
  AdminBookTypeGroupedAllocationResponseDTO,
  AdminGroupedAllocationResponseDTO,
  toAdminGroupedAllocationResponseDTOFromGroupedAllocation,
} from '@app/dtos/allocation.dto';
import { BaseQueryDTO, BaseQueryResponseDTO, BaseResponseDTO } from '@app/dtos/base.dto';
import { AdminCreditInflowResponseDTO, toAdminCreditInflowResponseDTO } from '@app/dtos/credit-flow.dto';
import {
  AdminProjectResponseDTO,
  TrimmedProjectResponseDTO,
  toAdminProjectResponseDTO,
  toTrimmedProjectResponseDTO,
} from '@app/dtos/project.dto';
import { TrimmedUserResponseDTO } from '@app/dtos/trimmed.dto';
import { HistoricalRiskBuffer, ProjectVintage } from '@app/entities';
import { TransformDecimal } from '@app/helpers/transforms.helper';
import { IsNonEmptyString } from '@app/helpers/validations.helper';
import { InternalProjectVintageResponse } from '@app/interfaces/project-vintage.interface';
import { PricingService } from '@app/services';

export class AdminProjectVintageRequestDTO
  extends PickType(ProjectVintage, ['interval'])
  implements AdminProjectVintageRequest
{
  @IsNonEmptyString()
  @IsNotEmpty()
  @ApiProperty({ type: String, format: 'integer' })
  name!: string;

  @IsUUID()
  @IsNotEmpty()
  @ApiProperty({ type: String, format: 'uuid' })
  projectId!: uuid;
}

export class AdminProjectVintageRiskBufferRequestDTO
  extends PickType(ProjectVintage, ['highBufferPercentage', 'lowBufferPercentage', 'riskBufferPercentage'])
  implements AdminProjectVintageRiskBufferRequest
{
  @IsNonEmptyString()
  @ApiProperty({ type: String })
  notes!: string;

  @IsUUID()
  @ApiProperty({ type: String, format: 'uuid' })
  projectVintageId!: uuid;
}

export class TrimmedProjectVintageResponseDTO
  extends IntersectionType(
    BaseResponseDTO,
    // PickType(ProjectVintage, ['highBufferPercentage', 'interval', 'lowBufferPercentage', 'riskBufferPercentage']),
    PickType(ProjectVintage, ['interval']),
  )
  implements TrimmedProjectVintageResponse
{
  // @Expose()
  // @TransformDecimal()
  // @ApiPropertyOptional({ type: 'decimal', readOnly: true, nullable: true })
  // averageCostBasis?: Decimal;

  @Expose()
  @ApiProperty({ type: Boolean })
  isRctEligible!: boolean;

  @Expose()
  @ApiProperty({ type: String })
  name!: string;

  // @Expose()
  // @TransformDecimal()
  // @ApiPropertyOptional({ type: 'decimal', readOnly: true, nullable: true })
  // price?: Decimal;

  @Expose()
  @ApiPropertyOptional({ type: () => TrimmedProjectResponseDTO, readOnly: true, nullable: true })
  project!: TrimmedProjectResponseDTO;
}

export class MobileProjectVintageResponseDTO
  extends TrimmedProjectVintageResponseDTO
  implements MobileProjectVintageResponse {}

export class PortalProjectVintageResponseDTO
  extends TrimmedProjectVintageResponseDTO
  implements PortalProjectVintageResponse {}

export class AdminProjectVintageResponseDTO
  extends IntersectionType(
    OmitType(TrimmedProjectVintageResponseDTO, ['project']),
    PickType(ProjectVintage, ['highBufferPercentage', 'lowBufferPercentage', 'riskBufferPercentage']),
  )
  implements AdminProjectVintageResponse
{
  @Expose()
  @Type(() => AdminGroupedAllocationResponseDTO)
  @ApiProperty({ type: () => AdminGroupedAllocationResponseDTO, readOnly: true, nullable: true })
  assetAllocations?: AdminGroupedAllocationResponseDTO;

  @Expose()
  @Type(() => AdminBookTypeGroupedAllocationResponseDTO)
  @ApiProperty({ type: () => [AdminBookTypeGroupedAllocationResponseDTO], readOnly: true, nullable: true })
  assetAllocationsByBookType?: AdminBookTypeGroupedAllocationResponseDTO[];

  @Expose()
  @TransformDecimal()
  @ApiPropertyOptional({ type: 'decimal', readOnly: true, nullable: true })
  averageCostBasis?: Decimal;

  @Expose()
  @ApiPropertyOptional({
    type: () => [AdminCreditInflowResponseDTO],
    readOnly: true,
    nullable: true,
    description: 'only returned with findOne queries',
  })
  creditFlows?: AdminCreditInflowResponseDTO[];

  @Expose()
  @TransformDecimal()
  @ApiPropertyOptional({ type: 'decimal', readOnly: true, nullable: true })
  price?: Decimal;

  @Expose()
  @TransformDecimal()
  @ApiPropertyOptional({ type: 'decimal', readOnly: true, nullable: true })
  markupPrice?: Decimal;

  @Expose()
  @ApiPropertyOptional({ type: () => AdminProjectResponseDTO, readOnly: true, nullable: true })
  project!: AdminProjectResponseDTO;
}

export class AdminHistoricalBufferResponseDTO
  extends PickType(HistoricalRiskBuffer, ['id', 'timestamp', 'newPercentage', 'notes', 'oldPercentage', 'type'])
  implements AdminHistoricalBufferResponse
{
  @Expose()
  @Type(() => AdminProjectVintageResponseDTO)
  @ApiProperty({ type: () => AdminProjectVintageResponseDTO, readOnly: true })
  projectVintage!: AdminProjectVintageResponseDTO;

  @Expose()
  @Type(() => TrimmedUserResponseDTO)
  @ApiProperty({ type: () => TrimmedUserResponseDTO, readOnly: true })
  user!: TrimmedUserResponseDTO;
}

export class AdminProjectVintageRelationsQueryDTO implements AdminProjectVintageRelationsQuery {
  @Transform(({ value }) => value.toString().split(','))
  @IsEnum(ProjectVintageRelations, { each: true })
  @IsArray()
  @IsOptional()
  @ApiProperty({
    type: [ProjectVintageRelations],
    enum: ProjectVintageRelations,
    description: 'relations to include',
    example: ProjectVintageRelations.PROJECT,
  })
  includeRelations: ProjectVintageRelations[] = [];
}

export class AdminProjectVintageQueryDTO
  extends IntersectionType(BaseQueryDTO, AdminProjectVintageRelationsQueryDTO)
  implements AdminProjectVintageQuery
{
  @Transform(({ value }) => {
    try {
      return value.toString().split(',').map(uuid);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {
      throw new BadRequestException('assetIds must be a uuid array');
    }
  })
  @IsArray()
  @ArrayMinSize(1)
  @IsOptional()
  @ApiPropertyOptional({
    type: [String],
    format: 'uuid',
    description: 'ids to retrieve',
  })
  ids?: uuid[];

  @Transform(({ value }) => {
    try {
      return value.toString().split(',').map(uuid);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {
      throw new BadRequestException('assetIds must be a uuid array');
    }
  })
  @IsArray()
  @ArrayMinSize(1)
  @IsOptional()
  @ApiPropertyOptional({
    type: [String],
    format: 'uuid',
    description: 'projectIds to retrieve',
  })
  projectIds?: uuid[];

  @Transform(({ value }) => {
    try {
      return value.toString().split(',').map(uuid);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {
      throw new BadRequestException('assetIds must be a uuid array');
    }
  })
  @IsArray()
  @ArrayMinSize(1)
  @IsOptional()
  @ApiPropertyOptional({
    type: [Number],
    format: 'integer',
    description: 'projectType ids to filter by',
  })
  projectTypeIds?: number[];

  @IsEnum(ProjectVintageOrderByOption)
  @IsOptional()
  @ApiPropertyOptional({
    enum: ProjectVintageOrderByOption,
    nullable: true,
    default: ProjectVintageOrderByOption.CREATED_AT,
  })
  orderBy: ProjectVintageOrderByOption = ProjectVintageOrderByOption.CREATED_AT;
}

export class AdminHistoricalBufferQueryDTO extends BaseQueryDTO implements AdminHistoricalBufferQuery {
  @IsUUID()
  @IsOptional()
  @ApiPropertyOptional({ type: String, format: 'uuid', nullable: true })
  projectVintageId?: uuid;

  @IsEnum(ProjectVintageBufferType)
  @IsOptional()
  @ApiPropertyOptional({
    type: ProjectVintageBufferType,
    nullable: true,
  })
  type?: ProjectVintageBufferType;

  @IsUUID()
  @IsOptional()
  @ApiPropertyOptional({ type: String, format: 'uuid', nullable: true })
  userId?: uuid;

  @IsEnum(HistoricalBufferOrderByOption)
  @IsOptional()
  @ApiPropertyOptional({
    type: HistoricalBufferOrderByOption,
    nullable: true,
    default: HistoricalBufferOrderByOption.TIMESTAMP,
  })
  orderBy: HistoricalBufferOrderByOption = HistoricalBufferOrderByOption.TIMESTAMP;
}

export class AdminProjectVintageQueryResponseDTO
  extends BaseQueryResponseDTO
  implements AdminProjectVintageQueryResponse
{
  @Expose()
  @Type(() => AdminProjectVintageResponseDTO)
  @ApiProperty({ type: () => [AdminProjectVintageResponseDTO], readOnly: true, default: [] })
  data: AdminProjectVintageResponseDTO[] = [];
}

export class AdminHistoricalBufferQueryResponseDTO
  extends BaseQueryResponseDTO
  implements AdminHistoricalBufferQueryResponse
{
  @Expose()
  @Type(() => AdminHistoricalBufferResponseDTO)
  @ApiProperty({ type: () => [AdminHistoricalBufferResponseDTO], readOnly: true })
  data: AdminHistoricalBufferResponseDTO[] = [];
}

/* transformations */

export function toAdminHistoricalBufferResponseDTO(buffer: HistoricalRiskBuffer): AdminHistoricalBufferResponseDTO {
  return Object.assign(new AdminHistoricalBufferResponseDTO(), {
    ...buffer,
    projectVintage: toAdminProjectVintageResponseDTO(buffer.projectVintage),
    user: {
      id: buffer.user.id,
      createdAt: buffer.user.createdAt,
      updatedAt: buffer.user.updatedAt,
    },
  });
}

// todo : definitely need to figure out where trimmed is used and what is needed
export function toTrimmedProjectVintageResponseDTO(projectVintage: ProjectVintage): TrimmedProjectVintageResponseDTO {
  const response: TrimmedProjectVintageResponseDTO = Object.assign(new TrimmedProjectVintageResponseDTO(), {
    ...projectVintage,
    isRctEligible: projectVintage.grouping?.is_rct_eligible,
    averageCostBasis: projectVintage.prices?.average_cost_basis
      ? new Decimal(projectVintage.prices?.average_cost_basis)
      : undefined,
    name: projectVintage.name(),
    price: projectVintage.prices?.price ? new Decimal(projectVintage.prices?.price) : undefined,
    project: toTrimmedProjectResponseDTO(projectVintage.project),
  });

  // if (!isAdmin) {
  //   delete response.price;
  //   delete response.averageCostBasis;
  //   delete response.highBufferPercentage;
  //   delete response.lowBufferPercentage;
  //   delete response.riskBufferPercentage;
  // }

  return response;
}

export function toPortalProjectVintageResponseDTO(
  projectVintage: InternalProjectVintageResponse,
): PortalProjectVintageResponseDTO {
  const response: PortalProjectVintageResponseDTO = Object.assign(new PortalProjectVintageResponseDTO(), {
    ...projectVintage,
    isRctEligible: projectVintage.grouping?.is_rct_eligible,
    averageCostBasis: projectVintage.prices?.average_cost_basis
      ? new Decimal(projectVintage.prices?.average_cost_basis)
      : undefined,
    name: projectVintage.name(),
    price: projectVintage.prices?.price ? new Decimal(projectVintage.prices?.price) : undefined,
    project: toTrimmedProjectResponseDTO(projectVintage.project),
  });

  return response;
}

export function toAdminProjectVintageResponseDTO(
  projectVintage: InternalProjectVintageResponse,
  includeRelations: ProjectVintageRelations[] = [],
  services?: { pricing?: PricingService },
): AdminProjectVintageResponseDTO {
  if (includeRelations.includes(ProjectVintageRelations.LINE_ITEMS) && !projectVintage.creditInflows.isInitialized()) {
    throw new InternalServerErrorException('projectVintage.creditInflows must be included for relation');
  }
  if (includeRelations.includes(ProjectVintageRelations.PROJECT) && !projectVintage.project.name) {
    throw new InternalServerErrorException('projectVintage.project must be included for relation');
  }

  if (includeRelations.includes(ProjectVintageRelations.PRICES) && !projectVintage.prices) {
    throw new InternalServerErrorException('projectVintage.prices must be included for relation');
  }

  if (includeRelations.includes(ProjectVintageRelations.ASSET_ALLOCATIONS) && !projectVintage.assetAllocations) {
    throw new InternalServerErrorException('projectVintage.assetAllocations must be included for relation');
  }
  if (
    includeRelations.includes(ProjectVintageRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE) &&
    !projectVintage.assetAllocationsByBookType
  ) {
    throw new InternalServerErrorException('projectVintage.assetAllocationsByBookType must be included for relation');
  }

  const inflows = includeRelations.includes(ProjectVintageRelations.LINE_ITEMS)
    ? projectVintage.creditInflows.getItems().map((m) => toAdminCreditInflowResponseDTO(m))
    : undefined;
  const project =
    includeRelations.includes(ProjectVintageRelations.PROJECT) && projectVintage.project
      ? toAdminProjectResponseDTO(projectVintage.project)
      : undefined;
  const assetAllocations = includeRelations.includes(ProjectVintageRelations.ASSET_ALLOCATIONS)
    ? toAdminGroupedAllocationResponseDTOFromGroupedAllocation(
        projectVintage.assetAllocations!, // it would have thrown an error above if it didn't exist
        includeRelations.includes(ProjectVintageRelations.PRICES) ? [GroupedAllocationRelations.PRICES] : [],
        projectVintage.prices?.price,
      )
    : undefined;
  const assetAllocationsByBookType =
    includeRelations.includes(ProjectVintageRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE) &&
    projectVintage.assetAllocationsByBookType
      ? projectVintage.assetAllocationsByBookType.map((m) => {
          return { ...toAdminGroupedAllocationResponseDTOFromGroupedAllocation(m), bookType: m.bookType };
        })
      : undefined;

  let markupPrice: Decimal | undefined = undefined;

  if (projectVintage.prices?.price !== undefined) {
    const markup = services?.pricing?.markupUnitPrice(new Decimal(projectVintage.prices.price).toNumber(), true);
    markupPrice =
      markup !== undefined ? new Decimal(markup).add(1).mul(new Decimal(projectVintage.prices?.price)) : undefined;
  }

  return Object.assign(new AdminProjectVintageResponseDTO(), {
    ...projectVintage,
    isRctEligible: projectVintage.grouping?.is_rct_eligible,
    assetAllocations,
    assetAllocationsByBookType,
    averageCostBasis: projectVintage.averageCostBasis || projectVintage.prices?.average_cost_basis,
    name: projectVintage.name(),
    price: projectVintage.prices?.price,
    markupPrice,
    project,
    creditFlows: inflows, // todo : is this even used? never returned
  });
}
