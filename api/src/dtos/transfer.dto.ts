/* third party */
import { ApiProperty, ApiPropertyOptional, IntersectionType, PickType } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import { ArrayMinSize, IsArray, IsEnum, IsOptional, IsUUID, ValidateNested } from 'class-validator';
/* rubicon */
import {
  AdminTransferQuery,
  AdminTransferQueryResponse,
  AdminTransferRelationsQuery,
  AdminTransferRequest,
  AdminTransferResponse,
  TransferOrderByOption,
  TransferRelation,
  uuid,
} from '@rubiconcarbon/shared-types';
/* app */
import { BaseQueryDTO, BaseQueryResponseDTO, BaseResponseDTO } from '@app/dtos/base.dto';
import {
  AdminAssetTransferRequestDTO,
  AdminAssetTransferResponseDTO,
  AdminRrtAssetCompositionRequestDTO,
} from './asset.dto';
import { Transfer } from '@app/entities';
import { TrimmedUserResponseDTO } from './trimmed.dto';

export class AdminTransferRequestDTO extends PickType(Transfer, ['memo'] as const) implements AdminTransferRequest {
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => AdminAssetTransferRequestDTO)
  @ApiProperty({ type: [AdminAssetTransferRequestDTO] })
  assetTransfers!: AdminAssetTransferRequestDTO[];
}

export class AdminRrtCompositionRequestDTO extends PickType(Transfer, ['memo'] as const) {
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => AdminRrtAssetCompositionRequestDTO)
  @ApiProperty({ type: [AdminRrtAssetCompositionRequestDTO] })
  assets!: AdminRrtAssetCompositionRequestDTO[];

  @IsUUID()
  @ApiProperty({ type: String, format: 'uuid' })
  portfolioId!: uuid;
}

export class AdminTransferResponseDTO
  extends IntersectionType(BaseResponseDTO, PickType(Transfer, ['memo', 'totalAmount'] as const))
  implements AdminTransferResponse
{
  @Expose()
  @Type(() => AdminAssetTransferResponseDTO)
  @ApiPropertyOptional({ type: () => [AdminAssetTransferResponseDTO], readOnly: true, nullable: true })
  assetTransfers?: AdminAssetTransferResponseDTO[];

  @Expose()
  @ApiProperty({ type: Number, format: 'integer' })
  docsCount!: number;

  @Expose()
  @Type(() => TrimmedUserResponseDTO)
  @ApiProperty({ type: () => TrimmedUserResponseDTO, readOnly: true })
  user!: TrimmedUserResponseDTO;
}

export class AdminTransferRelationsQueryDTO implements AdminTransferRelationsQuery {
  @Transform(({ value }) => value.toString().split(','))
  @IsEnum(TransferRelation, { each: true })
  @IsArray()
  @IsOptional()
  @ApiProperty({
    enum: TransferRelation,
    description: 'relations to include',
    example: TransferRelation.ASSETS,
  })
  includeRelations: TransferRelation[] = [];
}

export class AdminTransferQueryDTO
  extends IntersectionType(BaseQueryDTO, AdminTransferRelationsQueryDTO)
  implements AdminTransferQuery
{
  @IsUUID()
  @IsOptional()
  @ApiPropertyOptional({
    type: String,
    format: 'uuid',
    nullable: true,
    description: 'filter where this asset was transferred',
  })
  assetId?: uuid;

  @IsUUID()
  @IsOptional()
  @ApiPropertyOptional({
    type: String,
    format: 'uuid',
    nullable: true,
    description: 'filter where destination is to this owner',
  })
  destinationId?: uuid;

  @IsUUID()
  @IsOptional()
  @ApiPropertyOptional({
    type: String,
    format: 'uuid',
    nullable: true,
    description: 'filter where source or destination is this owner',
  })
  ownerId?: uuid;

  @IsUUID()
  @IsOptional()
  @ApiPropertyOptional({
    type: String,
    format: 'uuid',
    nullable: true,
    description: 'filter where source is from this owner',
  })
  sourceId?: uuid;

  @IsUUID()
  @IsOptional()
  @ApiPropertyOptional({ type: String, format: 'uuid', nullable: true })
  userId?: uuid;

  @IsEnum(TransferOrderByOption)
  @IsOptional()
  @ApiPropertyOptional({
    enum: TransferOrderByOption,
    nullable: true,
    default: TransferOrderByOption.CREATED_AT,
  })
  orderBy: TransferOrderByOption = TransferOrderByOption.CREATED_AT;
}

export class AdminTransferQueryResponseDTO extends BaseQueryResponseDTO implements AdminTransferQueryResponse {
  @Expose()
  @Type(() => AdminTransferResponseDTO)
  @ApiProperty({ type: () => [AdminTransferResponseDTO], readOnly: true })
  data: AdminTransferResponseDTO[] = [];
}

/* transformations */

export function toAdminTransferResponseDTO(
  transfer: Transfer,
  includedRelations: TransferRelation[] = [],
): AdminTransferResponseDTO {
  let assetTransfers;

  const user = includedRelations.includes(TransferRelation.USER)
    ? {
        id: transfer.user.id,
        createdAt: transfer.user.createdAt,
        updatedAt: transfer.user.updatedAt,
        email: transfer.user.email,
        name: transfer.user.name,
      }
    : undefined;

  return Object.assign(new AdminTransferResponseDTO(), {
    ...transfer.transactionDetails,
    ...transfer,
    assetTransfers,
    user,
  });
}
