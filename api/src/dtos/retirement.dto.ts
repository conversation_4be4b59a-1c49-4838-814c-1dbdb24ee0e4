/* third party */
import { ApiProperty, ApiPropertyOptional, IntersectionType, PartialType, PickType } from '@nestjs/swagger';
import { ArrayMinSize, IsArray, IsEnum, IsIn, IsOptional, IsString, IsUUID, ValidateNested } from 'class-validator';
import { Expose, Transform, Type } from 'class-transformer';
/* rubicon */
import {
  AdminRetirementLinkUpdateRequest,
  AdminRetirementQuery,
  AdminRetirementQueryResponse,
  AdminRetirementRelationsQuery,
  AdminRetirementRequest,
  AdminRetirementResponse,
  AdminSuggestedBufferComposition,
  AdminSuggestedBufferElement,
  AdminSuggestedBufferRetirementRequest,
  AdminSuggestedBufferRetirementResponse,
  AdminUpdateRetirementAmountsRequest,
  AllPortalRetirementRelations,
  AssetType,
  BaseRetirementOrderBy,
  OrderByDirection,
  PortalRetirementRelations,
  PortalRetirementRelationsQuery,
  PortalRetirementRequest,
  PortalRetirementResponse,
  RetirementOrderByOptions,
  RetirementRelations,
  RetirementStatus,
  RetirementType,
  TrimmedRetirementLink,
  uuid,
} from '@rubiconcarbon/shared-types';
/* app */
import { BaseQueryDTO, BaseQueryResponseDTO, BaseResponseDTO } from '@app/dtos/base.dto';
import { AssetFlow, ProjectVintage, Retirement, RetirementLink } from '@app/entities';
import { IsIntGreaterThan, IsIntGreaterThanOrEqualTo } from '@app/validators';
import {
  AdminRctAssetRetirementResponseDTO,
  AdminVintageAssetRetirementResponseDTO,
  BaseAssetRetirementRequestDTO,
  PortalRctAssetRetirementResponseDTO,
  PortalVintageAssetRetirementResponseDTO,
  toAdminAssetResponseDTO,
} from './asset.dto';
import { BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { TrimmedProjectVintageResponseDTO, toAdminProjectVintageResponseDTO } from './project-vintage.dto';
import {
  TrimmedOrganizationResponseDTO,
  TrimmedUserResponseDTO,
  toTrimmedBookResponseDTO,
  toTrimmedOrganizationResponseDTO,
} from './trimmed.dto';
import Decimal from 'decimal.js';

export class BaseRetirementOrderByDTO implements BaseRetirementOrderBy {
  constructor(orderBy: string, orderByDirection: string) {
    this.orderBy = orderBy as RetirementOrderByOptions;
    this.orderByDirection = orderByDirection as OrderByDirection;
  }

  @IsEnum(RetirementOrderByOptions)
  @ApiProperty({
    enum: RetirementOrderByOptions,
    description: 'order by options',
  })
  orderBy!: RetirementOrderByOptions;

  @IsEnum(OrderByDirection)
  @ApiProperty({
    enum: OrderByDirection,
    description: 'orderBy in this direction',
  })
  orderByDirection!: OrderByDirection;
}

export class TrimmedRetirementLinkDTO
  extends PickType(RetirementLink, ['url', 'label'])
  implements TrimmedRetirementLink {}

export class PortalRetirementRequestDTO
  extends PickType(Retirement, ['assetType', 'beneficiary', 'isPublic', 'memo', 'registryAccount', 'type'])
  implements PortalRetirementRequest
{
  @Type(() => BaseAssetRetirementRequestDTO)
  @ValidateNested({ each: true })
  @IsArray()
  @ArrayMinSize(1)
  @ApiProperty({ type: () => [BaseAssetRetirementRequestDTO] })
  assets!: BaseAssetRetirementRequestDTO[];

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    type: String,
    nullable: true,
    description: 'the token received from email to confirm retirement request',
  })
  token?: string;
}

export class AdminRetirementRequestDTO extends PortalRetirementRequestDTO implements AdminRetirementRequest {
  @IsUUID()
  @ApiProperty({ type: String, format: 'uuid' })
  organizationId!: uuid;
}

export class AdminUpdateRetirementAmountsRequestDTO implements AdminUpdateRetirementAmountsRequest {
  @Expose()
  @IsIntGreaterThanOrEqualTo(0)
  @ApiProperty({ type: 'integer' })
  amountTransacted!: number;

  @IsUUID()
  @ApiProperty({ type: String, format: 'uuid' })
  projectVintageId!: uuid;

  @IsUUID()
  @ApiProperty({ type: String, format: 'uuid' })
  sourceId!: uuid;
}

export class AdminRetirementLinkUpdateRequestDTO implements AdminRetirementLinkUpdateRequest {
  @Expose()
  @ValidateNested({ each: true })
  @Type(() => TrimmedRetirementLinkDTO)
  @ApiProperty({ type: Array })
  links!: TrimmedRetirementLinkDTO[];

  @Expose()
  @IsUUID()
  @ApiProperty({ type: String, format: 'uuid' })
  projectVintageId!: uuid;
}

export class AdminSuggestedBufferRetirementRequestDTO implements AdminSuggestedBufferRetirementRequest {
  @Transform(({ value }) => value.toString().split(','))
  @IsArray()
  @IsUUID(undefined, { each: true })
  @Type(() => uuid)
  @ApiProperty({
    type: () => [uuid],
    readOnly: true,
    description: '',
  })
  retirementIds!: uuid[];
}

export class AdminSuggestedBufferElementDTO implements AdminSuggestedBufferElement {
  @Expose()
  @Type(() => TrimmedProjectVintageResponseDTO)
  @ApiProperty({ type: () => TrimmedProjectVintageResponseDTO, readOnly: true })
  projectVintage!: TrimmedProjectVintageResponseDTO;

  @Expose()
  @IsIntGreaterThan(0)
  @ApiProperty({ type: 'integer' })
  amount!: number;
}

export class AdminSuggestedBufferCompositionDTO implements AdminSuggestedBufferComposition {
  @Expose()
  @IsString()
  @ApiProperty({ type: String, description: 'category of the suggested buffer retirement' })
  category!: string;

  @Expose()
  @Type(() => AdminSuggestedBufferElementDTO)
  @ValidateNested({ each: true })
  @IsArray()
  @ApiProperty({
    type: () => [AdminSuggestedBufferElementDTO],
    description: 'suggested buffer retirements for the category',
  })
  suggestedBufferRetirements!: AdminSuggestedBufferElementDTO[];
}

export class AdminSuggestedBufferRetirementResponseDTO implements AdminSuggestedBufferRetirementResponse {
  suggestedAllocations!: AdminSuggestedBufferCompositionDTO[];
}

export class PortalRetirementResponseDTO
  extends IntersectionType(
    BaseResponseDTO,
    PickType(Retirement, [
      'amount',
      'assetType',
      'beneficiary',
      'dateFinished',
      'dateStarted',
      'isPublic',
      'memo',
      'registryAccount',
      'status',
      'type',
      'uiKey',
    ]),
  )
  implements PortalRetirementResponse
{
  @Expose()
  @Type(() =>
    IntersectionType(
      PartialType(PortalRctAssetRetirementResponseDTO),
      PartialType(PortalVintageAssetRetirementResponseDTO),
    ),
  )
  @ApiProperty({
    type: () => [PortalRctAssetRetirementResponseDTO || PortalVintageAssetRetirementResponseDTO],
    readOnly: true,
  })
  assets!: (PortalRctAssetRetirementResponseDTO | PortalVintageAssetRetirementResponseDTO)[];

  // @Expose()
  // @Type(() => TrimmedOrganizationResponseDTO)
  // @ApiProperty({
  //   type: () => TrimmedOrganizationResponseDTO,
  //   readOnly: true,
  // })
  // organization!: TrimmedOrganizationResponseDTO;

  @Expose()
  @ApiProperty({ type: Number, format: 'integer' })
  docsCount!: number;

  @Expose()
  @Type(() => TrimmedUserResponseDTO)
  @ApiPropertyOptional({ type: () => TrimmedUserResponseDTO, readOnly: true, nullable: true })
  requestedBy?: TrimmedUserResponseDTO;
}

export class AdminRetirementResponseDTO
  extends IntersectionType(
    BaseResponseDTO,
    PickType(Retirement, [
      'amount',
      'assetType',
      'beneficiary',
      'dateFinished',
      'dateStarted',
      'isPublic',
      'memo',
      'registryAccount',
      'status',
      'type',
      'uiKey',
    ]),
  )
  implements AdminRetirementResponse
{
  @Expose()
  @Type(() =>
    IntersectionType(
      PartialType(AdminRctAssetRetirementResponseDTO),
      PartialType(AdminVintageAssetRetirementResponseDTO),
    ),
  )
  @ApiProperty({
    type: () => [AdminRctAssetRetirementResponseDTO || AdminVintageAssetRetirementResponseDTO],
    readOnly: true,
  })
  assets!: (AdminRctAssetRetirementResponseDTO | AdminVintageAssetRetirementResponseDTO)[];

  @Expose()
  @ApiProperty({ type: Number, format: 'integer' })
  docsCount!: number;

  @Expose()
  @Type(() => TrimmedOrganizationResponseDTO)
  @ApiProperty({
    type: () => TrimmedOrganizationResponseDTO,
    readOnly: true,
  })
  organization!: TrimmedOrganizationResponseDTO;

  @Expose()
  @Type(() => TrimmedUserResponseDTO)
  @ApiPropertyOptional({ type: () => TrimmedUserResponseDTO, readOnly: true, nullable: true })
  requestedBy?: TrimmedUserResponseDTO;
}

export class PortalRetirementRelationsQueryDTO implements PortalRetirementRelationsQuery {
  @Transform(({ value }) => value.toString().split(','))
  @IsIn(AllPortalRetirementRelations, { each: true })
  @IsArray()
  @IsOptional()
  @ApiProperty({
    type: [String],
    description: 'relations to include',
    example: RetirementRelations.ASSETS,
  })
  includeRelations: PortalRetirementRelations[] = [];
}

export class AdminRetirementRelationsQueryDTO implements AdminRetirementRelationsQuery {
  @Transform(({ value }) => value.toString().split(','))
  @IsEnum(RetirementRelations, { each: true })
  @IsArray()
  @IsOptional()
  @ApiProperty({
    enum: RetirementRelations,
    description: 'relations to include',
    example: RetirementRelations.ASSETS,
  })
  includeRelations: RetirementRelations[] = [];
}

export class AdminRetirementQueryDTO
  extends IntersectionType(BaseQueryDTO, AdminRetirementRelationsQueryDTO)
  implements AdminRetirementQuery
{
  @IsUUID()
  @IsOptional()
  @ApiPropertyOptional({ type: String, format: 'uuid', nullable: true })
  assetId?: uuid;

  @IsUUID()
  @IsOptional()
  @ApiPropertyOptional({
    type: String,
    format: 'uuid',
    nullable: true,
  })
  organizationId?: uuid;

  @Transform(({ value }) => value.toString().split(','))
  @IsEnum(RetirementStatus, { each: true })
  @IsArray()
  @IsOptional()
  @ApiPropertyOptional({ type: Array, enum: RetirementStatus, nullable: true })
  statuses?: RetirementStatus[];

  @Transform(({ value }) => value.toString().split(','))
  @IsEnum(RetirementType, { each: true })
  @IsArray()
  @IsOptional()
  @ApiPropertyOptional({ enum: RetirementType, nullable: true })
  types?: RetirementType[];

  @Transform(({ value }) => {
    if (!value || value.length === 0) {
      return;
    }
    const orderBys = value.toString().split(',');
    const errors: string[] = [];
    const mappings: BaseRetirementOrderByDTO[] = orderBys.map((ob: string) => {
      const split = ob.split(':');
      return new BaseRetirementOrderByDTO(split[0], split[1]);
    });
    if (errors.length > 0) {
      throw new BadRequestException(errors);
    }
    return mappings;
  })
  @ValidateNested({ each: true })
  @Type(() => BaseRetirementOrderByDTO)
  @IsArray()
  @IsOptional()
  @ApiPropertyOptional({
    type: [String],
    format: '[RetirementOrderByOptions:OrderByDirection]',
    nullable: true,
    default: [`${RetirementOrderByOptions.DATE_STARTED}:${OrderByDirection.ASC}`],
  })
  orderBys: BaseRetirementOrderByDTO[] = [
    new BaseRetirementOrderByDTO(RetirementOrderByOptions.DATE_STARTED, OrderByDirection.ASC_NULLS_LAST),
  ];
}

export class AdminRetirementQueryResponseDTO extends BaseQueryResponseDTO implements AdminRetirementQueryResponse {
  @Expose()
  @Type(() => AdminRetirementResponseDTO)
  @ApiProperty({ type: () => [AdminRetirementResponseDTO], readOnly: true })
  data: AdminRetirementResponseDTO[] = [];
}

/* transformations */

function toAdminRctAssetRetirementResponseDTO(
  rctFlow: AssetFlow,
  vintageFlows?: AssetFlow[],
): AdminRctAssetRetirementResponseDTO {
  return {
    ...rctFlow,
    asset: toAdminAssetResponseDTO(rctFlow.asset),
    associatedVintages: vintageFlows ? vintageFlows.map((m) => toAdminVintageAssetRetirementResponseDTO(m)) : undefined,
    source: toTrimmedBookResponseDTO(rctFlow.source),
    totalPrice: new Decimal(rctFlow.rawPrice).plus(rctFlow.otherFee || 0).plus(rctFlow.serviceFee || 0),
  };
}

export function toAdminVintageAssetRetirementResponseDTO(
  vintageFlow: AssetFlow,
): AdminVintageAssetRetirementResponseDTO {
  if (!vintageFlow.detailedAsset) {
    throw new InternalServerErrorException(
      `projectVintage must be returned in response (find better way later after cleanup)`,
    );
  }
  const x = {
    ...vintageFlow,
    asset: toAdminAssetResponseDTO(vintageFlow.asset),
    source: toTrimmedBookResponseDTO(vintageFlow.source),
    projectVintage: toAdminProjectVintageResponseDTO(vintageFlow.detailedAsset as ProjectVintage),
    links: vintageFlow.links.isInitialized()
      ? vintageFlow.links.getItems().map((m) => {
          return { ...m };
        })
      : undefined,
    totalPrice: new Decimal(vintageFlow.rawPrice).minus(vintageFlow.otherFee || 0).minus(vintageFlow.serviceFee || 0),
  };
  return x;
}

export function toAdminRetirementResponseDTO(
  retirement: Retirement,
  includedRelations: RetirementRelations[] = [],
): AdminRetirementResponseDTO {
  if (includedRelations.includes(RetirementRelations.ASSETS)) {
    if (!(retirement.assetFlows?.length > 0)) {
      throw new InternalServerErrorException(`retirement.assetFlows must be included in response`);
    }
    if (
      includedRelations.includes(RetirementRelations.LINKS) &&
      retirement.assetFlows.some((s) => !s.links.isInitialized())
    ) {
      throw new InternalServerErrorException(`retirement.assetFlows.links must be included in response`);
    }
  }
  if (
    includedRelations.includes(RetirementRelations.CUSTOMER_PORTFOLIO) &&
    !retirement.customerPortfolio.organization.name
  ) {
    throw new InternalServerErrorException(`retirement.customerPortfolio.organization must be included in response`);
  }

  const organization: TrimmedOrganizationResponseDTO | undefined = includedRelations.includes(
    RetirementRelations.CUSTOMER_PORTFOLIO,
  )
    ? toTrimmedOrganizationResponseDTO(retirement.customerPortfolio.organization)
    : undefined;

  const assetResponsesDTO: (AdminRctAssetRetirementResponseDTO | AdminVintageAssetRetirementResponseDTO)[] | undefined =
    includedRelations.includes(RetirementRelations.ASSETS)
      ? retirement.assetFlows
          .filter(
            (f) =>
              (f.source.id === retirement.customerPortfolio.id ||
                f.destination.id === retirement.customerPortfolio.id) &&
              [AssetType.RCT, AssetType.REGISTRY_VINTAGE].includes(f.assetType), // todo (RBC-3166) / add logic for rrt retirements
          )
          .map((flow) => {
            // if the flow assetType is RCT, see if we need to also return the vintages retired for the rct
            if (flow.assetType === AssetType.RCT) {
              const rctVintageFlows: AssetFlow[] | undefined =
                retirement.type === RetirementType.RETIREMENT && // only retirements should have rcts
                includedRelations.includes(RetirementRelations.RCT_VINTAGES)
                  ? retirement.assetFlows.filter(
                      (assetFlow) =>
                        // get the asset flows where the source of the asset flow is from the rct (source book = rct asset)
                        // and where the asset type is registry vintage
                        // these are the asset flows (aka vintage assets) that were retired as part of the rct
                        assetFlow.source.id === flow.asset.id && assetFlow.assetType === AssetType.REGISTRY_VINTAGE,
                    )
                  : undefined;
              return toAdminRctAssetRetirementResponseDTO(flow, rctVintageFlows);
            } else {
              return toAdminVintageAssetRetirementResponseDTO(flow);
            }
          })
      : undefined;

  const requestedByUserDTO: TrimmedUserResponseDTO | undefined =
    includedRelations.includes(RetirementRelations.REQUESTED_BY) && retirement.requestedBy
      ? retirement.requestedBy
      : undefined;

  const response = Object.assign(new AdminRetirementResponseDTO(), {
    ...retirement.transactionDetails,
    ...retirement,
    organization,
    assets: assetResponsesDTO,
    requestedBy: requestedByUserDTO,
  });
  return response;
}

export function toPortalRetirementResponseDTO(
  retirement: Retirement,
  includedRelations: RetirementRelations[] = [],
): PortalRetirementResponseDTO {
  if (includedRelations.includes(RetirementRelations.ASSETS)) {
    if (!(retirement.assetFlows?.length > 0)) {
      throw new InternalServerErrorException(`retirement.assetFlows must be included in response`);
    }
    if (
      includedRelations.includes(RetirementRelations.LINKS) &&
      retirement.assetFlows.some((s) => !s.links.isInitialized())
    ) {
      throw new InternalServerErrorException(`retirement.assetFlows.links must be included in response`);
    }
  }
  if (
    includedRelations.includes(RetirementRelations.CUSTOMER_PORTFOLIO) &&
    !retirement.customerPortfolio.organization.name
  ) {
    throw new InternalServerErrorException(`retirement.customerPortfolio.organization must be included in response`);
  }

  const organization: TrimmedOrganizationResponseDTO | undefined = includedRelations.includes(
    RetirementRelations.CUSTOMER_PORTFOLIO,
  )
    ? toTrimmedOrganizationResponseDTO(retirement.customerPortfolio.organization)
    : undefined;

  const assetResponsesDTO:
    | (PortalRctAssetRetirementResponseDTO | PortalVintageAssetRetirementResponseDTO)[]
    | undefined = includedRelations.includes(RetirementRelations.ASSETS)
    ? retirement.assetFlows
        .filter(
          (f) =>
            (f.source.id === retirement.customerPortfolio.id || f.destination.id === retirement.customerPortfolio.id) &&
            [AssetType.RCT, AssetType.REGISTRY_VINTAGE].includes(f.assetType), // todo (RBC-3166) / add logic for rrt retirements
        )
        .map((flow) => {
          // if the flow assetType is RCT, see if we need to also return the vintages retired for the rct
          if (flow.assetType === AssetType.RCT) {
            const rctVintageFlows: AssetFlow[] | undefined =
              retirement.type === RetirementType.RETIREMENT && // only retirements should have rcts
              includedRelations.includes(RetirementRelations.RCT_VINTAGES)
                ? retirement.assetFlows.filter(
                    (assetFlow) =>
                      // get the asset flows where the source of the asset flow is from the rct (source book = rct asset)
                      // and where the asset type is registry vintage
                      // these are the asset flows (aka vintage assets) that were retired as part of the rct
                      assetFlow.source.id === flow.asset.id && assetFlow.assetType === AssetType.REGISTRY_VINTAGE,
                  )
                : undefined;
            return toAdminRctAssetRetirementResponseDTO(flow, rctVintageFlows); // todo : see if this works
          } else {
            return toAdminVintageAssetRetirementResponseDTO(flow); // todo : see if this works
          }
        })
    : undefined;

  const requestedByUserDTO: TrimmedUserResponseDTO | undefined =
    includedRelations.includes(RetirementRelations.REQUESTED_BY) && retirement.requestedBy
      ? retirement.requestedBy
      : undefined;

  const response = Object.assign(new PortalRetirementResponseDTO(), {
    ...retirement.transactionDetails,
    ...retirement,
    organization,
    assets: assetResponsesDTO,
    requestedBy: requestedByUserDTO,
  });
  return response;
}
