/* third party */
import { ApiProperty, ApiPropertyOptional, IntersectionType, PartialType, PickType } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsEnum,
  IsIn,
  IsNumber,
  IsObject,
  IsOptional,
  IsUUID,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import Decimal from 'decimal.js';
/* rubicon */
import {
  AdminBookEstimateRetirementResponse,
  AdminBookQuery,
  AdminBookQueryResponse,
  AdminBookRelationsQuery,
  AdminBookResponse,
  AdminBookUpdateRequest,
  AdminRctCustomPortfolioCreateRequest,
  AdminRctPublicPortfolioCreateRequest,
  AdminRrtPortfolioCreateRequest,
  AdminRrtPortfolioResponse,
  AllAdminBookTypes,
  AllMobileBookRelations,
  AllMobileBookTypes,
  AllPortalBookRelations,
  AllPortalBookTypes,
  BaseBookLimit,
  BookAction,
  BookOrderByOption,
  BookRelations,
  BookType,
  GroupedAllocationRelations,
  MobileBookQuery,
  MobileBookQueryResponse,
  MobileBookRelations,
  MobileBookRelationsQuery,
  MobileBookResponse,
  PortalBookQuery,
  PortalBookQueryResponse,
  PortalBookRelations,
  PortalBookRelationsQuery,
  PortalBookResponse,
  uuid,
} from '@rubiconcarbon/shared-types';
/* app */
import { BaseQueryDTO, BaseQueryResponseDTO, BaseResponseDTO } from '@app/dtos/base.dto';
import { TrimmedBookResponseDTO, toTrimmedOrganizationResponseDTO } from '@app/dtos/trimmed.dto';
import { toTrimmedProjectResponseDTO } from '@app/dtos/project.dto';
import { toAdminProjectTypeResponseDTO } from '@app/dtos/project-types.dto';
import { TrimmedProjectVintageResponseDTO } from '@app/dtos/project-vintage.dto';
import { Book } from '@app/entities';
import { IsDecimalGreaterThanOrEqualTo, IsIntGreaterThanOrEqualTo } from '@app/validators';
import { TransformDateISO, TransformDecimal } from '@app/helpers/transforms.helper';
import { IsNonEmptyString } from '@app/helpers/validations.helper';
import { BadRequestException, InternalServerErrorException } from '@nestjs/common';
import {
  AdminAssetTypeGroupedAllocationResponseDTO,
  AdminBookTypeGroupedAllocationResponseDTO,
  AdminGroupedAllocationWithNestedResponseDTO,
  AdminProjectGroupedAllocationResponseDTO,
  AdminProjectTypeGroupedAllocationResponseDTO,
  MobileGroupedAllocationWithNestedResponseDTO,
  MobileProjectGroupedAllocationResponseDTO,
  PortalGroupedAllocationWithNestedResponseDTO,
  PortalProjectGroupedAllocationResponseDTO,
  toAdminGroupedAllocationResponseDTOFromGroupedAllocation,
  toPortalGroupedAllocationResponseDTOFromGroupedAllocation,
} from './allocation.dto';
import { InternalBookResponse, InternalRrtPortfolioResponse } from '@app/interfaces/book.interface';
import { AdminRrtVintageAssetResponseDTO } from './asset.dto';

export class BaseBookLimitDTO implements BaseBookLimit {
  @Expose()
  @IsIntGreaterThanOrEqualTo(0)
  @IsOptional()
  @ApiPropertyOptional({
    type: Number,
    format: 'integer',
    nullable: true,
    description: 'max amount that can be held in book',
  })
  holdingAmountMax?: number;

  @Expose()
  @IsIntGreaterThanOrEqualTo(0)
  @IsOptional()
  @ApiPropertyOptional({
    type: Number,
    format: 'integer',
    nullable: true,
    description: 'min amount that can held in book',
  })
  holdingAmountMin?: number;

  @Expose()
  @IsDecimalGreaterThanOrEqualTo(new Decimal(0))
  @IsOptional()
  @ApiPropertyOptional({
    type: Number,
    format: 'decimal',
    nullable: true,
    description: 'max price that can be held in book',
  })
  holdingPriceMax?: Decimal;

  @Expose()
  @IsDecimalGreaterThanOrEqualTo(new Decimal(0))
  @IsOptional()
  @ApiPropertyOptional({
    type: Number,
    format: 'decimal',
    nullable: true,
    description: 'min price that can be held in book',
  })
  holdingPriceMin?: Decimal;
}

export class AdminRctCustomPortfolioCreateRequestDTO
  extends PickType(Book, ['description', 'isEnabled', 'name'])
  implements AdminRctCustomPortfolioCreateRequest
{
  @Type(() => BaseBookLimitDTO)
  @ValidateNested()
  @IsObject()
  @IsOptional()
  @ApiPropertyOptional({ type: () => BaseBookLimitDTO, nullable: true, default: {} })
  limit: BaseBookLimitDTO = new BaseBookLimitDTO();

  @IsUUID()
  @IsOptional()
  @ApiPropertyOptional({ type: String, format: 'uuid' })
  organizationId?: uuid;

  @ValidateIf((o) => o.purchasePrice || !o.purchasePriceWithBuffer)
  @TransformDecimal()
  @IsDecimalGreaterThanOrEqualTo(new Decimal(0))
  @ApiPropertyOptional({ type: Number, format: 'decimal', nullable: true })
  purchasePrice?: Decimal;

  @ValidateIf((o) => o.purchasePriceWithBuffer || !o.purchasePrice)
  @TransformDecimal()
  @IsDecimalGreaterThanOrEqualTo(new Decimal(0))
  @ApiPropertyOptional({ type: Number, format: 'decimal', nullable: true })
  purchasePriceWithBuffer?: Decimal;
}

export class AdminRctPublicPortfolioCreateRequestDTO
  extends PickType(Book, ['description', 'isEnabled', 'name'])
  implements AdminRctPublicPortfolioCreateRequest
{
  @Type(() => BaseBookLimitDTO)
  @ValidateNested()
  @IsObject()
  @IsOptional()
  @ApiPropertyOptional({ type: () => BaseBookLimitDTO, nullable: true, default: {} })
  limit: BaseBookLimitDTO = new BaseBookLimitDTO();

  @ValidateIf((o) => o.purchasePrice || !o.purchasePriceWithBuffer)
  @TransformDecimal()
  @IsDecimalGreaterThanOrEqualTo(new Decimal(0))
  @ApiPropertyOptional({ type: Number, format: 'decimal', nullable: true })
  purchasePrice?: Decimal;

  @ValidateIf((o) => o.purchasePriceWithBuffer || !o.purchasePrice)
  @TransformDecimal()
  @IsDecimalGreaterThanOrEqualTo(new Decimal(0))
  @ApiPropertyOptional({ type: Number, format: 'decimal', nullable: true })
  purchasePriceWithBuffer?: Decimal;
}

export class AdminRrtPortfolioCreateRequestDTO
  extends PickType(Book, ['description', 'isEnabled', 'name'])
  implements AdminRrtPortfolioCreateRequest
{
  @TransformDecimal()
  @IsDecimalGreaterThanOrEqualTo(new Decimal(0))
  @ApiProperty({ type: Number, format: 'decimal' })
  purchasePrice!: Decimal;
}

export class AdminBookUpdateRequestDTO
  extends PartialType(PickType(Book, ['description', 'isEnabled', 'name']))
  implements AdminBookUpdateRequest
{
  @Type(() => BaseBookLimitDTO)
  @ValidateNested()
  @IsObject()
  @IsOptional()
  @ApiPropertyOptional({ type: () => BaseBookLimitDTO, nullable: true })
  limit?: BaseBookLimitDTO;

  @IsArray()
  @IsNumber(undefined, { each: true })
  @IsOptional()
  @ApiPropertyOptional({
    type: [Number],
    format: 'number[]',
  })
  projectTypeIds?: number[];

  @TransformDecimal()
  @IsDecimalGreaterThanOrEqualTo(new Decimal(0))
  @IsOptional()
  @ApiPropertyOptional({ type: Number, format: 'decimal', nullable: true })
  purchasePrice?: Decimal;

  @TransformDecimal()
  @IsDecimalGreaterThanOrEqualTo(new Decimal(0))
  @IsOptional()
  @ApiPropertyOptional({ type: Number, format: 'decimal', nullable: true })
  purchasePriceWithBuffer?: Decimal;
}

export class MobileBookResponseDTO
  extends IntersectionType(
    BaseResponseDTO,
    PickType(Book, ['description', 'isEnabled', 'name', 'purchasePrice', 'purchasePriceWithBuffer']),
  )
  implements MobileBookResponse
{
  // @Expose()
  // @Type(() => PortalGroupedAllocationWithNestedResponseDTO)
  // @ApiProperty({ type: () => PortalGroupedAllocationWithNestedResponseDTO, readOnly: true, nullable: true })
  // assetAllocations?: PortalGroupedAllocationWithNestedResponseDTO; // todo : see if needed

  @Expose()
  @Type(() => MobileGroupedAllocationWithNestedResponseDTO)
  @ApiPropertyOptional({ type: () => MobileGroupedAllocationWithNestedResponseDTO, readOnly: true, nullable: true })
  ownerAllocations?: MobileGroupedAllocationWithNestedResponseDTO;

  @Expose()
  @Type(() => MobileProjectGroupedAllocationResponseDTO)
  @ApiProperty({ type: () => [MobileProjectGroupedAllocationResponseDTO], readOnly: true, nullable: true })
  ownerAllocationsByProject?: MobileProjectGroupedAllocationResponseDTO[];

  @Expose()
  @ApiProperty({ enum: BookType, readOnly: true, example: BookType.PORTFOLIO_DEFAULT })
  type!: BookType;
}

export class PortalBookResponseDTO
  extends IntersectionType(
    BaseResponseDTO,
    PickType(Book, ['description', 'isEnabled', 'name', 'purchasePrice', 'purchasePriceWithBuffer']),
  )
  implements PortalBookResponse
{
  @Expose()
  @Type(() => PortalGroupedAllocationWithNestedResponseDTO)
  @ApiProperty({ type: () => PortalGroupedAllocationWithNestedResponseDTO, readOnly: true, nullable: true })
  assetAllocations?: PortalGroupedAllocationWithNestedResponseDTO; // todo : see if needed

  @Expose()
  @Type(() => PortalGroupedAllocationWithNestedResponseDTO)
  @ApiPropertyOptional({ type: () => PortalGroupedAllocationWithNestedResponseDTO, readOnly: true, nullable: true })
  ownerAllocations?: PortalGroupedAllocationWithNestedResponseDTO;

  @Expose()
  @Type(() => PortalProjectGroupedAllocationResponseDTO)
  @ApiProperty({ type: () => [PortalProjectGroupedAllocationResponseDTO], readOnly: true, nullable: true })
  ownerAllocationsByProject?: PortalProjectGroupedAllocationResponseDTO[];

  @Expose()
  @ApiProperty({ enum: BookType, readOnly: true, example: BookType.PORTFOLIO_DEFAULT })
  type!: BookType;
}

export class AdminBookResponseDTO
  extends IntersectionType(
    TrimmedBookResponseDTO,
    PickType(Book, ['limit', 'priceUpdatedAt', 'purchasePrice', 'purchasePriceWithBuffer']),
  )
  implements AdminBookResponse
{
  @Expose()
  @Type(() => AdminGroupedAllocationWithNestedResponseDTO)
  @ApiProperty({ type: () => AdminGroupedAllocationWithNestedResponseDTO, readOnly: true, nullable: true })
  assetAllocations?: AdminGroupedAllocationWithNestedResponseDTO;

  @Expose()
  @Type(() => AdminBookTypeGroupedAllocationResponseDTO)
  @ApiProperty({ type: () => [AdminBookTypeGroupedAllocationResponseDTO], readOnly: true, nullable: true })
  assetAllocationsByBookType?: AdminBookTypeGroupedAllocationResponseDTO[];

  @Expose()
  @Type(() => AdminGroupedAllocationWithNestedResponseDTO)
  @ApiProperty({ type: () => AdminGroupedAllocationWithNestedResponseDTO, readOnly: true, nullable: true })
  ownerAllocations?: AdminGroupedAllocationWithNestedResponseDTO;

  @Expose()
  @Type(() => AdminAssetTypeGroupedAllocationResponseDTO)
  @ApiProperty({ type: () => [AdminAssetTypeGroupedAllocationResponseDTO], readOnly: true, nullable: true })
  ownerAllocationsByAssetType?: AdminAssetTypeGroupedAllocationResponseDTO[];

  @Expose()
  @Type(() => AdminProjectGroupedAllocationResponseDTO)
  @ApiProperty({ type: () => [AdminProjectGroupedAllocationResponseDTO], readOnly: true, nullable: true })
  ownerAllocationsByProject?: AdminProjectGroupedAllocationResponseDTO[];

  @Expose()
  @Type(() => AdminProjectTypeGroupedAllocationResponseDTO)
  @ApiProperty({ type: () => [AdminProjectTypeGroupedAllocationResponseDTO], readOnly: true, nullable: true })
  ownerAllocationsByProjectType?: AdminProjectTypeGroupedAllocationResponseDTO[];

  @Expose()
  @TransformDecimal()
  @ApiPropertyOptional({ type: Number, format: 'decimal', nullable: true })
  portfolioScore?: Decimal;
}

export class AdminRrtPortfolioResponseDTO
  extends IntersectionType(BaseResponseDTO, PickType(Book, ['description', 'isEnabled', 'name']))
  implements AdminRrtPortfolioResponse
{
  @Expose()
  @Type(() => AdminRrtVintageAssetResponseDTO)
  @ApiProperty({ type: () => AdminRrtVintageAssetResponseDTO, readOnly: true, nullable: true })
  assets!: AdminRrtVintageAssetResponseDTO[];

  @Expose()
  @TransformDateISO()
  @ApiProperty({ type: Date, format: 'iso', readOnly: true })
  priceUpdatedAt!: Date;

  @Expose()
  @TransformDecimal()
  @ApiPropertyOptional({ type: Number, format: 'decimal' })
  purchasePrice!: Decimal;
}

export class AdminBookEstimateRetirementResponseDTO implements AdminBookEstimateRetirementResponse {
  @Expose()
  @ApiProperty({ type: 'integer', readOnly: true })
  amountTransacted!: number;

  @Expose()
  @Type(() => TrimmedProjectVintageResponseDTO)
  @ApiProperty({ type: () => TrimmedProjectVintageResponseDTO, readOnly: true })
  projectVintage!: TrimmedProjectVintageResponseDTO;
}

export class MobileBookRelationsQueryDTO implements MobileBookRelationsQuery {
  @Transform(({ value }) => value.toString().split(','))
  @IsEnum(BookRelations, { each: true })
  @IsIn(AllMobileBookRelations, { each: true })
  @IsArray()
  @IsOptional()
  @ApiProperty({
    enum: BookRelations,
    description: 'relations to include',
    example: [BookRelations.OWNER_ALLOCATIONS],
  })
  includeRelations: MobileBookRelations[] = [];
}

export class PortalBookRelationsQueryDTO implements PortalBookRelationsQuery {
  @Transform(({ value }) => value.toString().split(','))
  @IsEnum(BookRelations, { each: true })
  @IsIn(AllPortalBookRelations, { each: true })
  @IsArray()
  @IsOptional()
  @ApiProperty({
    enum: BookRelations,
    description: 'relations to include',
    example: [BookRelations.OWNER_ALLOCATIONS],
  })
  includeRelations: PortalBookRelations[] = [];
}

export class AdminBookRelationsQueryDTO implements AdminBookRelationsQuery {
  @Transform(({ value }) => value.toString().split(','))
  @IsEnum(BookRelations, { each: true })
  @IsArray()
  @IsOptional()
  @ApiProperty({
    enum: BookRelations,
    description: 'relations to include',
    example: [BookRelations.OWNER_ALLOCATIONS],
  })
  includeRelations: BookRelations[] = [];
}

export class MobileBookQueryDTO
  extends IntersectionType(BaseQueryDTO, MobileBookRelationsQueryDTO)
  implements MobileBookQuery
{
  @IsArray()
  @ArrayMinSize(1)
  @IsUUID(undefined, { each: true })
  @IsOptional()
  @ApiPropertyOptional({
    type: [String],
    format: 'uuid[]',
  })
  ids?: uuid[];

  @IsNonEmptyString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, example: 'Book Name' })
  name?: string;

  @Transform(({ value }) => value.toString().split(','))
  @IsEnum(BookType, { each: true })
  @IsIn(AllMobileBookTypes, { each: true })
  @IsOptional()
  @ApiProperty({ type: [BookType], enum: BookType, example: BookType.RCT_PUBLIC })
  types: BookType[] = AllMobileBookTypes;

  @IsEnum(BookOrderByOption)
  @IsOptional()
  @ApiProperty({ enum: BookOrderByOption, default: BookOrderByOption.NAME })
  orderBy: BookOrderByOption = BookOrderByOption.NAME;
}

export class PortalBookQueryDTO
  extends IntersectionType(BaseQueryDTO, PortalBookRelationsQueryDTO)
  implements PortalBookQuery
{
  @IsArray()
  @ArrayMinSize(1)
  @IsUUID(undefined, { each: true })
  @IsOptional()
  @ApiPropertyOptional({
    type: [String],
    format: 'uuid[]',
  })
  ids?: uuid[];

  @IsNonEmptyString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, example: 'Book Name' })
  name?: string;

  @Transform(({ value }) => value.toString().split(','))
  @IsEnum(BookType, { each: true })
  @IsIn(AllPortalBookTypes, { each: true })
  @IsOptional()
  @ApiProperty({ type: [BookType], enum: BookType, example: BookType.RCT_PUBLIC })
  types: BookType[] = AllPortalBookTypes;

  @IsEnum(BookOrderByOption)
  @IsOptional()
  @ApiProperty({ enum: BookOrderByOption, default: BookOrderByOption.NAME })
  orderBy: BookOrderByOption = BookOrderByOption.NAME;
}

export class AdminBookQueryDTO
  extends IntersectionType(BaseQueryDTO, AdminBookRelationsQueryDTO)
  implements AdminBookQuery
{
  @Transform(({ value }) => value.toString().split(','))
  @IsArray()
  @ArrayMinSize(1)
  @IsEnum(BookAction, { each: true })
  @IsOptional()
  @ApiProperty({ type: [BookAction], enum: BookAction, example: [BookAction.BUY] })
  allowedActions?: BookAction[];

  @Transform(({ value }) => {
    try {
      return value.toString().split(',').map(uuid);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {
      throw new BadRequestException('assetIds must be a uuid array');
    }
  })
  @IsArray()
  @ArrayMinSize(1)
  @IsUUID(undefined, { each: true })
  @IsOptional()
  @ApiPropertyOptional({
    type: [String],
    format: 'uuid[]',
  })
  ids?: uuid[];

  @Transform(({ value }) => value.toLowerCase() === 'true')
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean, nullable: true })
  isEnabled?: boolean;

  @IsNonEmptyString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, example: 'Book Name' })
  name?: string;

  @IsUUID()
  @IsOptional()
  @ApiPropertyOptional({
    type: String,
    format: 'uuid',
  })
  organizationId?: uuid;

  @Transform(({ value }) => value.toString().split(','))
  @IsArray()
  @ArrayMinSize(1)
  @IsIn(AllAdminBookTypes, { each: true })
  @IsEnum(BookType, { each: true })
  @IsOptional()
  @ApiProperty({ type: [BookType], enum: BookType, example: [BookType.COMPLIANCE_DEFAULT] })
  types: BookType[] = AllAdminBookTypes;

  @IsEnum(BookOrderByOption)
  @IsOptional()
  @ApiProperty({ enum: BookOrderByOption, default: BookOrderByOption.NAME })
  orderBy: BookOrderByOption = BookOrderByOption.NAME;
}

export class AdminBookQueryResponseDTO extends BaseQueryResponseDTO implements AdminBookQueryResponse {
  @Expose()
  @Type(() => AdminBookResponseDTO)
  @ApiProperty({ type: () => [AdminBookResponseDTO], readOnly: true })
  data: AdminBookResponseDTO[] = [];
}

export class PortalBookQueryResponseDTO extends BaseQueryResponseDTO implements PortalBookQueryResponse {
  @Expose()
  @Type(() => PortalBookResponseDTO)
  @ApiProperty({ type: () => [PortalBookResponseDTO], readOnly: true })
  data: PortalBookResponseDTO[] = [];
}

export class MobileBookQueryResponseDTO extends BaseQueryResponseDTO implements MobileBookQueryResponse {
  @Expose()
  @Type(() => MobileBookResponseDTO)
  @ApiProperty({ type: () => [MobileBookResponseDTO], readOnly: true })
  data: MobileBookResponseDTO[] = [];
}

/* transforms */

export function toAdminBookResponseDTO(
  book: InternalBookResponse,
  includeRelations: BookRelations[] = [],
): AdminBookResponseDTO {
  if (includeRelations.includes(BookRelations.ASSET_ALLOCATIONS) && !book.assetAllocations) {
    throw new InternalServerErrorException('book.assetAllocations must be included for relation');
  }
  if (includeRelations.includes(BookRelations.ASSET_ALLOCATIONS_NESTED) && !book.assetAllocations?.allocations) {
    throw new InternalServerErrorException('book.assetAllocations.allocations must be included for relation');
  }
  if (includeRelations.includes(BookRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE) && !book.assetAllocationsByBookType) {
    throw new InternalServerErrorException('book.assetAllocationsByBookType must be included for relation');
  }
  if (includeRelations.includes(BookRelations.OWNER_ALLOCATIONS) && !book.ownerAllocations) {
    throw new InternalServerErrorException('book.ownerAllocations must be included for relation');
  }
  if (includeRelations.includes(BookRelations.OWNER_ALLOCATIONS_NESTED) && !book.ownerAllocations?.allocations) {
    throw new InternalServerErrorException('book.ownerAllocations.allocations must be included for relation');
  }
  if (includeRelations.includes(BookRelations.OWNER_ALLOCATIONS_BY_ASSET_TYPE) && !book.ownerAllocationsByAssetType) {
    throw new InternalServerErrorException('book.ownerAllocationsByAssetType must be included for relation');
  }
  if (includeRelations.includes(BookRelations.OWNER_ALLOCATIONS_BY_PROJECT) && !book.ownerAllocationsByProject) {
    throw new InternalServerErrorException('book.ownerAllocationsByProject must be included for relation');
  }
  if (
    includeRelations.includes(BookRelations.OWNER_ALLOCATIONS_BY_PROJECT_TYPE) &&
    !book.ownerAllocationsByProjectType
  ) {
    throw new InternalServerErrorException('book.ownerAllocationsByProjectType must be included for relation');
  }

  const ownerGroupingRelations: GroupedAllocationRelations[] = [];
  const assetGroupingRelations: GroupedAllocationRelations[] = [];
  if (includeRelations.includes(BookRelations.OWNER_ALLOCATIONS_NESTED)) {
    ownerGroupingRelations.push(GroupedAllocationRelations.ALLOCATIONS);
  }
  if (includeRelations.includes(BookRelations.ASSET_ALLOCATIONS_NESTED)) {
    assetGroupingRelations.push(GroupedAllocationRelations.ALLOCATIONS);
  }
  if (includeRelations.includes(BookRelations.PRICES)) {
    ownerGroupingRelations.push(GroupedAllocationRelations.PRICES);
    assetGroupingRelations.push(GroupedAllocationRelations.PRICES);
  }

  let portfolioScore: Decimal | undefined = undefined;
  if (book.ownerAllocationsByProject) {
    const projectScores: (number | undefined)[] = book.ownerAllocationsByProject.map(
      (m) => m.project.integrityGradeScore,
    );
    const totalAllocation =
      book.ownerAllocations?.totalAmountAllocated ||
      Decimal.sum(...book.ownerAllocationsByProject.map((m) => m.totalAmountAllocated), 0).toNumber();
    if (totalAllocation && projectScores.every((e) => e != undefined)) {
      portfolioScore = Decimal.sum(
        ...book.ownerAllocationsByProject.map((m) =>
          new Decimal(m.totalAmountAllocated).mul(m.project.integrityGradeScore || 0),
        ),
        0,
      ).div(totalAllocation);
    }
  }

  const response: AdminBookResponseDTO = Object.assign(new AdminBookResponseDTO(), {
    ...book,
    assetAllocations:
      includeRelations.includes(BookRelations.ASSET_ALLOCATIONS) ||
      includeRelations.includes(BookRelations.ASSET_ALLOCATIONS_NESTED)
        ? toAdminGroupedAllocationResponseDTOFromGroupedAllocation(book.assetAllocations!, assetGroupingRelations)
        : undefined,
    assetAllocationsByBookType: includeRelations.includes(BookRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE)
      ? book.assetAllocationsByBookType?.map((m) => {
          return {
            ...toAdminGroupedAllocationResponseDTOFromGroupedAllocation(
              m,
              assetGroupingRelations.filter((f) => f !== GroupedAllocationRelations.ALLOCATIONS),
            ),
            bookType: m.bookType,
          };
        })
      : undefined,
    ownerAllocations:
      includeRelations.includes(BookRelations.OWNER_ALLOCATIONS) ||
      includeRelations.includes(BookRelations.OWNER_ALLOCATIONS_NESTED)
        ? toAdminGroupedAllocationResponseDTOFromGroupedAllocation(book.ownerAllocations!, ownerGroupingRelations)
        : undefined,
    ownerAllocationsByAssetType: includeRelations.includes(BookRelations.OWNER_ALLOCATIONS_BY_ASSET_TYPE)
      ? book.ownerAllocationsByAssetType?.map((m) => {
          return {
            ...toAdminGroupedAllocationResponseDTOFromGroupedAllocation(
              m,
              ownerGroupingRelations.filter((f) => f !== GroupedAllocationRelations.ALLOCATIONS),
            ),
            assetType: m.assetType,
          };
        })
      : undefined,
    ownerAllocationsByProject: includeRelations.includes(BookRelations.OWNER_ALLOCATIONS_BY_PROJECT)
      ? book.ownerAllocationsByProject?.map((m) => {
          return {
            ...toAdminGroupedAllocationResponseDTOFromGroupedAllocation(
              m,
              ownerGroupingRelations.filter((f) => f !== GroupedAllocationRelations.ALLOCATIONS),
            ),
            project: toTrimmedProjectResponseDTO(m.project),
          };
        })
      : undefined,
    ownerAllocationsByProjectType: includeRelations.includes(BookRelations.OWNER_ALLOCATIONS_BY_PROJECT_TYPE)
      ? book.ownerAllocationsByProjectType?.map((m) => {
          return {
            ...toAdminGroupedAllocationResponseDTOFromGroupedAllocation(
              m,
              ownerGroupingRelations.filter((f) => f !== GroupedAllocationRelations.ALLOCATIONS),
            ),
            project: toAdminProjectTypeResponseDTO(m.projectType),
          };
        })
      : undefined,
    organization:
      includeRelations.includes(BookRelations.ORGANIZATION) && book.organization
        ? toTrimmedOrganizationResponseDTO(book.organization)
        : undefined,
    portfolioScore,
  });

  return response;
}

export function toPortalBookResponseDTO(
  book: InternalBookResponse,
  includeRelations: BookRelations[] = [],
): PortalBookResponseDTO {
  if (includeRelations.includes(BookRelations.OWNER_ALLOCATIONS) && !book.ownerAllocations) {
    throw new InternalServerErrorException('book.ownerAllocations must be included for relation');
  }
  if (includeRelations.includes(BookRelations.OWNER_ALLOCATIONS_NESTED) && !book.ownerAllocations?.allocations) {
    throw new InternalServerErrorException('book.ownerAllocations.allocations must be included for relation');
  }
  if (includeRelations.includes(BookRelations.OWNER_ALLOCATIONS_BY_PROJECT) && !book.ownerAllocationsByProject) {
    throw new InternalServerErrorException('book.ownerAllocationsByProject must be included for relation');
  }

  const ownerGroupingRelations: GroupedAllocationRelations[] = [];
  const assetGroupingRelations: GroupedAllocationRelations[] = [];
  if (includeRelations.includes(BookRelations.OWNER_ALLOCATIONS_NESTED)) {
    ownerGroupingRelations.push(GroupedAllocationRelations.ALLOCATIONS);
  }
  if (includeRelations.includes(BookRelations.ASSET_ALLOCATIONS_NESTED)) {
    assetGroupingRelations.push(GroupedAllocationRelations.ALLOCATIONS);
  }
  return Object.assign(new PortalBookResponseDTO(), {
    ...book,
    ownerAllocations:
      includeRelations.includes(BookRelations.OWNER_ALLOCATIONS) ||
      includeRelations.includes(BookRelations.OWNER_ALLOCATIONS_NESTED)
        ? toPortalGroupedAllocationResponseDTOFromGroupedAllocation(book.ownerAllocations!, ownerGroupingRelations)
        : undefined,
    ownerAllocationsByProject: includeRelations.includes(BookRelations.OWNER_ALLOCATIONS_BY_PROJECT)
      ? book.ownerAllocationsByProject?.map((m) => {
          return {
            ...toPortalGroupedAllocationResponseDTOFromGroupedAllocation(m),
            project: toTrimmedProjectResponseDTO(m.project),
          };
        })
      : undefined,
  });
}

export function toMobileBookResponseDTO(
  book: InternalBookResponse,
  includeRelations: BookRelations[] = [],
): MobileBookResponseDTO {
  const obj = Object.assign(new MobileBookResponseDTO(), toPortalBookResponseDTO(book, includeRelations));
  return obj;
}

export function toAdminRrtPortfolioResponseDTO(rrt: InternalRrtPortfolioResponse): AdminRrtPortfolioResponseDTO {
  return Object.assign(new AdminRrtPortfolioResponseDTO(), {
    ...rrt,
    assets: rrt.assets.map((m) => {
      return { ...m, netQuantity: m.currentNetQuantity, portfolio: m.rrt };
    }),
  });
}
