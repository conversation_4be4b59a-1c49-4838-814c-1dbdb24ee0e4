import { PermissionEnum } from '@rubiconcarbon/shared-types';

export type Resource =
  | 'actions'
  | 'alerts'
  | 'books'
  | 'chatbot'
  | 'customer-sales'
  | 'data-management'
  | 'documents'
  | 'feature-flags'
  | 'forwards'
  | 'ledger'
  | 'market-news'
  | 'marketing-agreements'
  | 'model-portfolios'
  | 'notifications'
  | 'organizations-tab' // todo : remove tab
  | 'privileges'
  | 'project-types'
  | 'projects-tab' // todo : remove tab
  | 'quotes-tab'
  | 'registries'
  | 'reporting'
  | 'reserves'
  | 'retirements-tab' // todo : remove tab
  | 'trades'
  | 'transactions'
  | 'transfers'
  | 'users-tab'
  | 'user-actions'
  | 'vintages-tab'; // todo : remove tab;

export class PermitAction {
  constructor(
    public readonly action: string,
    public readonly resource: Resource,
  ) {}
}

export const permissionsMapping: { [key in PermissionEnum]: PermitAction } = {
  login: new PermitAction('login', 'actions'),
  verified: new PermitAction('verified', 'privileges'),
  admin: new PermitAction('admin', 'privileges'),
  'alerts:create-portfolio-policy-violation-entry': new PermitAction(
    'create-portfolio-policy-violation-entry',
    'alerts',
  ),
  'alerts:create-portfolio-stale-price-entry': new PermitAction('create-portfolio-stale-price-entry', 'alerts'),
  'alerts:subscribe-portfolio-alerts': new PermitAction('subscribe-portfolio-alerts', 'alerts'),
  'books:calculate-retirement': new PermitAction('calculate-retirement', 'books'),
  'books:create-rct-portfolios': new PermitAction('create-rct-portfolios', 'books'),
  'books:create-rrt-portfolios': new PermitAction('create-rrt-portfolios', 'books'),
  'books:read': new PermitAction('read', 'books'),
  'books:update': new PermitAction('update', 'books'),
  'books:update-price': new PermitAction('update-price', 'books'),
  'books:update-project-types': new PermitAction('update-project-types', 'books'),
  'books:update-rrt-composition': new PermitAction('update-rrt-composition', 'books'),
  'chatbot:inspect': new PermitAction('inspect', 'chatbot'),
  'chatbot:pricing': new PermitAction('pricing', 'chatbot'),
  'chatbot:products': new PermitAction('products', 'chatbot'),
  'chatbot:usage': new PermitAction('usage', 'chatbot'),
  'chatbot:vintages': new PermitAction('vintages', 'chatbot'),
  'customer-sales:cancel': new PermitAction('cancel', 'customer-sales'),
  'customer-sales:create': new PermitAction('create', 'customer-sales'),
  'customer-sales:read': new PermitAction('read', 'customer-sales'),
  'customer-sales:set-binding': new PermitAction('set-binding', 'customer-sales'),
  'customer-sales:set-delivered': new PermitAction('set-delivered', 'customer-sales'),
  'customer-sales:set-executed': new PermitAction('set-executed', 'customer-sales'),
  'customer-sales:update-payment': new PermitAction('update-payment', 'customer-sales'),
  'data-management:read-science': new PermitAction('read-science', 'data-management'),
  'data-management:read-trading': new PermitAction('read-trading', 'data-management'),
  'data-management:write-science': new PermitAction('write-science', 'data-management'),
  'data-management:write-trading': new PermitAction('write-trading', 'data-management'),
  'documents:create': new PermitAction('create', 'documents'),
  'documents:delete': new PermitAction('delete', 'documents'),
  'documents:read': new PermitAction('read', 'documents'),
  'documents:update': new PermitAction('update', 'documents'),
  'feature-flags:write': new PermitAction('write', 'feature-flags'),
  'forwards:cancel': new PermitAction('cancel', 'forwards'),
  'forwards:create': new PermitAction('create', 'forwards'),
  'forwards:read': new PermitAction('read', 'forwards'),
  'forwards:update': new PermitAction('update', 'forwards'),
  'forwards:cancel-line-items': new PermitAction('cancel-line-items', 'forwards'),
  'forwards:create-line-items': new PermitAction('create-line-items', 'forwards'),
  'forwards:settle-line-items': new PermitAction('settle-line-items', 'forwards'),
  'forwards:update-line-items': new PermitAction('update-line-items', 'forwards'),
  'ledger:direct-access': new PermitAction('direct-access', 'ledger'),
  'ledger:sync': new PermitAction('sync', 'ledger'),
  'market-news:delete': new PermitAction('delete', 'market-news'),
  'market-news:create': new PermitAction('create', 'market-news'),
  'market-news:tag': new PermitAction('tag', 'market-news'),
  'market-news:update': new PermitAction('update', 'market-news'),
  'marketing-agreements:cancel': new PermitAction('cancel', 'marketing-agreements'),
  'marketing-agreements:create': new PermitAction('create', 'marketing-agreements'),
  'marketing-agreements:read': new PermitAction('read', 'marketing-agreements'),
  'marketing-agreements:update': new PermitAction('update', 'marketing-agreements'),
  'marketing-agreements:cancel-line-items': new PermitAction('cancel-line-items', 'marketing-agreements'),
  'marketing-agreements:create-line-items': new PermitAction('create-line-items', 'marketing-agreements'),
  'marketing-agreements:update-line-items': new PermitAction('update-line-items', 'marketing-agreements'),
  'model-portfolios:advanced-view': new PermitAction('advanced-view', 'model-portfolios'),
  'model-portfolios:components-write': new PermitAction('components-write', 'model-portfolios'),
  'model-portfolios:comment': new PermitAction('comment', 'model-portfolios'),
  'model-portfolios:create': new PermitAction('create', 'model-portfolios'),
  'model-portfolios:delete': new PermitAction('delete', 'model-portfolios'),
  'model-portfolios:price': new PermitAction('price', 'model-portfolios'),
  'model-portfolios:read': new PermitAction('read', 'model-portfolios'),
  'model-portfolios:share': new PermitAction('share', 'model-portfolios'),
  'model-portfolios:update': new PermitAction('update', 'model-portfolios'),
  'model-portfolios:rfp': new PermitAction('rfp', 'model-portfolios'),
  'notifications:trigger-periodic': new PermitAction('trigger-periodic', 'notifications'),
  'organizations:create': new PermitAction('create', 'organizations-tab'),
  'organizations:manage-users': new PermitAction('manage-users', 'organizations-tab'),
  'organizations:read': new PermitAction('read', 'organizations-tab'),
  'organizations:update': new PermitAction('update', 'organizations-tab'),
  'project-types:create': new PermitAction('create', 'project-types'),
  'project-types:read': new PermitAction('read', 'project-types'),
  'projects:export-csv': new PermitAction('export-csv', 'projects-tab'),
  'projects:read': new PermitAction('read', 'projects-tab'),
  'projects:write': new PermitAction('write', 'projects-tab'),
  'quotes:read': new PermitAction('read', 'quotes-tab'),
  'quotes:write': new PermitAction('write', 'quotes-tab'),
  'registries:create': new PermitAction('create', 'registries'),
  'registries:update': new PermitAction('update', 'registries'),
  'reporting:data-management': new PermitAction('data-management', 'reporting'),
  'reporting:inventory': new PermitAction('inventory', 'reporting'),
  'reporting:market-data': new PermitAction('market-data', 'reporting'),
  'reporting:recon': new PermitAction('recon', 'reporting'),
  'reserves:read': new PermitAction('read', 'reserves'),
  'reserves:write': new PermitAction('write', 'reserves'),
  'retirements:clear-admin-review': new PermitAction('clear-admin-review', 'retirements-tab'),
  'retirements:clear-pm-review': new PermitAction('clear-pm-review', 'retirements-tab'),
  'retirements:clear-processing': new PermitAction('clear-processing', 'retirements-tab'),
  'retirements:create': new PermitAction('create', 'retirements-tab'),
  'retirements:read': new PermitAction('read', 'retirements-tab'),
  'retirements:update': new PermitAction('update', 'retirements-tab'),
  'retirements:update-amounts': new PermitAction('update-amounts', 'retirements-tab'),
  'trades:auto-cancel': new PermitAction('auto-cancel', 'trades'),
  'trades:cancel': new PermitAction('cancel', 'trades'),
  'trades:confirm-binding': new PermitAction('confirm-binding', 'trades'),
  'trades:confirm-delivery': new PermitAction('confirm-delivery', 'trades'),
  'trades:confirm-executed': new PermitAction('confirm-executed', 'trades'),
  'trades:confirm-firm': new PermitAction('confirm-firm', 'trades'),
  'trades:confirm-indicative': new PermitAction('confirm-indicative', 'trades'),
  'trades:confirm-payment': new PermitAction('confirm-payment', 'trades'),
  'trades:read': new PermitAction('read', 'trades'),
  'trades:update': new PermitAction('update', 'trades'),
  'trades:update-amounts': new PermitAction('update-amounts', 'trades'),
  'transfers:execute': new PermitAction('execute', 'transfers'),
  'transactions:read': new PermitAction('read', 'transactions'),
  'transfers:read': new PermitAction('read', 'transfers'),
  'user-actions:view-all': new PermitAction('view-all', 'user-actions'),
  'user-actions:view-customer': new PermitAction('view-customer', 'user-actions'),
  'users:create': new PermitAction('create', 'users-tab'),
  'users:read': new PermitAction('read', 'users-tab'),
  'users:update': new PermitAction('update', 'users-tab'),
  'vintages:manage-risk-buffer': new PermitAction('manage-risk-buffer', 'vintages-tab'),
  'vintages:read': new PermitAction('read', 'vintages-tab'),
  'vintages:write': new PermitAction('write', 'vintages-tab'),
};
