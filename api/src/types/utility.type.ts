import 'reflect-metadata';

/**
 * RequireType utility class that makes all properties required (opposite of PartialType)
 * Similar to NestJS PartialType but ensures all fields are required instead of optional
 * Preserves all class-validator and class-transformer decorators
 */
export function RequireType<T>(classRef: new (...args: any[]) => T): new (...args: any[]) => Required<T> {
  class RequiredClass extends (classRef as any) {
    constructor(...args: any[]) {
      super(...args);
    }
  }

  // Copy class name for better debugging
  Object.defineProperty(RequiredClass, 'name', {
    value: `Required${classRef.name}`,
    configurable: true,
  });

  // Copy all class-level metadata (class decorators)
  if (typeof Reflect !== 'undefined' && Reflect.getMetadataKeys) {
    const classMetadataKeys = Reflect.getMetadataKeys(classRef);
    for (const key of classMetadataKeys) {
      const metadata = Reflect.getMetadata(key, classRef);
      Reflect.defineMetadata(key, metadata, RequiredClass);
    }
  }

  // Copy all property descriptors and their metadata
  const propertyNames = Object.getOwnPropertyNames(classRef.prototype);
  for (const propertyName of propertyNames) {
    if (propertyName !== 'constructor') {
      // Copy property descriptor
      const descriptor = Object.getOwnPropertyDescriptor(classRef.prototype, propertyName);
      if (descriptor) {
        Object.defineProperty(RequiredClass.prototype, propertyName, descriptor);
      }

      // Copy all property-level metadata (property decorators)
      if (typeof Reflect !== 'undefined' && Reflect.getMetadataKeys) {
        const propertyMetadataKeys = Reflect.getMetadataKeys(classRef.prototype, propertyName);
        for (const key of propertyMetadataKeys) {
          const metadata = Reflect.getMetadata(key, classRef.prototype, propertyName);
          Reflect.defineMetadata(key, metadata, RequiredClass.prototype, propertyName);
        }
      }
    }
  }

  // Also check for properties defined on the class itself (static properties)
  const staticPropertyNames = Object.getOwnPropertyNames(classRef);
  for (const propertyName of staticPropertyNames) {
    if (propertyName !== 'prototype' && propertyName !== 'name' && propertyName !== 'length') {
      const descriptor = Object.getOwnPropertyDescriptor(classRef, propertyName);
      if (descriptor) {
        Object.defineProperty(RequiredClass, propertyName, descriptor);
      }

      // Copy static property metadata
      if (typeof Reflect !== 'undefined' && Reflect.getMetadataKeys) {
        const staticMetadataKeys = Reflect.getMetadataKeys(classRef, propertyName);
        for (const key of staticMetadataKeys) {
          const metadata = Reflect.getMetadata(key, classRef, propertyName);
          Reflect.defineMetadata(key, metadata, RequiredClass, propertyName);
        }
      }
    }
  }

  return RequiredClass as new (...args: any[]) => Required<T>;
}

/**
 * Example usage of RequireType:
 *
 * // For a class with optional properties:
 * class UserDto {
 *   @IsOptional()
 *   name?: string;
 *
 *   @IsOptional()
 *   email?: string;
 * }
 *
 * // Create a version where all fields are required:
 * class RequiredUserDto extends RequireType(UserDto) {}
 *
 * // Now all properties in RequiredUserDto are required (not optional)
 * // This is useful when you want to ensure all fields are provided
 * // as opposed to PartialType which makes all fields optional
 */
