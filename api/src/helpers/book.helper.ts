/* third party */
import { InternalServerErrorException } from '@nestjs/common';
/* rubicon */
import { BookType, OwnerType } from '@rubiconcarbon/shared-types';
/* app */

export function toBookOwnerType(bookType: BookType): OwnerType {
  switch (bookType) {
    case BookType.AGED_DEFAULT:
      return OwnerType.BOOK_AGED_DEFAULT;
    case BookType.COMPLIANCE_DEFAULT:
      return OwnerType.BOOK_COMPLIANCE_DEFAULT;
    case BookType.OPPORTUNISTIC_DEFAULT:
      return OwnerType.BOOK_OPPORTUNISTIC_DEFAULT;
    case BookType.RCT_CUSTOM:
      return OwnerType.POR<PERSON><PERSON>IO_RCT_CUSTOM;
    case BookType.POR<PERSON>OLIO_CUSTOMER:
      return OwnerType.POR<PERSON><PERSON>IO_CUSTOMER;
    case BookType.PORTFOLIO_DEFAULT:
      return OwnerType.BOOK_PORTFOLIO_DEFAULT;
    case BookType.RCT_PUBLIC:
      return OwnerType.PORTFOLIO_RCT_PUBLIC;
    case BookType.PORTFOLIO_RESERVES:
      return OwnerType.PORTFOLIO_RESERVES;
    case BookType.REHABILITATION_DEFAULT:
      return OwnerType.BOOK_REHABILITATION_DEFAULT;
    case BookType.RRT_PUBLIC:
      return OwnerType.PORTFOLIO_RRT_PUBLIC;
    case BookType.OFFSETS:
      return OwnerType.OFFSETS;
    default:
      throw new InternalServerErrorException(`cannot find matching owner for book type ${bookType}`);
  }
}
