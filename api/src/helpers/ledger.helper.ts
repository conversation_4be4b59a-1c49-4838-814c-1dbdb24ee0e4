import { LedgerTransaction } from '@app/interfaces/ledger.interface';
import { environment } from '@env/environment';
import { Knex } from '@mikro-orm/postgresql';
import { LedgerTransactionAction, LedgerTransactionType, uuid } from '@rubiconcarbon/shared-types';

// find (still) pending ledger transactions (aka not released or reverted)
export async function findAvailablePendingLedgerTransactions(
  knex: Knex,
  relatedId: uuid,
  filters?: {
    action?: LedgerTransactionAction;
  },
): Promise<LedgerTransaction[]> {
  return await knex
    .select(['id', 'type', 'action', 'external_id'])
    .from(`${environment.db.schema.ledger}.transactions`)
    .where({ external_id: relatedId })
    .modify((qb) => {
      qb.andWhere('type', LedgerTransactionType.PENDING);
      qb.andWhere('is_fully_released', false);
      qb.andWhere('is_reverted', false);
      if (filters?.action) qb.andWhere('action', filters.action);
    })
    .orderBy('created_at', 'desc');
}
