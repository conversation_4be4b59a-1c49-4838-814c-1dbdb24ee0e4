import { Book, RrtAsset } from '@app/entities';
import {
  AdminBookQuery,
  BaseQueryResponsePage,
  BookOrderByOption,
  OrderByDirection,
  uuid,
} from '@rubiconcarbon/shared-types';
import {
  InternalAssetTypeGroupedAllocationsResponse,
  InternalBookTypeGroupedAllocationsResponse,
  InternalGroupedAllocationsResponse,
  InternalProjectGroupedAllocationsResponse,
  InternalProjectTypeGroupedAllocationsResponse,
} from './allocation.interface';
import Decimal from 'decimal.js';

export interface InternalBookResponse extends Book {
  assetAllocations?: InternalGroupedAllocationsResponse;
  assetAllocationsByBookType?: InternalBookTypeGroupedAllocationsResponse[];
  ownerAllocations?: InternalGroupedAllocationsResponse;
  ownerAllocationsByAssetType?: InternalAssetTypeGroupedAllocationsResponse[];
  ownerAllocationsByProject?: InternalProjectGroupedAllocationsResponse[];
  ownerAllocationsByProjectType?: InternalProjectTypeGroupedAllocationsResponse[];
  portfolioScore?: Decimal;
}

export interface InternalRrtPortfolioResponse extends Book {
  assets: (RrtAsset & { calculatedFactor: Decimal; grossQuantity: number; portfolioNetPercentage: Decimal })[];
  ownerAllocations: InternalGroupedAllocationsResponse;
  assetAllocations: InternalGroupedAllocationsResponse;
}

export interface InternalPortfolioBookQueryRequest extends AdminBookQuery {
  organizationId?: uuid;
  isEnabled?: boolean;
  limit: number;
  offset: number;
  orderBy: BookOrderByOption;
  orderByDirection: OrderByDirection;
}

export interface InternalBookQueryResponse {
  data: InternalBookResponse[];
  page: BaseQueryResponsePage;
}
