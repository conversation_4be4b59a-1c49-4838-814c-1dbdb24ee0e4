/* third party */
import { Controller, Get, NotFoundException, Param, ParseUUIDPipe, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
/* rubicon */
import { AllPortalBookTypes, PermissionEnum, uuid } from '@rubiconcarbon/shared-types';
/* app */
import { AuthService, CurrentUser, RequirePermissions } from '@app/auth';
import { UserClaims } from '@app/auth/interfaces';
import {
  PortalBookQueryDTO,
  PortalBookQueryResponseDTO,
  PortalBookRelationsQueryDTO,
  PortalBookResponseDTO,
  toPortalBookResponseDTO,
} from '@app/dtos/book.dto';
import { hide } from '@app/helpers';
import { BooksService } from '@app/services';

@ApiTags('Book')
@ApiBearerAuth('jwt')
@Controller('books')
export class BooksController {
  constructor(
    private authService: AuthService,
    private booksService: BooksService,
  ) {}

  /**
   * Returns PortalBookResponseDTO with some information hidden depending on permissions.
   */
  @Get()
  @RequirePermissions()
  @ApiOperation({ summary: 'Get a list of public/custom portfolios viewable by portal user' })
  @ApiOkResponse({ description: 'Success', type: PortalBookQueryResponseDTO })
  async findMany(
    @CurrentUser() user: UserClaims,
    @Query() query: PortalBookQueryDTO,
  ): Promise<PortalBookQueryResponseDTO> {
    const isVerified = await this.authService.check(user, 'verified', { route: 'books' });
    const books = await this.booksService.findManyForPortal(user, query);

    const response = {
      data: books.data.map((m) =>
        isVerified
          ? toPortalBookResponseDTO(m, query.includeRelations)
          : hide(toPortalBookResponseDTO(m), ['purchasePrice', 'purchasePriceWithBuffer']),
      ),
      page: books.page,
    };

    return response;
  }

  /**
   * Returns one Book for Portal.
   */
  @Get(':id')
  @RequirePermissions(PermissionEnum.LOGIN, PermissionEnum.VERIFIED)
  @ApiOperation({ summary: 'Get a trimmed book by id' })
  @ApiOkResponse({ description: 'Success', type: PortalBookResponseDTO })
  async findOne(
    @Param('id', ParseUUIDPipe) id: uuid,
    @Query() query: PortalBookRelationsQueryDTO,
    @CurrentUser() user: UserClaims,
  ): Promise<PortalBookResponseDTO> {
    const book = await this.booksService.findOne(id, query.includeRelations, user);
    // external users should not see the external or rubicon books
    if (!AllPortalBookTypes.includes(book.type)) {
      throw new NotFoundException(`Book ${id} not found`);
    }
    return toPortalBookResponseDTO(book, query.includeRelations);
  }
}
