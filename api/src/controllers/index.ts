/* ADMIN */
import { AdminAlertsController } from './admin/alerts.controller';
import { AdminBooksController } from './admin/books.controller';
import { AdminBufferCategoriesController } from './admin/buffer-categories.controller';
import { AdminCountriesController } from './admin/countries.controller';
import { AdminDocumentsController } from './admin/documents.controller';
import { AdminForwardsController } from './admin/forwards.controller';
import { AdminHistoricalController } from './admin/historical.controller';
import { AdminCreditInflowsController } from './admin/credit-flows.controller';
import { AdminMarketNewsController } from './admin/market-news.controller';
import { AdminMarketingAgreementsController } from './admin/marketing-agreements.controller';
import { AdminModelPortfoliosController } from './admin/model-portfolios.controller';
import { AdminNotificationsController } from './admin/notifications.controller';
import { AdminOrganizationsController } from './admin/organizations.controller';
import { AdminPricingController } from './admin/pricing.controller';
import { AdminProjectTypesController } from './admin/project-types.controller';
import { AdminProjectsController } from './admin/projects.controller';
import { AdminProjectVintagesController } from './admin/project-vintages.controller';
import { AdminPurchasesController } from './admin/purchases.controller';
import { AdminQuotesController } from './admin/quotes.controller';
import { AdminRegistriesController } from './admin/registries.controller';
import { AdminReservesController } from './admin/reserves.controller';
import { AdminRrtsController } from './admin/rrts.controller';
import { AdminRetirementsController } from './admin/retirements.controller';
import { AdminSupersetController } from './admin/superset.controller';
import { AdminTradesController } from './admin/trades.controller';
import { AdminTransactionsController } from './admin/transactions.controller';
import { AdminTransfersController } from './admin/transfers.controller';
import { AdminUsersController } from './admin/users.controller';
/* PORTAL */
import { BooksController } from './books.controller';
import { EmailsController } from './emails.controller';
import { DocumentsController } from './documents.controller';
import { FeatureFlagsController } from './feature-flags.controller';
import { MarketNewsController } from './market-news.controller';
import { ModelPortfoliosController } from './model-portfolios.controller';
import { OrganizationsController } from './organizations.controller';
import { PricingController } from './pricing.controller';
import { ProjectsController } from './projects.controller';
import { PurchasesController } from './purchases.controller';
import { RetirementsController } from './retirements.controller';
import { SettingsController } from './settings.controller';
import { TransactionsController } from './transactions.controller';
import { UsersController } from './users.controller';
import { URLShortenerController } from './url-shortener.controller';
import { AdminURLShortenerController } from './admin/url-shortener.controller';
import { UserActionsController } from './users-actions.controller';
import { AdminUserActionsController } from './admin/users-actions.controller';
/* MOBILE */
import { MobileBooksController } from './mobile/books.controller';
import { MobileEmailsController } from './mobile/emails.controller';
import { MobileModelPortfoliosController } from './mobile/model-portfolios.controller';
import { MobileOrganizationsController } from './mobile/organizations.controller';
import { MobileProjectsController } from './mobile/projects.controller';
import { MobileTransactionsController } from './mobile/transactions.controller';
import { MobilePricingController } from './mobile/pricing.controller';
/* CORE CONTROLLERS */
export { HealthController } from './health.controller';
export { PrometheusController } from './prometheus.controller';

export const Controllers = [
  AdminAlertsController,
  AdminBooksController,
  AdminBufferCategoriesController,
  AdminCountriesController,
  AdminCreditInflowsController,
  AdminDocumentsController,
  AdminForwardsController,
  AdminHistoricalController,
  AdminMarketingAgreementsController,
  AdminMarketNewsController,
  AdminModelPortfoliosController,
  AdminNotificationsController,
  AdminOrganizationsController,
  AdminPricingController,
  AdminProjectsController,
  AdminProjectTypesController,
  AdminProjectVintagesController,
  AdminPurchasesController,
  AdminQuotesController,
  AdminRegistriesController,
  AdminReservesController,
  AdminRetirementsController,
  AdminRrtsController,
  AdminSupersetController,
  AdminTradesController,
  AdminTransactionsController,
  AdminTransfersController,
  AdminURLShortenerController,
  AdminUserActionsController,
  AdminUsersController,
  BooksController,
  DocumentsController,
  EmailsController,
  FeatureFlagsController,
  MarketNewsController,
  MobileBooksController,
  MobileEmailsController,
  MobileModelPortfoliosController,
  MobileOrganizationsController,
  MobilePricingController,
  MobileProjectsController,
  MobileTransactionsController,
  ModelPortfoliosController,
  OrganizationsController,
  PricingController,
  ProjectsController,
  PurchasesController,
  RetirementsController,
  SettingsController,
  TransactionsController,
  URLShortenerController,
  UserActionsController,
  UsersController,
];

/* istanbul ignore file */
