/* third party */
import { Body, Controller, Get, Param, ParseUUIDPipe, Post, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
/* rubicon */
import { PermissionEnum, TransferRelation, uuid } from '@rubiconcarbon/shared-types';
/* app */
import { CurrentUser, RequirePermissions, UserClaims } from '@app/auth';
import { AdminBookQueryResponseDTO, toAdminBookResponseDTO } from '@app/dtos/book.dto';
import {
  AdminRrtCompositionRequestDTO,
  AdminTransferQueryDTO,
  AdminTransferQueryResponseDTO,
  AdminTransferRelationsQueryDTO,
  AdminTransferRequestDTO,
  AdminTransferResponseDTO,
  toAdminTransferResponseDTO,
} from '@app/dtos/transfer.dto';
import { Transfer } from '@app/entities';
import { TransfersService } from '@app/services';
/**
 * Returns all Books.
 *
 */
@RequirePermissions(PermissionEnum.TRANSFERS_READ)
@ApiTags('Admin')
@ApiBearerAuth('jwt')
@Controller('admin/transfers')
export class AdminTransfersController {
  constructor(private transfersService: TransfersService) {}

  @Post()
  @RequirePermissions(PermissionEnum.TRANSFERS_EXECUTE)
  @ApiOperation({ summary: 'Execute Transfer' })
  @ApiOkResponse({ description: 'Success', type: AdminTransferResponseDTO })
  async transfer(
    @CurrentUser() claims: UserClaims,
    @Body() data: AdminTransferRequestDTO,
  ): Promise<AdminTransferResponseDTO> {
    const transfer: Transfer = await this.transfersService.executeInternal(claims, data);
    return toAdminTransferResponseDTO(transfer, [TransferRelation.USER]);
  }

  @Get()
  @ApiOperation({ summary: 'Get a list of Transfers' })
  @ApiOkResponse({ description: 'Success', type: AdminTransferQueryResponseDTO })
  async findMany(@Query() query: AdminTransferQueryDTO): Promise<AdminTransferQueryResponseDTO> {
    const response = await this.transfersService.findMany(query);
    return {
      data: response.data.map((m) => toAdminTransferResponseDTO(m, query.includeRelations)),
      page: response.page,
    };
  }

  @Get('sources')
  @ApiOperation({ summary: 'Get a list of sources for Transfer' })
  @ApiOkResponse({ description: 'Success', type: AdminBookQueryResponseDTO })
  async findSources(): Promise<AdminBookQueryResponseDTO> {
    const books = await this.transfersService.findSources();
    return {
      data: books.map((m) => toAdminBookResponseDTO(m)),
      page: {
        size: books.length,
        limit: books.length,
        offset: 0,
        totalCount: books.length,
      },
    };
  }

  @Get('sources/:source_id/destinations')
  @ApiOperation({ summary: 'Get a list of destinations for the given source' })
  @ApiOkResponse({ description: 'Success', type: AdminBookQueryResponseDTO })
  async findSourceDestinations(@Param('source_id', ParseUUIDPipe) sourceId: uuid): Promise<AdminBookQueryResponseDTO> {
    const books = await this.transfersService.findSourceDestinations(sourceId);
    return {
      data: books.map((m) => toAdminBookResponseDTO(m)),
      page: {
        size: books.length,
        limit: books.length,
        offset: 0,
        totalCount: books.length,
      },
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a single Transfer by id' })
  @ApiOkResponse({ description: 'Success', type: AdminTransferResponseDTO })
  async findOne(
    @Param('id', ParseUUIDPipe) id: uuid,
    @Query() query: AdminTransferRelationsQueryDTO,
  ): Promise<AdminTransferResponseDTO> {
    const transfer = await this.transfersService.findOne(id, query);
    const x = toAdminTransferResponseDTO(transfer, query.includeRelations);
    return x;
  }
}
