/* third party */
import { Body, Controller, Get, Param, ParseArrayPipe, ParseUUIDPipe, Patch, Post, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
/* rubicon */
import { PermissionEnum, ProjectVintageRelations, uuid } from '@rubiconcarbon/shared-types';
/* app */
import { CurrentUser, RequirePermissions } from '@app/auth';
import { UserClaims } from '@app/auth/interfaces';
import {
  AdminProjectVintageQueryDTO,
  AdminProjectVintageQueryResponseDTO,
  AdminProjectVintageRelationsQueryDTO,
  AdminProjectVintageRequestDTO,
  AdminProjectVintageResponseDTO,
  AdminProjectVintageRiskBufferRequestDTO,
  toAdminProjectVintageResponseDTO,
} from '@app/dtos/project-vintage.dto';
import { PricingService, ProjectVintagesService } from '@app/services';

@RequirePermissions(PermissionEnum.VINTAGES_READ)
@ApiTags('Project Vintage')
@ApiBearerAuth('jwt')
@Controller('admin/project-vintages')
export class AdminProjectVintagesController {
  constructor(
    private projectVintagesService: ProjectVintagesService,
    private pricingService: PricingService,
  ) {}

  /**
   * Create one Project Vintage.
   */
  @Post()
  @RequirePermissions(PermissionEnum.VINTAGES_WRITE)
  @ApiOperation({ summary: 'Add a Project Vintage' })
  @ApiOkResponse({ description: 'Success', type: AdminProjectVintageResponseDTO })
  async create(
    @Body() data: AdminProjectVintageRequestDTO,
    @CurrentUser() user: UserClaims,
  ): Promise<AdminProjectVintageResponseDTO> {
    const projectVintage = await this.projectVintagesService.create(data, user);
    return toAdminProjectVintageResponseDTO(projectVintage, [ProjectVintageRelations.PROJECT]);
  }

  @Get()
  @ApiOperation({ summary: 'Get All ProjectVintages' })
  @ApiOkResponse({ description: 'Success', type: AdminProjectVintageQueryResponseDTO })
  async findAll(@Query() query: AdminProjectVintageQueryDTO): Promise<AdminProjectVintageQueryResponseDTO> {
    const response = await this.projectVintagesService.findAll(query);
    return {
      data: response.data.map((m) =>
        toAdminProjectVintageResponseDTO(m, query.includeRelations, {
          pricing: this.pricingService,
        }),
      ),
      page: response.page,
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get one Project Vintage by Id' })
  @ApiOkResponse({ description: 'Success', type: AdminProjectVintageResponseDTO })
  async findOne(
    @Param('id', ParseUUIDPipe) id: uuid,
    @Query() query: AdminProjectVintageRelationsQueryDTO,
  ): Promise<AdminProjectVintageResponseDTO> {
    const projectVintage = await this.projectVintagesService.findOne(id, query.includeRelations);
    return toAdminProjectVintageResponseDTO(projectVintage, query.includeRelations);
  }

  @Patch('risk-buffers')
  @RequirePermissions(PermissionEnum.VINTAGES_MANAGE_RISK_BUFFER)
  @ApiOperation({ summary: 'Add/Update Bulk ProjectVintage Risk Buffer Percentage' })
  @ApiOkResponse({ description: 'Success', type: [AdminProjectVintageResponseDTO] })
  async bulkAddOrUpdateRiskBufferPercentage(
    @CurrentUser() user: UserClaims,
    @Body(
      new ParseArrayPipe({
        items: AdminProjectVintageRiskBufferRequestDTO,
        transformOptions: { exposeUnsetFields: false },
        whitelist: true,
        forbidUnknownValues: false,
      }),
    )
    data: AdminProjectVintageRiskBufferRequestDTO[],
  ): Promise<AdminProjectVintageResponseDTO[]> {
    const projectVintages = await this.projectVintagesService.addOrUpdateRiskBuffer(user, data);
    return projectVintages.map((m) => toAdminProjectVintageResponseDTO(m));
  }
}
