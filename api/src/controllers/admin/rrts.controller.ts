/* third party */
import { Body, Controller, Get, Param, ParseUUI<PERSON>ipe, Patch, Post } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
/* rubicon */
import { PermissionEnum, TransferRelation, uuid } from '@rubiconcarbon/shared-types';
/* app */
import { CurrentUser, RequirePermissions, UserClaims } from '@app/auth';
import {
  AdminBookResponseDTO,
  AdminRrtPortfolioCreateRequestDTO,
  AdminRrtPortfolioResponseDTO,
  toAdminBookResponseDTO,
  toAdminRrtPortfolioResponseDTO,
} from '@app/dtos/book.dto';
import {
  AdminRrtCompositionRequestDTO,
  AdminTransferResponseDTO,
  toAdminTransferResponseDTO,
} from '@app/dtos/transfer.dto';
import { Book, Transfer } from '@app/entities';
import { BooksService, TransfersService } from '@app/services';

/**
 * Returns all Books.
 *
 */
@RequirePermissions(PermissionEnum.BOOKS_READ)
@ApiTags('Admin')
@ApiBearerAuth('jwt')
@Controller('admin/portfolios/rrts')
export class AdminRrtsController {
  constructor(
    private booksService: BooksService,
    private transfersService: TransfersService,
  ) {}

  @Post()
  @RequirePermissions(PermissionEnum.BOOKS_CREATE_RRT_PORTFOLIOS)
  @ApiOperation({ summary: 'create rrt portfolio' })
  @ApiOkResponse({ description: 'Success', type: AdminBookResponseDTO })
  async createRrtPublicPortfolio(
    @CurrentUser() claims: UserClaims,
    @Body() data: AdminRrtPortfolioCreateRequestDTO,
  ): Promise<AdminBookResponseDTO> {
    const book: Book = await this.booksService.createRrtPublicPortfolio(claims, data);
    return toAdminBookResponseDTO(book);
  }

  @Get(':id')
  @ApiOperation({ summary: 'get rrt portfolio by id' })
  @ApiOkResponse({ description: 'Success', type: AdminRrtPortfolioResponseDTO })
  async findOne(@Param('id', ParseUUIDPipe) id: uuid): Promise<AdminRrtPortfolioResponseDTO> {
    const rrt = await this.booksService.findOneRrt(id);
    return toAdminRrtPortfolioResponseDTO(rrt);
  }

  @Patch(':id/assets')
  @RequirePermissions(PermissionEnum.BOOKS_UPDATE_RRT_COMPOSITION)
  @ApiOperation({ summary: 'update rrt composition' })
  @ApiOkResponse({ description: 'Success', type: AdminTransferResponseDTO })
  async rrtCompositionTransfer(
    @CurrentUser() claims: UserClaims,
    @Body() data: AdminRrtCompositionRequestDTO,
  ): Promise<AdminTransferResponseDTO> {
    const transfer: Transfer = await this.transfersService.updateRrtComposition(claims, data);
    return toAdminTransferResponseDTO(transfer, [TransferRelation.USER]);
  }
}
