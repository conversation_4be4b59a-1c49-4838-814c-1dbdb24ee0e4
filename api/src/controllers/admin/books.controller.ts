/* third party */
import { Body, Controller, ForbiddenException, Get, Param, ParseUUIDPipe, Patch, Post, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
/* rubicon */
import { BookRelations, GroupedAllocationRelations, PermissionEnum, uuid } from '@rubiconcarbon/shared-types';
/* app */
import { AuthService, CurrentUser, RequirePermissions, UserClaims } from '@app/auth';
import {
  AdminBookEstimateRetirementResponseDTO,
  AdminBookQueryDTO,
  AdminBookQueryResponseDTO,
  AdminBookRelationsQueryDTO,
  AdminBookResponseDTO,
  AdminBookUpdateRequestDTO,
  AdminRctCustomPortfolioCreateRequestDTO,
  AdminRctPublicPortfolioCreateRequestDTO,
  AdminRrtPortfolioCreateRequestDTO,
  toAdminBookResponseDTO,
} from '@app/dtos/book.dto';
import { toTrimmedProjectVintageResponseDTO } from '@app/dtos/project-vintage.dto';
import { Book } from '@app/entities';
import { InternalGroupingParent } from '@app/interfaces/grouping-parent.interface';
import { ParsePositiveIntPipe } from '@app/pipes/parse-positive-int.pipe';
import { BooksService, RetirementsService } from '@app/services';
import {
  GroupingParentQueryResponseDTO,
  GroupingRelationsQueryDTO,
  toAdminGroupingParentResponseDTO,
} from '@app/dtos/grouping.dto';
import {
  AdminGroupedAllocationWithNestedResponseDTO,
  toAdminGroupedAllocationResponseDTOFromGroupedAllocation,
} from '@app/dtos/allocation.dto';

/**
 * Returns all Books.
 *
 */
@RequirePermissions(PermissionEnum.BOOKS_READ)
@ApiTags('Admin')
@ApiBearerAuth('jwt')
@Controller('admin/books')
export class AdminBooksController {
  constructor(
    private authService: AuthService,
    private booksService: BooksService,
    private retirementsService: RetirementsService,
  ) {}

  @Post('rct-custom')
  @RequirePermissions(PermissionEnum.BOOKS_CREATE_RCT_PORTFOLIOS)
  @ApiOperation({ summary: 'Create a Book' })
  @ApiOkResponse({ description: 'Success', type: AdminBookResponseDTO })
  async createRctCustomPortfolio(
    @CurrentUser() claims: UserClaims,
    @Body() data: AdminRctCustomPortfolioCreateRequestDTO,
  ): Promise<AdminBookResponseDTO> {
    const book: Book = await this.booksService.createRctCustomPortfolio(claims, data);
    return toAdminBookResponseDTO(book, [BookRelations.ORGANIZATION]);
  }

  @Post('rct-public')
  @RequirePermissions(PermissionEnum.BOOKS_CREATE_RCT_PORTFOLIOS)
  @ApiOperation({ summary: 'Create a Book' })
  @ApiOkResponse({ description: 'Success', type: AdminBookResponseDTO })
  async createRctPublicPortfolio(
    @CurrentUser() claims: UserClaims,
    @Body() data: AdminRctPublicPortfolioCreateRequestDTO,
  ): Promise<AdminBookResponseDTO> {
    const book: Book = await this.booksService.createRctPublicPortfolio(claims, data);
    return toAdminBookResponseDTO(book);
  }

  @Post('rrt-public')
  @RequirePermissions(PermissionEnum.BOOKS_CREATE_RCT_PORTFOLIOS)
  @ApiOperation({ summary: 'Create a Book' })
  @ApiOkResponse({ description: 'Success', type: AdminBookResponseDTO })
  async createRrtPublicPortfolio(
    @CurrentUser() claims: UserClaims,
    @Body() data: AdminRrtPortfolioCreateRequestDTO,
  ): Promise<AdminBookResponseDTO> {
    const book: Book = await this.booksService.createRrtPublicPortfolio(claims, data);
    return toAdminBookResponseDTO(book);
  }

  @Get()
  @ApiOperation({ summary: 'Get a list of Books' })
  @ApiOkResponse({ description: 'Success', type: AdminBookQueryResponseDTO })
  async findMany(@Query() query: AdminBookQueryDTO): Promise<AdminBookQueryResponseDTO> {
    const response = await this.booksService.findMany(query);
    return {
      data: response.data.map((m) => toAdminBookResponseDTO(m, query.includeRelations)),
      page: response.page,
    };
  }

  @Get('parents')
  @ApiOperation({ summary: 'Get a list of (book) parents' })
  @ApiOkResponse({ description: 'Success', type: GroupingParentQueryResponseDTO })
  async getParents(@Query() query: GroupingRelationsQueryDTO): Promise<GroupingParentQueryResponseDTO> {
    const response: InternalGroupingParent[] = await this.booksService.getParentHoldings(query.includeRelations);
    return {
      data: response.map((m) => toAdminGroupingParentResponseDTO(m, query.includeRelations)),
      page: {
        limit: response.length,
        size: response.length,
        offset: 0,
        totalCount: response.length,
      },
    };
  }

  // it's not blocked for single in case there's some reason it needs to get called from the UI.
  @Get(':id')
  @ApiOperation({ summary: 'Get a single Book by id' })
  @ApiOkResponse({ description: 'Success', type: AdminBookResponseDTO })
  async findOne(
    @Param('id', ParseUUIDPipe) id: uuid,
    @Query() query: AdminBookRelationsQueryDTO,
  ): Promise<AdminBookResponseDTO> {
    const book = await this.booksService.findOne(id, query.includeRelations);
    return toAdminBookResponseDTO(book, query.includeRelations);
  }

  @Get([':id/assets/holdings', ':id/holdings'])
  @ApiOperation({ summary: 'Get asset holdings by book' })
  @ApiOkResponse({ description: 'Success', type: AdminGroupedAllocationWithNestedResponseDTO })
  async getDeliverableAssets(
    @Param('id', ParseUUIDPipe) id: uuid,
  ): Promise<AdminGroupedAllocationWithNestedResponseDTO> {
    const response = await this.booksService.getHoldings(id);
    return toAdminGroupedAllocationResponseDTOFromGroupedAllocation(response, [
      GroupedAllocationRelations.ALLOCATIONS,
    ]) as AdminGroupedAllocationWithNestedResponseDTO;
  }

  // todo : needs to be rewritten
  @Get(':id/retirement-calculations/:amountRequested')
  @RequirePermissions(PermissionEnum.BOOKS_CALCULATE_RETIREMENT)
  @ApiOperation({ summary: 'Calculate Retirement for a Book' })
  @ApiOkResponse({ description: 'Success', type: [AdminBookEstimateRetirementResponseDTO] })
  async calculateRetirement(
    @Param('id', ParseUUIDPipe) id: uuid,
    @Param('amountRequested', ParsePositiveIntPipe) amountRequested: number,
  ): Promise<AdminBookEstimateRetirementResponseDTO[]> {
    const amounts = await this.retirementsService.calculateEstimateRetirementAmounts(id, amountRequested);
    return amounts.map((m) => {
      return {
        amountTransacted: m.amountTransacted,
        projectVintage: toTrimmedProjectVintageResponseDTO(m.vintage),
      };
    });
  }

  @Patch(':id')
  @RequirePermissions(PermissionEnum.BOOKS_UPDATE)
  @ApiOperation({ summary: 'Update Book' })
  @ApiOkResponse({ description: 'Success', type: AdminBookResponseDTO })
  async update(
    @CurrentUser() user: UserClaims,
    @Param('id', ParseUUIDPipe) id: uuid,
    @Body() data: AdminBookUpdateRequestDTO,
  ): Promise<AdminBookResponseDTO> {
    if (
      (data.purchasePrice !== undefined || data.purchasePriceWithBuffer !== undefined) &&
      !(await this.authService.check(user, PermissionEnum.BOOKS_UPDATE_PRICE, { route: 'books' }))
    ) {
      throw new ForbiddenException();
    }
    if (
      data.projectTypeIds !== undefined &&
      !(await this.authService.check(user, PermissionEnum.BOOKS_UPDATE_PROJECT_TYPES, { route: 'books' }))
    ) {
      throw new ForbiddenException();
    }
    const book = await this.booksService.update(user, id, data);
    return toAdminBookResponseDTO(
      book,
      book.projectTypes.isInitialized() ? [BookRelations.OWNER_ALLOCATIONS_BY_PROJECT_TYPE] : [],
    );
  }
}
