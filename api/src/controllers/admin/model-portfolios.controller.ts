/* third party */
import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Patch, Post, Query } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
/* rubicon */
import { ModelPortfolioRelations, PermissionEnum, uuid } from '@rubiconcarbon/shared-types';
/* app */
import { CurrentUser, RequirePermissions, UserClaims } from '@app/auth';
import {
  AdminModelPortfolioComponentRequestCreateDTO,
  AdminModelPortfolioComponentRequestDTO,
  AdminModelPortfolioComponentRequestDeleteDTO,
  AdminModelPortfolioComponentRequestUpdateDTO,
  AdminModelPortfolioCreateRequestDTO,
  AdminModelPortfolioQueryDTO,
  AdminModelPortfolioQueryResponseDTO,
  AdminModelPortfolioRelationsQueryDTO,
  AdminModelPortfolioResponseDTO,
  AdminModelPortfolioUpdateRequestDTO,
  toAdminModelPortfolioResponseDTO,
} from '@app/dtos/model-portfolio.dto';
import { InternalModelPortfolioQueryResponse } from '@app/interfaces/model-portfolio.interface';
import { ModelPortfoliosService } from '@app/services';

@RequirePermissions(PermissionEnum.MODEL_PORTFOLIOS_READ)
@ApiTags('Admin ModelPortfolios')
@ApiBearerAuth('jwt')
@Controller('admin/model-portfolios')
export class AdminModelPortfoliosController {
  constructor(private modelPortfoliosService: ModelPortfoliosService) {}

  @Post()
  @RequirePermissions(PermissionEnum.MODEL_PORTFOLIOS_CREATE)
  @ApiOperation({ summary: 'Create a new ModelPortfolio' })
  @ApiOkResponse({ description: 'Success', type: AdminModelPortfolioResponseDTO })
  async create(
    @CurrentUser() user: UserClaims,
    @Body() data: AdminModelPortfolioCreateRequestDTO,
  ): Promise<AdminModelPortfolioResponseDTO> {
    const modelPortfolio = await this.modelPortfoliosService.createModelPortfolioFromAdmin(user, data);
    return toAdminModelPortfolioResponseDTO(modelPortfolio);
  }

  @Get()
  @ApiOperation({ summary: 'Get List of ModelPortfolios Based on Query' })
  @ApiOkResponse({ description: 'Success', type: AdminModelPortfolioQueryResponseDTO })
  async findAll(@Query() query: AdminModelPortfolioQueryDTO): Promise<AdminModelPortfolioQueryResponseDTO> {
    const internalModelPortfolios: InternalModelPortfolioQueryResponse =
      await this.modelPortfoliosService.findManyModelPortfolios({ ...query, isAdmin: true });
    return {
      data: internalModelPortfolios.data.map((m) => toAdminModelPortfolioResponseDTO(m, query.includeRelations)),
      page: internalModelPortfolios.page,
    };
  }

  @Get('generate-name')
  async auth(@Query('organizationId', ParseUUIDPipe) organizationId: uuid): Promise<string> {
    return await this.modelPortfoliosService.generateAdminModelPortfolioName(organizationId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get One ModelPortfolio by Id' })
  @ApiOkResponse({ description: 'Success', type: AdminModelPortfolioResponseDTO })
  async findOne(
    @Param('id', ParseUUIDPipe) id: uuid,
    @Query() query: AdminModelPortfolioRelationsQueryDTO,
  ): Promise<AdminModelPortfolioResponseDTO> {
    const relations = query.includeRelations || [
      ModelPortfolioRelations.MODEL_PORTFOLIO_COMPONENTS,
      ModelPortfolioRelations.PROJECT,
      ModelPortfolioRelations.PROJECT_VINTAGE,
      ModelPortfolioRelations.PORTFOLIO,
    ]; // todo : fix FE to always pass in
    const modelPortfolio = await this.modelPortfoliosService.findOneModelPortfolio(id, relations);
    return toAdminModelPortfolioResponseDTO(modelPortfolio, relations);
  }

  @Patch(':id')
  @RequirePermissions(PermissionEnum.MODEL_PORTFOLIOS_UPDATE)
  @ApiOperation({ summary: 'Update ModelPortfolio' })
  @ApiOkResponse({ description: 'Success', type: AdminModelPortfolioResponseDTO })
  async update(
    @CurrentUser() user: UserClaims,
    @Param('id', ParseUUIDPipe) id: uuid,
    @Body() data: AdminModelPortfolioUpdateRequestDTO,
  ): Promise<AdminModelPortfolioResponseDTO> {
    const modelPortfolio = await this.modelPortfoliosService.updateModelPortfolio(user, id, data);
    return toAdminModelPortfolioResponseDTO(modelPortfolio);
  }

  @Patch(':id/components')
  @RequirePermissions(PermissionEnum.MODEL_PORTFOLIOS_COMPONENTS_WRITE)
  @ApiOperation({
    summary: 'Update ModelPortfolio Components',
    description: 'Updates the components of a model portfolio.',
  })
  @ApiExtraModels(
    AdminModelPortfolioComponentRequestUpdateDTO,
    AdminModelPortfolioComponentRequestCreateDTO,
    AdminModelPortfolioComponentRequestDeleteDTO,
  )
  @ApiBody({
    schema: {
      oneOf: [
        { $ref: getSchemaPath(AdminModelPortfolioComponentRequestCreateDTO) },
        { $ref: getSchemaPath(AdminModelPortfolioComponentRequestUpdateDTO) },
        { $ref: getSchemaPath(AdminModelPortfolioComponentRequestDeleteDTO) },
      ],
    },
  })
  @ApiOkResponse({ description: 'ModelPortfolio components updated successfully' })
  async updateModelPortfolioComponents(
    @CurrentUser() user: UserClaims,
    @Param('id', ParseUUIDPipe) id: uuid,
    @Body() data: AdminModelPortfolioComponentRequestDTO,
  ): Promise<void> {
    // this is needed so the caller doesn't need to specify the `type` field
    if ((data as any).create !== undefined) data.type = 'create';
    if ((data as any).update !== undefined) data.type = 'update';
    if ((data as any).delete !== undefined) data.type = 'delete';

    await this.modelPortfoliosService.updateModelPortfolioComponentsFromController(user, id, data);
  }

  @Delete(':id')
  @RequirePermissions(PermissionEnum.MODEL_PORTFOLIOS_DELETE)
  @ApiOperation({ summary: 'Delete ModelPortfolio' })
  @ApiOkResponse({ description: 'Success' })
  async delete(@CurrentUser() user: UserClaims, @Param('id', ParseUUIDPipe) id: uuid): Promise<void> {
    await this.modelPortfoliosService.deleteModelPortfolio(user, id);
  }
}
