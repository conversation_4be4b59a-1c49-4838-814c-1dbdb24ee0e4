/* third party */
import { FilterQuery, FindOptions } from '@mikro-orm/core';
import { EntityManager } from '@mikro-orm/postgresql';
import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
/* rubicon */
import {
  AssetFlowOrderByOptions,
  AssetFlowRelation,
  AssetType,
  TransactionType,
  uuid,
} from '@rubiconcarbon/shared-types';
/* app */
import { UserClaims } from '@app/auth/interfaces';
import { BaseAssetFlowOrderByDTO, PortalAssetFlowQueryDTO } from '@app/dtos/flow.dto';
import { AssetFlow, Book, ProjectVintage, Purchase, Retirement, Trade, Transfer } from '@app/entities';
import { OrderByDirectionSQL } from '@app/enums/orderby.enum';
import { InternalAssetFlowQueryResponse, InternalAssetFlowResponse } from '@app/interfaces/flow.interface';
import { InternalPortalUser } from '@app/interfaces/user.interface';
import { getPortalUserWithOrganizationFromClaims, returnEmptyQueryResponse } from '@app/helpers';

@Injectable()
export class FlowsService {
  constructor(private em: EntityManager) {}

  /**
   * find many AssetFlow entities for portal Organization
   *
   * steps:
   * 1. get InternalPortalUser from UserClaims
   * 2. if user not found, throw NotFoundException or return empty response
   * 3. check if user has Organization with CustomerPortfolio, throw UnprocessableEntityException if not
   * 4. build where filter for AssetFlow based on query params
   * 5. handle retirements and get transaction ids if needed
   * 6. build finalizedWhere filter for AssetFlow
   * 7. count total AssetFlow entities if includeTotalCount is true
   * 8. find AssetFlow entities with pagination and relations
   * 9. populate InternalAssetFlow relations
   * 10. return InternalAssetFlowQueryResponse with data and page info
   *
   * @param {PortalAssetFlowQueryDTO} query
   * @param {UserClaims} claims
   * @returns {Promise<InternalAssetFlowQueryResponse>}
   * @throws NotFoundException, UnprocessableEntityException
   */
  async findManyForPortalOrganization(
    query: PortalAssetFlowQueryDTO,
    claims: UserClaims,
  ): Promise<InternalAssetFlowQueryResponse> {
    let portalUser: InternalPortalUser;

    try {
      portalUser = await getPortalUserWithOrganizationFromClaims(this.em, claims);
    } catch (e) {
      if (e instanceof NotFoundException) {
        throw e;
      }

      return {
        ...returnEmptyQueryResponse(query),
        data: [],
      };
    }
    if (!portalUser.organizationUser.organization.customerPortfolio) {
      throw new UnprocessableEntityException(`User 
      ${claims.email} must be part of an Organization with a CustomerPortfolio`);
    }

    const where: FilterQuery<AssetFlow> = {
      amount: { $ne: 0 },
    };

    if (query.assetId) {
      where.asset = { id: query.assetId };
    }

    if (query.transactionTypes) {
      where.transactionType = query.transactionTypes;
    }

    if (query.transactionSubtypes) {
      where.transactionSubtype = query.transactionSubtypes;
    }

    if (query.transactionId) {
      where.transaction = query.transactionId;
    }

    if (query.status) {
      where.status = query.status;
    }

    if (query.assetType) {
      where.assetType = query.assetType;
    }

    let transactionIds: uuid[] = [];
    // for retirements, we also want the vintages attached to a specific retirement
    if (
      !query.transactionTypes ||
      (query.transactionTypes.includes(TransactionType.RETIREMENT) &&
        (!query.assetType || query.assetType === AssetType.REGISTRY_VINTAGE))
    ) {
      const retirementRcts = await this.em.find(AssetFlow, {
        transactionType: TransactionType.RETIREMENT,
        source: { id: portalUser.organizationUser.organization.customerPortfolio.book.id },
        assetType: AssetType.RCT,
      });
      const retirementFlows = await this.em.find(AssetFlow, {
        transactionType: TransactionType.RETIREMENT,
        transaction: retirementRcts.map((m) => m.transaction.id),
        assetType: AssetType.REGISTRY_VINTAGE,
      });
      transactionIds = retirementFlows.map((m) => m.id);
    }

    const finalizedWhere: FilterQuery<AssetFlow> = {
      ...where,
      $or: [
        { source: portalUser.organizationUser.organization.customerPortfolio.book.id },
        { destination: portalUser.organizationUser.organization.customerPortfolio.book.id },
        { id: transactionIds },
      ],
    };

    const count = query.includeTotalCount ? await this.em.count(AssetFlow, finalizedWhere) : undefined;
    const assetFlows = await this.em.find(AssetFlow, finalizedWhere, {
      limit: query.limit,
      offset: query.offset,
      orderBy: this.privateSetAssetFlowOrderBys(query.orderBys),
      populate: this.privatePopulateAssetFlowRelations(query.includeRelations),
      connectionType: 'read',
    });

    const assetFlowsResponse: InternalAssetFlowResponse[] = await this.privatePopulateInternalAssetFlowRelations(
      this.em,
      assetFlows,
      query.includeRelations,
    );
    return {
      data: assetFlowsResponse,
      page: {
        size: assetFlowsResponse.length,
        limit: query.limit,
        offset: query.offset,
        totalCount: count,
      },
    };
  }

  /* private */

  /**
   * build the relations to populate for AssetFlow entity queries (private helper)
   *
   * steps:
   * 1. initialize populateString array with default relations
   * 2. add AssetFlowRelation.DESTINATION, AssetFlowRelation.LINKS, AssetFlowRelation.SOURCE if requested
   * 3. return populateString array as FindOptions populate type
   *
   * @param {AssetFlowRelation[]} relations
   * @returns {FindOptions<AssetFlow, S>['populate']}
   */
  private privatePopulateAssetFlowRelations<S extends string>(
    relations: AssetFlowRelation[] = [],
  ): FindOptions<AssetFlow, S>['populate'] {
    const populateString: string[] = [
      'asset.details.portfolioDetails',
      'asset.details.rrtDetails',
      'asset.details.vintageDetails',
      'transaction.details',
    ];
    if (relations.includes(AssetFlowRelation.DESTINATION)) {
      populateString.push(AssetFlowRelation.DESTINATION);
    }
    if (relations.includes(AssetFlowRelation.LINKS)) {
      populateString.push(AssetFlowRelation.LINKS);
    }
    if (relations.includes(AssetFlowRelation.SOURCE)) {
      populateString.push(AssetFlowRelation.SOURCE);
    }
    return populateString as unknown as FindOptions<AssetFlow, S>['populate'];
  }

  /**
   * populate internal asset flow relations and return InternalAssetFlowResponse array (private helper)
   *
   * steps:
   * 1. find related Purchase, Retirement, Trade, Transfer, Book, and ProjectVintage entities if needed
   * 2. for each AssetFlow, find detailedAsset and related objects
   * 3. throw InternalServerErrorException if detailedAsset not found
   * 4. return array of InternalAssetFlowResponse
   *
   * @param {EntityManager} tx
   * @param {AssetFlow[]} assetFlows
   * @param {AssetFlowRelation[]} includeRelations
   * @returns {Promise<InternalAssetFlowResponse[]>}
   * @throws InternalServerErrorException
   */
  private async privatePopulateInternalAssetFlowRelations(
    tx: EntityManager,
    assetFlows: AssetFlow[],
    includeRelations: AssetFlowRelation[] = [],
  ): Promise<InternalAssetFlowResponse[]> {
    const relatedPurchases = includeRelations.includes(AssetFlowRelation.RELATED_OBJECT)
      ? await tx.find(Purchase, { id: assetFlows.map((m) => m.transaction.id) })
      : [];
    const relatedRetirements = includeRelations.includes(AssetFlowRelation.RELATED_OBJECT)
      ? await tx.find(Retirement, { id: assetFlows.map((m) => m.transaction.id) })
      : [];
    const relatedTrades = includeRelations.includes(AssetFlowRelation.RELATED_OBJECT)
      ? await tx.find(Trade, { id: assetFlows.map((m) => m.transaction.id) })
      : [];
    const relatedTransfers = includeRelations.includes(AssetFlowRelation.RELATED_OBJECT)
      ? await tx.find(Transfer, { id: assetFlows.map((m) => m.transaction.id) })
      : [];
    const assetBooks = await tx.find(Book, { id: assetFlows.map((m) => m.asset.id) });
    const assetVintages = await tx.find(
      ProjectVintage,
      { id: assetFlows.map((m) => m.asset.id) },
      { populate: ['project.country', 'project.projectType'] },
    );

    return assetFlows.map((af) => {
      // todo (RBC-3163) : fix and probably make shared helper
      const detailedAsset =
        af.assetType === AssetType.RCT
          ? assetBooks.find((book) => book.id === af.asset.id)
          : af.assetType === AssetType.REGISTRY_VINTAGE
            ? assetVintages.find((pv) => pv.id === af.asset.id)
            : undefined;
      if (!detailedAsset) {
        throw new InternalServerErrorException(`could not found detailed asset for ${af.assetType} ${af.asset.id}`);
      }
      return {
        assetFlow: af,
        detailedAsset,
        purchases: relatedPurchases.find((f) => f.id === af.transaction.id),
        retirements: relatedRetirements.find((f) => f.id === af.transaction.id),
        trade: relatedTrades.find((f) => f.id === af.transaction.id),
        transfer: relatedTransfers.find((f) => f.id === af.transaction.id),
      };
    });
  }

  /**
   * set order by options for AssetFlow queries (private helper)
   *
   * steps:
   * 1. initialize order by array
   * 2. for each orderBy in orderBys, push corresponding order by object to array
   * 3. return order by array
   *
   * @param {BaseAssetFlowOrderByDTO[]} orderBys
   * @returns {any}
   */
  private privateSetAssetFlowOrderBys(orderBys: BaseAssetFlowOrderByDTO[]): any {
    const x = [];
    for (const ob of orderBys) {
      if (ob.orderBy === AssetFlowOrderByOptions.ASSET_ID) {
        x.push({ asset: { id: OrderByDirectionSQL[ob.orderByDirection] } });
      }
      if (ob.orderBy === AssetFlowOrderByOptions.CREATED_AT) {
        x.push({ createdAt: OrderByDirectionSQL[ob.orderByDirection] });
      }
      if (ob.orderBy === AssetFlowOrderByOptions.TRANSACTION_ID) {
        x.push({ transaction: { id: OrderByDirectionSQL[ob.orderByDirection] } });
      }
      if (ob.orderBy === AssetFlowOrderByOptions.UPDATED_AT) {
        x.push({ updatedAt: OrderByDirectionSQL[ob.orderByDirection] });
      }
    }

    return x;
  }
}
