/* third party */
import { EntityManager } from '@mikro-orm/postgresql';
import { Injectable, InternalServerErrorException, UnprocessableEntityException } from '@nestjs/common';
import Decimal from 'decimal.js';
/* rubicon */
import {
  AdminGroupedPriceResponse,
  AllAssetTypes,
  AssetType,
  BookRelations,
  BookType,
  GroupingRelations,
  ProjectRelations,
  ProjectTypeRelations,
  ProjectVintageRelations,
  uuid,
} from '@rubiconcarbon/shared-types';
/* app */
import {
  InternalAllocationResponse,
  InternalAssetGroupedAllocationWithNestedResponse,
  InternalAssetTypeGroupedAllocationsResponse,
  InternalBookTypeGroupedAllocationsResponse,
  InternalGroupedAllocationsResponse,
  InternalHolding,
  InternalProjectGroupedAllocationsResponse,
  InternalProjectTypeGroupedAllocationsResponse,
} from '@app/interfaces/allocation.interface';
import { AssetsService } from '@app/services/assets.service';
import { InternalProjectTypeResponse } from '@app/interfaces/project-type.interface';
import { InternalProjectResponse } from '@app/interfaces/project.interface';
import { InternalProjectVintageResponse } from '@app/interfaces/project-vintage.interface';
import { Asset, AssetDetail, Book, Grouping, Organization, Project, ProjectType, ProjectVintage } from '@app/entities';
import { InternalBookResponse } from '@app/interfaces/book.interface';
import { InternalGroupingParent } from '@app/interfaces/grouping-parent.interface';
import { ProjectVintageGrouping } from '@app/entities/project-vintage-groupings';
import { groupBy } from '@app/helpers';

@Injectable()
export class InternalPopulateAllocationsService {
  constructor(private assetsService: AssetsService) {}

  async internalPopulatePurchasableAssetsAllocations(
    tx: EntityManager,
    organization: Organization,
    assetTypes?: AssetType[],
  ): Promise<InternalAssetGroupedAllocationWithNestedResponse[]> {
    const response: InternalAssetGroupedAllocationWithNestedResponse[] = [];
    // organization is not configured to allow purchases so return no assets
    if (!organization.customerPortfolio || !organization.customerPortfolio.isEnabled) {
      return response;
    }

    const purchasableAssets = await this.assetsService.internalGetPurchasableAssets(
      tx,
      organization.customerPortfolio,
      assetTypes,
    );
    const holdingsByAssetId: Map<uuid, InternalHolding[]> = groupBy(purchasableAssets, (x) => {
      return x.asset_id;
    });

    const assets = await tx.find(Asset, { id: [...holdingsByAssetId.keys()] });

    for (const asset of assets) {
      const assetHoldings = holdingsByAssetId.get(asset.id) || [];
      const allocations = await this.privatePopulateAllocationsResponse(tx, assetHoldings);
      response.push({
        ...this.privatePopulateGroupedAllocationsResponse(assetHoldings),
        allocations,
        asset,
      });
    }

    return response;
  }

  async internalPopulateBookAllocations(
    tx: EntityManager,
    book: InternalBookResponse,
    assetTypes: AssetType[] = AllAssetTypes,
    includeRelations: BookRelations[] = [],
  ): Promise<InternalBookResponse> {
    const populatedBooks = await this.internalPopulateBooksAllocations(tx, [book], assetTypes, includeRelations);
    if (populatedBooks.length !== 1 || populatedBooks[0].id !== book.id) {
      console.error(`book ${book.id} did not populate as expected : ` + JSON.stringify(populatedBooks));
      throw new InternalServerErrorException();
    }
    return populatedBooks[0];
  }

  async internalPopulateBooksAllocations(
    tx: EntityManager,
    books: InternalBookResponse[],
    assetTypes: AssetType[] = AllAssetTypes,
    includeRelations: BookRelations[] = [],
  ): Promise<InternalBookResponse[]> {
    if (books.length === 0) {
      return books;
    }
    const getAssetHoldings: boolean = [
      BookRelations.ASSET_ALLOCATIONS,
      BookRelations.ASSET_ALLOCATIONS_NESTED,
      BookRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE,
    ].some((s) => includeRelations.includes(s));
    const getOwnerHoldings: boolean = [
      BookRelations.OWNER_ALLOCATIONS,
      BookRelations.OWNER_ALLOCATIONS_NESTED,
      BookRelations.OWNER_ALLOCATIONS_BY_ASSET_TYPE,
      BookRelations.OWNER_ALLOCATIONS_BY_PROJECT,
      BookRelations.OWNER_ALLOCATIONS_BY_PROJECT_TYPE,
    ].some((s) => includeRelations.includes(s));

    const bookAssets: AssetDetail[] = getAssetHoldings
      ? await tx.find(AssetDetail, { portfolioId: books.map((m) => m.id) })
      : [];
    const assetHoldings: InternalHolding[] = getAssetHoldings
      ? await this.assetsService.internalGetAssetHoldings(tx.getKnex(), {
          assetIds: bookAssets.map((m) => m.id),
          assetTypes,
          includePrices: includeRelations.includes(BookRelations.PRICES),
        })
      : [];

    const ownerHoldings: InternalHolding[] = getOwnerHoldings
      ? await this.assetsService.internalGetOwnerHoldings(tx.getKnex(), {
          assetTypes,
          ownerIds: books.map((m) => m.id),
          includePrices: includeRelations.includes(BookRelations.PRICES),
        })
      : [];

    for (const book of books) {
      const bookAssetHoldings = assetHoldings.filter((f) =>
        bookAssets
          .filter((f) => f.portfolioId === book.id)
          .map((m) => m.id)
          .includes(f.asset_id),
      );

      const bookOwnerHoldings = ownerHoldings.filter((f) => f.owner_id === book.id);
      book.assetAllocations = this.privatePopulateGroupedAllocationsResponse(bookAssetHoldings);
      book.ownerAllocations = this.privatePopulateGroupedAllocationsResponse(bookOwnerHoldings);
      if (includeRelations.includes(BookRelations.PRICES)) {
        book.assetAllocations.groupedPrices = this.privatePopulateGroupedPricesResponse(bookAssetHoldings);
        book.ownerAllocations.groupedPrices = this.privatePopulateGroupedPricesResponse(bookOwnerHoldings);
      }
      if (includeRelations.includes(BookRelations.ASSET_ALLOCATIONS_NESTED)) {
        book.assetAllocations.allocations = await this.privatePopulateAllocationsResponse(tx, bookAssetHoldings);
      }
      if (includeRelations.includes(BookRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE)) {
        book.assetAllocationsByBookType = this.privatePopulateBookTypeGroupedAllocationsResponse(
          includeRelations.includes(BookRelations.PRICES),
          bookAssetHoldings,
        );
      }
      if (includeRelations.includes(BookRelations.OWNER_ALLOCATIONS_NESTED)) {
        book.ownerAllocations.allocations = await this.privatePopulateAllocationsResponse(tx, bookOwnerHoldings);
      }
      if (includeRelations.includes(BookRelations.OWNER_ALLOCATIONS_BY_ASSET_TYPE)) {
        book.ownerAllocationsByAssetType = this.privatePopulateAssetTypeGroupedAllocationsResponse(
          includeRelations.includes(BookRelations.PRICES),
          bookOwnerHoldings,
        );
      }
      if (includeRelations.includes(BookRelations.OWNER_ALLOCATIONS_BY_PROJECT)) {
        book.ownerAllocationsByProject = await this.privatePopulateProjectGroupedAllocationsResponse(
          tx,
          includeRelations.includes(BookRelations.PRICES),
          bookOwnerHoldings,
        );
      }
      if (includeRelations.includes(BookRelations.OWNER_ALLOCATIONS_BY_PROJECT_TYPE)) {
        const isPortfolio = [BookType.RCT_CUSTOM.toString(), BookType.RCT_PUBLIC.toString()].includes(book.type);
        if (isPortfolio && !book.projectTypes.isInitialized()) {
          // rct portfolios have their own projectTypes that are allowed for that portfolio,
          // need to return all allowed projectTypes, not just the allocated ones
          await tx.populate(book, ['projectTypes']);
        }

        const bookSpecificProjectTypes = isPortfolio ? book.projectTypes.getItems() : undefined;
        book.ownerAllocationsByProjectType = await this.privatePopulateProjectTypeGroupedAllocationsResponse(
          tx,
          includeRelations.includes(BookRelations.PRICES),
          bookOwnerHoldings,
          bookSpecificProjectTypes,
        );
      }
    }

    return books;
  }

  async internalPopulateGroupedParentAllocations(
    tx: EntityManager,
    parents: InternalGroupingParent[],
    includeRelations: GroupingRelations[],
  ): Promise<InternalGroupingParent[]> {
    const includePrices = includeRelations.includes(GroupingRelations.PRICES);

    const groupingsMap = new Map<uuid, Book[]>();
    let allBooks: Book[] = [];
    parents.forEach((p) => {
      const grouping: Grouping[] = p.groupings.getItems();
      const books = grouping.map((m) => m.book);
      groupingsMap.set(p.id, books);
      allBooks = [...allBooks, ...books];
    });

    // get holdings for all sub books
    const holdings: InternalHolding[] = await this.assetsService.internalGetOwnerHoldings(tx.getKnex(), {
      ownerIds: allBooks.map((m) => m.id),
      includePrices,
    });

    // group by parents
    for (const gp of parents) {
      const gpHoldings = holdings.filter((f) =>
        groupingsMap
          .get(gp.id)
          ?.map((m) => m.id)
          .includes(f.owner_id),
      );
      gp.ownerAllocations = this.privatePopulateGroupedAllocationsResponse(gpHoldings);
      if (includeRelations.includes(GroupingRelations.OWNER_ALLOCATIONS_BY_ASSET_TYPE)) {
        // I think forcing both asset types makes the most sense but maybe remove this if it seems wrong
        gp.ownerAllocationsByAssetType = this.privatePopulateAssetTypeGroupedAllocationsResponse(
          includePrices,
          gpHoldings,
          AllAssetTypes,
        );
      }
      if (includePrices) {
        gp.ownerAllocations.groupedPrices = this.privatePopulateGroupedPricesResponse(gpHoldings);
      }
      gp.ownerAllocations.allocations = await this.privatePopulateAllocationsResponse(tx, gpHoldings);
    }

    return parents;
  }

  async internalPopulateProjectsAllocations(
    tx: EntityManager,
    projects: InternalProjectResponse[],
    projectLedgerAssets: { project_vintage_id: uuid; project_id: uuid }[],
    includeRelations: ProjectRelations[],
  ): Promise<InternalProjectResponse[]> {
    // get asset holdings for all project vintages
    const assetHoldings: InternalHolding[] = await this.assetsService.internalGetAssetHoldings(tx.getKnex(), {
      assetIds: projectLedgerAssets.map((m) => m.project_vintage_id),
      includePrices: false,
    });

    for (const project of projects) {
      const projectAssets = projectLedgerAssets.filter((f) => f.project_id === project.id);
      const projectHoldings = assetHoldings.filter((f) =>
        projectAssets.map((pv) => pv.project_vintage_id).includes(f.asset_id),
      );
      const groupedResponse = this.privatePopulateGroupedAllocationsResponse(projectHoldings);
      project.assetAllocations = groupedResponse;
      project.assetAllocationsByBookType = includeRelations.includes(ProjectRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE)
        ? this.privatePopulateBookTypeGroupedAllocationsResponse(false, projectHoldings)
        : undefined;
    }
    return projects;
  }

  async internalPopulateProjectTypeAllocations(
    tx: EntityManager,
    projectTypes: InternalProjectTypeResponse[],
    projectTypeLedgerAssets: { project_type_id: number; project_vintage_id: uuid }[],
    includeRelations: ProjectTypeRelations[],
  ): Promise<InternalProjectTypeResponse[]> {
    // get project vintages and asset holdings for all projects
    const assetHoldings: InternalHolding[] = await this.assetsService.internalGetAssetHoldings(tx.getKnex(), {
      assetIds: projectTypeLedgerAssets.map((m) => m.project_vintage_id),
      includePrices: false,
    });

    for (const pt of projectTypes) {
      const ptPvs = projectTypeLedgerAssets.filter((f) => f.project_type_id === pt.id);
      const ptHoldings = assetHoldings.filter((f) => ptPvs.map((pv) => pv.project_vintage_id).includes(f.asset_id));
      pt.assetAllocations = this.privatePopulateGroupedAllocationsResponse(ptHoldings);
      pt.assetAllocationsByBookType = includeRelations.includes(ProjectTypeRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE)
        ? this.privatePopulateBookTypeGroupedAllocationsResponse(false, ptHoldings)
        : undefined;
    }

    return projectTypes;
  }

  async internalPopulateProjectVintagesAllocations(
    tx: EntityManager,
    projectVintages: InternalProjectVintageResponse[],
    includeRelations: ProjectVintageRelations[],
  ): Promise<InternalProjectVintageResponse[]> {
    const includePrices = includeRelations.includes(ProjectVintageRelations.PRICES);
    if (includePrices) {
      await tx.populate(projectVintages, ['prices']);
    }

    // get asset holdings for project vintages
    const assetHoldings: InternalHolding[] = await this.assetsService.internalGetAssetHoldings(tx.getKnex(), {
      assetIds: projectVintages.map((m) => m.id),
      includePrices: false, // never include prices in the holdings since it is retrieved already
    });

    for (const pv of projectVintages) {
      const pvHoldings = assetHoldings.filter((f) => f.asset_id === pv.id);
      const groupedResponse = this.privatePopulateGroupedAllocationsResponse(pvHoldings);
      pv.assetAllocations = groupedResponse;
      pv.assetAllocationsByBookType = includeRelations.includes(ProjectVintageRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE)
        ? this.privatePopulateBookTypeGroupedAllocationsResponse(includePrices, pvHoldings)
        : undefined;
    }

    return projectVintages;
  }

  /* private */

  private async privatePopulateAllocationsResponse(
    tx: EntityManager,
    groupedAssetHoldings: InternalHolding[],
  ): Promise<InternalAllocationResponse[]> {
    const assets = await tx.find(
      Asset,
      { id: groupedAssetHoldings.map((m) => m.asset_id) },
      { populate: ['details.portfolioDetails', 'details.rrtDetails', 'details.vintageDetails'] },
    );
    const assetBooks = await tx.find(Book, { id: groupedAssetHoldings.map((m) => m.asset_id) });
    const assetVintages = await tx.find(
      ProjectVintage,
      { id: groupedAssetHoldings.map((m) => m.asset_id) },
      { populate: ['project.country', 'project.projectType', 'project.projectSDGs.sdgType'] },
    );
    const owners = await tx.find(Book, { id: groupedAssetHoldings.map((m) => m.owner_id) });

    return groupedAssetHoldings.map((m) => {
      const asset = assets.find((f) => f.id === m.asset_id);
      if (!asset) {
        // should never happen
        throw new UnprocessableEntityException(`could not find ${m.asset_type} asset ${m.asset_id}`);
      }
      const owner = owners.find((f) => f.id === m.owner_id);
      if (!owner) {
        // should never happen
        throw new UnprocessableEntityException(`could not find ${m.owner_type} owner ${m.owner_id}`);
      }
      const response = {
        amountAllocated: m.holdings,
        amountAvailable: m.holdings - m.pending_outflow,
        amountCustomerTransferredOutflow: m.holdings_customer_transferred_outflow,
        amountPendingBuy: m.pending_buy_inflow,
        amountPendingCustomerTransferOutflow: m.pending_customer_transfer_outflow,
        amountPendingPurchase: m.pending_purchase_outflow, // todo (td-95) : does this need to be inflow for customer portfolio?
        amountPendingRetirement: m.pending_retirement_outflow,
        amountPendingSell: m.pending_sell_outflow,
        amountRetired: m.holdings_retired_outflow,
        amountSold: m.holdings_sold_outflow,
        averageCostBasis: m.average_cost_basis || undefined,
        currentPrice: m.price || undefined,
        asset,
        // todo (RBC-3163) : FIX
        detailedAsset:
          asset.type === AssetType.RCT
            ? assetBooks.find((f) => f.id === asset.id)
            : asset.type === AssetType.REGISTRY_VINTAGE
              ? assetVintages.find((f) => f.id === asset.id)
              : undefined,
        owner,
      };

      if (response.asset.type === AssetType.REGISTRY_VINTAGE && m.average_cost_basis) {
        (response.detailedAsset as ProjectVintage).prices!.average_cost_basis = m.average_cost_basis;
      }
      if (response.asset.type === AssetType.REGISTRY_VINTAGE && m.price) {
        (response.detailedAsset as ProjectVintage).prices!.price = m.price;
      }

      return response;
    });
  }

  private privatePopulateAssetTypeGroupedAllocationsResponse(
    includePrices: boolean,
    groupedAssetHoldings: InternalHolding[],
    assetTypes?: AssetType[],
  ): InternalAssetTypeGroupedAllocationsResponse[] {
    const response: InternalAssetTypeGroupedAllocationsResponse[] = [];
    assetTypes = assetTypes || [...new Set(groupedAssetHoldings.map((m) => m.asset_type))];

    for (const at of assetTypes) {
      const assetTypeHoldings = groupedAssetHoldings.filter((f) => f.asset_type === at);

      response.push({
        ...this.privatePopulateGroupedAllocationsResponse(assetTypeHoldings),
        groupedPrices: includePrices ? this.privatePopulateGroupedPricesResponse(assetTypeHoldings) : undefined,
        assetType: at,
      });
    }

    return response;
  }

  private privatePopulateBookTypeGroupedAllocationsResponse(
    includePrices: boolean,
    groupedAssetHoldings: InternalHolding[],
    bookTypes?: BookType[],
  ): InternalBookTypeGroupedAllocationsResponse[] {
    const response: InternalBookTypeGroupedAllocationsResponse[] = [];
    bookTypes = bookTypes || [...new Set(groupedAssetHoldings.map((m) => m.owner_type))];

    for (const bt of bookTypes) {
      const bookTypeHoldings = groupedAssetHoldings.filter((f) => f.owner_type === bt);

      response.push({
        ...this.privatePopulateGroupedAllocationsResponse(bookTypeHoldings),
        groupedPrices: includePrices ? this.privatePopulateGroupedPricesResponse(bookTypeHoldings) : undefined,
        bookType: bt,
      });
    }

    return response;
  }

  private async privatePopulateProjectGroupedAllocationsResponse(
    tx: EntityManager,
    includePrices: boolean,
    groupedAssetHoldings: InternalHolding[],
    projects?: Project[],
  ): Promise<InternalProjectGroupedAllocationsResponse[]> {
    // get vintages by project
    const projectVintagesGroupings: ProjectVintageGrouping[] = await tx.find(
      ProjectVintageGrouping,
      { project_vintage_id: groupedAssetHoldings.map((m) => m.asset_id) },
      { populate: ['project.country', 'project.projectSDGs.sdgType', 'project.projectType', 'project.registry'] },
    );

    // map vintages to project object
    const byProject: Map<Project, ProjectVintageGrouping[]> = groupBy(projectVintagesGroupings, (x) => {
      return x.project;
    });
    // projects = projects from parameter or the byProject keys
    projects = projects || [...byProject.keys()];

    const response: InternalProjectGroupedAllocationsResponse[] = [];
    // for each project, get holdings for project and group
    for (const p of projects) {
      const projectHoldings = groupedAssetHoldings.filter((f) =>
        (byProject.get(p) || []).map((m) => m.project_vintage_id).includes(f.asset_id),
      );

      response.push({
        ...this.privatePopulateGroupedAllocationsResponse(projectHoldings),
        groupedPrices: includePrices ? this.privatePopulateGroupedPricesResponse(projectHoldings) : undefined,
        project: p,
      });
    }

    return response;
  }

  private async privatePopulateProjectTypeGroupedAllocationsResponse(
    tx: EntityManager,
    includePrices: boolean,
    groupedAssetHoldings: InternalHolding[],
    projectTypes?: ProjectType[],
  ): Promise<InternalProjectTypeGroupedAllocationsResponse[]> {
    // get vintages by projectType
    const projectVintagesGroupings: ProjectVintageGrouping[] = await tx.find(
      ProjectVintageGrouping,
      { project_vintage_id: groupedAssetHoldings.map((m) => m.asset_id) },
      { populate: ['projectType'] },
    );

    // map vintages to projectType object
    const byProjectType: Map<ProjectType, ProjectVintageGrouping[]> = groupBy(projectVintagesGroupings, (x) => {
      return x.projectType;
    });

    // projectTypes = projectTypes from parameter or the byProjectType keys
    projectTypes = projectTypes || [...byProjectType.keys()];

    const response: InternalProjectTypeGroupedAllocationsResponse[] = [];
    // for each projectType, get holdings for projectType and group
    for (const pt of projectTypes) {
      const projectTypeHoldings = groupedAssetHoldings.filter((f) =>
        (byProjectType.get(pt) || []).map((m) => m.project_vintage_id).includes(f.asset_id),
      );

      response.push({
        ...this.privatePopulateGroupedAllocationsResponse(projectTypeHoldings),
        groupedPrices: includePrices ? this.privatePopulateGroupedPricesResponse(projectTypeHoldings) : undefined,
        projectType: pt,
      });
    }

    return response;
  }

  private privatePopulateGroupedAllocationsResponse(
    groupedAssetHoldings: InternalHolding[],
  ): InternalGroupedAllocationsResponse {
    return {
      totalAmountAllocated: Decimal.sum(...groupedAssetHoldings.map((m) => m.holdings), 0).toNumber(),
      totalAmountAvailable: Decimal.sum(
        ...groupedAssetHoldings.map((m) => m.holdings - m.pending_outflow),
        0,
      ).toNumber(),
      totalAmountCustomerTransferredOutflow: Decimal.sum(
        ...groupedAssetHoldings.map((m) => m.holdings_customer_transferred_outflow),
        0,
      ).toNumber(),
      totalAmountPendingBuy: Decimal.sum(...groupedAssetHoldings.map((m) => m.pending_buy_inflow), 0).toNumber(),
      totalAmountPendingCustomerTransferOutflow: Decimal.sum(
        ...groupedAssetHoldings.map((m) => m.pending_customer_transfer_outflow),
        0,
      ).toNumber(),
      totalAmountPendingPurchase: Decimal.sum(
        ...groupedAssetHoldings.map((m) => m.pending_purchase_outflow),
        0,
      ).toNumber(),
      totalAmountPendingRetirement: Decimal.sum(
        ...groupedAssetHoldings.map((m) => m.pending_retirement_outflow),
        0,
      ).toNumber(),
      totalAmountPendingSell: Decimal.sum(...groupedAssetHoldings.map((m) => m.pending_sell_outflow), 0).toNumber(),
      totalAmountRetired: Decimal.sum(...groupedAssetHoldings.map((m) => m.holdings_retired_outflow), 0).toNumber(),
      totalAmountSold: Decimal.sum(...groupedAssetHoldings.map((m) => m.holdings_sold_outflow), 0).toNumber(),
    };
  }

  private privatePopulateGroupedPricesResponse(
    groupedAllocations: InternalHolding[],
  ): AdminGroupedPriceResponse | undefined {
    const nullPrices = groupedAllocations.filter(
      (allocation) => allocation.price == null && (allocation.holdings !== 0 || allocation.pending_inflow !== 0),
    );
    if (nullPrices.length > 0) {
      console.warn(`allocations have null prices, so not returning prices : + ${JSON.stringify(nullPrices)}`);
      return;
    }

    groupedAllocations = groupedAllocations.filter((f) => f.holdings !== 0 || f.pending_inflow !== 0);
    return {
      totalPriceAllocated: Decimal.sum(...groupedAllocations.map((m) => new Decimal(m.holdings).mul(m.price!)), 0),
      totalPriceAvailable: Decimal.sum(
        ...groupedAllocations.map((m) => new Decimal(m.holdings - m.pending_outflow).mul(m.price!)),
        0,
      ),
      totalPriceCustomerTransferredOutflow: Decimal.sum(
        ...groupedAllocations.map((m) => new Decimal(m.holdings_customer_transferred_outflow).mul(m.price!)),
        0,
      ),
      totalPricePendingBuy: Decimal.sum(
        ...groupedAllocations.map((m) => new Decimal(m.pending_buy_inflow).mul(m.price!)),
        0,
      ),
      totalPricePendingCustomerTransferOutflow: Decimal.sum(
        ...groupedAllocations.map((m) => new Decimal(m.pending_customer_transfer_outflow).mul(m.price!)),
        0,
      ),
      totalPricePendingPurchase: Decimal.sum(
        ...groupedAllocations.map((m) => new Decimal(m.pending_purchase_outflow).mul(m.price!)),
        0,
      ),
      totalPricePendingRetirement: Decimal.sum(
        ...groupedAllocations.map((m) => new Decimal(m.pending_retirement_outflow).mul(m.price!)),
        0,
      ),
      totalPricePendingSell: Decimal.sum(
        ...groupedAllocations.map((m) => new Decimal(m.pending_sell_outflow).mul(m.price!)),
        0,
      ),
      totalPriceRetired: Decimal.sum(
        ...groupedAllocations.map((m) => new Decimal(m.holdings_retired_outflow).mul(m.price!)),
        0,
      ),
      totalPriceSold: Decimal.sum(
        ...groupedAllocations.map((m) => new Decimal(m.holdings_sold_outflow).mul(m.price!)),
        0,
      ),
    };
  }
}
