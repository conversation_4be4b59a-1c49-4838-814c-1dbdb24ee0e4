/* third party */
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RequiredEntityData } from '@mikro-orm/postgresql';
import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
/* rubicon */
import {
  AssetType,
  AuditLogAction,
  ModelPortfolioRelations,
  NotificationEvent,
  PermissionEnum,
  uuid,
} from '@rubiconcarbon/shared-types';
/* env */
import { UserClaims } from '@app/auth/interfaces';
import {
  AdminModelPortfolioComponentRequestDTO,
  AdminModelPortfolioCreateRequestDTO,
  AdminModelPortfolioUpdateRequestDTO,
  BaseModelPortfolioComponentCreateDTO,
  BaseModelPortfolioComponentUpdateDTO,
  ModelPortfolioBookComponentCreateDTO,
  ModelPortfolioVintageComponentCreateDTO,
  ModelPortfolioVintageComponentUpdateDTO,
  PortalModelPortfolioResponseDTO,
} from '@app/dtos/model-portfolio.dto';
import {
  Asset,
  Book,
  ModelPortfolio,
  ModelPortfolioComponent,
  Organization,
  OrganizationUser,
  Project,
  ProjectVintage,
  User,
} from '@app/entities';
import { ModelPortfolioOrderBySQL, OrderByDirectionSQL } from '@app/enums/orderby.enum';
import {
  InternalModelPortfolio,
  InternalModelPortfolioQuery,
  InternalModelPortfolioQueryResponse,
} from '@app/interfaces/model-portfolio.interface';
import { AuditLogsService } from '@app/utility/audit-log';
import Decimal from 'decimal.js';
import { environment } from '@env/environment';
import { generateRandomKey, getPortalUserWithOrganizationFromClaims, returnEmptyQueryResponse } from '@app/helpers';
import { AuthService } from '@app/auth';
import { NotificationsService } from './notifications.service';
import { InternalModelPortfolioCreated, InternalModelPortfolioUpdated } from '@app/dtos/sendgrid-payload.dto';
import { BufferCategory } from '@app/entities/buffer-category.entity';
import { InternalPortalUser } from '@app/interfaces/user.interface';

// aka portfolio sandbox
// also created when byorct is requested by a customer through the byorct.service
@Injectable()
export class ModelPortfoliosService {
  constructor(
    private readonly auditLogsService: AuditLogsService,
    private readonly em: EntityManager,
    private readonly authService: AuthService,
    private readonly notificationsService: NotificationsService,
  ) {}

  /**
   * append project and vintage information to PortalModelPortfolioResponseDTO
   *
   * steps:
   * 1. get knex instance from EntityManager
   * 2. build projects and vintages arrays from modelPortfolioComponents
   * 3. query project_flags_v2 and project_vintage_amounts_v3 for hasBalance
   * 4. update modelPortfolioComponents with projectPipeline and vintagePipeline
   * 5. return updated PortalModelPortfolioResponseDTO
   *
   * @param {PortalModelPortfolioResponseDTO} dto
   * @returns {Promise<PortalModelPortfolioResponseDTO>}
   */
  // todo (TD-105) : add to components during service call instead of after
  async appendProjectAndVintageInformation(
    dto: PortalModelPortfolioResponseDTO,
  ): Promise<PortalModelPortfolioResponseDTO> {
    const knex = this.em.getKnex();

    const projectsMap = new Map<uuid | undefined, boolean>();
    const vintagesMap = new Map<uuid | undefined, boolean>();

    const projectIds =
      (dto.modelPortfolioComponents
        ?.filter((m) => m.type === 'vintage' || m.type === 'custom')
        .map((m) => (m.type === 'vintage' || m.type === 'custom' ? m.project.id : undefined)) as uuid[]) || [];
    const projectVintageIds =
      (dto.modelPortfolioComponents
        ?.filter((m) => m.type === 'vintage')
        .map((m) => (m.type === 'vintage' ? m.projectVintage.id : undefined)) as uuid[]) || [];

    if (projectIds.length > 0) {
      (
        await knex
          .select('id', 'has_balance as hasBalance')
          .from(`${environment.db.schema.rubicon}.project_flags_v2`)
          .whereIn('id', projectIds)
      ).forEach((x) => {
        projectsMap.set(x.id, x.hasBalance);
      });
    }

    if (projectVintageIds.length > 0) {
      (
        await knex
          .select('asset_id as id')
          .column(knex.raw('holdings > 0 as "hasBalance"'))
          .from(`${environment.db.schema.rubicon}.project_vintage_amounts_v3`)
          .whereIn('asset_id', projectVintageIds)
      ).forEach((x) => {
        vintagesMap.set(x.id, x.hasBalance);
      });
    }

    // todo : double check pipelines
    dto.modelPortfolioComponents?.forEach((x) =>
      Object.assign(x, {
        projectPipeline: (x.type === 'vintage' || x.type === 'custom') && !projectsMap.get(x.project.id),
        vintagePipeline: (x.type === 'vintage' && !vintagesMap.get(x.projectVintage?.id)) || x.type === 'custom', // double check if vintagePipeline is needed
      }),
    );

    return dto;
  }

  /**
   * create a ModelPortfolio entity for the customer portal (including BYO and RCTs)
   *
   * steps:
   * 1. generate id and get InternalPortalUser from UserClaims
   * 2. check name uniqueness per Organization
   * 3. create ModelPortfolio entity with provided data
   * 4. create InternalModelPortfolioCreated payload and send notification
   * 5. create AuditLogsService entry for MODEL_PORTFOLIO_CREATED
   * 6. return created ModelPortfolio entity
   *
   * @param {EntityManager} tx
   * @param {UserClaims} claims
   * @param {Date} now
   * @param {boolean} includeRiskAdjustment
   * @param {string} name
   * @param {Decimal} priceEstimate
   * @returns {Promise<ModelPortfolio>}
   * @throws ConflictException
   */
  async createModelPortfolioFromPortal(
    tx: EntityManager,
    claims: UserClaims,
    now: Date,
    includeRiskAdjustment: boolean,
    name?: string,
    priceEstimate?: Decimal,
  ): Promise<ModelPortfolio> {
    const id: uuid = uuid();
    const portalUser: InternalPortalUser = await getPortalUserWithOrganizationFromClaims(tx, claims);

    const modelPortfolioData: RequiredEntityData<ModelPortfolio> = {
      name: await this.privateGetOrValidateModelPortfolioName(tx, name, portalUser.organizationUser.organization),
      groupingId: id,
      id: id,
      uiKey: generateRandomKey('BYO'),
      priceEstimate: priceEstimate,
      organization: portalUser.organizationUser.organization,
      rfp: false,
      showCustomer: true,
      includeRiskAdjustment,
      createdAt: now,
      updatedAt: now,
      createdBy: portalUser,
      updatedBy: portalUser,
    };

    return this.privateCreateModelPortfolio(tx, claims, modelPortfolioData);
  }

  /**
   * create a ModelPortfolio entity from admin
   *
   * steps:
   * 1. start a database transaction
   * 2. generate id and get User entity from claims
   * 3. find Organization entity if organizationId provided
   * 4. check organizationId validity
   * 5. check groupingId consistency for Organization
   * 6. create ModelPortfolio entity with provided data
   * 7. create InternalModelPortfolioCreated payload and send notification
   * 8. create AuditLogsService entry for MODEL_PORTFOLIO_CREATED
   * 9. return created ModelPortfolio entity
   *
   * @param {UserClaims} claims
   * @param {AdminModelPortfolioCreateRequestDTO} data
   * @returns {Promise<ModelPortfolio>}
   * @throws UnprocessableEntityException
   */
  async createModelPortfolioFromAdmin(
    claims: UserClaims,
    data: AdminModelPortfolioCreateRequestDTO,
  ): Promise<ModelPortfolio> {
    return await this.em.transactional(async (tx) => {
      const now = new Date();
      const id: uuid = uuid();
      const user = await tx.findOneOrFail(User, { email: claims.email });

      const organization = data.organizationId ? await tx.findOne(Organization, data.organizationId) : undefined;
      if (data.organizationId && !organization) {
        throw new UnprocessableEntityException(`organizationId ${data.organizationId} must be a valid uuid`);
      }

      // check that the only modelPortfolio orgs are all the same
      if (organization && data.groupingId) {
        const groupedPortfolios = await tx.find(
          ModelPortfolio,
          { groupingId: data.groupingId },
          { populate: ['organization'] },
        );
        groupedPortfolios.forEach((portfolio) => {
          if (portfolio.organization && portfolio.organization !== organization) {
            throw new UnprocessableEntityException(
              `cannot group portfolio for organization ${organization.name} with portfolios for organization ${portfolio.organization.name}`,
            );
          }
        });
      }

      const modelPortfolioData: RequiredEntityData<ModelPortfolio> = {
        ...data,
        name: await this.privateGetOrValidateModelPortfolioName(tx, data.name, organization || undefined),
        groupingId: data.groupingId || id,
        id: id,
        uiKey: generateRandomKey('BYO'),
        organization: data.organizationId || undefined,
        createdAt: now,
        updatedAt: now,
        createdBy: user,
        updatedBy: user,
      };

      return this.privateCreateModelPortfolio(tx, claims, modelPortfolioData);
    });
  }

  /**
   * delete a ModelPortfolio entity by id
   *
   * steps:
   * 1. start a database transaction
   * 2. get current date and User entity from claims
   * 3. find ModelPortfolio entity by id
   * 4. mark ModelPortfolio as deleted and update updatedAt and updatedBy
   * 5. create AuditLogsService entry for MODEL_PORTFOLIO_DELETED
   *
   * @param {UserClaims} claims
   * @param {uuid} id
   * @returns {Promise<void>}
   */
  async deleteModelPortfolio(claims: UserClaims, id: uuid): Promise<void> {
    await this.em.transactional(async (tx) => {
      const now = new Date();
      const user = await tx.findOneOrFail(User, { email: claims.email });
      const modelPortfolio = await tx.findOneOrFail(ModelPortfolio, { id });
      modelPortfolio.updatedAt = now;
      modelPortfolio.updatedBy = user;
      modelPortfolio.isDeleted = true;
      await this.auditLogsService.create(tx, claims, AuditLogAction.MODEL_PORTFOLIO_DELETED, now, id, {
        ...modelPortfolio,
      });
    });
  }

  /**
   * find many ModelPortfolio entities with filtering and pagination
   *
   * steps:
   * 1. get knex instance and InternalPortalUser if claims provided
   * 2. build knex query for model portfolio ids based on filters
   * 3. find ModelPortfolio entities by ids with pagination and relations
   * 4. return InternalModelPortfolioQueryResponse with data and page info
   *
   * @param {InternalModelPortfolioQuery} query
   * @param {UserClaims} claims
   * @returns {Promise<InternalModelPortfolioQueryResponse>}
   * @throws NotFoundException, InternalServerErrorException
   */
  async findManyModelPortfolios(
    query: InternalModelPortfolioQuery,
    claims?: UserClaims,
  ): Promise<InternalModelPortfolioQueryResponse> {
    const knex = this.em.getKnex();
    let portalUser: InternalPortalUser;

    if (claims || !query.isAdmin) {
      if (!claims) {
        throw new InternalServerErrorException(`claims must be passed in when called from portal endpoint`);
      }
      if (query.isAdmin) {
        throw new InternalServerErrorException(`claims must not be passed in when called from admin endpoint`);
      }
      try {
        portalUser = await getPortalUserWithOrganizationFromClaims(this.em, claims);
      } catch (e) {
        if (e instanceof NotFoundException) {
          throw e;
        }

        return {
          ...returnEmptyQueryResponse(query),
          data: [],
        };
      }
    }

    // get model portfolio ids that match query filters
    const knexQuery = knex
      .select(['mp.id as id'])
      .from(`${environment.db.schema.rubicon}.model_portfolios as mp`)
      .leftJoin(`${environment.db.schema.rubicon}.model_portfolio_components as mpc`, 'mpc.model_portfolio_id', 'mp.id')
      .leftJoin(`${environment.db.schema.rubicon}.projects as p`, 'p.id', 'mpc.project_id')
      .leftJoin(`${environment.db.schema.rubicon}.project_vintages as pv`, 'pv.id', 'mpc.project_vintage_id')
      .leftJoin(`${environment.db.schema.rubicon}.projects as pv_p`, 'pv_p.id', 'pv.project_id')
      .where((builder) => {
        builder.where({ 'mp.is_deleted': false });

        if (portalUser) {
          builder.where({ 'mp.organization_id': portalUser.organizationUser.organization.id });
          builder.where({ 'mp.show_customer': true });
        }

        if (query.isAdmin) {
          if (query.createdById) {
            builder.where({ 'mp.created_by_id': query.createdById });
          }
          if (query.organizationId) {
            builder.where({ 'mp.organization_id': query.organizationId });
          }
          if (query.updatedById) {
            builder.where({ 'mp.updated_by_id': query.updatedById });
          }
        }

        if (query.uiKey) {
          builder.where({ 'mp.ui_key': query.uiKey });
        }

        if (query.projectId || query.registryProjectId || query.projectVintageId) {
          builder.where((builder2) => {
            if (query.projectId) {
              builder2.orWhere({ 'p.id': query.projectId });
              builder2.orWhere({ 'pv_p.id': query.projectId });
            }

            if (query.registryProjectId) {
              builder2.orWhere({ 'mpc.registry_project_id': query.registryProjectId });
              builder2.orWhere({ 'p.registry_project_id': query.registryProjectId });
              builder2.orWhere({ 'pv_p.registry_project_id': query.registryProjectId });
            }

            if (query.projectVintageId) {
              builder2.orWhere({ 'pv.id': query.projectVintageId });
            }
          });
        }
      });

    const modelPortfolioIds: { id: uuid }[] = await knexQuery;
    const modelPortfolios = await this.em.find(
      ModelPortfolio,
      { id: modelPortfolioIds.map((m) => m.id) },
      {
        limit: query.limit,
        offset: query.offset,
        orderBy: query.orderBys.map((m) => {
          return { [ModelPortfolioOrderBySQL[m.orderBy]]: OrderByDirectionSQL[m.orderByDirection] };
        }),
        populate: [
          'createdBy',
          'modelPortfolioComponents.project',
          'modelPortfolioComponents.vintage.project',
          'modelPortfolioComponents.vintage.grouping',
          'organization',
          'updatedBy',
        ],
      },
    );
    return {
      data: modelPortfolios,
      page: {
        limit: query.limit,
        offset: query.offset,
        size: modelPortfolios.length,
        totalCount: query.includeTotalCount ? modelPortfolioIds.length : undefined,
      },
    };
  }

  /**
   * find a ModelPortfolio entity by id
   *
   * steps:
   * 1. find ModelPortfolio entity by id and isDeleted false
   * 2. populate relations for ModelPortfolio
   * 3. append additional info to ModelPortfolio entity
   * 4. return InternalModelPortfolio
   *
   * @param {uuid} id
   * @param {ModelPortfolioRelations[]} includeRelations
   * @returns {Promise<InternalModelPortfolio>}
   * @throws NotFoundException
   */
  async findOneModelPortfolio(
    id: uuid,
    includeRelations: ModelPortfolioRelations[] = [],
  ): Promise<InternalModelPortfolio> {
    const result = await this.em.findOneOrFail(
      ModelPortfolio,
      { id, isDeleted: false },
      { populate: ['createdBy', 'organization', 'updatedBy'] },
    );

    return await this.privateAppendModelPortfolioInfo(this.em, result, includeRelations);
  }

  /**
   * find a public ModelPortfolio entity by id for a user
   *
   * steps:
   * 1. find OrganizationUser entity by user email from claims
   * 2. if OrganizationUser not found, throw ForbiddenException
   * 3. find ModelPortfolio entity by id, showCustomer true, organization, and isDeleted false
   * 4. populate relations for ModelPortfolio
   * 5. append additional info to ModelPortfolio entity
   * 6. return InternalModelPortfolio
   *
   * @param {UserClaims} claims
   * @param {uuid} id
   * @param {ModelPortfolioRelations[]} includeRelations
   * @returns {Promise<InternalModelPortfolio>}
   * @throws ForbiddenException, NotFoundException
   */
  async findOnePublicModelPortfolio(
    claims: UserClaims,
    id: uuid,
    includeRelations: ModelPortfolioRelations[] = [],
  ): Promise<InternalModelPortfolio> {
    const orgUser = await this.em.findOne(OrganizationUser, { user: { email: claims.email } }, { populate: ['user'] });
    if (!orgUser) {
      throw new ForbiddenException('User not assigned to an organization');
    }
    const result = await this.em.findOneOrFail(
      ModelPortfolio,
      { id, showCustomer: true, organization: orgUser.organization.id, isDeleted: false },
      { populate: ['createdBy', 'modelPortfolioComponents', 'updatedBy'] },
    );

    return await this.privateAppendModelPortfolioInfo(this.em, result, includeRelations);
  }

  /**
   * generate a public ModelPortfolio name for a user
   *
   * steps:
   * 1. find OrganizationUser entity by user email from claims
   * 2. if OrganizationUser not found, throw ConflictException
   * 3. count ModelPortfolio entities for organization
   * 4. generate name using privateGenerateModelPortfolioName
   * 5. return generated name
   *
   * @param {UserClaims} claims
   * @returns {Promise<string>}
   * @throws ConflictException
   */
  async generatePublicModelPortfolioName(claims: UserClaims): Promise<string> {
    const orgUser = await this.em.findOne(
      OrganizationUser,
      { user: { email: claims.email } },
      { populate: ['organization', 'user'] },
    );
    if (!orgUser) {
      throw new ConflictException('User not assigned to an organization');
    }

    const count = await this.em.count(ModelPortfolio, { organization: orgUser.organization.id });
    return await this.privateGenerateModelPortfolioName(
      this.em,
      orgUser.organization.name,
      orgUser.organization,
      count + 1,
    );
  }

  /**
   * generate an admin ModelPortfolio name for an organization
   *
   * steps:
   * 1. find Organization entity by organizationId
   * 2. if Organization not found, throw BadRequestException
   * 3. count ModelPortfolio entities for organization created before createdAt
   * 4. generate name using privateGenerateModelPortfolioName
   * 5. return generated name
   *
   * @param {uuid} organizationId
   * @param {Date} createdAt
   * @param {EntityManager} tx
   * @returns {Promise<string>}
   * @throws BadRequestException
   */
  async generateAdminModelPortfolioName(
    organizationId: uuid,
    createdAt: Date = new Date(),
    tx: EntityManager = this.em,
  ): Promise<string> {
    const organization = await tx.findOne(Organization, organizationId);
    if (!organization) {
      throw new BadRequestException(['organizationId must be a valid uuid']);
    }
    const count = await tx.count(ModelPortfolio, { organization, createdAt: { $lt: createdAt } });
    return await this.privateGenerateModelPortfolioName(tx, organization.name, organization, count + 1);
  }

  /**
   * update a ModelPortfolio entity by id
   *
   * steps:
   * 1. start a database transaction
   * 2. find ModelPortfolio entity by id and isDeleted false
   * 3. find User entity by email from claims
   * 4. update ModelPortfolio fields as needed
   * 5. check permissions for showCustomer, priceEstimate, and status
   * 6. update groupingId and check for grouped portfolios
   * 7. update memo if provided
   * 8. update updatedAt and updatedBy
   * 9. send notification and create AuditLogsService entry for MODEL_PORTFOLIO_UPDATED
   * 10. return updated ModelPortfolio entity
   *
   * @param {UserClaims} claims
   * @param {uuid} id
   * @param {AdminModelPortfolioUpdateRequestDTO} data
   * @returns {Promise<ModelPortfolio>}
   * @throws UnprocessableEntityException, ForbiddenException
   */
  async updateModelPortfolio(
    claims: UserClaims,
    id: uuid,
    data: AdminModelPortfolioUpdateRequestDTO,
  ): Promise<ModelPortfolio> {
    return await this.em.transactional(async (tx) => {
      const now = new Date();
      const modelPortfolio = await tx.findOneOrFail(
        ModelPortfolio,
        { id, isDeleted: false },
        { populate: ['organization.name'] },
      );

      const user = await tx.findOneOrFail(User, { email: claims.email });
      if (data.name) {
        modelPortfolio.name = data.name;
      }
      if (
        data.showCustomer != undefined &&
        (!data.showCustomer ||
          (await this.authService.check(claims, PermissionEnum.MODEL_PORTFOLIOS_SHARE, {
            route: 'updateModelPortfolio',
          })))
      ) {
        modelPortfolio.showCustomer = data.showCustomer;
      }
      if (data.organizationId) {
        const organization = await tx.findOne(Organization, data.organizationId);
        if (!organization) {
          throw new UnprocessableEntityException(`organizationId ${data.organizationId} must be a valid uuid`);
        }
        const updatedName = data.name || modelPortfolio.name;
        const matchingName = await tx.findOne(ModelPortfolio, { name: updatedName, organization });
        if (matchingName) {
          throw new UnprocessableEntityException(
            `model portfolio with name ${updatedName} already exists for organization ${organization.name}`,
          );
        }
        modelPortfolio.organization = organization;
      }
      if (
        data.priceEstimate &&
        (await this.authService.check(claims, PermissionEnum.MODEL_PORTFOLIOS_PRICE, { route: 'updateModelPortfolio' }))
      ) {
        modelPortfolio.priceEstimate = data.priceEstimate;
      }
      if (data.includeRiskAdjustment != undefined) {
        modelPortfolio.includeRiskAdjustment = data.includeRiskAdjustment;
      }
      if (data.status !== undefined && data.status !== modelPortfolio.status) {
        if (
          !(await this.authService.check(claims, PermissionEnum.MODEL_PORTFOLIOS_COMPONENTS_WRITE, {
            route: 'updateModelPortfolio',
          }))
        ) {
          throw new ForbiddenException();
        } else {
          modelPortfolio.status = data.status;
        }
      }
      if (
        data.rfp !== undefined &&
        (await this.authService.check(claims, PermissionEnum.MODEL_PORTFOLIOS_RFP, { route: 'updateModelPortfolio' }))
      ) {
        modelPortfolio.rfp = data.rfp;
      }
      // if we want to update an existing model portfolio's grouping
      if (data.groupingId !== undefined && data.groupingId !== modelPortfolio.groupingId) {
        const currentlyGroupedPortfolios = await tx.find(ModelPortfolio, { groupingId: modelPortfolio.groupingId });
        const otherPortfolios = currentlyGroupedPortfolios.filter((f) => f.id !== modelPortfolio.id);
        if (otherPortfolios.length > 0) {
          // if there are any other groupoed portfolios, throw an error for now
          // currently grouping an existing portfolio with another existing portfolio "should never happen"
          // so there is no logic of what to do with the "ungrouping" of a portfolio or an "update grouping" of a portfolio
          // do the other portfolios also get updated to the new group? do they stay their own group? do they all separate?
          throw new UnprocessableEntityException(
            `there are ${otherPortfolios.length} portfolios linked, cannot update linking`,
          );
        }
        // if there are no other groupings already existing to this portfolio,
        // then it's just being added to a grouping, so it doesn't seem to need much logic
        // this is probably just going to be a hidden api implementation unless FE implements
        modelPortfolio.groupingId = data.groupingId;
      }
      if (data.memo !== undefined) {
        modelPortfolio.memo = data.memo;
      }

      modelPortfolio.updatedAt = now;
      modelPortfolio.updatedBy = user;

      const sendgridPayload: InternalModelPortfolioUpdated = new InternalModelPortfolioUpdated(
        await this.notificationsService.getNotificationUrlWithUuid(
          NotificationEvent.MODEL_PORTFOLIO_UPDATED,
          modelPortfolio.id,
          'edit-portfolio-sandbox',
          '',
        ),
        modelPortfolio.status,
        modelPortfolio.organization?.name,
        modelPortfolio.name,
        claims,
      );
      await this.notificationsService.handleEvent(tx, NotificationEvent.MODEL_PORTFOLIO_UPDATED, sendgridPayload);

      await this.auditLogsService.create(tx, claims, AuditLogAction.MODEL_PORTFOLIO_UPDATED, now, id, {
        id,
        ...data,
      });

      return modelPortfolio;
    });
  }

  /**
   * update ModelPortfolioComponent entities for a ModelPortfolio
   *
   * steps:
   * 1. create new ModelPortfolioComponent entities for data.create
   * 2. update existing ModelPortfolioComponent entities for data.update
   * 3. mark ModelPortfolioComponent entities as deleted for data.delete
   * 4. create AuditLogsService entry for MODEL_PORTFOLIO_COMPONENTS_UPDATED
   *
   * @param {EntityManager} tx
   * @param {Date} now
   * @param {User} user
   * @param {ModelPortfolio} modelPortfolio
   * @param {AdminModelPortfolioComponentRequestDTO} data
   * @returns {Promise<void>}
   * @throws NotFoundException
   */
  async updateModelPortfolioComponents(
    tx: EntityManager,
    now: Date,
    user: User,
    modelPortfolio: ModelPortfolio,
    data: AdminModelPortfolioComponentRequestDTO,
  ): Promise<void> {
    function isVintageComponent(f: BaseModelPortfolioComponentCreateDTO): f is ModelPortfolioVintageComponentCreateDTO {
      return 'projectId' in f;
    }

    function isBookComponent(f: BaseModelPortfolioComponentCreateDTO): f is ModelPortfolioBookComponentCreateDTO {
      return 'bookId' in f;
    }

    function isVintageComponentUpdate(
      d: BaseModelPortfolioComponentUpdateDTO,
    ): d is ModelPortfolioVintageComponentUpdateDTO {
      return 'projectId' in d;
    }

    // create
    if (data.type === 'create') {
      for (const f of data.create) {
        if (isBookComponent(f)) {
          await this.validateBookComponentIsRCT(tx, f);
        }
        tx.create(ModelPortfolioComponent, {
          ...f,
          book: isBookComponent(f) && f.bookId ? tx.getReference(Book, f.bookId) : undefined,
          bufferCategory:
            isVintageComponent(f) && f.bufferCategoryId
              ? tx.getReference(BufferCategory, f.bufferCategoryId)
              : undefined,
          modelPortfolio,
          project: isVintageComponent(f) && f.projectId ? tx.getReference(Project, f.projectId) : undefined,
          vintage: isVintageComponent(f) && f.vintageId ? tx.getReference(ProjectVintage, f.vintageId) : undefined,
          createdAt: now,
          updatedAt: now,
          createdBy: user,
          updatedBy: user,
        });
      }
    }

    // update
    if (data.type === 'update') {
      const modelPortfolioComponents = await tx.find(ModelPortfolioComponent, {
        id: data.update.map((m) => m.id),
        modelPortfolio: modelPortfolio,
        isDeleted: false,
      });
      for (const d of data.update) {
        let mbc = modelPortfolioComponents.find((f) => f.id === d.id);
        if (!mbc) {
          throw new NotFoundException(`ModelPortfolioComponent ${d.id} not found`);
        }
        mbc = Object.assign(mbc, { ...d });
        mbc.bufferCategory =
          isVintageComponentUpdate(d) && d.bufferCategoryId
            ? tx.getReference(BufferCategory, d.bufferCategoryId)
            : mbc.bufferCategory;
        mbc.updatedAt = now;
        mbc.updatedBy = user;
      }
    }

    // delete
    if (data.type === 'delete') {
      const modelPortfolioComponents = await tx.find(ModelPortfolioComponent, {
        id: data.delete.map((m) => m.id),
        modelPortfolio: modelPortfolio,
        isDeleted: false,
      });
      modelPortfolioComponents.forEach((bc) => (bc.isDeleted = true));
      tx.remove(modelPortfolioComponents);
    }

    await this.auditLogsService.create(
      tx,
      { id: user.id, email: user.email },
      AuditLogAction.MODEL_PORTFOLIO_COMPONENTS_UPDATED,
      now,
      modelPortfolio.id,
      {
        ...data,
      },
    );
  }

  /**
   * update ModelPortfolioComponent entities from controller
   *
   * steps:
   * 1. start a database transaction
   * 2. get current date and User entity from claims
   * 3. find ModelPortfolio entity by id and isDeleted false
   * 4. call updateModelPortfolioComponents to update components
   *
   * @param {UserClaims} claims
   * @param {uuid} modelPortfolioId
   * @param {AdminModelPortfolioComponentRequestDTO} data
   * @returns {Promise<void>}
   */
  async updateModelPortfolioComponentsFromController(
    claims: UserClaims,
    modelPortfolioId: uuid,
    data: AdminModelPortfolioComponentRequestDTO,
  ): Promise<void> {
    await this.em.transactional(async (tx) => {
      const now = new Date();
      const user = await tx.findOneOrFail(User, { email: claims.email });
      const modelPortfolio = await tx.findOneOrFail(ModelPortfolio, { id: modelPortfolioId, isDeleted: false });

      await this.updateModelPortfolioComponents(tx, now, user, modelPortfolio, data);
    });
  }

  /* private */

  /**
   * Core function to create and persist a ModelPortfolio entity.
   *
   * Steps:
   * 1. Create the ModelPortfolio entity using the provided data
   * 2. Send a notification for MODEL_PORTFOLIO_CREATED
   * 3. Create an audit log entry for MODEL_PORTFOLIO_CREATED
   * 4. Return the created ModelPortfolio entity
   *
   * @param {EntityManager} tx
   * @param {UserClaims} claims
   * @param {RequiredEntityData<ModelPortfolio>} modelPortfolioData
   * @returns {Promise<ModelPortfolio>}
   */
  private async privateCreateModelPortfolio(
    tx: EntityManager,
    claims: UserClaims,
    modelPortfolioData: RequiredEntityData<ModelPortfolio>,
  ): Promise<ModelPortfolio> {
    const modelPortfolio = tx.create(ModelPortfolio, modelPortfolioData);

    const sendgridPayload: InternalModelPortfolioCreated = new InternalModelPortfolioCreated(
      await this.notificationsService.getNotificationUrlWithUuid(
        NotificationEvent.MODEL_PORTFOLIO_CREATED,
        modelPortfolio.id,
        'edit-portfolio-sandbox',
        '',
      ),
      modelPortfolio.status,
      modelPortfolio.organization?.name,
      modelPortfolio.name,
      claims,
    );

    await this.notificationsService.handleEvent(tx, NotificationEvent.MODEL_PORTFOLIO_CREATED, sendgridPayload);
    await this.auditLogsService.create(
      tx,
      claims,
      AuditLogAction.MODEL_PORTFOLIO_CREATED,
      modelPortfolio.createdAt,
      modelPortfolio.id,
      {
        ...modelPortfolio,
      },
    );

    return modelPortfolio;
  }

  /**
   * append additional info to InternalModelPortfolio entity (private helper)
   *
   * steps:
   * 1. if includeRelations is empty, return portfolio
   * 2. if includeRelations includes ModelPortfolioRelations.GROUPED_PORTFOLIOS, populate groupedPortfolios
   * 3. if includeRelations includes ModelPortfolioRelations.MODEL_PORTFOLIO_COMPONENTS, populate modelPortfolioComponents.bufferCategory
   * 4. if includeRelations includes ModelPortfolioRelations.PROJECT or MODEL_PORTFOLIO_COMPONENTS, call privateAppendModelPortfolioComponentInfo
   * 5. return updated InternalModelPortfolio
   *
   * @param {EntityManager} tx
   * @param {InternalModelPortfolio} portfolio
   * @param {ModelPortfolioRelations[]} includeRelations
   * @returns {Promise<InternalModelPortfolio>}
   */
  private async privateAppendModelPortfolioInfo(
    tx: EntityManager,
    portfolio: InternalModelPortfolio,
    includeRelations: ModelPortfolioRelations[],
  ): Promise<InternalModelPortfolio> {
    if (includeRelations.length === 0) return portfolio;
    // todo (TD-76) : maybe create a better way to get total so we don't have to initalize all components
    if (includeRelations.includes(ModelPortfolioRelations.GROUPED_PORTFOLIOS)) {
      portfolio.groupedPortfolios = await tx.find(
        ModelPortfolio,
        { id: { $ne: portfolio.id }, groupingId: portfolio.groupingId, isDeleted: false },
        { populate: ['modelPortfolioComponents.bufferCategory'] },
      );
    }

    if (includeRelations.includes(ModelPortfolioRelations.MODEL_PORTFOLIO_COMPONENTS)) {
      await tx.populate(portfolio, ['modelPortfolioComponents.bufferCategory']);
    }
    if (
      includeRelations.some((s) =>
        [
          ModelPortfolioRelations.PORTFOLIO,
          ModelPortfolioRelations.PROJECT,
          ModelPortfolioRelations.PROJECT_VINTAGE,
        ].includes(s),
      )
    ) {
      await this.privateAppendModelPortfolioComponentInfo(
        tx,
        portfolio.modelPortfolioComponents.getItems(),
        includeRelations,
      );
    }
    return portfolio;
  }

  /**
   * append additional info to ModelPortfolioComponent entities (private helper)
   *
   * steps:
   * 1. if includeRelations includes ModelPortfolioRelations.PROJECT_VINTAGE, populate vintage.project and related fields
   * 2. if includeRelations includes ModelPortfolioRelations.PROJECT, populate project and related fields
   *
   * @param {EntityManager} tx
   * @param {ModelPortfolioComponent[]} modelPortfolioComponents
   * @param {ModelPortfolioRelations[]} includeRelations
   * @returns {Promise<void>}
   */
  private async privateAppendModelPortfolioComponentInfo(
    tx: EntityManager,
    modelPortfolioComponents: ModelPortfolioComponent[],
    includeRelations: ModelPortfolioRelations[],
  ): Promise<void> {
    if (includeRelations.includes(ModelPortfolioRelations.PROJECT_VINTAGE)) {
      await tx.populate(modelPortfolioComponents, [
        'vintage.grouping',
        'vintage.project.bufferCategory',
        'vintage.project.country',
        'vintage.project.flags',
        'vintage.project.projectType.bufferCategory',
        'vintage.project.projectSDGs.sdgType',
      ]);
    }
    if (includeRelations.includes(ModelPortfolioRelations.PROJECT)) {
      await tx.populate(modelPortfolioComponents, [
        'project.bufferCategory',
        'project.country',
        'project.flags',
        'project.projectType.bufferCategory',
        'project.projectSDGs.sdgType',
      ]);
    }

    if (includeRelations.includes(ModelPortfolioRelations.PORTFOLIO)) {
      await tx.populate(modelPortfolioComponents, ['book']);
    }
  }

  /**
   * generate a unique ModelPortfolio name for an organization (private helper)
   *
   * steps:
   * 1. loop to generate name with incremented count
   * 2. check if ModelPortfolio with generated name exists for organization and createdAt
   * 3. if not found, return generated name
   * 4. if max reached, throw InternalServerErrorException
   *
   * @param {EntityManager} tx
   * @param {string} name
   * @param {Organization} organization
   * @param {number} startingCount
   * @param {Date} createdAt
   * @returns {Promise<string>}
   * @throws InternalServerErrorException
   */
  private async privateGenerateModelPortfolioName(
    tx: EntityManager,
    name: string,
    organization?: Organization,
    startingCount: number = 0,
    createdAt: Date = new Date(),
  ): Promise<string> {
    for (let count = startingCount + 1; count < startingCount + 1000; count++) {
      const generatedName = `${name} #${count}`;
      const found = await tx.findOne(ModelPortfolio, {
        name: generatedName,
        organization,
        createdAt: { $lt: createdAt },
      });
      if (!found) {
        return generatedName;
      }
    }
    throw new InternalServerErrorException(`model portfolio name reached max, find a new method`);
  }

  /**
   * get or validate a unique ModelPortfolio name for an organization (private helper)
   *
   * steps:
   * 1. if organization provided, check for unique name among org portfolios
   * 2. if name not provided, generate default name
   * 3. if no organization, check for unique name globally or generate default name
   * 4. return name
   *
   * @param {EntityManager} tx
   * @param {string} name
   * @param {Organization} organization
   * @returns {Promise<string>}
   * @throws ConflictException
   */
  private async privateGetOrValidateModelPortfolioName(
    tx: EntityManager,
    name?: string,
    organization?: Organization,
  ): Promise<string> {
    // check name uniqueness per org
    if (organization) {
      const orgPortfolios: ModelPortfolio[] = await tx.find(
        ModelPortfolio,
        { organization: organization, isDeleted: false },
        { populate: ['organization'] },
      );
      if (name) {
        const matchingName = orgPortfolios.find((f) => f.name.toLowerCase().trim() === name?.toLowerCase().trim());
        if (matchingName) {
          throw new ConflictException(`portfolio name ${name} must be unique per organization`);
        }
      } else {
        // generate default name
        name = await this.privateGenerateModelPortfolioName(tx, organization.name, organization, orgPortfolios.length);
      }
    } else {
      if (name) {
        const matchingName = await tx.findOne(ModelPortfolio, {
          name: name,
          organization: null,
        });
        if (matchingName) {
          throw new ConflictException(`portfolio name ${name} must be unique`);
        }
      } else {
        // generate default name
        name = await this.privateGenerateModelPortfolioName(tx, 'Default', undefined);
      }
    }

    return name;
  }

  private async validateBookComponentIsRCT(
    tx: EntityManager,
    component: ModelPortfolioBookComponentCreateDTO,
  ): Promise<void> {
    const asset = await tx.findOne(Asset, component.bookId);

    if (!asset) {
      throw new BadRequestException(`Asset with id ${component.bookId} does not exist`);
    }
    if (asset.type !== AssetType.RCT) {
      throw new BadRequestException(`Asset with id ${component.bookId} is not an RCT asset`);
    }
  }
}
