/* third party */
import { EntityManager } from '@mikro-orm/postgresql';
import { Injectable } from '@nestjs/common';
/* app */
import {
  AssetFlow,
  ForwardLineItem,
  MarketingAgreementLineItem,
  Organization,
  Transaction,
  TransactionDetail,
} from '@app/entities';
import { OrderByDirectionSQL, TransactionOrderBySQL } from '@app/enums/orderby.enum';
import { InternalTransactionQueryResponse, InternalTransactionResponse } from '@app/interfaces/transaction.interface';
import { UserClaims } from '@app/auth';
import { TransactionStatus, TransactionType, uuid } from '@rubiconcarbon/shared-types';
import { environment } from '@env/environment';
import {
  AdminTransactionQueryDTO,
  AdminTransactionSearchQueryDTO,
  PortalTransactionQueryDTO,
} from '@app/dtos/transaction.dto';
import { InternalPortalUser } from '@app/interfaces/user.interface';
import { getPortalUserWithOrganizationFromClaims } from '@app/helpers';

@Injectable()
export class TransactionsService {
  constructor(private em: EntityManager) {}

  /**
   * find Transactions based on query
   *
   * steps:
   * 1. get Knex instance from EntityManager
   * 2. if UserClaims provided, retrieve Organization for User
   * 3. build Knex query for TransactionDetail ids based on query and Organization
   * 4. execute query to get TransactionDetail ids
   * 5. fetch TransactionDetail entities with pagination and ordering
   * 6. fetch AssetFlow, ForwardLineItem, and MarketingAgreementLineItem for Transaction ids
   * 7. attach related entities to TransactionDetail results
   * 8. return InternalActionQueryResponse with data and page info
   *
   * @param {AdminTransactionQueryDTO | PortalTransactionQueryDTO} query
   * @param {UserClaims} claims
   * @returns {Promise<InternalTransactionQueryResponse>}
   */
  async findMany(
    query: AdminTransactionQueryDTO | PortalTransactionQueryDTO,
    claims?: UserClaims,
  ): Promise<InternalTransactionQueryResponse> {
    const knex = this.em.getKnex();
    let customerOrganization: Organization;

    if (claims) {
      const portalUser: InternalPortalUser = await getPortalUserWithOrganizationFromClaims(this.em, claims);
      customerOrganization = portalUser.organizationUser.organization;
    }

    const knexQuery = knex
      .select(['td.id'])
      .from(`${environment.db.schema.rubicon}.transactions_details_v2 as td`)
      .innerJoin(`${environment.db.schema.rubicon}.transactions as t`, 't.id', 'td.id')
      .leftJoin(`${environment.db.schema.rubicon}.transaction_assets_v2 as ta`, 'ta.transaction_id', 'td.id')
      .where((builder) => {
        if (customerOrganization) {
          // portal/mobile
          builder.where({ 'td.counterparty_id': customerOrganization.id });
          builder.where({ 't.show_customer': true });
        } else {
          // admin
          if (query instanceof AdminTransactionQueryDTO && query.counterpartyName) {
            builder.where({ 'td.counterparty_name': query.counterpartyName });
          }
          if (query instanceof AdminTransactionQueryDTO && query.counterpartyId) {
            builder.where({ 'td.counterparty_id': query.counterpartyId });
          }
        }

        builder.whereIn('td.status', query.statuses);
        if (query.ids) {
          builder.whereIn('td.id', query.ids);
        }
        if (query.types) {
          builder.whereIn('td.type', query.types);
        }
        if (query.assetIds) {
          builder.whereIn('ta.asset_id', query.assetIds);
        }
        if (query.assetName) {
          builder.where({ 'td.asset_name': query.assetName });
        }
        if (query.subtypes) {
          builder.whereIn('td.subtype', query.subtypes);
        }
        if (query.uiKey) {
          builder.where({ 'td.ui_key': query.uiKey });
        }
      });

    const results: { id: uuid }[] = await knexQuery;

    let queryResponse: InternalTransactionQueryResponse | undefined = undefined;
    if (query.includeTotalCount) {
      const response = await this.em.findAndCount(
        TransactionDetail,
        { id: results.map((m) => m.id) },
        {
          limit: query.limit,
          offset: query.offset,
          orderBy: query.orderBys.map((m) => {
            return { [TransactionOrderBySQL[m.orderBy]]: OrderByDirectionSQL[m.orderByDirection] };
          }),
        },
      );
      queryResponse = {
        data: response[0],
        page: {
          limit: query.limit,
          offset: query.offset,
          size: response[0].length,
          totalCount: response[1],
        },
      };
    } else {
      const response = await this.em.find(
        TransactionDetail,
        { id: results.map((m) => m.id) },
        {
          limit: query.limit,
          offset: query.offset,
          orderBy: query.orderBys.map((m) => {
            return { [TransactionOrderBySQL[m.orderBy]]: OrderByDirectionSQL[m.orderByDirection] };
          }),
        },
      );
      queryResponse = {
        data: response,
        page: {
          limit: query.limit,
          offset: query.offset,
          size: response.length,
        },
      };
    }

    // todo : hack to get assetFlows and forwardLineItems
    // until all assetFlows.relatedIds are in actions
    // and maybe we add detailedObject to transactions or something
    const assetFlows = await this.em.find(
      AssetFlow,
      { transaction: queryResponse.data.map((m) => m.id) },
      {
        populate: [
          'asset.details.portfolioDetails',
          'asset.details.rrtDetails',
          'asset.details.vintageDetails',
          'transaction',
        ],
      },
    );
    const forwardLineItems = await this.em.find(
      ForwardLineItem,
      { id: queryResponse.data.map((m) => m.id) },
      { populate: ['projectVintage.asset.details.vintageDetails', 'projectVintage.project'] },
    );
    const marketingAgreementLineItems = await this.em.find(
      MarketingAgreementLineItem,
      { id: queryResponse.data.map((m) => m.id) },
      { populate: ['projectVintage.asset.details.vintageDetails', 'projectVintage.project'] },
    );
    queryResponse.data.forEach((transaction) => {
      transaction.assetFlows = assetFlows.filter((af) => af.transaction.id === transaction.id);
      transaction.forwardLineItem = forwardLineItems.find((f) => f.id === transaction.id);
      transaction.marketingAgreementLineItem = marketingAgreementLineItems.find((f) => f.id === transaction.id);
    });
    return queryResponse;
  }

  /**
   * search for TransactionDetail by fuzzy or direct match on asset, counterparty, or uiKey
   *
   * steps:
   * 1. validate that at least one search field is present
   * 2. build base Knex query for TransactionDetail with regex and similarity scoring
   * 3. add orWhereRaw clauses for uiKey, counterparty, asset name, and registry project id as needed
   * 4. if fuzzy search, union with similarity-based queries for counterparty and asset
   * 5. build final query with type and status filters
   * 6. execute query to get Transaction ids
   * 7. fetch Transaction entities and related details
   * 8. fetch ForwardLineItem and MarketingAgreementLineItem if asset search
   * 9. return InternalTransactionResponse array with related entities attached
   *
   * @param {AdminTransactionSearchQueryDTO} query
   * @returns {Promise<InternalTransactionResponse[]>}
   */
  async search(query: AdminTransactionSearchQueryDTO): Promise<InternalTransactionResponse[]> {
    if ((!query.asset && !query.counterparty && !query.uiKey) || query.q == '') return [];

    // start with base query where if the passed in letters are checked against uiKey counterparty, asset_name, asset_registry_project_id and defaulted to a score of -1
    // ~* is regular expression "text ~* text" --> boolean (String matches regular expression, case-insensitive)
    const knex = this.em.getKnex();
    let knexQuery = knex
      .from(`${environment.db.schema.rubicon}.transactions_details_v2 as t`)
      .leftJoin(`${environment.db.schema.rubicon}.transaction_assets_v2 as ta`, 'ta.transaction_id', 't.id')
      .select(
        knex.raw(
          `t.id,
           t.ui_key,
           t.type,
           t.status,
           (NOT t.ui_key ~* ?)::text || '|' ||
            (NOT t.counterparty_name ~* ?)::text || '|' ||
            ta.asset_name ~* ?)::text || '|' ||
            ta.asset_portfolio_name ~* ?)::text || '|' ||
            ta.asset_project_name ~* ?)::text || '|' ||
            ta.asset_registry_project_id ~* ?)::text || '|' ||
            t.ui_key as sorting,
            -1 as score`,
          [query.q, query.q, query.q, query.q],
        ),
      );

    // if query.uiKey is true, compare the query.q to to the uiKey
    if (query.uiKey) knexQuery = knexQuery.orWhereRaw('t.ui_key ~* ?', [query.q]);
    // if query.counterparty is true, compare the query.q to to the counterparty_name
    if (query.counterparty) knexQuery = knexQuery.orWhereRaw('t.counterparty_name ~* ?', [query.q]);
    // if query.asset is true, compare the query.q to to the asset name and registry_project_id
    if (query.asset) {
      knexQuery = knexQuery.orWhereRaw('ta.asset_name ~* ?', [query.q]);
      knexQuery = knexQuery.orWhereRaw('ta.asset_registry_project_id ~* ?', [query.q]);
    }

    // if fuzzy searching, compare query to counterparty_name and asset_name
    if (query.fuzzy && (query.counterparty || query.asset)) {
      if (query.counterparty) {
        const knexFuzzy = knex
          .from(`${environment.db.schema.rubicon}.transactions_details_v2 as t`)
          .select(
            knex.raw(`t.id, t.ui_key, t.type, t.status, 'true|true|true|true', SIMILARITY(t.counterparty_name, ?)`, [
              query.q,
            ]),
          )
          .orWhereRaw('SIMILARITY(t.counterparty_name, ?) > 0.3', [query.q]);
        knexQuery = knexQuery.union(knexFuzzy);
      }

      if (query.asset) {
        const knexAssetFuzzy = knex
          .from(`${environment.db.schema.rubicon}.transactions_details_v2 as t`)
          .leftJoin(`${environment.db.schema.rubicon}.transaction_assets_v2 as ta`, 'ta.transaction_id', 't.id')
          .select(
            knex.raw(`t.id, t.ui_key, t.type, t.status, 'true|true|true|true', SIMILARITY(ta.asset_name, ?)`, [
              query.q,
            ]),
          )
          .orWhereRaw('SIMILARITY(ta.asset_name, ?) > 0.3', [query.q]);
        knexQuery = knexQuery.union(knexAssetFuzzy);
      }
    }

    const fullQuery = knex
      .with('q', knexQuery.orderByRaw('sorting, score DESC'))
      .select('q.id', 'q.ui_key', 'q.type', 'q.status')
      .distinct()
      .from('q');

    fullQuery.whereIn('q.type', query.transactionTypes);
    // if only searching specific transaction statuses, add where clause
    if (query.transactionStatuses !== undefined && query.transactionStatuses.length > 0) {
      fullQuery.whereIn('q.status', query.transactionStatuses);
    }

    const x: { id: uuid; uiKey: string; type: TransactionType; status: TransactionStatus }[] = await fullQuery.limit(
      query.limit,
    );

    const transactions = await this.em.find(Transaction, { id: x.map((m) => m.id) }, { populate: ['details'] });
    let forwardLineItems: ForwardLineItem[];
    let marketingAgreementLineItems: MarketingAgreementLineItem[];
    if (query.asset) {
      await this.em.populate(transactions, ['assetFlows']);
      forwardLineItems = await this.em.find(ForwardLineItem, {
        id: transactions.filter((f) => f.type === TransactionType.FORWARD_LINE_ITEM).map((m) => m.id),
      });
      marketingAgreementLineItems = await this.em.find(MarketingAgreementLineItem, {
        id: transactions.filter((f) => f.type === TransactionType.MARKETING_AGREEMENT_LINE_ITEM).map((m) => m.id),
      });
    }

    return transactions.map((m) => {
      return {
        ...m.details,
        assetFlows: m.assetFlows.isInitialized() ? m.assetFlows.getItems() : undefined,
        forwardLineItem: forwardLineItems.find((f) => f.id === m.id),
        marketingAgreementLineItem: marketingAgreementLineItems.find((f) => f.id === m.id),
      };
    });
  }
}
