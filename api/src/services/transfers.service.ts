/* third party */
import { FilterQuery, FindOptions, LoadStrategy } from '@mikro-orm/core';
import { EntityManager } from '@mikro-orm/postgresql';
import {
  BadRequestException,
  ConflictException,
  Injectable,
  InternalServerErrorException,
  UnprocessableEntityException,
} from '@nestjs/common';
import Decimal from 'decimal.js';
/* rubicon */
import {
  AssetFlowStatus,
  AssetType,
  AuditLogAction,
  BookRelations,
  BookType,
  NotificationEvent,
  TransactionType,
  TransferRelation,
  uuid,
} from '@rubiconcarbon/shared-types';
/* env */
import { environment } from '@env/environment';
/* app */
import { UserClaims } from '@app/auth/interfaces';
import { Asset, AssetFlow, Book, ProjectVintage, RrtAsset, Transaction, Transfer, User } from '@app/entities';
import {
  InternalPortfolioCompositionUpdated,
  InternalTransferAsset,
  InternalTransferExecuted,
} from '@app/dtos/sendgrid-payload.dto';
import {
  AdminRrtCompositionRequestDTO,
  AdminTransferQueryDTO,
  AdminTransferRelationsQueryDTO,
  AdminTransferRequestDTO,
} from '@app/dtos/transfer.dto';
import { OrderByDirectionSQL } from '@app/enums/orderby.enum';
import { InternalTransferQueryResponse } from '@app/interfaces/transfer.interface';
import { LedgerTransactionsService } from '@app/ledger/services/ledger-transactions.service';
import { LedgerTransfersService } from '@app/ledger/services/ledger-transfers.service';
import { AuditLogsService } from '@app/utility/audit-log';
import { NotificationsService } from './notifications.service';
import { InternalPopulateAllocationsService } from './allocations-populate.service';
import { InternalBookResponse } from '@app/interfaces/book.interface';
import { InternalAllocationResponse } from '@app/interfaces/allocation.interface';

@Injectable()
export class TransfersService {
  constructor(
    private auditLogsService: AuditLogsService,
    private readonly em: EntityManager,
    private ledgerTransactionsService: LedgerTransactionsService,
    private ledgerTransfersService: LedgerTransfersService,
    private notificationsService: NotificationsService,
    private populateAllocationsService: InternalPopulateAllocationsService,
  ) {}

  /**
   * execute a Transfer for a set of AssetTransfers
   *
   * steps:
   * 1. start a transaction and get User by email from claims
   * 2. retrieve related Asset, ProjectVintage, source Book, and destination Book entities
   * 3. validate each source Book has enough available for all combined transfers
   * 4. create Transaction entity for the Transfer
   * 5. for each AssetTransfer, validate assets and books, check available amounts, and create AssetFlow entities
   * 6. handle RCT debits/credits and send composition update emails if needed
   * 7. for non-RCT transfers, collect transfer asset details for notifications
   * 8. create Transfer entity and persist to database
   * 9. create ledger transaction and audit log entry
   * 10. send notifications for composition updates and transfer execution
   * 11. on error, revert ledger transaction if needed and rethrow
   * 12. return the created Transfer entity
   *
   * @param {UserClaims} claims
   * @param {AdminTransferRequestDTO} data
   * @returns {Promise<Transfer>}
   * @throws UnprocessableEntityException
   * @throws BadRequestException
   * @throws InternalServerErrorException
   */
  async executeInternal(claims: UserClaims, data: AdminTransferRequestDTO): Promise<Transfer> {
    const now = new Date();
    const transferId = uuid();
    let ledgerTransactionId;
    const transferAssets: InternalTransferAsset[] = [];
    const componentUpdateEmails: InternalPortfolioCompositionUpdated[] = [];

    try {
      const transfer = await this.em.transactional(async (tx) => {
        const user = await tx.findOneOrFail(User, { email: claims.email });

        const assetFlows: AssetFlow[] = [];
        let totalAmount = 0;

        // get related assets, source books, and destination books
        const assets = await tx.find(
          Asset,
          data.assetTransfers.map((m) => m.assetId),
        );
        const detailedVintages: ProjectVintage[] = await tx.find(
          ProjectVintage,
          { id: assets.map((m) => m.id) },
          { populate: ['project'] },
        );
        const sourceBooks: InternalBookResponse[] = await tx.find(
          Book,
          data.assetTransfers.map((m) => m.sourceId),
        );
        await this.populateAllocationsService.internalPopulateBooksAllocations(tx, sourceBooks, undefined, [
          BookRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE,
          BookRelations.OWNER_ALLOCATIONS_NESTED,
        ]);
        const destinationBooks = await tx.find(
          Book,
          data.assetTransfers.map((m) => m.destinationId),
        );

        // if source is an rctBook (public or private) validate each source has enough available for all combined transfers
        // note: if/when we have other asset types, this may need to be re-checked
        for (const source of sourceBooks) {
          if (!source.assetType) {
            continue;
          }
          // get total amount of all transfers from the rct source book
          const totalTransferAmountFromSource = Decimal.sum(
            ...data.assetTransfers.filter((f) => f.sourceId === source.id).map((m) => m.amount),
          ).toNumber();
          // validate that the source book has that many rcts available
          const availableAmount = source.assetAllocationsByBookType?.find(
            (f) => f.bookType === BookType.PORTFOLIO_DEFAULT,
          )?.totalAmountAvailable;

          // allowing transfers during copying of prod to be exact match of prod
          if (
            claims.name !== 'init-console-script' &&
            (!availableAmount || availableAmount < totalTransferAmountFromSource)
          ) {
            throw new UnprocessableEntityException(
              `${source.name} can only transfer a total amount less than or equal to total RCT amount available ${availableAmount} (trying to transfer ${totalTransferAmountFromSource})`,
            );
          }
        }

        // create transaction
        const transaction = tx.create(Transaction, { id: transferId, type: TransactionType.INTERNAL_TRANSFER });
        await tx.persistAndFlush(transaction);

        for (const transfer of data.assetTransfers) {
          // todo : (TD-100) add source_destination check (maybe eventually make it a trigger for all flows?)

          // get each asset (rct/vintage) from the request
          const asset = assets.find((asset) => asset.id === transfer.assetId);
          if (!asset) {
            throw new UnprocessableEntityException('Asset not found');
          }
          // get the source book of the transfer
          const sourceBook = sourceBooks.find((book) => book.id === transfer.sourceId);
          if (!sourceBook) {
            throw new UnprocessableEntityException('Source not found');
          }
          // get the destination book of the transfer
          const destinationBook = destinationBooks.find((book) => book.id === transfer.destinationId);
          if (!destinationBook) {
            throw new UnprocessableEntityException('Destination not found');
          }

          // get source asset and amount available
          const sourceAsset = sourceBook.ownerAllocations?.allocations?.find((f) => f.asset.id === asset.id);

          // allowing transfers during copying of prod to be exact match of prod
          if (
            claims.name !== 'init-console-script' &&
            (!sourceAsset || sourceAsset.amountAvailable < transfer.amount)
          ) {
            throw new BadRequestException([
              `asset ${transfer.assetId} can only transfer less than or equal to ${sourceAsset?.amountAvailable || 0} from ${sourceBook.name}`,
            ]);
          }

          // create assetFlow for transfer
          assetFlows.push(
            tx.create(AssetFlow, {
              id: uuid(),
              createdAt: now,
              updatedAt: now,
              amount: transfer.amount,
              asset: transfer.assetId,
              assetType: AssetType.REGISTRY_VINTAGE,
              destination: destinationBook,
              rawPrice: new Decimal(0),
              settledAt: now,
              source: sourceBook,
              status: AssetFlowStatus.SETTLED,
              transaction: transferId,
              transactionType: TransactionType.INTERNAL_TRANSFER,
            }),
          );

          // if the source book is an RCT, debit RCTs from default book
          if (sourceBook.assetType === AssetType.RCT) {
            assetFlows.push(
              tx.create(AssetFlow, {
                id: uuid(),
                createdAt: now,
                updatedAt: now,
                amount: transfer.amount,
                asset: sourceBook.id,
                assetType: AssetType.RCT,
                destination: environment.rubicon.books.offsets,
                rawPrice: new Decimal(0),
                settledAt: now,
                source: uuid(environment.rubicon.books.portfolio.default),
                status: AssetFlowStatus.SETTLED,
                transaction: transferId,
                transactionType: TransactionType.INTERNAL_TRANSFER,
              }),
            );
            // send email for rct composition update
            componentUpdateEmails.push(
              new InternalPortfolioCompositionUpdated(
                await this.notificationsService.getNotificationUrl(NotificationEvent.BASKET_COMPOSITION_UPDATED),
                sourceBook.name,
                now,
                claims,
              ),
            );
          }
          // if the destintaion book is an RCT, credits RCTs to destination book
          if (destinationBook.assetType === AssetType.RCT) {
            assetFlows.push(
              tx.create(AssetFlow, {
                id: uuid(),
                createdAt: now,
                updatedAt: now,
                amount: transfer.amount,
                asset: destinationBook.id,
                assetType: AssetType.RCT,
                destination: uuid(environment.rubicon.books.portfolio.default),
                rawPrice: new Decimal(0),
                settledAt: now,
                source: environment.rubicon.books.offsets,
                status: AssetFlowStatus.SETTLED,
                transaction: transferId,
                transactionType: TransactionType.INTERNAL_TRANSFER,
              }),
            );
            // send email for rct composition update
            componentUpdateEmails.push(
              new InternalPortfolioCompositionUpdated(
                await this.notificationsService.getNotificationUrl(NotificationEvent.BASKET_COMPOSITION_UPDATED),
                destinationBook.name,
                now,
                claims,
              ),
            );
          }

          // if not a rct composition update, add total amount update
          // todo (3163) : confirm with Guy if totalAmount email should be sent
          if (sourceBook.assetType !== AssetType.RCT && destinationBook.assetType !== AssetType.RCT) {
            const detailedVintage = detailedVintages.find((f) => f.id === asset.id);
            if (!detailedVintage) {
              throw new InternalServerErrorException(`expected ${asset.type} asset ${asset.id} to be a vintage`);
            }

            transferAssets.push({
              sourceBookName: sourceBook.name,
              destinationBookName: destinationBook.name,
              projectName: detailedVintage.project.name,
              registryProjectId: detailedVintage.project.registryProjectId,
              vintageName: detailedVintage.name(),
              amount: transfer.amount,
            });

            totalAmount += transfer.amount;
          }
        }

        // create transfer
        const transfer = tx.create(Transfer, {
          id: transferId,
          createdAt: now,
          updatedAt: now,
          memo: data.memo,
          totalAmount,
          assetFlows,
          user,
        });
        // create ledger transaction that auto-settles amounts
        const response = await this.ledgerTransfersService.transferTransaction(tx, transfer);
        ledgerTransactionId = response.id;
        await this.auditLogsService.create(tx, claims, AuditLogAction.TRANSFER_EXECUTED, now, transferId, {});
        return transfer;
      });

      if (claims.name !== 'init-console-script') {
        try {
          if (componentUpdateEmails.length > 0) {
            for (const email of componentUpdateEmails) {
              await this.notificationsService.handleEvent(this.em, NotificationEvent.BASKET_COMPOSITION_UPDATED, email);
            }
          }
          if (transferAssets.length > 0) {
            await this.notificationsService.handleEvent(
              this.em,
              NotificationEvent.TRANSFER_EXECUTED,
              new InternalTransferExecuted(transfer.memo || '', transfer.createdAt, claims.email, transferAssets),
            );
          }
        } catch (e) {
          console.warn(`email did not send ${JSON.stringify(e)}`);
        }
      }

      return transfer;
    } catch (e) {
      if (ledgerTransactionId) {
        await this.ledgerTransactionsService.revertTransactionsAfterFailedApi(
          transferId,
          now,
          [ledgerTransactionId],
          `executing transfer ${transferId}`,
        );
      }
      throw e;
    }
  }

  async updateRrtComposition(claims: UserClaims, data: AdminRrtCompositionRequestDTO): Promise<Transfer> {
    const now = new Date();
    const transferId = uuid();
    let ledgerTransactionId;
    const updatedFields: string[] = [];

    try {
      const transfer = await this.em.transactional(async (tx) => {
        const user = await tx.findOneOrFail(User, { email: claims.email });

        // get assets
        const projectVintages = await tx.find(
          ProjectVintage,
          {
            id: data.assets.map((m) => m.projectVintageId),
          },
          { populate: ['project'] },
        );
        const missingAssetIds = data.assets
          .map((m) => m.projectVintageId)
          .filter((f) => !projectVintages.map((m) => m.id).includes(f));
        if (missingAssetIds.length > 0) {
          throw new UnprocessableEntityException(
            `projectVintageIds [${missingAssetIds.join(',')}] must be valid uuids`,
          );
        }
        const rrtAssets = await tx.find(
          RrtAsset,
          { rrt: data.portfolioId },
          { populate: ['projectVintage.project', 'rrt'] },
        );

        // get default portfolio, rrt portfolio, and allocations
        const defaultPortfolio: InternalBookResponse | null = await tx.findOne(
          Book,
          environment.rubicon.books.portfolio.default,
        );
        if (!defaultPortfolio) {
          // should never happen
          throw new InternalServerErrorException(`default portfolio not found`);
        }
        const rrtPortfolio: InternalBookResponse | null = await tx.findOne(Book, {
          id: data.portfolioId,
          type: BookType.RRT_PUBLIC,
        });
        if (!rrtPortfolio) {
          throw new UnprocessableEntityException(`RRT Portfolio ${data.portfolioId} not found`);
        }
        await this.populateAllocationsService.internalPopulateBooksAllocations(
          tx,
          [defaultPortfolio, rrtPortfolio],
          undefined,
          [BookRelations.OWNER_ALLOCATIONS_NESTED],
        );

        // create transaction
        const transaction = tx.create(Transaction, { id: transferId, type: TransactionType.INTERNAL_TRANSFER });
        await tx.persistAndFlush(transaction);

        // starter consts
        const assetFlows: AssetFlow[] = [];
        let totalTransferQuantity = 0;
        let rrtNetAmount =
          defaultPortfolio.ownerAllocations?.allocations?.find((f) => f.asset.id === rrtPortfolio.id)
            ?.amountAvailable || 0;

        // update rrt assets
        for (const assetTransfer of data.assets) {
          // validate projectVintage exists with properly set fields
          const projectVintage = projectVintages.find((f) => f.id === assetTransfer.projectVintageId);
          if (!projectVintage) {
            throw new UnprocessableEntityException(
              `projectVintageId ${assetTransfer.projectVintageId} must be a valid uuid`,
            );
          }
          if (!projectVintage.project.beZeroRating) {
            throw new UnprocessableEntityException(`project ${projectVintage.project.name} must have a beZeroRating`);
          }

          // validate amounts and calculated factors
          const amountErrors: string[] = [];
          if (assetTransfer.netQuantity > assetTransfer.grossQuantity) {
            amountErrors.push(
              `netQuantity must be an int less than or equal to grossQuantity for asset ${projectVintage.project.name} ${projectVintage.name()}`,
            );
          }
          const calculatedFactor =
            assetTransfer.grossQuantity && assetTransfer.netQuantity
              ? new Decimal(assetTransfer.grossQuantity).div(assetTransfer.netQuantity)
              : 0;
          if (calculatedFactor > assetTransfer.beZeroFactor) {
            amountErrors.push(
              `the calculated percentage ${calculatedFactor} must be less than or equal to the beZeroFactor ${assetTransfer.beZeroFactor} for asset ${projectVintage.project.name} ${projectVintage.name()}`,
            );
          }
          if (amountErrors.length > 0) {
            throw new BadRequestException(amountErrors);
          }

          // get rrt vintage allocation and default vintage allocation
          let rrtAsset = rrtAssets.find((f) => f.projectVintage.id === assetTransfer.projectVintageId);
          const rrtAllocation = rrtPortfolio.ownerAllocations?.allocations?.find(
            (f) => f.asset.id === assetTransfer.projectVintageId,
          );
          const defaultVintageAllocation = defaultPortfolio.ownerAllocations?.allocations?.find(
            (f) => f.asset.id === assetTransfer.projectVintageId,
          );

          // create rrtAsset if it doesn't exist
          if (!rrtAsset) {
            const rrtAssetId = uuid();
            tx.create(Asset, { id: rrtAssetId, createdAt: now, updatedAt: now, type: AssetType.RRT_VINTAGE });
            rrtAsset = tx.create(RrtAsset, {
              id: rrtAssetId,
              createdAt: now,
              updatedAt: now,
              beZeroFactor: assetTransfer.beZeroFactor,
              currentNetQuantity: 0,
              initialNetQuantity: assetTransfer.netQuantity,
              initialGrossQuantity: assetTransfer.grossQuantity,
              totalNetQuantity: 0,
              totalGrossQuantity: 0,
              asset: projectVintage.id,
              projectVintage: projectVintage.id,
              rrt: rrtPortfolio,
              beZeroRatingAtCreation: projectVintage.project.beZeroRating,
            });
          }

          // update beZeroFactor
          if (!new Decimal(assetTransfer.beZeroFactor).equals(rrtAsset.beZeroFactor)) {
            updatedFields.push(`updated beZeroFactor from ${rrtAsset.beZeroFactor} to ${assetTransfer.beZeroFactor}`);
            rrtAsset.beZeroFactor = assetTransfer.beZeroFactor;
            rrtAsset.updatedAt = now;
          }

          // validate and update gross quantity
          const grossQuantityDifference = assetTransfer.grossQuantity - (rrtAllocation?.amountAvailable || 0);
          if (grossQuantityDifference !== 0) {
            this.privateTransferRrtProjectVintageAllocation(
              tx,
              transferId,
              now,
              rrtAsset,
              grossQuantityDifference,
              assetFlows,
              defaultVintageAllocation,
              rrtAllocation,
            );
            updatedFields.push(
              `updated ${projectVintage.project.registryProjectId} ${projectVintage.name()} grossQuantity from ${rrtAllocation?.amountAvailable || 0} to ${assetTransfer.grossQuantity}`,
            );
            rrtAsset.totalGrossQuantity += grossQuantityDifference;
            rrtAsset.updatedAt = now;
            totalTransferQuantity += new Decimal(grossQuantityDifference).abs().toNumber();
          }

          // validate and update net quantity
          const netQuantityDifference = assetTransfer.netQuantity - rrtAsset.currentNetQuantity;
          if (netQuantityDifference !== 0) {
            const defaultRrtAllocation = defaultPortfolio.ownerAllocations?.allocations?.find(
              (f) => f.asset.id === rrtPortfolio.id,
            );
            this.privateTransferRrtNetAllocation(
              tx,
              transferId,
              now,
              rrtAsset,
              netQuantityDifference,
              assetFlows,
              defaultRrtAllocation,
            );
            updatedFields.push(
              `updated netQuantity from ${rrtAsset.currentNetQuantity} to ${assetTransfer.netQuantity}`,
            );
            rrtAsset.totalNetQuantity += netQuantityDifference;
            rrtAsset.currentNetQuantity = assetTransfer.netQuantity;
            rrtAsset.updatedAt = now;
            totalTransferQuantity += new Decimal(netQuantityDifference).abs().toNumber();
            rrtNetAmount += netQuantityDifference;
          }
        }

        // netAmount validation (should never happen but adding it just in case)
        if (rrtNetAmount < 0) {
          console.error('rrt composition results in negative rrt amount' + JSON.stringify(data));
          throw new ConflictException(`net rrt amount ${rrtNetAmount} should not be less than 0`);
        }

        // create transfer
        const transfer = tx.create(Transfer, {
          id: transferId,
          createdAt: now,
          updatedAt: now,
          memo: data.memo,
          totalAmount: totalTransferQuantity, // includes amounts transferred for grossAmount AND netAmount
          assetFlows,
          user,
        });

        // create ledger transaction that auto-settles amounts
        const response = await this.ledgerTransfersService.transferTransaction(tx, transfer);
        ledgerTransactionId = response.id;
        await this.auditLogsService.create(tx, claims, AuditLogAction.TRANSFER_EXECUTED, now, transferId, {
          updatedFields,
        });
        return transfer;
      });

      if (claims.name !== 'init-console-script') {
        try {
          const rrtPortfolio = await this.em.findOneOrFail(Book, { id: data.portfolioId, type: BookType.RRT_PUBLIC });
          await this.notificationsService.handleEvent(
            this.em,
            NotificationEvent.BASKET_COMPOSITION_UPDATED,
            new InternalPortfolioCompositionUpdated(
              await this.notificationsService.getNotificationUrl(NotificationEvent.BASKET_COMPOSITION_UPDATED),
              rrtPortfolio.name,
              now,
              claims,
            ),
          );
        } catch (e) {
          console.warn(`email did not send ${JSON.stringify(e)}`);
        }
      }

      return transfer;
    } catch (e) {
      if (ledgerTransactionId) {
        await this.ledgerTransactionsService.revertTransactionsAfterFailedApi(
          transferId,
          now,
          [ledgerTransactionId],
          `executing transfer ${transferId}`,
        );
      }
      throw e;
    }
  }

  async findMany(query: AdminTransferQueryDTO): Promise<InternalTransferQueryResponse> {
    const where: FilterQuery<Transfer> = {};

    if (query.userId) {
      where.user = query.userId;
    }

    // todo : (TD-20) add in other filter queries

    if (query.includeTotalCount) {
      const response = await this.em.findAndCount(Transfer, where, {
        limit: query.limit,
        offset: query.offset,
        orderBy: { [query.orderBy]: OrderByDirectionSQL[query.orderByDirection] },
        populate: ['transactionDetails'],
        connectionType: 'read',
      });

      return {
        data: response[0],
        page: {
          limit: query.limit,
          offset: query.offset,
          size: response[0].length,
          totalCount: response[1],
        },
      };
    } else {
      const response = await this.em.find(Transfer, where, {
        limit: query.limit,
        offset: query.offset,
        orderBy: { [query.orderBy]: OrderByDirectionSQL[query.orderByDirection] },
        populate: ['transactionDetails'],
        connectionType: 'read',
      });

      return {
        data: response,
        page: {
          limit: query.limit,
          offset: query.offset,
          size: response.length,
        },
      };
    }
  }

  async findOne(id: uuid, query: AdminTransferRelationsQueryDTO): Promise<Transfer> {
    const transfer = await this.em.findOneOrFail(
      Transfer,
      { id },
      {
        populate: this.privatePopulateTransferRelations(query.includeRelations),
        connectionType: 'read',
        strategy: LoadStrategy.JOINED,
      },
    );

    // if (query.includeRelations.includes(TransferRelation.ASSETS)) {
    //   transfer.vintageFlows = await this.em.find(VintageFlow, { relatedId: id }, { populate: ['projectVintage']});
    //   transfer.rctFlows = await this.em.find(RctFlow, { relatedId: id }, { populate: ['book'] });

    //   // hack until all sources and destinations are "books"
    //   const books = await this.em.find(Book, { id: [...transfer.vintageFlows.map((m) => m.sourceId), ...transfer.vintageFlows.map((m) => m.destinationId), ...transfer.rctFlows.map((m) => m.sourceId), ...transfer.rctFlows.map((m) => m.destinationId)]});
    //   transfer.vintageFlows.forEach((f) => {
    //     f.sourceBook = books.find((book) => book.id === f.sourceId);
    //     f.destinationBook = books.find((book) => book.id === f.destinationId);
    //   });
    //   transfer.rctFlows.forEach((f) => {
    //     f.sourceBook = books.find((book) => book.id === f.sourceId);
    //     f.destinationBook = books.find((book) => book.id === f.destinationId);
    //   });
    // }

    return transfer;
  }

  // get all source books from sources_destinations
  // currently sources_destinations only holds the default books
  // it does not hold relations between portfolio:default and other portfolio books
  async findSources(): Promise<Book[]> {
    const knex = this.em.getKnex();
    const sourceIds: { source_id: uuid }[] = await knex
      .raw(`select source_id from "${environment.db.schema.rubicon}".sources_destinations`)
      .then((r) => r.rows);

    const books = await this.em.find(
      Book,
      { id: sourceIds.map((m) => m.source_id) },
      { orderBy: { name: OrderByDirectionSQL.asc } },
    );

    return books;
  }

  // get all destinations books of a given source book from sources_destinations
  // currently sources_destinations only holds the default books
  // it does not hold relations between portfolio:default and other portfolio books
  async findSourceDestinations(id: uuid): Promise<Book[]> {
    const knex = this.em.getKnex();
    const destinationIds: { destination_id: uuid }[] = await knex
      .raw(`select destination_id from "${environment.db.schema.rubicon}".sources_destinations where source_id='${id}'`)
      .then((r) => r.rows);

    const books = await this.em.find(
      Book,
      { id: destinationIds.map((m) => m.destination_id) },
      { orderBy: { name: OrderByDirectionSQL.asc } },
    );
    return books;
  }

  /* private */

  private privatePopulateTransferRelations<S extends string>(
    relations: TransferRelation[],
  ): FindOptions<Transfer, S>['populate'] {
    const populateString: string[] = ['transactionDetails'];
    if (relations.includes(TransferRelation.USER)) {
      populateString.push('user');
    }
    return populateString as unknown as FindOptions<Transfer, S>['populate'];
  }

  // todo : confirm this actually updates the assetFlows
  private privateTransferRrtNetAllocation(
    tx: EntityManager,
    transferId: uuid,
    now: Date,
    rrtAsset: RrtAsset,
    transferQuantity: number,
    assetFlows: AssetFlow[],
    defaultPortfolioAllocation?: InternalAllocationResponse,
  ): void {
    if (transferQuantity === 0) {
      throw new InternalServerErrorException(
        `transferQuantity is 0, should not call privateTransferRrtProjectVintageAllocation`,
      );
    }

    if (transferQuantity < 0) {
      const absTransferQuantity = transferQuantity * -1;
      if (!defaultPortfolioAllocation || defaultPortfolioAllocation.amountAvailable < absTransferQuantity) {
        throw new ConflictException(
          `rrt portfolio ${rrtAsset.rrt.name} must have at least a netAmount of ${absTransferQuantity} for the transfer`,
        );
      }

      assetFlows.push(
        tx.create(AssetFlow, {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          amount: absTransferQuantity,
          asset: rrtAsset.rrt.id,
          assetType: AssetType.RRT,
          destination: uuid(environment.rubicon.books.offsets),
          rawPrice: new Decimal(0),
          settledAt: now,
          source: uuid(environment.rubicon.books.portfolio.default),
          status: AssetFlowStatus.SETTLED,
          transaction: transferId,
          transactionType: TransactionType.INTERNAL_TRANSFER,
        }),
      );
    } else {
      assetFlows.push(
        tx.create(AssetFlow, {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          amount: transferQuantity,
          asset: rrtAsset.rrt.id,
          assetType: AssetType.RRT,
          destination: uuid(environment.rubicon.books.portfolio.default),
          rawPrice: new Decimal(0),
          settledAt: now,
          source: uuid(environment.rubicon.books.offsets),
          status: AssetFlowStatus.SETTLED,
          transaction: transferId,
          transactionType: TransactionType.INTERNAL_TRANSFER,
        }),
      );
    }
  }

  // todo : confirm this actually updates the assetFlows
  private privateTransferRrtProjectVintageAllocation(
    tx: EntityManager,
    transferId: uuid,
    now: Date,
    rrtAsset: RrtAsset,
    transferQuantity: number,
    assetFlows: AssetFlow[],
    defaultAllocation?: InternalAllocationResponse,
    rrtAllocation?: InternalAllocationResponse,
  ): void {
    if (transferQuantity === 0) {
      throw new InternalServerErrorException(
        `transferQuantity is 0, should not call privateTransferRrtProjectVintageAllocation`,
      );
    }

    if (transferQuantity < 0) {
      const absTransferQuantity = transferQuantity * -1;
      if (!rrtAllocation || rrtAllocation.amountAvailable < absTransferQuantity) {
        throw new ConflictException(
          `RRT Portfolio ${rrtAsset.rrt.name} must have at least ${absTransferQuantity} of ${rrtAsset.projectVintage.project.registryProjectId} ${rrtAsset.projectVintage.name()} for the transfer`,
        );
      }

      assetFlows.push(
        tx.create(AssetFlow, {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          amount: absTransferQuantity,
          asset: rrtAsset.projectVintage.id,
          assetType: AssetType.REGISTRY_VINTAGE,
          destination: uuid(environment.rubicon.books.portfolio.default),
          rawPrice: new Decimal(0),
          settledAt: now,
          source: rrtAsset.rrt.id,
          status: AssetFlowStatus.SETTLED,
          transaction: transferId,
          transactionType: TransactionType.INTERNAL_TRANSFER,
        }),
      );
    } else {
      if (!defaultAllocation || defaultAllocation.amountAvailable < transferQuantity) {
        throw new ConflictException(
          `default portfolio must have at least ${transferQuantity} of ${rrtAsset.projectVintage.project.registryProjectId} ${rrtAsset.projectVintage.name()} for the transfer`,
        );
      }

      assetFlows.push(
        tx.create(AssetFlow, {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          amount: transferQuantity,
          asset: rrtAsset.projectVintage.id,
          assetType: AssetType.REGISTRY_VINTAGE,
          destination: rrtAsset.rrt.id,
          rawPrice: new Decimal(0),
          settledAt: now,
          source: uuid(environment.rubicon.books.portfolio.default),
          status: AssetFlowStatus.SETTLED,
          transaction: transferId,
          transactionType: TransactionType.INTERNAL_TRANSFER,
        }),
      );
    }
  }
}
