/* third party */
import { FilterQuery, FindOptions, LoadStrategy, QueryOrder } from '@mikro-orm/core';
import { EntityManager, Knex } from '@mikro-orm/postgresql';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
/* rubicon */
import {
  AdminProjectSearchResponse,
  AuditLogAction,
  BookOrderByOption,
  BookRelations,
  BookType,
  NotificationEvent,
  OrderByDirection,
  PriceRange,
  ProjectRelations,
  uuid,
} from '@rubiconcarbon/shared-types';
/* env */
import { environment } from '@env/environment';
/* app */
import { UserClaims } from '@app/auth/interfaces';
import { Project, ProjectSDG, ProjectType, ProjectVintage, Registry, SDGType, User } from '@app/entities';
import { AuditLogsService } from '@app/utility/audit-log';
import {
  AdminBulkProjectUpdateRequestDTO,
  AdminProjectCreateRequestDTO,
  AdminProjectQueryDTO,
  AdminProjectRelationsQueryDTO,
  AdminProjectResponseDTO,
  AdminProjectSearchDTO,
  AdminProjectUpdateRequestDTO,
} from '@app/dtos/project.dto';
import { OrderByDirectionSQL } from '@app/enums/orderby.enum';
import { InternalPopulateAllocationsService } from '@app/services/allocations-populate.service';
import { NotificationsService } from './notifications.service';
import { InternalProjectCreated } from '@app/dtos/sendgrid-payload.dto';
import Decimal from 'decimal.js';
import { InternalProjectQueryResponse, InternalProjectResponse } from '@app/interfaces/project.interface';
import { BufferCategory } from '@app/entities/buffer-category.entity';
import { S3Service } from './s3.service';
import { ProjectUpdateLog } from '@app/entities/project-update-log.entity';
import { BooksService } from './books.service';

@Injectable()
export class ProjectsService {
  constructor(
    private auditLogsService: AuditLogsService,
    private readonly em: EntityManager,
    private ledgerProjectVintagesService: InternalPopulateAllocationsService,
    private notificationsService: NotificationsService,
    private s3Service: S3Service,
    private booksService: BooksService,
  ) {}

  async signUrls<
    T extends Pick<AdminProjectResponseDTO, 'mapImageUrl' | 'previewImageUrl' | 'illustrationImageUrl' | 'kml'>,
  >(type: { new (): T }, project: T): Promise<T> {
    const mapImageUrl = project.mapImageUrl
      ? await this.s3Service.getSignedDownloadLinkFromFullUrl(project.mapImageUrl)
      : undefined;
    const previewImageUrl = project.previewImageUrl
      ? await this.s3Service.getSignedDownloadLinkFromFullUrl(project.previewImageUrl)
      : undefined;
    const illustrationImageUrl = project.illustrationImageUrl
      ? await this.s3Service.getSignedDownloadLinkFromFullUrl(project.illustrationImageUrl)
      : undefined;
    const kml = project.kml?.url
      ? {
          latitude: project.kml.latitude,
          longitude: project.kml.longitude,
          url: project.kml.url ? await this.s3Service.getSignedDownloadLinkFromFullUrl(project.kml.url) : undefined,
        }
      : project.kml;
    return Object.assign(new type(), {
      ...project,
      mapImageUrl,
      previewImageUrl,
      illustrationImageUrl,
      kml,
    });
  }

  async create(data: AdminProjectCreateRequestDTO, claims: UserClaims): Promise<Project> {
    const now = new Date();
    const project = await this.em.transactional(async (tx) => {
      const projectType = await tx.findOne(ProjectType, data.projectTypeId);
      if (!projectType) {
        throw new BadRequestException([`projectTypeId ${data.projectTypeId} must be a valid id`]);
      }
      const registry =
        (await tx.findOne(Registry, { name: data.registryName })) ||
        (await tx.findOneOrFail(Registry, { name: 'Other' }));
      const sdgs = data.sdgIds ? await tx.find(SDGType, { id: data.sdgIds }) : [];
      const project = tx.create(Project, {
        ...data,
        id: uuid(),
        createdAt: now,
        updatedAt: now,
        projectType,
        registry,
      });
      if (sdgs.length > 0) {
        const projectSdgs = sdgs.map((m) =>
          tx.create(ProjectSDG, { project, sdgType: m, createdAt: now, updatedAt: now }),
        );
        project.projectSDGs.add(projectSdgs);
      }
      await this.auditLogsService.create(tx, claims, AuditLogAction.PROJECT_CREATED, now, project.id, {
        ...data,
      });
      return project;
    });

    try {
      await this.em.transactional(async (tx) => {
        const sendgridPayload: InternalProjectCreated = new InternalProjectCreated(
          await this.notificationsService.getNotificationUrl(NotificationEvent.PROJECT_CREATED),
          project.name,
          now,
          project.registryProjectId,
        );
        await this.notificationsService.handleEvent(
          tx,
          NotificationEvent.PROJECT_CREATED,
          sendgridPayload,
          data.surpressEmails,
        );
      });
    } catch (e) {
      console.error(
        `error sending ${NotificationEvent.PROJECT_CREATED} email for project ${project.id} : ` + JSON.stringify(e),
      );
    }

    return project;
  }

  async findAll(query: AdminProjectQueryDTO): Promise<InternalProjectQueryResponse> {
    const knex = this.em.getKnex();
    const where: FilterQuery<Project> = {};
    const flags: any = {};
    let hasFlags = false;

    if (query.projectTypeIds) {
      const projectTypes: ProjectType[] = await this.em.find(ProjectType, { id: query.projectTypeIds });
      where.projectType = projectTypes;
    }

    if (query.ids) {
      where.id = query.ids;
    }

    if (query.hasAmount) {
      hasFlags = true;
      Object.assign(flags as any, { hasBalance: true });
    }

    if (query.hasTrades) {
      hasFlags = true;
      Object.assign(flags as any, { hasTrades: true });
    }

    if (hasFlags) {
      where.flags = flags;
    }

    const includeAssetAllocations = [
      ProjectRelations.ASSET_ALLOCATIONS,
      ProjectRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE,
    ].some((s) => query.includeRelations.includes(s));
    let ledgerAssetIds: {
      project_vintage_id: uuid;
      project_id: uuid;
      risk_buffer_percentage?: Decimal;
      is_science_team_approved: boolean;
      is_byorct_approved: boolean;
      is_suspended: boolean;
      is_rct_standard: boolean;
    }[] = [];
    if (includeAssetAllocations || query.byoBufferEligible || query.isByorctEligible) {
      ledgerAssetIds = await knex
        .raw(
          `select 
            project_vintage_id, 
            project_id, 
            project_vintage_risk_buffer_percentage as risk_buffer_percentage, 
            project_is_science_team_approved as is_science_team_approved, 
            project_is_byorct_approved as is_byorct_approved, 
            project_is_suspended as is_suspended, 
            project_is_rct_standard as is_rct_standard
          from "${environment.db.schema.rubicon}".project_vintage_groupings_v2;`,
        )
        .then((r) => {
          return r.rows;
        });

      if (query.ids && query.ids.length > 0) {
        ledgerAssetIds = ledgerAssetIds.filter((f) => query.ids!.includes(f.project_id));
      }

      if (query.byoBufferEligible) {
        ledgerAssetIds = ledgerAssetIds.filter(
          (d) =>
            d.risk_buffer_percentage &&
            // check that risk buffer is under 100%
            new Decimal(1).greaterThanOrEqualTo(d.risk_buffer_percentage),
        );
      }

      if (query.isByorctEligible) {
        ledgerAssetIds = ledgerAssetIds.filter(
          (d) => d.is_science_team_approved && d.is_byorct_approved && !d.is_suspended && d.is_rct_standard,
        );
      }

      where.id = ledgerAssetIds.map((m) => m.project_id);
    }

    let hasManyResponse: InternalProjectQueryResponse;
    if (query.includeTotalCount) {
      const response = await this.em.findAndCount(Project, where, {
        limit: query.limit,
        offset: query.offset,
        populate: this.privatePopulateProjectRelations(query.includeRelations),
        orderBy: { [query.orderBy]: OrderByDirectionSQL[query.orderByDirection] },
        connectionType: 'read',
      });
      hasManyResponse = {
        data: response[0],
        page: {
          size: response[0].length,
          limit: query.limit,
          offset: query.offset,
          totalCount: response[1],
        },
      };
    } else {
      const response = await this.em.find(Project, where, {
        limit: query.limit,
        offset: query.offset,
        populate: this.privatePopulateProjectRelations(query.includeRelations),
        orderBy: { [query.orderBy]: OrderByDirectionSQL[query.orderByDirection] },
        connectionType: 'read',
      });
      hasManyResponse = {
        data: response,
        page: {
          size: response.length,
          limit: query.limit,
          offset: query.offset,
        },
      };
    }
    if (hasManyResponse.data.length === 0) {
      return hasManyResponse;
    }

    if (includeAssetAllocations) {
      await this.ledgerProjectVintagesService.internalPopulateProjectsAllocations(
        this.em,
        hasManyResponse.data,
        ledgerAssetIds,
        query.includeRelations,
      );
    }

    if (query.includeRelations.includes(ProjectRelations.PRICE_RANGE)) {
      await this.privateCalculatePriceRange(knex, hasManyResponse.data);
    }

    if (query.includeRelations.includes(ProjectRelations.PRODUCTS)) {
      // get allocations for all *public* portfolios
      const books = (
        await this.booksService.findMany({
          types: [BookType.RCT_PUBLIC],
          includeRelations: [BookRelations.OWNER_ALLOCATIONS_NESTED, BookRelations.ASSET_ALLOCATIONS],
          limit: 1000,
          offset: 0,
          orderBy: BookOrderByOption.NAME,
          includeTotalCount: false,
          orderByDirection: OrderByDirection.ASC,
        })
      ).data;

      const productsByProject = new Map<uuid, Set<uuid>>();

      books.forEach((b) =>
        b.ownerAllocations?.allocations?.forEach((a) => {
          const projectId = (a.detailedAsset as ProjectVintage).project.id;
          const hit = productsByProject.get(projectId);
          if (!hit) {
            productsByProject.set(projectId, new Set([b.id]));
          } else {
            hit.add(b.id);
          }
        }),
      );

      for (const project of hasManyResponse.data) {
        const hit = productsByProject.get(project.id);
        if (hit) {
          project.productIds = Array.from(hit);
        } else {
          project.productIds = [];
        }
      }
    }

    return hasManyResponse;
  }

  async findOne(id: uuid, query: AdminProjectRelationsQueryDTO): Promise<InternalProjectResponse> {
    const knex = this.em.getKnex();

    const project = await this.em.findOneOrFail(
      Project,
      { id },
      {
        populate: this.privatePopulateProjectRelations(query.includeRelations),
        connectionType: 'read',
      },
    );

    if (query.includeRelations.includes(ProjectRelations.PRICE_RANGE)) {
      await this.privateCalculatePriceRange(knex, [project]);
    }

    if (query.includeRelations.includes(ProjectRelations.ASSET_ALLOCATIONS)) {
      const ledgerAssetIds: { project_vintage_id: uuid; project_id: uuid; risk_buffer_percentage?: Decimal }[] =
        await knex
          .raw(
            `select project_vintage_id, project_id, project_vintage_risk_buffer_percentage as risk_buffer_percentage
            from "${environment.db.schema.rubicon}".project_vintage_groupings_v2
            where project_id in (${[project].map(() => '?').join(',')});`,
            [...[project].map((m) => m.id)],
          )
          .then((r) => {
            return r.rows;
          });

      await this.ledgerProjectVintagesService.internalPopulateProjectsAllocations(
        this.em,
        [project],
        ledgerAssetIds,
        query.includeRelations,
      );
    }

    // with grouped allocations, taking this out for now until FE confirms if they need it, look into when/why/how
    // if (query.includeRelations.includes(ProjectRelations.PROJECT_VINTAGES)) {
    //   await this.ledgerProjectVintagesService.populateProjectVintagesAmounts(
    //     this.em,
    //     project.projectVintages.getItems(),
    //   );
    // }

    return project;
  }

  async search(query: AdminProjectSearchDTO): Promise<AdminProjectSearchResponse[]> {
    if ((!query.name && !query.id) || query.q == '') return [];

    const knex = this.em.getKnex();

    let knexQuery = knex
      .withSchema(environment.db.schema.rubicon)
      .from('projects as p')
      .select(
        knex.raw(
          `p.id,
          p.name,
          p.registry_project_id as "registryProjectId",
          (NOT p.registry_project_id ~* ?)::text || '|' ||(NOT p.name ~* ?)::text || '|' || p.name as sorting,
          -1 as score`,
          [query.q, query.q],
        ),
      );

    if (query.name) knexQuery = knexQuery.orWhereRaw('p.name ~* ?', [query.q]);
    if (query.id) knexQuery = knexQuery.orWhereRaw('p.registry_project_id ~* ?', [query.q]);

    if (query.fuzzy) {
      const knexFuzzy = knex
        .withSchema(environment.db.schema.rubicon)
        .from('projects as p')
        .select(
          knex.raw(`p.id, p.name, p.registry_project_id as "registryProjectId", 'true|true', SIMILARITY(p.name, ?)`, [
            query.q,
          ]),
        )
        .whereRaw('SIMILARITY(p.name, ?) > 0.3', [query.q]);
      knexQuery = knexQuery.union(knexFuzzy);
    }

    const fullQuery = knex
      .with('q', knexQuery.orderByRaw('sorting, score DESC'))
      .select('q.id', 'q.name', 'q.registryProjectId')
      .distinct()
      .from('q');

    if (query.hasBalance !== undefined) {
      fullQuery
        .leftJoin(`${environment.db.schema.rubicon}.project_flags_v2 as pf`, 'pf.id', 'q.id')
        .where({ 'pf.has_balance': query.hasBalance });
    }

    if (
      [
        query.regions,
        query.emissionsImpactTypes,
        query.projectTypesCategories,
        query.eligibilityAccreditations,
        query.integrityGrades,
      ].some((value) => value !== undefined && value !== null)
    ) {
      fullQuery.leftJoin(`${environment.db.schema.rubicon}.projects`, 'projects.id', 'q.id');
    }

    if (query.regions !== undefined) {
      fullQuery
        .leftJoin(`${environment.db.schema.rubicon}.countries`, 'countries.alpha3', 'projects.country_code')
        .whereIn('countries.region', query.regions);
    }

    if (query.emissionsImpactTypes !== undefined) {
      fullQuery.whereIn('projects.emissions_impact_type', query.emissionsImpactTypes);
    }

    if (query.projectTypesCategories !== undefined) {
      fullQuery
        .leftJoin(`${environment.db.schema.rubicon}.project_types`, 'project_types.id', 'projects.project_type_id')
        .whereIn('project_types.category', query.projectTypesCategories);
    }

    if (query.eligibilityAccreditations !== undefined) {
      fullQuery.where('projects.eligibility_accreditations', '&&', query.eligibilityAccreditations);
    }

    if (query.integrityGrades !== undefined) {
      fullQuery.whereRaw(
        `
        (CASE 
          WHEN projects.integrity_grade_score >= 84.5 THEN 'A'
          WHEN projects.integrity_grade_score < 84.5 AND projects.integrity_grade_score >= 74.5 THEN 'B'
          WHEN projects.integrity_grade_score < 74.5 AND projects.integrity_grade_score >= 59.5 THEN 'C'
          WHEN projects.integrity_grade_score < 59.5 THEN 'D'
        END) = ANY(?)`,
        [query.integrityGrades],
      );
    }

    const x: { id: uuid; name: string; registryProjectId: string }[] = await fullQuery.limit(query.limit);

    const result = await this.em.find(
      Project,
      { id: x.map((m) => m.id) },
      { populate: ['bufferCategory', 'country', 'projectType.bufferCategory'] },
    );

    result.forEach((x) => {
      // buffer category fallback
      x.bufferCategory = x.bufferCategory ?? x.projectType.bufferCategory;
    });

    return result.map((m) => {
      return {
        id: m.id,
        country: m.country,
        hasBalance: m.flags?.hasBalance || false,
        isScienceTeamApproved: m.isScienceTeamApproved,
        name: m.name,
        projectType: {
          id: m.projectType.id,
          category: m.projectType.category,
          name: m.projectType.name,
          type: m.projectType.type,
        },
        bufferCategory: m.bufferCategory,
        rctStandard: m.rctStandard,
        registryProjectId: m.registryProjectId,
        suspended: m.suspended,
        integrityGradeScore: m.integrityGradeScore,
      };
    });
  }

  async update(id: uuid, data: AdminProjectUpdateRequestDTO, claims: UserClaims): Promise<Project> {
    const now = new Date();
    const response = await this.em.transactional(async (tx) => {
      const project: Project = await tx.findOneOrFail(
        Project,
        { id },
        { populate: ['projectType', 'registry', 'projectSDGs.sdgType', 'country'] },
      );

      if (data.projectTypeId !== undefined) {
        try {
          project.projectType = await tx.findOneOrFail(ProjectType, { id: data.projectTypeId });
          /* eslint-disable-next-line @typescript-eslint/no-unused-vars */
        } catch (err) {
          throw new BadRequestException([`projectTypeId ${data.projectTypeId} must be a valid id`]);
        }
        delete data['projectTypeId'];
      }

      if (data.sdgIds !== undefined) {
        const projectSdgs = await tx.find(ProjectSDG, { project }, { populate: ['sdgType'] });
        const existing = projectSdgs.map((s) => s.sdgTypeId());

        const toKeep = projectSdgs.filter((x) => (data.sdgIds || []).includes(x.sdgTypeId()));
        const toRemove = projectSdgs.filter((x) => !(data.sdgIds || []).includes(x.sdgTypeId()));
        const toAdd = (data.sdgIds || []).filter((x) => !existing.includes(x));

        // remove extras
        tx.remove(toRemove);

        // add new ones
        for (const s of toAdd) {
          toKeep.push(
            tx.create(ProjectSDG, { project, sdgType: tx.getReference(SDGType, s), createdAt: now, updatedAt: now }),
          );
        }
        project.projectSDGs.set(toKeep);
        await tx.populate(project.projectSDGs, ['sdgType']);
      }

      if (data.registryName && data.registryName !== project.registryName) {
        const registry =
          (await tx.findOne(Registry, { name: data.registryName })) ||
          (await tx.findOneOrFail(Registry, { name: 'Other' }));
        project.registry = registry;
      }

      if (!data.eligibilityAccreditations) delete data.eligibilityAccreditations;
      if (data.isScienceTeamApproved === undefined) delete data.isScienceTeamApproved;
      if (data.rctStandard === undefined) delete data.rctStandard;
      if (data.suspended === undefined) delete data.suspended;
      if (data.isByorctApproved === undefined) delete data.isByorctApproved;
      Object.assign(project, { ...data, updatedAt: now });
      await this.auditLogsService.create(tx, claims, AuditLogAction.PROJECT_UPDATED, now, id, { ...data });

      return project;
    });

    await this.em.populate(response, ['country', 'registry']);
    return response;
  }

  async updateBulk(claims: UserClaims, data: AdminBulkProjectUpdateRequestDTO[]): Promise<Project[]> {
    // get and build lookup baps for buffer cats to match at the end
    const bufferCategories = await this.em.find(BufferCategory, {});
    const bufferCatsByName = new Map<string, BufferCategory>(bufferCategories.map((x) => [x.name, x]));
    const bufferCatsById = new Map<uuid, BufferCategory>(bufferCategories.map((x) => [x.id, x]));
    if (data.map((d) => d.memo).filter((m) => m !== data[0].memo).length > 0) {
      throw new BadRequestException(`Memo in given payload must match`);
    }
    const memo = data[0].memo;

    const now = new Date();
    const response = await this.em.transactional(async (tx) => {
      const projects: Project[] = await tx.find(
        Project,
        { id: data.map((m) => m.id) },
        {
          populate: [
            'projectType',
            'registry',
            'projectSDGs.sdgType',
            'country',
            'isScienceTeamApproved',
            'rctStandard',
            'suspended',
            'isByorctApproved',
          ],
        },
      );
      const badProjectIds = data.map((d) => d.id).filter((f) => !projects.map((m) => m.id).includes(f));
      if (badProjectIds.length > 0) {
        throw new NotFoundException(`projects [${badProjectIds.join(',')}] not found`);
      }

      for (const d of data) {
        const project = projects.find((f) => f.id === d.id);
        if (!project) {
          // already validated from above
          throw new NotFoundException(`project ${d.id} not found`);
        }

        if (d.projectTypeId !== undefined) {
          try {
            project.projectType = await tx.findOneOrFail(ProjectType, { id: d.projectTypeId });
            /* eslint-disable-next-line @typescript-eslint/no-unused-vars */
          } catch (err) {
            throw new BadRequestException([`projectTypeId ${d.projectTypeId} must be a valid id`]);
          }
          delete d['projectTypeId'];
        }

        if (d.sdgIds !== undefined) {
          const sdgs = await tx.find(ProjectSDG, { project: { id: d.id } }, { populate: ['sdgType'] });
          const existing = sdgs.map((s) => s.sdgTypeId());

          const toKeep = sdgs.filter((x) => (d.sdgIds || []).includes(x.sdgTypeId()));
          const toRemove = sdgs.filter((x) => !(d.sdgIds || []).includes(x.sdgTypeId()));
          const toAdd = (d.sdgIds || []).filter((x) => !existing.includes(x));

          // remove extras
          tx.remove(toRemove);

          // add new ones
          for (const s of toAdd) {
            toKeep.push(
              tx.create(ProjectSDG, { project, sdgType: tx.getReference(SDGType, s), createdAt: now, updatedAt: now }),
            );
          }
          project.projectSDGs.set(toKeep);
          await tx.populate(project.projectSDGs, ['sdgType']);
        }

        if (d.registryName && d.registryName !== project.registryName) {
          const registry =
            (await tx.findOne(Registry, { name: d.registryName })) ||
            (await tx.findOneOrFail(Registry, { name: 'Other' }));
          project.registry = registry;
        }

        if (!d.eligibilityAccreditations) delete d.eligibilityAccreditations;
        if (d.isScienceTeamApproved === undefined) delete d.isScienceTeamApproved;
        if (d.rctStandard === undefined) delete d.rctStandard;
        if (d.suspended === undefined) delete d.suspended;
        if (d.isByorctApproved === undefined) delete d.isByorctApproved;
        Object.assign(project, { ...d, updatedAt: now });

        if (d.bufferCategory) {
          // override with the buffer category specified by name
          project.bufferCategory =
            bufferCatsByName.get(d.bufferCategory) ?? (d.bufferCategoryId && bufferCatsById.get(d.bufferCategoryId));
        }

        await this.auditLogsService.create(tx, claims, AuditLogAction.PROJECT_UPDATED, now, project.id, { data });
      }
      await this.logBulkProjectUpdate(tx, claims, memo, { data }, now);
      return projects;
    });

    await this.em.populate(response, ['country', 'registry']);

    return response;
  }

  async logBulkProjectUpdate(
    tx: EntityManager,
    claims: Pick<UserClaims, 'id' | 'email'>,
    memo: string,
    data: any,
    timestamp: Date,
  ): Promise<ProjectUpdateLog> {
    try {
      const user = await tx.findOneOrFail(User, { id: claims.id }); // this should never fail
      return tx.create(ProjectUpdateLog, {
        createdAt: timestamp,
        updatedAt: timestamp,
        user,
        memo,
        data,
      });
    } catch (e) {
      console.error(
        `meant to add project update log ${claims.email}, ${JSON.stringify(data)} but failed with ${JSON.stringify(e)}`,
      );

      throw e;
    }
  }

  async getLogs(): Promise<ProjectUpdateLog[]> {
    return await this.em.find(
      ProjectUpdateLog,
      {},
      {
        orderBy: { createdAt: QueryOrder.DESC },
        populate: ['user'],
        strategy: LoadStrategy.JOINED,
        connectionType: 'read',
      },
    );
  }

  /* private */

  private privateCalculateMarkupPrice(unitPrice: Decimal): Decimal {
    let markup = new Decimal(0.5);
    if (unitPrice.greaterThan(40)) {
      markup = new Decimal(0.2);
    } else if (unitPrice.greaterThanOrEqualTo(10)) {
      markup = new Decimal(50).sub(unitPrice.sub(10)).div(100);
    }
    const markupPrice = unitPrice.mul(markup.add(1));
    return markupPrice;
  }

  private async privateCalculatePriceRange(
    knex: Knex,
    projects: InternalProjectResponse[],
  ): Promise<InternalProjectResponse[]> {
    const prices: { project_id: uuid; price: string }[] = await knex
      .select(['project_id', 'price'])
      .from(`${environment.db.schema.rubicon}.project_vintage_prices_v2`)
      .distinctOn('project_id')
      .whereIn(
        'project_id',
        projects.map((m) => m.id),
      )
      .andWhereRaw('price is not null')
      .orderByRaw('project_id asc, rubicon_date desc');

    for (const project of projects) {
      const projectPrice = prices.find((f) => f.project_id === project.id);
      if (!projectPrice) {
        console.error(`could not find price for project ${project.name} (${project.registryProjectId})`);
        continue;
      }

      const unitPrice = new Decimal(projectPrice.price);
      const markupPrice = this.privateCalculateMarkupPrice(unitPrice);

      if (markupPrice.lessThan(10)) {
        project.priceRange = PriceRange['10_LESS'];
      } else if (markupPrice.lessThan(25)) {
        project.priceRange = PriceRange['10_25'];
      } else if (markupPrice.lessThan(50)) {
        project.priceRange = PriceRange['25_50'];
      } else if (markupPrice.lessThan(100)) {
        project.priceRange = PriceRange['50_100'];
      } else {
        project.priceRange = PriceRange['100_PLUS'];
      }
    }
    return projects;
  }

  private privatePopulateProjectRelations<S extends string>(
    relations: ProjectRelations[],
  ): FindOptions<Project, S>['populate'] {
    const populateString: string[] = ['projectType'];
    if (relations.includes(ProjectRelations.COUNTRY)) {
      populateString.push(ProjectRelations.COUNTRY);
    }
    if (relations.includes(ProjectRelations.PROJECT_VINTAGES)) {
      populateString.push(ProjectRelations.PROJECT_VINTAGES);
    }
    if (relations.includes(ProjectRelations.SDGS)) {
      populateString.push('projectSDGs.sdgType'); // todo : (TD-8) fix enum when FE confirms using ProjectRelation
    }
    if (relations.includes(ProjectRelations.BUFFER_CATEGORY)) {
      populateString.push(ProjectRelations.BUFFER_CATEGORY);
      populateString.push('projectType.bufferCategory'); // for the fallback behavior
    }
    return populateString as unknown as FindOptions<Project, S>['populate'];
  }
}
