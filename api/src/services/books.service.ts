/* third party */
import { FilterQuery, FindOptions, LockMode } from '@mikro-orm/core';
import { EntityManager } from '@mikro-orm/postgresql';
import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import Decimal from 'decimal.js';
/* rubicon */
import {
  AllAdminBookTypes,
  AllPortalBookTypes,
  AssetType,
  AuditLogAction,
  BookAction,
  BookRelations,
  BookType,
  GroupingRelations,
  NotificationEvent,
  uuid,
} from '@rubiconcarbon/shared-types';
/* env */
import { environment } from '@env/environment';
/* app */
import { UserClaims } from '@app/auth';
import {
  AdminBookQueryDTO,
  AdminBookUpdateRequestDTO,
  AdminRctCustomPortfolioCreateRequestDTO,
  AdminRctPublicPortfolioCreateRequestDTO,
  AdminRrtPortfolioCreateRequestDTO,
  BaseBookLimitDTO,
  PortalBookQueryDTO,
} from '@app/dtos/book.dto';
import {
  InternalPortfolioCreated,
  InternalPortfolioPriceUpdated,
  PortfolioPriceChangeDetails,
} from '@app/dtos/sendgrid-payload.dto';
import {
  Asset,
  Book,
  Grouping,
  GroupingParent,
  HistoricalBookPrice,
  Organization,
  ProjectType,
  RrtAsset,
} from '@app/entities';
import { BookPriceType } from '@app/enums/book.enum';
import { OrderByDirectionSQL } from '@app/enums/orderby.enum';
import { getPortalUserWithOrganizationFromClaims } from '@app/helpers';
import {
  InternalEmptyGroupedAllocationsResponse,
  InternalGroupedAllocationsResponse,
} from '@app/interfaces/allocation.interface';
import {
  InternalBookQueryResponse,
  InternalBookResponse,
  InternalPortfolioBookQueryRequest,
  InternalRrtPortfolioResponse,
} from '@app/interfaces/book.interface';
import { InternalGroupingParent } from '@app/interfaces/grouping-parent.interface';
import { AuditLogsService } from '@app/utility/audit-log';
import { InternalPopulateAllocationsService } from './allocations-populate.service';
import { AlertsService } from './alerts.service';
import { NotificationsService } from './notifications.service';
import { InternalPortalUser } from '@app/interfaces/user.interface';

@Injectable()
export class BooksService {
  constructor(
    private alertsService: AlertsService,
    private auditLogsService: AuditLogsService,
    private readonly em: EntityManager,
    private populateAllocationsService: InternalPopulateAllocationsService,
    private notificationsService: NotificationsService,
  ) {}

  /**
   * create a CustomPortfolio Book entity
   *
   * steps:
   * 1. get current date as now
   * 2. start a database transaction
   * 3. generate Book id
   * 4. if organizationId is provided, find Organization entity
   * 5. if organizationId is provided and Organization not found, throw BadRequestException
   * 6. validate purchasePrice and purchasePriceWithBuffer using privateValidatePortfolioBookPrice
   * 7. create Book entity with provided data and Organization
   * 8. create Grouping entity for Book
   * 9. create Asset entity for Book
   * 10. if purchasePrice is provided, create HistoricalBookPrice entity for purchasePrice
   * 11. if purchasePriceWithBuffer is provided, create HistoricalBookPrice entity for purchasePriceWithBuffer
   * 12. create AuditLogsService entry for Book creation
   * 13. return created Book entity from transaction
   * 14. send Book created notification using privateHandleBookCreatedNotification
   * 15. return created Book entity
   *
   * @param {UserClaims} claims
   * @param {AdminRctCustomPortfolioCreateRequestDTO} data
   * @returns {Promise<Book>}
   * @throws BadRequestException
   */
  async createRctCustomPortfolio(claims: UserClaims, data: AdminRctCustomPortfolioCreateRequestDTO): Promise<Book> {
    const now: Date = new Date();

    const book = await this.em.transactional(async (tx) => {
      const bookId: uuid = uuid();

      const organization = data.organizationId
        ? await tx.findOne(Organization, { id: data.organizationId })
        : undefined;
      if (data.organizationId && !organization) {
        throw new BadRequestException([`organizationId must be a valid uuid`]);
      }

      this.privateValidatePortfolioBookPrice(data.purchasePrice, data.purchasePriceWithBuffer);

      const book = tx.create(Book, {
        ...data,
        id: bookId,
        createdAt: now,
        updatedAt: now,
        priceUpdatedAt: now,
        type: BookType.RCT_CUSTOM,
        allowedActions: [BookAction.RETIRE],
        assetType: AssetType.RCT,
        organization,
      });
      tx.create(Grouping, {
        parent: environment.rubicon.groupings.bookType.portfolio,
        book: bookId,
      });

      tx.create(Asset, {
        id: bookId,
        createdAt: now,
        updatedAt: now,
        type: AssetType.RCT,
      });

      if (data.purchasePrice) {
        tx.create(HistoricalBookPrice, {
          timestamp: now,
          updatedBy: claims.id,
          book,
          oldPrice: new Decimal(0),
          newPrice: data.purchasePrice,
          type: BookPriceType.PURCHASE_PRICE_WITHOUT_BUFFER,
        });
      }
      if (data.purchasePriceWithBuffer) {
        tx.create(HistoricalBookPrice, {
          timestamp: now,
          updatedBy: claims.id,
          book,
          oldPrice: new Decimal(0),
          newPrice: data.purchasePriceWithBuffer,
          type: BookPriceType.PURCHASE_PRICE_WITH_BUFFER,
        });
      }

      await this.auditLogsService.create(tx, claims, AuditLogAction.BOOK_CREATED, now, bookId, { ...book });
      return book;
    });

    // send email notifications
    await this.privateHandleBookCreatedNotification(now, NotificationEvent.BOOK_CREATED, book);

    return book;
  }

  /**
   * create a PublicPortfolio Book entity
   *
   * steps:
   * 1. get current date as now
   * 2. start a database transaction
   * 3. generate Book id
   * 4. validate purchasePrice and purchasePriceWithBuffer using privateValidatePortfolioBookPrice
   * 5. create Book entity with provided data
   * 6. create Grouping entity for Book
   * 7. create Asset entity for Book
   * 8. if purchasePrice is provided, create HistoricalBookPrice entity for purchasePrice
   * 9. if purchasePriceWithBuffer is provided, create HistoricalBookPrice entity for purchasePriceWithBuffer
   * 10. create AuditLogsService entry for Book creation
   * 11. return created Book entity from transaction
   * 12. send Book created notification using privateHandleBookCreatedNotification
   * 13. return created Book entity
   *
   * @param {UserClaims} claims
   * @param {AdminRctPublicPortfolioCreateRequestDTO} data
   * @returns {Promise<Book>}
   * @throws BadRequestException
   */
  async createRctPublicPortfolio(claims: UserClaims, data: AdminRctPublicPortfolioCreateRequestDTO): Promise<Book> {
    const now: Date = new Date();
    const book = await this.em.transactional(async (tx) => {
      const bookId: uuid = uuid();

      this.privateValidatePortfolioBookPrice(data.purchasePrice, data.purchasePriceWithBuffer);

      const book = tx.create(Book, {
        ...data,
        id: bookId,
        createdAt: now,
        updatedAt: now,
        priceUpdatedAt: now,
        type: BookType.RCT_PUBLIC,
        allowedActions: [BookAction.RETIRE],
        assetType: AssetType.RCT,
      });

      tx.create(Grouping, {
        parent: environment.rubicon.groupings.bookType.portfolio,
        book: bookId,
      });
      tx.create(Asset, {
        id: bookId,
        createdAt: now,
        updatedAt: now,
        type: AssetType.RCT,
      });

      if (data.purchasePrice) {
        tx.create(HistoricalBookPrice, {
          timestamp: now,
          updatedBy: claims.id,
          book,
          oldPrice: new Decimal(0),
          newPrice: data.purchasePrice,
          type: BookPriceType.PURCHASE_PRICE_WITHOUT_BUFFER,
        });
      }
      if (data.purchasePriceWithBuffer) {
        tx.create(HistoricalBookPrice, {
          timestamp: now,
          updatedBy: claims.id,
          book,
          oldPrice: new Decimal(0),
          newPrice: data.purchasePriceWithBuffer,
          type: BookPriceType.PURCHASE_PRICE_WITH_BUFFER,
        });
      }

      await this.auditLogsService.create(tx, claims, AuditLogAction.BOOK_CREATED, now, bookId, { ...book });
      return book;
    });

    // send email notifications
    await this.privateHandleBookCreatedNotification(now, NotificationEvent.BOOK_CREATED, book);

    return book;
  }

  async createRrtPublicPortfolio(claims: UserClaims, data: AdminRrtPortfolioCreateRequestDTO): Promise<Book> {
    const now: Date = new Date();
    const book = await this.em.transactional(async (tx) => {
      const bookId: uuid = uuid();
      const book = tx.create(Book, {
        ...data,
        id: bookId,
        createdAt: now,
        updatedAt: now,
        priceUpdatedAt: now,
        type: BookType.RRT_PUBLIC,
        allowedActions: [],
        assetType: AssetType.RRT,
        limit: new BaseBookLimitDTO(),
      });

      tx.create(Asset, {
        id: bookId,
        createdAt: now,
        updatedAt: now,
        type: AssetType.RRT,
      });

      tx.create(Grouping, {
        parent: environment.rubicon.groupings.bookType.portfolio,
        book: bookId,
      });

      tx.create(HistoricalBookPrice, {
        timestamp: now,
        updatedBy: claims.id,
        book,
        oldPrice: new Decimal(0),
        newPrice: data.purchasePrice,
        type: BookPriceType.PURCHASE_PRICE_WITHOUT_BUFFER,
      });

      await this.auditLogsService.create(tx, claims, AuditLogAction.BOOK_CREATED, now, bookId, { ...book });
      return book;
    });

    // send email notifications
    await this.privateHandleBookCreatedNotification(now, NotificationEvent.BOOK_CREATED, book);

    return book;
  }

  /**
   * find many Book entities for admin endpoint
   *
   * steps:
   * 1. build filter query from AdminBookQueryDTO
   * 2. find Book entities matching filter with pagination and sorting
   * 3. populate Book relations as needed
   * 4. populate allocations if requested
   * 5. return InternalBookQueryResponse
   *
   * @param {AdminBookQueryDTO} query
   * @returns {Promise<InternalBookQueryResponse>}
   */
  async findMany(query: AdminBookQueryDTO): Promise<InternalBookQueryResponse> {
    const where: FilterQuery<Book> = {};
    // only return books with types to be exposed on FE, ignoring external and rubicon
    where.type = query.types || AllAdminBookTypes;

    if (query.organizationId) {
      where.organization = query.organizationId;
    }

    if (query.ids && query.ids.length > 0) {
      where.id = query.ids;
    }

    if (query.allowedActions) {
      where.allowedActions = { $overlap: query.allowedActions };
    }

    if (query.isEnabled !== undefined) {
      // only show isEnabled options for admin
      // public should only see isEnabled = true
      where.isEnabled = query.isEnabled;
    }

    if (query.name) {
      where.name = query.name;
    }

    if (query.types) {
      where.type = query.types;
    }

    let findManyResponse: InternalBookQueryResponse;
    if (query.includeTotalCount) {
      const response = await this.em.findAndCount(Book, where, {
        limit: query.limit,
        offset: query.offset,
        orderBy: { [query.orderBy]: OrderByDirectionSQL[query.orderByDirection] },
        populate: this.privatePopulateBookRelations(query.includeRelations),
        connectionType: 'read',
      });
      findManyResponse = {
        data: response[0],
        page: {
          limit: query.limit,
          offset: query.offset,
          size: response[0].length,
          totalCount: response[1],
        },
      };
    } else {
      const response = await this.em.find(Book, where, {
        limit: query.limit,
        offset: query.offset,
        orderBy: { [query.orderBy]: OrderByDirectionSQL[query.orderByDirection] },
        populate: this.privatePopulateBookRelations(query.includeRelations),
        connectionType: 'read',
      });
      findManyResponse = {
        data: response,
        page: {
          limit: query.limit,
          offset: query.offset,
          size: response.length,
        },
      };
    }

    if (
      [
        BookRelations.ASSET_ALLOCATIONS,
        BookRelations.ASSET_ALLOCATIONS_NESTED,
        BookRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE,
        BookRelations.OWNER_ALLOCATIONS,
        BookRelations.OWNER_ALLOCATIONS_NESTED,
        BookRelations.OWNER_ALLOCATIONS_BY_ASSET_TYPE,
        BookRelations.OWNER_ALLOCATIONS_BY_PROJECT,
        BookRelations.OWNER_ALLOCATIONS_BY_PROJECT_TYPE,
      ].some((s) => query.includeRelations.includes(s))
    ) {
      await this.populateAllocationsService.internalPopulateBooksAllocations(
        this.em,
        findManyResponse.data,
        undefined,
        query.includeRelations,
      );
    }

    return findManyResponse;
  }

  /**
   * find many Book entities for portal endpoints
   *
   * steps:
   * 1. get InternalPortalUser entity from UserClaims using getPortalUserWithOrganizationFromClaims
   * 2. if InternalPortalUser not found throw NotFoundException
   * 3. call privateFindManyPortfolios with organizationId if exists, and isEnabled true
   * 4. return InternalBookQueryResponse
   *
   * @param {UserClaims} claims
   * @param {PortalBookQueryDTO} query
   * @returns {Promise<InternalBookQueryResponse>}
   * @throws NotFoundException
   */
  async findManyForPortal(claims: UserClaims, query: PortalBookQueryDTO): Promise<InternalBookQueryResponse> {
    let organizationId: uuid | undefined = undefined;
    try {
      const user: InternalPortalUser = await getPortalUserWithOrganizationFromClaims(this.em, claims);
      organizationId = user.organizationUser.organization.id;
    } catch (e) {
      if (e instanceof NotFoundException) {
        throw e;
      }
    }

    return await this.privateFindManyPortfolios({
      ...query,
      organizationId,
      isEnabled: true,
    });
  }

  /**
   * find a single Book entity by id
   *
   * steps:
   * 1. if UserClaims provided, call findManyForPortal to check access and retrieve Book
   * 2. if not, find Book by id
   * 3. if Book not found, throw NotFoundException
   * 4. populate requested relations for Book
   * 5. populate allocations if requested
   * 6. return InternalBookResponse
   *
   * @param {uuid} id
   * @param {BookRelations[]} includeRelations
   * @param {UserClaims} [claims]
   * @returns {Promise<InternalBookResponse>}
   * @throws NotFoundException
   */
  async findOne(id: uuid, includeRelations: BookRelations[] = [], claims?: UserClaims): Promise<InternalBookResponse> {
    const where: FilterQuery<Book> = { id };
    let book: InternalBookResponse | undefined = undefined;
    // claims should only be passed in for public endpoints
    if (claims) {
      const query = new PortalBookQueryDTO();
      query.limit = 1;
      query.ids = [id];
      const booksResponse = await this.findManyForPortal(claims, query);
      if (booksResponse.data.length === 0) {
        throw new NotFoundException(`Book ${id} not found`);
      }
      book = booksResponse.data[0];
    } else {
      book = await this.em.findOneOrFail(Book, where, { connectionType: 'read' });
    }

    if (!book) {
      throw new NotFoundException(`Book ${id} not found`);
    }

    if (includeRelations.includes(BookRelations.OWNER_ALLOCATIONS_BY_PROJECT_TYPE)) {
      await this.em.populate(book, ['projectTypes']);
    }
    if (includeRelations.includes(BookRelations.ORGANIZATION)) {
      await this.em.populate(book, ['organization']);
    }

    if (
      [
        BookRelations.ASSET_ALLOCATIONS,
        BookRelations.ASSET_ALLOCATIONS_NESTED,
        BookRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE,
        BookRelations.OWNER_ALLOCATIONS,
        BookRelations.OWNER_ALLOCATIONS_NESTED,
        BookRelations.OWNER_ALLOCATIONS_BY_ASSET_TYPE,
        BookRelations.OWNER_ALLOCATIONS_BY_PROJECT,
        BookRelations.OWNER_ALLOCATIONS_BY_PROJECT_TYPE,
      ].some((s) => includeRelations.includes(s))
    ) {
      await this.populateAllocationsService.internalPopulateBookAllocations(this.em, book, undefined, includeRelations);
    }
    return book;
  }

  async findOneRrt(id: uuid): Promise<InternalRrtPortfolioResponse> {
    const rrt: InternalRrtPortfolioResponse = {
      ...(await this.em.findOneOrFail(Book, {
        id,
        type: BookType.RRT_PUBLIC,
      })),
      assets: [],
      ownerAllocations: InternalEmptyGroupedAllocationsResponse,
      assetAllocations: InternalEmptyGroupedAllocationsResponse,
    };
    const rrtAssets = await this.em.find(RrtAsset, { rrt: id }, { populate: ['rrt', 'projectVintage'] });
    if (rrtAssets.length === 0) {
      return rrt;
    }

    // populate projectVintages in rrt portfolio
    await this.populateAllocationsService.internalPopulateBookAllocations(this.em, rrt, undefined, [
      BookRelations.OWNER_ALLOCATIONS_NESTED,
      BookRelations.ASSET_ALLOCATIONS,
    ]);

    for (const rrtAsset of rrtAssets) {
      const allocation = (rrt.ownerAllocations.allocations || []).find(
        (f) => f.asset.id === rrtAsset.projectVintage.id,
      );
      if (!allocation) {
        // todo : figure out rrt_vintages :(
        if (rrtAsset.currentNetQuantity > 0) {
          throw new InternalServerErrorException(
            `rrtAsset ${rrtAsset.id} has 0 allocation but ${rrtAsset.currentNetQuantity} net quantity`,
          );
        }
        rrt.assets.push({
          ...rrtAsset,
          calculatedFactor: new Decimal(0),
          grossQuantity: 0,
          portfolioNetPercentage: new Decimal(rrtAsset.currentNetQuantity).div(
            rrt.assetAllocations.totalAmountAvailable,
          ),
        });
      } else {
        rrt.assets.push({
          ...rrtAsset,
          calculatedFactor: new Decimal(allocation.amountAvailable).div(rrtAsset.currentNetQuantity),
          grossQuantity: allocation.amountAvailable,
          portfolioNetPercentage: new Decimal(rrtAsset.currentNetQuantity).div(
            rrt.assetAllocations.totalAmountAvailable,
          ),
        });
      }
    }
    return rrt;
  }

  /**
   * get grouped Asset holdings for a Book entity
   *
   * steps:
   * 1. find Book entity by id
   * 2. populate owner allocations for Book
   * 3. return InternalGroupedAllocationsResponse
   *
   * @param {uuid} id
   * @returns {Promise<InternalGroupedAllocationsResponse>}
   * @throws NotFoundException
   */
  async getHoldings(id: uuid): Promise<InternalGroupedAllocationsResponse> {
    const book = await this.em.findOneOrFail(Book, { id });
    return (
      await this.populateAllocationsService.internalPopulateBookAllocations(this.em, book, undefined, [
        BookRelations.OWNER_ALLOCATIONS_NESTED,
      ])
    ).ownerAllocations!;
  }

  /**
   * get grouped parent holdings for GroupingParent entities
   *
   * steps:
   * 1. find all GroupingParent entities and populate groupings and books
   * 2. if no parents, return empty array
   * 3. if requested, populate grouped parent allocations
   * 4. return array of InternalGroupingParent
   *
   * @param {GroupingRelations[]} includeRelations
   * @returns {Promise<InternalGroupingParent[]>}
   */
  async getParentHoldings(includeRelations: GroupingRelations[]): Promise<InternalGroupingParent[]> {
    const parents: InternalGroupingParent[] = await this.em.find(GroupingParent, {}, { populate: ['groupings.book'] });
    if (parents.length === 0) {
      return [];
    }

    if (
      includeRelations.some((s) =>
        [
          GroupingRelations.OWNER_ALLOCATIONS,
          GroupingRelations.OWNER_ALLOCATIONS_NESTED,
          GroupingRelations.OWNER_ALLOCATIONS_BY_ASSET_TYPE,
          GroupingRelations.PRICES,
        ].includes(s),
      )
    ) {
      await this.populateAllocationsService.internalPopulateGroupedParentAllocations(
        this.em,
        parents,
        includeRelations,
      );
    }

    return parents;
  }

  /**
   * update a Book entity
   *
   * steps:
   * 1. get current date as now
   * 2. start a database transaction
   * 3. find Book entity by id
   * 4. update Book fields and related entities as needed
   * 5. update project types and allocations if projectTypeIds provided
   * 6. update purchase prices and create HistoricalBookPrice entities if needed
   * 7. validate public portfolio book price
   * 8. create AuditLogsService entry for Book update
   * 9. send notifications if price changed
   * 10. return updated Book entity
   *
   * @param {UserClaims} claims
   * @param {uuid} id
   * @param {AdminBookUpdateRequestDTO} data
   * @returns {Promise<InternalBookResponse>}
   * @throws UnprocessableEntityException
   */
  async update(claims: UserClaims, id: uuid, data: AdminBookUpdateRequestDTO): Promise<InternalBookResponse> {
    const now: Date = new Date();
    let rawPriceChangeData: PortfolioPriceChangeDetails | undefined = undefined;
    let bufferedPriceChangeData: PortfolioPriceChangeDetails | undefined = undefined;

    const book = await this.em.transactional(async (tx) => {
      const book: InternalBookResponse = await tx.findOneOrFail(Book, { id }, { lockMode: LockMode.PESSIMISTIC_WRITE });
      book.updatedAt = now;

      if (data.name) {
        book.name = data.name;
      }
      if (data.description !== undefined) {
        book.description = data.description;
      }
      if (data.isEnabled !== undefined) {
        book.isEnabled = data.isEnabled;
        if (!book.isEnabled) {
          await this.alertsService.closeAllPortfolioPolicyViolations(claims, tx, now, book.id, `portfolio disabled`);
          await this.alertsService.closePortfolioStalePrice(claims, tx, now, book.id, `portfolio disabled`);
        }
      }

      if (data.limit) {
        if (data.limit.holdingAmountMax !== undefined) {
          book.limit.holdingAmountMax = data.limit.holdingAmountMax;
        }
        if (data.limit.holdingAmountMin !== undefined) {
          book.limit.holdingAmountMin = data.limit.holdingAmountMin;
        }
        if (data.limit.holdingPriceMax !== undefined) {
          book.limit.holdingPriceMax = data.limit.holdingPriceMax;
        }
        if (data.limit.holdingPriceMin !== undefined) {
          book.limit.holdingPriceMin = data.limit.holdingPriceMin;
        }
      }

      if ([BookType.RCT_CUSTOM, BookType.RCT_PUBLIC].includes(book.type) && data.projectTypeIds !== undefined) {
        // populate current projectTypes and allocations
        await this.populateAllocationsService.internalPopulateBookAllocations(tx, book, undefined, [
          BookRelations.OWNER_ALLOCATIONS_BY_PROJECT_TYPE,
        ]);

        // get all currently allocated project types
        const allocatedProjectTypes =
          book.ownerAllocationsByProjectType?.filter((f) => f.totalAmountAllocated > 0).map((m) => m.projectType) || [];
        // if no data.projectTypeIds and no allocated projectTypes remove all
        if (data.projectTypeIds === null || (data.projectTypeIds.length === 0 && allocatedProjectTypes.length === 0)) {
          book.projectTypes.removeAll();
        } else if (!allocatedProjectTypes.every((e) => data.projectTypeIds?.includes(e.id))) {
          // if not all of the allocated projectTypes are in the data.projectTypes, throw error
          throw new UnprocessableEntityException(
            `projectTypeIds must include [${allocatedProjectTypes.map((m) => m.id).join(',')}]`,
          );
        } else {
          // update projectTypes
          const projectTypes = await tx.find(ProjectType, { id: data.projectTypeIds });
          if (projectTypes.length !== [...new Set(data.projectTypeIds)].length) {
            console.error(
              `failed to get matching projectTypeIds. requested projectTypeIds : [${data.projectTypeIds.join(',')}], got [${projectTypes.map((m) => m.id).join(',')}]`,
            );
            throw new UnprocessableEntityException(`projectTypeIds must be valid`);
          }
          book.projectTypes.set(projectTypes);
        }

        // populate final bookTypes
        await this.populateAllocationsService.internalPopulateBookAllocations(tx, book, undefined, [
          BookRelations.OWNER_ALLOCATIONS_BY_PROJECT_TYPE,
        ]);
      }

      // update prices
      if (data.purchasePrice && ![BookType.RCT_CUSTOM, BookType.RCT_PUBLIC, BookType.RRT_PUBLIC].includes(book.type)) {
        throw new UnprocessableEntityException(`purchasePrice cannot be updated for book ${book.name}`);
      }
      if (data.purchasePriceWithBuffer && ![`${BookType.RCT_CUSTOM}`, `${BookType.RCT_PUBLIC}`].includes(book.type)) {
        throw new UnprocessableEntityException(`purchasePriceWithBuffer cannot be updated for book ${book.name}`);
      }
      if (data.purchasePrice !== undefined && !book.purchasePrice?.equals(data.purchasePrice)) {
        tx.create(HistoricalBookPrice, {
          id: uuid(),
          book,
          oldPrice: book.purchasePrice || new Decimal(0),
          newPrice: data.purchasePrice || new Decimal(0),
          type: BookPriceType.PURCHASE_PRICE_WITHOUT_BUFFER,
          timestamp: now,
          updatedBy: claims.id,
        });
        rawPriceChangeData = new PortfolioPriceChangeDetails(
          book.purchasePrice || new Decimal(0),
          data.purchasePrice,
          'raw',
        );

        book.purchasePrice = data.purchasePrice;
        book.priceUpdatedAt = now;

        await this.alertsService.closePortfolioStalePrice(claims, tx, now, book.id, `purchasePrice updated`);
      }

      if (
        data.purchasePriceWithBuffer !== undefined &&
        !book.purchasePriceWithBuffer?.equals(data.purchasePriceWithBuffer)
      ) {
        tx.create(HistoricalBookPrice, {
          id: uuid(),
          book,
          oldPrice: book.purchasePriceWithBuffer || new Decimal(0),
          newPrice: data.purchasePriceWithBuffer || new Decimal(0),
          type: BookPriceType.PURCHASE_PRICE_WITH_BUFFER,
          timestamp: now,
          updatedBy: claims.id,
        });

        bufferedPriceChangeData = new PortfolioPriceChangeDetails(
          book.purchasePriceWithBuffer || new Decimal(0),
          data.purchasePriceWithBuffer,
          'buffered',
        );

        book.purchasePriceWithBuffer = data.purchasePriceWithBuffer;
        book.priceUpdatedAt = now;

        await this.alertsService.closePortfolioStalePrice(claims, tx, now, book.id, `purchasePriceWithBuffer updated`);
      }
      this.privateValidatePortfolioBookPrice(book.purchasePrice, book.purchasePriceWithBuffer);

      await this.auditLogsService.create(tx, claims, AuditLogAction.BOOK_UPDATED, now, id, {
        ...book,
      });

      return book;
    });

    // send emails and notifications
    try {
      if (rawPriceChangeData || bufferedPriceChangeData) {
        const sendgridPayload: InternalPortfolioPriceUpdated = new InternalPortfolioPriceUpdated(
          await this.notificationsService.getNotificationUrl(NotificationEvent.BOOK_PRICE_UPDATED),
          book.name,
          now,
          claims,
          rawPriceChangeData,
          bufferedPriceChangeData,
        );
        await this.notificationsService.handleEvent(this.em, NotificationEvent.BOOK_PRICE_UPDATED, sendgridPayload);
      }
    } catch (e) {
      console.error(
        `error sending ${NotificationEvent.BOOK_PRICE_UPDATED} email for book ${book.name} : ` + JSON.stringify(e),
      );
    }

    return book;
  }

  /* private */

  /**
   * find many Portfolio Book entities for portal endpoints (private helper)
   *
   * steps:
   * 1. build SQL query to select Book ids based on filters
   * 2. filter by ids, isEnabled, name, and types as needed
   * 3. for org users, include custom portfolios tied to org
   * 4. find Book entities by ids with pagination and sorting
   * 5. populate allocations if requested
   * 6. return InternalBookQueryResponse
   *
   * @param {InternalPortfolioBookQueryRequest} query
   * @returns {Promise<InternalBookQueryResponse>}
   */
  private async privateFindManyPortfolios(
    query: InternalPortfolioBookQueryRequest,
  ): Promise<InternalBookQueryResponse> {
    const knex = this.em.getKnex();

    const bookIds: { id: uuid }[] = await knex
      .select(['b.id'])
      .from(`${environment.db.schema.rubicon}.books as b`)
      .where((builder) => {
        // specified portfolio ids
        if (query.ids && query.ids.length > 0) {
          builder.whereIn('b.id', query.ids);
        }
        // isEnabled (external users should only see isEnabled: true)
        if (query.isEnabled !== undefined) {
          builder.andWhere({ 'b.is_enabled': query.isEnabled });
        }
        // portfolio name
        if (query.name) {
          builder.andWhere({ 'b.name': query.name });
        }
        // determine which types of portfolios the external user can see
        builder.andWhere((builder1) => {
          // external users can only see at most custom and public rct books
          const subtypes = query.types || AllPortalBookTypes;
          // all external users can see public RCT and RRT portfolios
          if (subtypes.includes(BookType.RCT_PUBLIC)) {
            builder1.orWhere({ 'b.type': BookType.RCT_PUBLIC });
          }
          if (subtypes.includes(BookType.RRT_PUBLIC)) {
            builder1.orWhere({ 'b.type': BookType.RRT_PUBLIC });
          }
          // org users specifically can also see custom and customer portfolios that they have access to
          const orgPortfolioSubtypes = query.organizationId
            ? subtypes.filter((f) => [BookType.RCT_CUSTOM].includes(f))
            : [];
          // if the user is a valid user (tied to an org) and requests more than just public, add custom  portfolios that are tied to the org
          if (query.organizationId && orgPortfolioSubtypes.length > 0) {
            builder1.orWhere((builder2) => {
              builder2.whereIn('b.type', orgPortfolioSubtypes);
              builder2.andWhere({ 'b.organization_id': query.organizationId });
            });
          }
        });
      });

    // make a more succinct and clean call to help with pagination
    const books = await this.em.find(
      Book,
      { id: bookIds.map((m) => m.id) },
      {
        limit: query.limit,
        offset: query.offset,
        orderBy: { [query.orderBy]: OrderByDirectionSQL[query.orderByDirection] },
        populate: this.privatePopulateBookRelations(query.includeRelations),
        connectionType: 'read',
      },
    );

    if (
      [
        BookRelations.ASSET_ALLOCATIONS,
        BookRelations.ASSET_ALLOCATIONS_NESTED,
        BookRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE,
        BookRelations.OWNER_ALLOCATIONS,
        BookRelations.OWNER_ALLOCATIONS_NESTED,
        BookRelations.OWNER_ALLOCATIONS_BY_ASSET_TYPE,
        BookRelations.OWNER_ALLOCATIONS_BY_PROJECT,
        BookRelations.OWNER_ALLOCATIONS_BY_PROJECT_TYPE,
      ].some((s) => query.includeRelations?.includes(s))
    ) {
      await this.populateAllocationsService.internalPopulateBooksAllocations(
        this.em,
        books,
        undefined,
        query.includeRelations,
      );
    }

    return {
      data: books,
      page: {
        limit: query.limit,
        offset: query.offset,
        size: books.length,
        totalCount: query.includeTotalCount ? bookIds.length : undefined,
      },
    };
  }

  /**
   * handle Book created notification and send email
   *
   * steps:
   * 1. build InternalPortfolioCreated payload
   * 2. get notification url for event
   * 3. call NotificationsService to handle event
   * 4. handle and log any errors during notification sending
   *
   * @param {Date} now
   * @param {NotificationEvent} event
   * @param {Book} book
   * @returns {Promise<void>}
   */
  private async privateHandleBookCreatedNotification(now: Date, event: NotificationEvent, book: Book): Promise<void> {
    try {
      const sendgridPayload: InternalPortfolioCreated = new InternalPortfolioCreated(
        await this.notificationsService.getNotificationUrl(event),
        book.name,
        now,
      );
      await this.notificationsService.handleEvent(this.em, event, sendgridPayload);
    } catch (e) {
      console.error(
        `error sending ${NotificationEvent.BOOK_CREATED} email for book ${book.name} : ` + JSON.stringify(e),
      );
    }
  }

  /**
   * build the relations to populate for Book entity queries
   *
   * steps:
   * 1. initialize populateString array
   * 2. add BookRelations.ORGANIZATION and 'projectTypes' if requested
   * 3. return populateString array as FindOptions populate type
   *
   * @param {BookRelations[]} relations
   * @returns {FindOptions<Book, S>['populate']}
   */
  private privatePopulateBookRelations<S extends string>(
    relations: BookRelations[] = [],
  ): FindOptions<Book, S>['populate'] {
    const populateString: string[] = [];
    if (relations.includes(BookRelations.ORGANIZATION)) {
      populateString.push(BookRelations.ORGANIZATION);
    }
    if (relations.includes(BookRelations.OWNER_ALLOCATIONS_BY_PROJECT_TYPE)) {
      populateString.push('projectTypes');
    }
    return populateString as unknown as FindOptions<Book, S>['populate'];
  }

  /**
   * validate public Portfolio Book price
   *
   * steps:
   * 1. check if purchasePrice and purchasePriceWithBuffer are provided
   * 2. check that purchasePriceWithBuffer is not less than purchasePrice
   * 3. throw BadRequestException if validation fails
   *
   * @param {Decimal} purchasePrice
   * @param {Decimal} purchasePriceWithBuffer
   * @throws BadRequestException
   */
  private privateValidatePortfolioBookPrice(purchasePrice?: Decimal, purchasePriceWithBuffer?: Decimal): void {
    if (purchasePrice && purchasePriceWithBuffer && purchasePriceWithBuffer.lessThan(purchasePrice)) {
      throw new BadRequestException([
        `purchasePriceWithBuffer ${purchasePriceWithBuffer} must be greater than or equal to purchasePrice ${purchasePrice}`,
      ]);
    }
  }
}
