/* third party */
import { FilterQuery, FindOptions, LoadStrategy } from '@mikro-orm/core';
import { EntityManager } from '@mikro-orm/postgresql';
import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  UnprocessableEntityException,
} from '@nestjs/common';
import Decimal from 'decimal.js';
/* rubicon */
import {
  AdminTradeConfirmationResponse,
  AssetFlowStatus,
  AssetType,
  AuditLogAction,
  BaseTradeConfirmationProductsResponse,
  BookAction,
  BookRelations,
  BulkLedgerTransactionsResponse,
  DocumentType,
  NotificationEvent,
  PendingLedgerTransactionResponse,
  ReleasedLedgerTransactionResponse,
  TradeOrderByOptions,
  TradeRelation,
  TradeStatus,
  TradeType,
  TransactionType,
  uuid,
} from '@rubiconcarbon/shared-types';
/* env */
import { environment } from '@env/environment';
/* app */
import { UserClaims } from '@app/auth/interfaces';
import {
  AdminTradeCounterpartyRequestDTO,
  AdminTradeCreateAccountingRequestDTO,
  AdminTradeCreateRequestDTO,
  AdminTradeDeliverRequestDTO,
  AdminTradeExecuteRequestDTO,
  AdminTradeQueryDTO,
  AdminTradeRelationsQueryDTO,
  AdminTradeUpdateRequestDTO,
  BaseTradeOrderByDTO,
} from '@app/dtos/trade.dto';
import {
  InternalInventoryUpdated,
  InternalTradeCreated,
  InternalTradeUpdated,
  TradeConfirmationPayload,
} from '@app/dtos/sendgrid-payload.dto';
import {
  Asset,
  AssetFlow,
  Book,
  Counterparty,
  CreditFlow,
  Document,
  ProjectVintage,
  Trade,
  TradeConfirmation,
  TradeConfirmationProduct,
  TradeCounterparty,
  Transaction,
  User,
} from '@app/entities';
import { OrderByDirectionSQL } from '@app/enums/orderby.enum';
import { generateRandomKey } from '@app/helpers';
import { InternalCreditFlowRequest } from '@app/interfaces/credit-flow.interface';
import { InternalTradeQueryResponse } from '@app/interfaces/trade.interface';
import { LedgerTradesService } from '@app/ledger/services/ledger-trades.service';
import { LedgerTransactionsService } from '@app/ledger/services/ledger-transactions.service';
import { AuditLogsService } from '@app/utility/audit-log';
import { TransactionStatusLogsService } from '@app/utility/transaction-status-log';
import { AssetsService } from './assets.service';
import { CreditFlowsService } from './credit-flows.service';
import { NotificationsService } from './notifications.service';
import { AdminAssetTradeRequestDTO } from '@app/dtos/asset.dto';
import { validateComplianceVintages, validateRctPortfolioVintages } from '@app/validators/book.validator';
import { InternalHolding, InternalHoldingsQuery } from '@app/interfaces/allocation.interface';
import { InternalPopulateAllocationsService } from './allocations-populate.service';
import { InternalBookResponse } from '@app/interfaces/book.interface';
import { AdminTradeConfirmationRequestDTO, AdminTradeConfirmationResponseDTO } from '@app/dtos/trade-confirmation.dto';
import { SendgridService } from './sendgrid.service';
import {
  toFormattedNumberString,
  toFormattedPriceString,
  toFormattedSendgridDate,
} from '@app/helpers/transforms.helper';

@Injectable()
export class TradesService {
  constructor(
    private assetsService: AssetsService,
    private auditLogsService: AuditLogsService,
    private transactionStatusLogService: TransactionStatusLogsService,
    private creditFlowsService: CreditFlowsService,
    private readonly em: EntityManager,
    private ledgerTradesService: LedgerTradesService,
    private ledgerTransactionsService: LedgerTransactionsService,
    private notificationsService: NotificationsService,
    private populateAllocationsService: InternalPopulateAllocationsService,
    private sendgridService: SendgridService,
  ) {}

  // auto cancel trades that are still in indicative status after the good until dqte
  // does not affect ledger since indicative trades are not in ledger
  async autoCancel(claims: UserClaims): Promise<void> {
    const now = new Date();
    await this.em.transactional(async (tx) => {
      const user = await tx.findOneOrFail(User, { id: claims.id });
      // get all trades that have status indicative and good until date before now
      const trades = await tx.find(
        Trade,
        { status: TradeStatus.INDICATIVE, goodUntilDate: { $lte: now } },
        { populate: ['tradeCounterparties.counterparty.organization'] },
      );

      if (trades.length === 0) {
        return;
      }

      const tradeIds = trades.map((m) => m.id);
      // update trades to canceled
      await tx.nativeUpdate(Trade, { id: tradeIds }, { status: TradeStatus.CANCELED, updatedAt: now, updatedBy: user });
      // update vintage flows to canceled
      await tx.nativeUpdate(AssetFlow, { transaction: tradeIds }, { status: AssetFlowStatus.CANCELED, updatedAt: now });

      // send audit logs and sendgrid notifications
      for (const trade of trades) {
        await this.auditLogsService.create(tx, user, AuditLogAction.TRADE_CANCELED, now, trade.id, {});

        await this.transactionStatusLogService.create(tx, TradeStatus.CANCELED, now, TransactionType.TRADE, trade.id);

        const vintageFlows = await tx.find(AssetFlow, {
          transaction: trade.id,
          assetType: AssetType.REGISTRY_VINTAGE,
        });
        const projectVintage = await tx.findOneOrFail(
          ProjectVintage,
          { id: vintageFlows[0].asset.id },
          { populate: ['project'] },
        );

        // todo : (TD-83) consider a separate realtime template for bulk operations
        try {
          const sendgridPayload = new InternalTradeUpdated(
            await this.notificationsService.getNotificationUrlWithUuid(NotificationEvent.TRADE_UPDATED, trade.id),
            trade.uiKey,
            projectVintage.project.name,
            projectVintage.name(),
            trade.tradeCounterparties.find((f) => f.isPrimary)!.counterparty.organization.name,
            trade.amount,
            TradeStatus.CANCELED,
            now,
            claims,
          );
          await this.notificationsService.handleEvent(tx, NotificationEvent.TRADE_UPDATED, sendgridPayload);
        } catch (e) {
          console.error(
            `error sending ${NotificationEvent.TRADE_UPDATED} email for trade ${trade.uiKey} : ` + JSON.stringify(e),
          );
        }
      }
    });
  }

  // after firm status, trades are in binding status
  // ignoreDocCheck is only used for console scripts and should never be used by other calls
  async bind(
    claims: UserClaims,
    id: uuid,
    confirmationPayload?: AdminTradeConfirmationRequestDTO,
    ignoreDocCheck = false,
  ): Promise<Trade> {
    if (ignoreDocCheck && claims.name !== 'init-console-script') {
      throw new InternalServerErrorException('ignoreDocCheck cannot be true');
    }

    const now = new Date();
    return await this.em.transactional(async (tx) => {
      const user = await tx.findOneOrFail(User, { email: claims.email });
      const trade = await tx.findOneOrFail(Trade, id, {
        populate: [
          'book',
          'tradeCounterparties.counterparty.defaultFees',
          'tradeCounterparties.counterparty.organization',
        ],
      });
      if (trade.status !== TradeStatus.FIRM) {
        throw new UnprocessableEntityException(`trade ${id} must have status ${TradeStatus.FIRM}`);
      }
      const vintageFlows = await this.em.find(
        AssetFlow,
        { transaction: trade.id, assetType: AssetType.REGISTRY_VINTAGE },
        { populate: ['asset.details.vintageDetails'] },
      );

      // binding trades require a document with type PROOF_OF_CONFIRMATION uploaded
      if (!ignoreDocCheck) {
        const document = await tx.findOne(Document, {
          relatedUiKey: trade.uiKey,
          type: DocumentType.PROOF_OF_CONFIRMATION,
          isUploaded: true,
        });

        if (!document && !confirmationPayload) {
          throw new UnprocessableEntityException(
            `Trade must have a document with type ${DocumentType.PROOF_OF_CONFIRMATION} uploaded before moving to status ${TradeStatus.BINDING}`,
          );
        }
      }

      // update db amounts after ledger transaction made and documents are uploaded
      trade.status = TradeStatus.BINDING;
      trade.updatedAt = now;
      trade.updatedBy = user;

      await this.auditLogsService.create(tx, claims, AuditLogAction.TRADE_IS_BINDING, now, trade.id, {
        ...trade,
      });

      await this.transactionStatusLogService.create(tx, TradeStatus.BINDING, now, TransactionType.TRADE, trade.id);

      if (confirmationPayload) {
        const confirmation: AdminTradeConfirmationResponse = this.privateGenerateTradeConfirmationDetails(
          now,
          trade,
          vintageFlows,
          confirmationPayload,
        );
        const tradeConfirm = tx.create(TradeConfirmation, {
          buyer: confirmation.buyer,
          deliveryTerms: confirmation.deliveryTerms,
          formattedDate: confirmation.formattedDate,
          paymentTerms: confirmation.paymentTerms,
          recipientEmails: confirmation.recipientEmails.map((m) => m.toLowerCase() as Lowercase<string>),
          seller: confirmation.seller,
          totalPrice: confirmation.totalPrice,
          totalQuantity: confirmation.totalQuantity,
          trade,
          uiKey: confirmation.uiKey,
        });

        confirmation.products.map((m) =>
          tx.create(TradeConfirmationProduct, {
            data: m,
            tradeConfirmation: tradeConfirm,
          }),
        );

        // todo : (TD-83)
        if (claims.name !== 'init-console-script') {
          const sendgridPayload = new TradeConfirmationPayload(tradeConfirm);
          await this.sendgridService.createAndSendEmailTemplate(
            confirmation.recipientEmails,
            environment.sendgrid.sender.alerts,
            environment.sendgrid.template.tradeConfirm,
            sendgridPayload,
          );
        }
      }

      // todo : (TD-83)
      if (claims.name !== 'init-console-script') {
        const projectVintage = await tx.findOneOrFail(
          ProjectVintage,
          { id: vintageFlows[0].asset.id },
          { populate: ['project'] },
        );

        try {
          const sendgridPayload = new InternalTradeUpdated(
            await this.notificationsService.getNotificationUrlWithUuid(NotificationEvent.TRADE_UPDATED, trade.id),
            trade.uiKey,
            projectVintage.project.name, // this should be grabbed elsewhere
            projectVintage.name(), // this should be grabbed elsewhere
            trade.tradeCounterparties.find((f) => f.isPrimary)!.counterparty.organization.name,
            trade.amount,
            TradeStatus.BINDING,
            now,
            claims,
          );
          await this.notificationsService.handleEvent(tx, NotificationEvent.TRADE_UPDATED, sendgridPayload);
        } catch (e) {
          console.error(
            `error sending ${NotificationEvent.TRADE_UPDATED} email for trade ${trade.uiKey} : ` + JSON.stringify(e),
          );
        }
      }

      return trade;
    });
  }

  // cancel trade, releasing any ledger amounts necessary
  // update trades and vintageFlows to canceled
  async cancel(claims: UserClaims, id: uuid): Promise<Trade> {
    const now = new Date();
    let ledgerTransactionIds: uuid[] = [];

    try {
      return await this.em.transactional(async (tx) => {
        const user = await tx.findOneOrFail(User, { email: claims.email });
        const trade = await tx.findOneOrFail(
          Trade,
          { id },
          {
            populate: ['tradeCounterparties.counterparty.defaultFees', 'tradeCounterparties.counterparty.organization'],
          },
        );

        if ([TradeStatus.CANCELED, TradeStatus.SETTLED].includes(trade.status) || trade.isDelivered) {
          throw new UnprocessableEntityException(`trade ${id} already ${trade.status} or has already been delivered`);
        }

        // if trade has status indicative, skip ledger
        // otherwise release ledger balances
        if (trade.status !== TradeStatus.INDICATIVE) {
          const ledgerResponse: ReleasedLedgerTransactionResponse =
            await this.ledgerTradesService.cancelPendingTradeTransaction(tx, trade);
          ledgerTransactionIds = [ledgerResponse.id];
        }

        trade.status = TradeStatus.CANCELED;
        trade.updatedAt = now;
        trade.updatedBy = user;

        await tx.nativeUpdate(
          AssetFlow,
          { transaction: trade.id },
          { updatedAt: now, status: AssetFlowStatus.CANCELED },
        );

        const vintageFlows = await tx.find(AssetFlow, {
          transaction: trade.id,
          assetType: AssetType.REGISTRY_VINTAGE,
        });
        const projectVintage = await tx.findOneOrFail(
          ProjectVintage,
          { id: vintageFlows[0].asset.id },
          { populate: ['project'] },
        );

        // todo : (TD-83)
        try {
          const sendgridPayload = new InternalTradeUpdated(
            await this.notificationsService.getNotificationUrlWithUuid(NotificationEvent.TRADE_UPDATED, trade.id),
            trade.uiKey,
            projectVintage.project.name,
            projectVintage.name(),
            trade.tradeCounterparties.find((f) => f.isPrimary)!.counterparty.organization.name,
            trade.amount,
            TradeStatus.CANCELED,
            now,
            claims,
          );
          await this.notificationsService.handleEvent(tx, NotificationEvent.TRADE_UPDATED, sendgridPayload);
        } catch (e) {
          console.error(
            `error sending ${NotificationEvent.TRADE_UPDATED} email for trade ${trade.uiKey} : ` + JSON.stringify(e),
          );
        }

        return trade;
      });
    } catch (e) {
      if (ledgerTransactionIds.length > 0) {
        await this.ledgerTransactionsService.revertTransactionsAfterFailedApi(
          id,
          now,
          ledgerTransactionIds,
          `canceling trade ${id}`,
        );
      }
      throw e;
    }
  }

  // during the executed status, a trade can be marked as isDelivered
  // status stays "executed" until isDelivered and isPaid are both true
  // regardless of when Trade is settled, as soon as isDelivered is true, update ledger and balances and asset flows to settled
  // ignoreDocCheck is only used for console scripts and should never be used by other calls
  async deliver(
    claims: UserClaims,
    id: uuid,
    data: AdminTradeDeliverRequestDTO,
    ignoreDocCheck = false,
  ): Promise<Trade> {
    if (ignoreDocCheck && claims.name !== 'init-console-script') {
      throw new InternalServerErrorException('ignoreDocCheck cannot be true');
    }

    const now = new Date();
    let ledgerTransactionIds: uuid[] = [];

    try {
      return await this.em.transactional(async (tx) => {
        const user = await tx.findOneOrFail(User, { email: claims.email });
        const trade = await tx.findOneOrFail(
          Trade,
          { id },
          {
            populate: [
              'book',
              'tradeCounterparties.counterparty.defaultFees',
              'tradeCounterparties.counterparty.organization',
            ],
          },
        );
        if (trade.status !== TradeStatus.EXECUTED) {
          throw new UnprocessableEntityException(`Trade ${id} must have status ${TradeStatus.EXECUTED}`);
        }
        if (trade.isDelivered) {
          throw new UnprocessableEntityException(`Trade ${id} is already delivered`);
        }
        // trades that are marked isDelivered require a document with type PROOF_OF_DELIVERY uploaded
        if (!ignoreDocCheck) {
          const document = await tx.findOne(Document, {
            relatedUiKey: trade.uiKey,
            type: DocumentType.PROOF_OF_DELIVERY,
            isUploaded: true,
          });
          if (!document) {
            throw new UnprocessableEntityException(
              `Trade must have a document with type ${DocumentType.PROOF_OF_DELIVERY} uploaded before marking as isDelivered`,
            );
          }
        }

        const assetFlows = await tx.find(AssetFlow, { transaction: trade.id }, { populate: ['asset'] });
        const projectVintages = await tx.find(ProjectVintage, { id: assetFlows.map((m) => m.asset.id) });

        if (trade.type === TradeType.BUY) {
          for (const flow of assetFlows) {
            const projectVintage = projectVintages.find((f) => f.id === flow.asset.id);
            if (!projectVintage) {
              throw new InternalServerErrorException(
                `could not find projectVintage ${flow.asset.id} for flow ${flow.id}`,
              );
            }
            flow.detailedAsset = projectVintage;

            // create line item entry
            const creditInflowRequest: InternalCreditFlowRequest = {
              amount: trade.amount,
              acquiredAt: data.assetsDeliveredAt,
              projectVintageId: flow.asset.id,
              assetFlowId: flow.id,
            };

            this.creditFlowsService.createCreditInflowEntry(tx, creditInflowRequest, now);

            // todo : (TD-83) either take this out of the transaction or send in tx
            if (claims.name !== 'init-console-script') {
              try {
                const sendgridPayload: InternalInventoryUpdated = new InternalInventoryUpdated(
                  await this.notificationsService.getNotificationUrl(NotificationEvent.INVENTORY_UPDATED),
                  projectVintage.project.id,
                  projectVintage.name(),
                  trade.amount || 0,
                  now,
                  claims,
                );
                await this.notificationsService.handleEvent(tx, NotificationEvent.INVENTORY_UPDATED, sendgridPayload);
              } catch (e) {
                console.error('error handling inventory updated notification : ' + JSON.stringify(e));
              }
            }
          }

          // settle ledger balances
          trade.assetFlows = assetFlows;
          const ledgerResponse: BulkLedgerTransactionsResponse = await this.ledgerTradesService.settleTradeTransaction(
            tx,
            trade,
          );
          ledgerTransactionIds = this.ledgerTransactionsService.getTransactionIdsFromBulkResponse(ledgerResponse);
        } else {
          // create Outflow for trade sell
          await this.creditFlowsService.createCreditOutflowEntry(tx, assetFlows, projectVintages);
          trade.assetFlows = assetFlows;

          // settle ledger balances
          const ledgerResponse: BulkLedgerTransactionsResponse = await this.ledgerTradesService.settleTradeTransaction(
            tx,
            trade,
          );
          ledgerTransactionIds = this.ledgerTransactionsService.getTransactionIdsFromBulkResponse(ledgerResponse);
        }

        // update db for Trade and AssetFlows
        assetFlows.forEach((flow) => {
          flow.settledAt = data.assetsDeliveredAt;
          flow.status = AssetFlowStatus.SETTLED;
          flow.updatedAt = now;
        });
        trade.assetsDeliveredAt = data.assetsDeliveredAt;
        trade.isDelivered = true;
        trade.updatedAt = now;
        trade.updatedBy = user;

        await this.auditLogsService.create(tx, claims, AuditLogAction.TRADE_DELIVERED, now, id, { ...data });

        // todo : (TD-83)
        if (claims.name !== 'init-console-script') {
          try {
            const sendgridPayload = new InternalTradeUpdated(
              await this.notificationsService.getNotificationUrlWithUuid(NotificationEvent.TRADE_UPDATED, trade.id),
              trade.uiKey,
              projectVintages[0].project.name,
              projectVintages[0].name(),
              trade.tradeCounterparties.find((f) => f.isPrimary)!.counterparty.organization.name,
              trade.amount,
              'delivered',
              now,
              claims,
            );
            await this.notificationsService.handleEvent(tx, NotificationEvent.TRADE_UPDATED, sendgridPayload);
          } catch (e) {
            console.error(
              `error sending ${NotificationEvent.TRADE_UPDATED} email for trade ${trade.uiKey} : ` + JSON.stringify(e),
            );
          }
        }

        // if trade now have isDelivered and isPaid as true, settle
        if (trade.isDelivered && trade.isPaid) {
          return await this.privateSettle(tx, claims, user, trade, now);
        }
        return trade;
      });
    } catch (e) {
      if (ledgerTransactionIds.length > 0) {
        await this.ledgerTransactionsService.revertTransactionsAfterFailedApi(
          id,
          now,
          ledgerTransactionIds,
          `delivering trade ${id} asset`,
        );
      }
      throw e;
    }
  }

  // executing a trade requires a contract document
  // also pending_delivery, pending_payment order determined
  // status stays executing as FE determines what the next "pending" step is
  // status stays executing throughout pendingPayment check and pendingDelivery check
  // and then auto changes to "settled" after isPaid and isDelivered are completed
  // checks that vintages for an RCT Portfolio trade are RCT standard
  // ignoreDocCheck is only used for console scripts and should never be true for other calls
  // rctVintages only not validated for console scripts and should neve be false for other calls
  async execute(
    claims: UserClaims,
    id: uuid,
    data: AdminTradeExecuteRequestDTO,
    ignoreDocCheck = false,
    validateRctVintages: boolean = true,
  ): Promise<Trade> {
    if (ignoreDocCheck && claims.name !== 'init-console-script') {
      throw new InternalServerErrorException('ignoreDocCheck cannot be true');
    } else if (!validateRctVintages && claims.name !== 'init-console-script') {
      throw new InternalServerErrorException('validateRctVintages cannot be false');
    }

    return await this.em.transactional(async (tx) => {
      const now = new Date();
      const user = await tx.findOneOrFail(User, { email: claims.email });
      const trade = await tx.findOneOrFail(
        Trade,
        { id },
        {
          populate: [
            'book',
            'tradeCounterparties.counterparty.defaultFees',
            'tradeCounterparties.counterparty.organization',
          ],
        },
      );
      if (trade.status !== TradeStatus.BINDING) {
        throw new UnprocessableEntityException(`Trade ${id} must have status ${TradeStatus.BINDING}`);
      }
      // validate trade project vintage details for a buy
      if (trade.type === TradeType.BUY) {
        const assetFlows = await tx.find(AssetFlow, { transaction: trade.id }, { populate: ['asset'] });
        const projectVintages = await tx.find(ProjectVintage, { id: assetFlows.map((m) => m.asset.id) });
        if (validateRctVintages) {
          await validateRctPortfolioVintages(tx, trade.book, projectVintages);
        }
      }

      // trades require a document with type CONTRACT uploaded before moving to status EXECUTED
      if (!ignoreDocCheck) {
        const document = await tx.findOne(Document, {
          relatedUiKey: trade.uiKey,
          type: DocumentType.CONTRACT,
          isUploaded: true,
        });
        if (!document) {
          throw new UnprocessableEntityException(
            `Trade must have a document with type ${DocumentType.CONTRACT} uploaded before moving to status ${TradeStatus.EXECUTED}`,
          );
        }
      }

      trade.updatableStatusOrder = data.updatableStatusOrder;
      trade.status = TradeStatus.EXECUTED;
      trade.updatedAt = now;
      trade.updatedBy = user;

      await this.auditLogsService.create(tx, claims, AuditLogAction.TRADE_EXECUTED, now, id, {
        ...data,
      });

      await this.transactionStatusLogService.create(tx, TradeStatus.EXECUTED, now, TransactionType.TRADE, trade.id);
      const vintageFlows = await tx.find(AssetFlow, {
        transaction: trade.id,
        assetType: AssetType.REGISTRY_VINTAGE,
      });
      const projectVintage = await tx.findOneOrFail(
        ProjectVintage,
        { id: vintageFlows[0].asset.id },
        { populate: ['project'] },
      );

      // todo : (TD-83)
      if (claims.name !== 'init-console-script') {
        try {
          const sendgridPayload = new InternalTradeUpdated(
            await this.notificationsService.getNotificationUrlWithUuid(NotificationEvent.TRADE_UPDATED, trade.id),
            trade.uiKey,
            projectVintage.project.name,
            projectVintage.name(),
            trade.tradeCounterparties.find((f) => f.isPrimary)!.counterparty.organization.name,
            trade.amount,
            TradeStatus.EXECUTED,
            now,
            claims,
          );
          await this.notificationsService.handleEvent(tx, NotificationEvent.TRADE_UPDATED, sendgridPayload);
        } catch (e) {
          console.error(
            `error sending ${NotificationEvent.TRADE_UPDATED} email for trade ${trade.uiKey} : ` + JSON.stringify(e),
          );
        }
      }

      return trade;
    });
  }

  async findMany(query: AdminTradeQueryDTO): Promise<InternalTradeQueryResponse> {
    const where: FilterQuery<Trade> = {};
    if (query.bookId) {
      where.book = query.bookId;
    }
    if (query.organizationIds) {
      const x: TradeCounterparty[] = await this.em.find(TradeCounterparty, {
        counterparty: { organization: query.organizationIds },
      });
      where.id = x.map((c) => c.trade.id);
    }
    if (query.poid) {
      where.poid = query.poid;
    }
    if (query.projectVintageId) {
      const assetFlows: AssetFlow[] = await this.em.find(
        AssetFlow,
        {
          amount: { $ne: 0 },
          asset: query.projectVintageId,
        },
        { populate: ['transaction'] },
      );
      where.id = assetFlows.map((m) => m.transaction.id);
    }
    if (query.status) {
      where.status = query.status;
    }
    if (query.type) {
      where.type = query.type;
    }
    if (query.updatedById) {
      where.updatedBy = query.updatedById;
    }

    const count = query.includeTotalCount ? await this.em.count(Trade, where) : undefined;
    const trades: Trade[] = await this.em.find(Trade, where, {
      limit: query.limit,
      offset: query.offset,
      populate: this.privatePopulateTradeRelations(query.includeRelations),
      orderBy: this.privateSetTradeOrderBys(query.orderBys),
      connectionType: 'read',
    });

    if (query.includeRelations.includes(TradeRelation.PROJECT_VINTAGE)) {
      const assetFlows = await this.em.find(
        AssetFlow,
        { amount: { $ne: 0 }, transaction: trades.map((m) => m.id) },
        { populate: ['asset.details.vintageDetails', 'transaction'] },
      );
      // todo : if we don't need detailedAsset then we don't need this
      const projectVintages = await this.em.find(
        ProjectVintage,
        { id: assetFlows.map((m) => m.asset.id) },
        { populate: ['project.projectType'] },
      );
      assetFlows.forEach((flow) => (flow.detailedAsset = projectVintages.find((pv) => pv.id === flow.asset.id)));
      trades.forEach((t) => (t.assetFlows = assetFlows.filter((f) => f.transaction.id === t.id)));
    }

    return {
      data: trades,
      page: {
        limit: query.limit,
        offset: query.offset,
        size: trades.length,
        totalCount: count,
      },
    };
  }

  async findOne(id: uuid, query: AdminTradeRelationsQueryDTO): Promise<Trade> {
    const trade = await this.em.findOneOrFail(
      Trade,
      { id },
      {
        populate: this.privatePopulateTradeRelations(query.includeRelations),
        connectionType: 'read',
        strategy: LoadStrategy.JOINED,
      },
    );

    if (query.includeRelations.includes(TradeRelation.PROJECT_VINTAGE)) {
      const assetFlows = await this.em.find(
        AssetFlow,
        { amount: { $ne: 0 }, transaction: trade.id },
        { populate: ['asset.details.vintageDetails'] },
      );
      const projectVintages = await this.em.find(
        ProjectVintage,
        { id: assetFlows.map((m) => m.asset.id) },
        { populate: ['project.projectType', 'prices'] },
      );
      assetFlows.forEach((flow) => (flow.detailedAsset = projectVintages.find((pv) => pv.id === flow.asset.id)));
      trade.assetFlows = assetFlows;
    }

    return trade;
  }

  // after indicate status, trades are in firm status
  // validate that project vintage has the correct amounts if type is sell
  // hold balances on ledger
  // basically just stops it from being auto-canceled
  async firm(claims: UserClaims, id: uuid): Promise<Trade> {
    const ledgerTransactionIds: uuid[] = [];
    const now = new Date();

    try {
      const trade = await this.em.transactional(async (tx) => {
        const user = await tx.findOneOrFail(User, { email: claims.email });
        const trade = await tx.findOneOrFail(Trade, id, {
          populate: [
            'book',
            'tradeCounterparties.counterparty.defaultFees',
            'tradeCounterparties.counterparty.organization',
          ],
        });
        if (trade.status !== TradeStatus.INDICATIVE) {
          throw new UnprocessableEntityException(`trade ${id} must have status ${TradeStatus.INDICATIVE}`);
        }
        if (trade.goodUntilDate && trade.goodUntilDate < now) {
          // shouldn't happen
          throw new UnprocessableEntityException(`trade ${id} goodUntilDate has already been reached`);
        }
        const assetFlows = await tx.find(AssetFlow, { transaction: id }, { populate: ['asset'] });

        // if trade is sell, validate book has enough amounts for each vintage
        // only considers settled and pending balances
        // indicative trades are not included in calculations
        if (trade.type === TradeType.SELL) {
          await this.privateValidateSellVintageAmounts(tx, trade, assetFlows);
        }

        // update db amounts after ledger transaction made and documents are uploaded
        assetFlows.forEach((flow) => {
          flow.status = AssetFlowStatus.PENDING;
          flow.updatedAt = now;
        });
        trade.status = TradeStatus.FIRM;
        trade.updatedAt = now;
        trade.updatedBy = user;
        trade.assetFlows = assetFlows;

        const ledgerResponse: PendingLedgerTransactionResponse =
          await this.ledgerTradesService.createPendingTradeTransaction(tx, trade);
        ledgerTransactionIds.push(ledgerResponse.id);

        await this.auditLogsService.create(tx, claims, AuditLogAction.TRADE_FIRM, now, id, { ...assetFlows });

        await this.transactionStatusLogService.create(tx, TradeStatus.FIRM, now, TransactionType.TRADE, trade.id);

        const projectVintage = await tx.findOneOrFail(
          ProjectVintage,
          { id: assetFlows[0].asset.id },
          { populate: ['project'] },
        );

        // todo : (TD-83)
        if (claims.name !== 'init-console-script') {
          try {
            const sendgridPayload = new InternalTradeUpdated(
              await this.notificationsService.getNotificationUrlWithUuid(NotificationEvent.TRADE_UPDATED, trade.id),
              trade.uiKey,
              projectVintage.project.name,
              projectVintage.name(),
              trade.tradeCounterparties.find((f) => f.isPrimary)!.counterparty.organization.name,
              trade.amount,
              TradeStatus.FIRM,
              now,
              claims,
            );
            await this.notificationsService.handleEvent(tx, NotificationEvent.TRADE_UPDATED, sendgridPayload);
          } catch (e) {
            console.error(
              `error sending ${NotificationEvent.TRADE_UPDATED} email for trade ${trade.uiKey} : ` + JSON.stringify(e),
            );
          }
        }

        return trade;
      });

      return trade;
    } catch (e) {
      if (ledgerTransactionIds.length > 0) {
        await this.ledgerTransactionsService.revertTransactionsAfterFailedApi(
          id,
          now,
          ledgerTransactionIds,
          `firming trade ${id}`,
        );
      }
      throw e;
    }
  }

  async getSavedTradeConfirmation(tradeId: uuid): Promise<TradeConfirmation> {
    return await this.em.findOneOrFail(
      TradeConfirmation,
      { trade: tradeId },
      { populate: ['products', 'trade.amount'] },
    );
  }

  async getTradeConfirmationDetails(
    id: uuid,
    data: AdminTradeConfirmationRequestDTO,
  ): Promise<AdminTradeConfirmationResponseDTO> {
    const now = new Date();
    const trade = await this.em.findOneOrFail(
      Trade,
      { id },
      { populate: ['tradeCounterparties.counterparty.organization'] },
    );
    const vintageFlows = await this.em.find(
      AssetFlow,
      { transaction: trade.id, assetType: AssetType.REGISTRY_VINTAGE },
      { populate: ['asset.details.vintageDetails'] },
    );
    return this.privateGenerateTradeConfirmationDetails(now, trade, vintageFlows, data);
  }

  // indicative trades are basically just quotes.
  // vintage flows are of type pre-pending so that it doesn't affect ledger or calculations
  // there are no ledger holds yet
  // can be auto-canceled if goodUntilDate is set, otherwise must be canceled by trader
  // will check for compliance vintages and compliance permit unless !validateCompliance
  // validateCompliance can only be false if it's from the init cancel script
  async indicate(
    claims: UserClaims,
    data: AdminTradeCreateRequestDTO,
    validateCompliance: boolean = true,
  ): Promise<Trade> {
    const now = new Date();
    const tradeId: uuid = uuid();
    if (!validateCompliance && claims.name !== 'init-console-script') {
      throw new InternalServerErrorException('validateCompliance cannot be false');
    }

    return await this.em.transactional(async (tx) => {
      const user = await tx.findOneOrFail(User, { email: claims.email });
      const projectVintages = await tx.find(ProjectVintage, {
        id: data.projectVintages.map((m) => m.assetId),
      });
      // if any projectVintages in request is not found, throw a 400 error
      const missingProjectVintages = data.projectVintages
        .map((m) => m.assetId)
        .filter((f) => !projectVintages.map((m) => m.id).includes(f));
      if (missingProjectVintages.length > 0) {
        throw new BadRequestException([`projectVintages [${missingProjectVintages.join(',')}] not found`]);
      }

      // validate counterparties
      const counterparties = await this.privateValidateTradeCounterparties(tx, data.counterparties);

      // this book is the source or destination (depending on sell or buy respectively)
      const book = await tx.findOne(Book, data.bookId);
      if (!book) {
        throw new BadRequestException([`Book not found`]);
      }
      // make sure the book can buy/sell depending on trade action and book
      if (
        (data.type === TradeType.BUY && !book.allowedActions.includes(BookAction.BUY)) ||
        (data.type === TradeType.SELL && !book.allowedActions.includes(BookAction.SELL))
      ) {
        throw new UnprocessableEntityException(`Book ${book.name} cannot trade ${data.type}`);
      }
      // validate if book is compliance, will not validate for initital credits
      if (validateCompliance) {
        await validateComplianceVintages(tx, book, projectVintages);
      }

      // create transaction
      const transaction = tx.create(Transaction, { id: tradeId, type: TransactionType.TRADE });
      await tx.persistAndFlush(transaction);

      // create vintage flows with status pre-pending
      const assets: Asset[] = await tx.find(
        Asset,
        { id: data.projectVintages.map((m) => m.assetId) },
        { populate: ['details.vintageDetails'] },
      );

      const assetFlows = data.projectVintages.map((m) =>
        tx.create(AssetFlow, {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          amount: m.amount,
          asset: assets.find((f) => f.id === m.assetId) || m.assetId, // hack since assets should always exist or this will fail
          assetType: AssetType.REGISTRY_VINTAGE,
          destination: data.type === TradeType.BUY ? data.bookId : uuid(environment.rubicon.books.offsets),
          status: AssetFlowStatus.PRE_PENDING,
          rawPrice: data.rawPrice,
          otherFee: data.otherFee,
          serviceFee: Decimal.sum(...data.counterparties.map((m) => m.actualServiceFee)),
          source: data.type === TradeType.BUY ? uuid(environment.rubicon.books.offsets) : data.bookId,
          detailedAsset: projectVintages.find((pv) => pv.id === m.assetId),
          transaction: tradeId,
          transactionSubtype: data.type,
          transactionType: TransactionType.TRADE,
        }),
      );
      // create trade
      const trade = tx.create(Trade, {
        ...data,
        id: tradeId,
        createdAt: now,
        updatedAt: now,
        amount: Decimal.sum(...data.projectVintages.map((m) => m.amount), 0).toNumber(),
        book: data.bookId,
        isDelivered: false,
        isPaid: false,
        status: TradeStatus.INDICATIVE,
        updatableStatusOrder: ['pending_payment', 'pending_delivery'],
        uiKey: data.type === TradeType.BUY ? generateRandomKey('TB') : generateRandomKey('TS'),
        updatedBy: user,
        assetFlows,
      });

      // create trade counterparties
      const tradeCounterparties = data.counterparties.map((m) =>
        tx.create(TradeCounterparty, {
          ...m,
          createdAt: now,
          updatedAt: now,
          trade: tradeId,
          counterparty: counterparties.find((f) => f.organization.id === m.organizationId)!,
        }),
      );

      trade.tradeCounterparties.set(tradeCounterparties);

      await this.auditLogsService.create(tx, claims, AuditLogAction.TRADE_INDICATED, now, trade.id, {
        ...trade,
      });

      await this.transactionStatusLogService.create(tx, TradeStatus.INDICATIVE, now, TransactionType.TRADE, trade.id);

      // todo : (TD-83)
      if (claims.name !== 'init-console-script') {
        try {
          const sendgridPayload = new InternalTradeCreated(
            await this.notificationsService.getNotificationUrlWithUuid(NotificationEvent.TRADE_CREATED, trade.id),
            trade.uiKey,
            projectVintages[0].project.name,
            projectVintages[0].name(),
            trade.tradeCounterparties.find((f) => f.isPrimary)!.counterparty.organization.name,
            trade.amount,
            now,
            claims,
          );
          await this.notificationsService.handleEvent(tx, NotificationEvent.TRADE_CREATED, sendgridPayload);
        } catch (e) {
          console.error(
            `error sending ${NotificationEvent.TRADE_CREATED} email for trade ${trade.uiKey} : ` + JSON.stringify(e),
          );
        }
      }

      return trade;
    });
  }

  // during the execute step, isPaid can be marked as true when prompted
  // status stays "executed" until both isPaid and isDelivered are marked true
  async pay(claims: UserClaims, id: uuid): Promise<Trade> {
    return await this.em.transactional(async (tx) => {
      const now = new Date();
      const user = await tx.findOneOrFail(User, { email: claims.email });
      const trade = await tx.findOneOrFail(
        Trade,
        { id },
        {
          populate: ['tradeCounterparties.counterparty.defaultFees', 'tradeCounterparties.counterparty.organization'],
        },
      );
      if (trade.status !== TradeStatus.EXECUTED) {
        throw new UnprocessableEntityException(`Trade ${id} must have status ${TradeStatus.EXECUTED}`);
      }
      if (trade.isPaid) {
        throw new UnprocessableEntityException(`Trade ${id} is already paid`);
      }
      trade.isPaid = true;
      trade.updatedAt = now;
      trade.updatedBy = user;

      await this.auditLogsService.create(tx, claims, AuditLogAction.TRADE_PAID, now, id, {});

      await this.transactionStatusLogService.create(
        tx,
        TradeStatus.DEPRECATED_PAID,
        now,
        TransactionType.TRADE,
        trade.id,
      );

      const vintageFlows = await tx.find(AssetFlow, { transaction: trade.id, assetType: AssetType.REGISTRY_VINTAGE });
      const projectVintage = await tx.findOneOrFail(
        ProjectVintage,
        { id: vintageFlows[0].asset.id },
        { populate: ['project'] },
      );

      // todo : (TD-83)
      if (claims.name !== 'init-console-script') {
        try {
          const sendgridPayload = new InternalTradeUpdated(
            await this.notificationsService.getNotificationUrlWithUuid(NotificationEvent.TRADE_UPDATED, trade.id),
            trade.uiKey,
            projectVintage.project.name,
            projectVintage.name(),
            trade.tradeCounterparties.find((f) => f.isPrimary)!.counterparty.organization.name,
            trade.amount,
            'paid',
            now,
            claims,
          );
          await this.notificationsService.handleEvent(tx, NotificationEvent.TRADE_UPDATED, sendgridPayload);
        } catch (e) {
          console.error(
            `error sending ${NotificationEvent.TRADE_UPDATED} email for trade ${trade.uiKey} : ` + JSON.stringify(e),
          );
        }
      }

      // if trade is delivered
      // then traqde.isDelivered and trade.isPaid are both true
      // settle trade
      if (trade.isDelivered) {
        return await this.privateSettle(tx, claims, user, trade, now);
      }

      return trade;
    });
  }

  async update(claims: UserClaims, id: uuid, data: AdminTradeUpdateRequestDTO): Promise<Trade> {
    return await this.em.transactional(async (tx) => {
      const now = new Date();
      const user = await tx.findOneOrFail(User, { email: claims.email });
      const trade = await tx.findOneOrFail(
        Trade,
        { id },
        {
          populate: ['tradeCounterparties.counterparty.defaultFees', 'tradeCounterparties.counterparty.organization'],
        },
      );

      // this assumes that there's only one asset_flow associated to this trade
      const assetFlow = await tx.findOneOrFail(AssetFlow, { transaction: id });

      if (data.counterparties) {
        const counterparties = await this.privateValidateTradeCounterparties(tx, data.counterparties ?? []);

        // remove all existing trade counterparties
        const oldTradeCounterparties = await tx.find(TradeCounterparty, { trade: trade.id });
        tx.remove(oldTradeCounterparties);

        // add all counterparties as new counterparties
        const newTradeCounterparties = data.counterparties.map((m) =>
          tx.create(TradeCounterparty, {
            ...m,
            counterparty: counterparties.find((f) => f.organization.id === m.organizationId)!,
            trade: trade.id,
            createdAt: now,
            updatedAt: now,
          }),
        );
        trade.tradeCounterparties.set(newTradeCounterparties);
      }
      const updatedServiceFees = Decimal.sum(...trade.tradeCounterparties.map((m) => m.actualServiceFee ?? 0));

      trade.memo = data.memo !== undefined ? data.memo : trade.memo;
      assetFlow.otherFee = data.otherFee !== undefined ? data.otherFee : assetFlow.otherFee;
      assetFlow.rawPrice = data.rawPrice !== undefined ? data.rawPrice : assetFlow.rawPrice;
      assetFlow.serviceFee = updatedServiceFees;
      trade.updatedAt = now;
      trade.updatedBy = user;
      assetFlow.updatedAt = now;

      await this.auditLogsService.create(tx, claims, AuditLogAction.TRADE_UPDATED, now, id, {
        ...data,
        updatedCounterparties: data.counterparties ? trade.tradeCounterparties : undefined,
      });
      return trade;
    });
  }

  async updateAmounts(claims: UserClaims, id: uuid, data: AdminAssetTradeRequestDTO[]): Promise<Trade> {
    const now = new Date();
    let ledgerTransactionIds: uuid[] = [];

    try {
      return await this.em.transactional(async (tx) => {
        const user = await tx.findOneOrFail(User, { email: claims.email });
        const trade: Trade = await tx.findOneOrFail(Trade, id, {
          populate: ['tradeCounterparties.counterparty.defaultFees', 'tradeCounterparties.counterparty.organization'],
        });
        if (trade.isDelivered || trade.status === TradeStatus.CANCELED) {
          throw new UnprocessableEntityException(`Trade ${id} is already canceled or delivered`);
        }
        const projectVintages = await tx.find(ProjectVintage, {
          id: data.map((m) => m.assetId),
        });
        const missingProjectVintages = data
          .map((m) => m.assetId)
          .filter((f) => !projectVintages.map((m) => m.id).includes(f));
        if (missingProjectVintages.length > 0) {
          throw new BadRequestException([`ProjectVintages [${missingProjectVintages.join(',')}] not found`]);
        }
        const assetFlows = await tx.find(
          AssetFlow,
          { transaction: trade.id, asset: data.map((m) => m.assetId) },
          { populate: ['asset'] },
        );

        // currently it is assumed that the vintage has to stay the same.
        const missingFlows = data
          .map((m) => m.assetId)
          .filter((pvId) => !assetFlows.map((m) => m.asset.id).includes(pvId));
        if (missingProjectVintages.length > 0) {
          throw new BadRequestException([`VintageFlows [${missingFlows.join(',')}] not found`]);
        }
        assetFlows.forEach((flow) => {
          const pv = projectVintages.find((f) => f.id === flow.asset.id);
          flow.detailedAsset = pv;
          const newAmount = data.find((f) => f.assetId === flow.asset.id);
          if (!newAmount) {
            // should never happen
            throw new InternalServerErrorException(`new amount for asset ${flow.asset.id} not found`);
          }
          flow.amount = newAmount.amount;
          flow.updatedAt = now;
        });
        trade.updatedAt = now;
        trade.updatedBy = user;
        trade.amount = Decimal.sum(...data.map((m) => m.amount), 0).toNumber();
        trade.assetFlows = assetFlows;

        if (trade.status !== TradeStatus.INDICATIVE) {
          if (trade.type === TradeType.SELL) {
            // get allocations of the book for the sell
            const query: InternalHoldingsQuery = { includePrices: false };
            query.assetIds = projectVintages.map((m) => m.id);
            query.ownerIds = [trade.book.id];
            const assetAllocations: InternalHolding[] = await this.assetsService.internalGetOwnerHoldings(
              tx.getKnex(),
              query,
            );
            for (const pv of data) {
              const allocation = assetAllocations.find((f) => f.asset_id === pv.assetId);
              if ((allocation?.amount_available || 0) < pv.amount) {
                throw new UnprocessableEntityException(
                  `trade sell amount for ${pv.assetId} must be less than or equal to ${allocation?.amount_available || 0}`,
                );
              }
            }
          }
          const ledgerResponse: BulkLedgerTransactionsResponse =
            await this.ledgerTradesService.updateTradeAmountsTransaction(tx, trade);
          ledgerTransactionIds = this.ledgerTransactionsService.getTransactionIdsFromBulkResponse(ledgerResponse);
        }

        await this.auditLogsService.create(tx, claims, AuditLogAction.TRADE_UPDATED, now, trade.id, { ...data });

        // todo : (TD-83)
        try {
          const sendgridPayload = new InternalTradeUpdated(
            await this.notificationsService.getNotificationUrlWithUuid(NotificationEvent.TRADE_UPDATED, trade.id),
            trade.uiKey,
            projectVintages[0].project.name,
            projectVintages[0].name(),
            trade.tradeCounterparties.find((f) => f.isPrimary)!.counterparty.organization.name,
            trade.amount,
            trade.status,
            now,
            claims,
          );
          await this.notificationsService.handleEvent(tx, NotificationEvent.TRADE_UPDATED, sendgridPayload);
        } catch (e) {
          console.error(
            `error sending ${NotificationEvent.TRADE_UPDATED} email for trade ${trade.uiKey} : ` + JSON.stringify(e),
          );
        }

        return trade;
      });
    } catch (e) {
      if (ledgerTransactionIds.length > 0) {
        await this.ledgerTransactionsService.revertTransactionsAfterFailedApi(
          id,
          now,
          ledgerTransactionIds,
          `updating trade ${id} amounts`,
        );
      }
      throw e;
    }
  }

  async createAccountingEntry(
    id: uuid,
    data: AdminTradeCreateAccountingRequestDTO,
  ): Promise<{
    status: string;
    message?: string;
    originalCostBasis?: number;
    previousCostBasis?: number;
    costBasis?: number;
    amount?: number;
    remaining?: number;
  }> {
    return await this.em.transactional(async (tx) => {
      return await this.internalCreateAccountingEntry(tx, id, data);
    });
  }

  async internalCreateAccountingEntry(
    tx: EntityManager,
    id: uuid,
    data: AdminTradeCreateAccountingRequestDTO,
  ): Promise<{
    status: string;
    vintageId: uuid;
    originalCreditFlowId: uuid;
    tradeId: uuid;
    message?: string;
    originalCostBasis?: number;
    previousCostBasis?: number;
    costBasis?: number;
    amount?: number;
    remaining?: number;
  }> {
    if (data.amount !== undefined && data.targetCostBasis !== undefined) {
      throw new UnprocessableEntityException(
        `Can only specify either the amount to mark-down or the target cost basis, not both.`,
      );
    }

    const trade: Trade = await tx.findOneOrFail(Trade, id);
    if (trade.type !== TradeType.BUY) {
      throw new UnprocessableEntityException(`Trade ${id} is not a buy trade`);
    }
    if (trade.status !== TradeStatus.SETTLED) {
      throw new UnprocessableEntityException(`Trade ${id} is not settled`);
    }
    // we can only do this for trades with *one* asset flow and *one* credit flow
    const assetFlows: AssetFlow[] = await tx.find(AssetFlow, { transaction: trade.id, amount: { $ne: 0 } });
    if (assetFlows.length !== 1) {
      throw new UnprocessableEntityException(
        `Can only create accounting entries for Trades with exactly one asset flow`,
      );
    }
    const creditFlows: CreditFlow[] = await tx.find(
      CreditFlow,
      { assetFlow: assetFlows[0] },
      { populate: ['amounts', 'creditOutflows.assetFlow'] },
    );
    if (creditFlows.length !== 1) {
      throw new UnprocessableEntityException(
        `Can only create accounting entries for Trades with exactly one credit flow`,
      );
    }

    // get the remaining amount
    const remaining = (creditFlows[0].amounts?.totalInflows ?? 0) - (creditFlows[0].amounts?.totalOutflows ?? 0);
    if (remaining === 0) {
      throw new UnprocessableEntityException(`Trade ${id} has no remaining credits available`);
    }

    // compute cost basis (just to report back)
    const originalDollar = new Decimal(assetFlows[0].rawPrice)
      .add(new Decimal(assetFlows[0].otherFee ?? 0))
      .add(new Decimal(assetFlows[0].serviceFee ?? 0));
    const previousDollar = new Decimal(creditFlows[0].amounts!.costBasisTotalAmount);
    const originalCostBasis = originalDollar.div(assetFlows[0].amount).toNumber();
    const previousCostBasis = Number(creditFlows[0].amounts!.costBasis);

    let amount: Decimal;
    if (data.amount !== undefined) {
      amount = data.amount;
    } else if (data.targetCostBasis !== undefined) {
      if (data.targetCostBasis.toNumber() >= previousCostBasis) {
        throw new UnprocessableEntityException(
          `Can only mark-down the cost-basis. Current cost basis is ${previousCostBasis}, target is ${data.targetCostBasis.toNumber()}`,
        );
      }

      amount = data.targetCostBasis.mul(remaining).sub(previousDollar);
    } else {
      throw new UnprocessableEntityException(`Amount or target need to be specified.`);
    }

    const costBasis = previousDollar.add(amount).div(remaining).toNumber();

    // create new entries
    const af = tx.create(AssetFlow, {
      amount: 0,
      asset: assetFlows[0].asset,
      assetType: AssetType.REGISTRY_VINTAGE,
      destination: assetFlows[0].destination,
      rawPrice: amount,
      settledAt: new Date(),
      source: assetFlows[0].source,
      status: AssetFlowStatus.SETTLED,
      transaction: trade.id,
      transactionSubtype: TradeType.BUY,
      transactionType: TransactionType.TRADE,
    });

    const cf = tx.create(CreditFlow, {
      amount: 0,
      projectVintage: creditFlows[0].projectVintage,
      assetFlow: af,
      type: 'accounting',
      creditInflow: creditFlows[0],
    });

    tx.persistAndFlush([af, cf]);

    return {
      status: 'ok',
      originalCostBasis,
      previousCostBasis,
      costBasis,
      remaining,
      amount: amount.toNumber(),
      vintageId: creditFlows[0].projectVintage.id,
      originalCreditFlowId: creditFlows[0].id,
      tradeId: trade.id,
    };
  }

  /* private  */

  private privateGenerateTradeConfirmationDetails(
    now: Date,
    trade: Trade,
    vintageFlows: AssetFlow[],
    data: AdminTradeConfirmationRequestDTO,
  ): AdminTradeConfirmationResponseDTO {
    const isBuy = trade.type === TradeType.BUY;

    // validate trade has counterparties and a primary counterparty
    if (!trade.tradeCounterparties.isInitialized() || trade.tradeCounterparties.getItems().length === 0) {
      throw new UnprocessableEntityException('Counterparties not specified on trade');
    }
    const primaryCounterparty = trade.tradeCounterparties.getItems().find((m) => m.isPrimary);
    if (!primaryCounterparty) {
      throw new UnprocessableEntityException('Primary counterparty not found');
    }
    // validate that all recipient emails belong to the primary counterparty
    const recipientEmails = data.recipientEmails.map((m) => m.toLowerCase() as Lowercase<string>);
    if (!recipientEmails.every((s) => primaryCounterparty.counterparty.tradeConfirmEmails.includes(s))) {
      throw new UnprocessableEntityException(`all recipientEmails must be present in counterparty tradeConfirmEmails`);
    }
    // validate none of the vintage flows have the same asset_id
    if (vintageFlows.map((m) => m.asset.id).some((s) => vintageFlows.filter((f) => f.asset.id === s).length > 1)) {
      throw new UnprocessableEntityException('each asset must only have one flow in trade to send email.');
    }

    // set trade confirmation details
    const sellPaymentTermsDefault =
      'Payment due within three days following the effective date of this Agreement. Delivery will occur only upon receipt of the payment.';
    const rubiconEntity = 'Rubicon Carbon Services, LLC';
    const paymentTerms = data.paymentTerms || (isBuy ? undefined : sellPaymentTermsDefault);
    const buyer = isBuy ? rubiconEntity : primaryCounterparty.counterparty.organization.name;
    const seller = isBuy ? primaryCounterparty.counterparty.organization.name : rubiconEntity;

    // generate products from vintage flows
    let tradeQuantity: number = 0;
    let tradePrice: Decimal = new Decimal(0);

    const products: BaseTradeConfirmationProductsResponse[] = vintageFlows.map((flow) => {
      const totalFlowPrice = new Decimal(flow.rawPrice); // we are just sending raw price no fees
      tradeQuantity += flow.amount;
      tradePrice = tradePrice.plus(totalFlowPrice);

      return {
        productId: flow.asset.id,
        registryProjectId: flow.asset.details.vintageDetails?.registry_project_id || '', // should never be ''
        projectName: flow.asset.details.name || '', // should never be ''
        vintageName: flow.asset.details.vintageDetails?.project_vintage_label || '', // should never be ''
        quantity: toFormattedNumberString(flow.amount),
        pricePerTonne: toFormattedPriceString(totalFlowPrice.div(flow.amount)),
        totalPrice: toFormattedPriceString(totalFlowPrice),
      };
    });

    return {
      id: trade.id,
      createdAt: now,
      updatedAt: now,
      tradeId: trade.id,
      uiKey: trade.uiKey,
      recipientEmails,
      formattedDate: toFormattedSendgridDate(now),
      seller,
      buyer,
      paymentTerms,
      deliveryTerms: data.deliveryTerms,
      products: products,
      totalPrice: toFormattedPriceString(tradePrice),
      totalQuantity: toFormattedNumberString(tradeQuantity),
    };
  }

  private privatePopulateTradeRelations<S extends string>(
    relations: TradeRelation[],
  ): FindOptions<Trade, S>['populate'] {
    const populateString: string[] = [
      'tradeCounterparties.counterparty.defaultFees',
      'tradeCounterparties.counterparty.organization',
      'transactionDetails',
    ];
    if (relations.includes(TradeRelation.BOOK)) {
      populateString.push('book');
    }
    if (relations.includes(TradeRelation.UPDATED_BY)) {
      populateString.push('updatedBy');
    }
    return populateString as unknown as FindOptions<Trade, S>['populate'];
  }

  private privateSetTradeOrderBys(orderBys: BaseTradeOrderByDTO[]): any {
    const x = [];
    for (const ob of orderBys) {
      if (ob.orderBy === TradeOrderByOptions.BOOK_NAME) {
        x.push({ book: { name: OrderByDirectionSQL[ob.orderByDirection] } });
      }
      if (ob.orderBy === TradeOrderByOptions.COUNTERPARTY_ORGANIZATION_NAME) {
        x.push({ transactionDetails: { counterpartyName: OrderByDirectionSQL[ob.orderByDirection] } });
      }
      if (ob.orderBy === TradeOrderByOptions.CREATED_AT) {
        x.push({ createdAt: OrderByDirectionSQL[ob.orderByDirection] });
      }
      if (ob.orderBy === TradeOrderByOptions.STATUS) {
        x.push({ status: OrderByDirectionSQL[ob.orderByDirection] });
      }
      if (ob.orderBy === TradeOrderByOptions.STATUS) {
        x.push({ status: OrderByDirectionSQL[ob.orderByDirection] });
      }
      if (ob.orderBy === TradeOrderByOptions.TYPE) {
        x.push({ type: OrderByDirectionSQL[ob.orderByDirection] });
      }
      if (ob.orderBy === TradeOrderByOptions.UI_KEY) {
        x.push({ uiKey: OrderByDirectionSQL[ob.orderByDirection] });
      }
      if (ob.orderBy === TradeOrderByOptions.UPDATED_AT) {
        x.push({ updatedAt: OrderByDirectionSQL[ob.orderByDirection] });
      }
    }

    return x;
  }

  private async privateSettle(
    tx: EntityManager,
    claims: UserClaims,
    user: User,
    trade: Trade,
    now: Date,
  ): Promise<Trade> {
    trade.settledAt = now;
    trade.status = TradeStatus.SETTLED;
    trade.updatedAt = now;
    trade.updatedBy = user;

    await this.auditLogsService.create(tx, user, AuditLogAction.TRADE_SETTLED, now, trade.id, {});

    await this.transactionStatusLogService.create(tx, TradeStatus.SETTLED, now, TransactionType.TRADE, trade.id);

    const vintageFlows = await tx.find(AssetFlow, { transaction: trade.id, assetType: AssetType.REGISTRY_VINTAGE });
    const projectVintage = await tx.findOneOrFail(
      ProjectVintage,
      { id: vintageFlows[0].asset.id },
      { populate: ['project'] },
    );

    // todo : (TD-83)
    try {
      const sendgridPayload = new InternalTradeUpdated(
        await this.notificationsService.getNotificationUrlWithUuid(NotificationEvent.TRADE_UPDATED, trade.id),
        trade.uiKey,
        projectVintage.project.name,
        projectVintage.name(),
        trade.tradeCounterparties.find((f) => f.isPrimary)!.counterparty.organization.name,
        trade.amount,
        TradeStatus.SETTLED,
        now,
        claims,
      );
      await this.notificationsService.handleEvent(tx, NotificationEvent.TRADE_UPDATED, sendgridPayload);
    } catch (e) {
      console.error(
        `error sending ${NotificationEvent.TRADE_UPDATED} email for trade ${trade.uiKey} : ` + JSON.stringify(e),
      );
    }

    return trade;
  }

  private async privateValidateSellVintageAmounts(
    tx: EntityManager,
    trade: Trade,
    assetFlows: AssetFlow[],
  ): Promise<void> {
    // get book balances
    const sourceBook: InternalBookResponse = await this.populateAllocationsService.internalPopulateBookAllocations(
      tx,
      trade.book,
      [AssetType.REGISTRY_VINTAGE],
      [BookRelations.OWNER_ALLOCATIONS_NESTED],
    );

    for (const af of assetFlows) {
      // get book-vintage allocation
      const pvAllocation = sourceBook.ownerAllocations?.allocations?.find((f) => f.asset.id === af.asset.id);
      if (!pvAllocation) {
        // should never happen
        throw new UnprocessableEntityException(`ProjectVintage ${af.asset.id} not found`);
      }

      if (pvAllocation.amountAvailable < af.amount) {
        throw new UnprocessableEntityException(
          `Trade sell amount ${af.amount} for ${af.asset.id} in ${trade.book.name} must be less than or equal to ${pvAllocation.amountAvailable}`,
        );
      }
    }
  }

  private async privateValidateTradeCounterparties(
    tx: EntityManager,
    data: AdminTradeCounterpartyRequestDTO[],
  ): Promise<Counterparty[]> {
    const counterparties = await tx.find(
      Counterparty,
      { organization: data.map((m) => m.organizationId) },
      { populate: ['defaultFees', 'organization'] },
    );

    // validate one is primary
    if (data.filter((m) => m.isPrimary).length !== 1) {
      throw new UnprocessableEntityException('Trade must have exactly one primary counterparty');
    }

    // validate all counterparties exist
    const missingCounterparties = data
      .map((m) => m.organizationId)
      .filter((f) => !counterparties.map((m) => m.organization.id).includes(f));

    if (missingCounterparties.length > 0) {
      throw new UnprocessableEntityException(
        `counterparty organizationIds [${missingCounterparties.join(',')}] must be valid uuids`,
      );
    }
    // todo : maybe validate calculated fees?

    return counterparties;
  }
}
