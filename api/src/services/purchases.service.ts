/* third party */
import { FilterQuery, LockMode } from '@mikro-orm/core';
import { EntityManager } from '@mikro-orm/postgresql';
import {
  ConflictException,
  Injectable,
  InternalServerErrorException,
  UnprocessableEntityException,
} from '@nestjs/common';
/* rubicon */
import {
  AssetFlowStatus,
  AssetType,
  AuditLogAction,
  BasePurchaseOrderBy,
  BookAction,
  BulkLedgerTransactionsResponse,
  DocumentType,
  NotificationEvent,
  PendingLedgerTransactionResponse,
  PurchaseOrderByOptions,
  PurchaseRelations,
  PurchaseStatus,
  ReleasedLedgerTransactionResponse,
  TransactionType,
  uuid,
} from '@rubiconcarbon/shared-types';
/* env */
import { environment } from '@env/environment';
/* app */
import { UserClaims } from '@app/auth/interfaces';
import {
  Asset,
  AssetFlow,
  Book,
  CustomerPortfolio,
  Document,
  ProjectVintage,
  Purchase,
  Transaction,
  User,
} from '@app/entities';
import { generateRandomKey } from '@app/helpers/random.helper';
import { AuditLogsService } from '@app/utility/audit-log';
import { LedgerPurchasesService } from '@app/ledger/services/ledger-purchases.service';
import { LedgerTransactionsService } from '@app/ledger/services/ledger-transactions.service';
import { SendgridService } from './sendgrid.service';
import { NotificationsService } from './notifications.service';
import { InternalTransactionBase, PurchaseCreated } from '@app/dtos/sendgrid-payload.dto';
import {
  AdminPurchaseDeliverRequestDTO,
  AdminPurchaseExecuteRequestDTO,
  AdminPurchaseQueryDTO,
  AdminPurchaseRequestDTO,
} from '@app/dtos/purchase.dto';
import { AssetsService } from './assets.service';
import { InternalHolding } from '@app/interfaces/allocation.interface';
import { InternalPurchaseQueryResponse } from '@app/interfaces/purchase.interface';
import { OrderByDirectionSQL } from '@app/enums/orderby.enum';
import { getPortalUserWithOrganizationFromClaims } from '@app/helpers';
import { TransactionStatusLogsService } from '@app/utility/transaction-status-log';

@Injectable()
export class PurchasesService {
  constructor(
    private readonly em: EntityManager,
    private assetsService: AssetsService,
    private auditLogsService: AuditLogsService,
    private transactionStatusLogService: TransactionStatusLogsService,
    private ledgerPurchasesService: LedgerPurchasesService,
    private ledgerTransactionsService: LedgerTransactionsService,
    private notificationsService: NotificationsService,
    private readonly sendgrid: SendgridService,
  ) {}

  // status is set to bind (second step of the purchase)
  // document of type DocumentType.PROOF_OF_CONFIRMATION required
  async bind(id: uuid, claims: UserClaims, ignoreDocCheck: boolean = false): Promise<Purchase> {
    if (ignoreDocCheck && claims.name !== 'init-console-script') {
      throw new InternalServerErrorException('ignoreDocs cannot be true');
    }

    const now = new Date();
    const purchase = await this.em.transactional(async (tx) => {
      // get purchase
      const purchase = await tx.findOneOrFail(
        Purchase,
        { id },
        {
          populate: ['customerPortfolio.organization'],
          lockMode: LockMode.PESSIMISTIC_WRITE,
          // MikroORM writes the query in such a way that it is incompatible with the lock if we don't specify the table. Here `p0` is the Transaction table
          lockTableAliases: ['p0'],
        },
      );
      if (purchase.status !== PurchaseStatus.FIRM) {
        throw new UnprocessableEntityException(`Purchase must have status ${PurchaseStatus.FIRM}`);
      }

      // get document
      if (!ignoreDocCheck) {
        const document = await tx.findOne(Document, {
          relatedUiKey: purchase.uiKey,
          type: DocumentType.PROOF_OF_CONFIRMATION,
          isUploaded: true,
        });
        if (!document) {
          throw new UnprocessableEntityException(
            `Purchase must have a document with type ${DocumentType.PROOF_OF_CONFIRMATION} uploaded before moving to status ${PurchaseStatus.BINDING}`,
          );
        }
      }

      purchase.status = PurchaseStatus.BINDING;
      purchase.updatedAt = now;

      // create audit log
      await this.auditLogsService.create(tx, claims, AuditLogAction.PURCHASE_IS_BINDING, now, purchase.id, {
        updatedAt: now,
      });

      await this.transactionStatusLogService.create(
        tx,
        PurchaseStatus.BINDING,
        now,
        TransactionType.PURCHASE,
        purchase.id,
      );

      return purchase;
    });

    // send email notifications
    if (claims.name !== 'init-console-script') {
      await this.privateHandleNotificationsAndEmails(
        now,
        NotificationEvent.PURCHASE_STATUS_UPDATED,
        purchase,
        PurchaseStatus.BINDING,
      );
    }

    return purchase;
  }

  // cancel can happen anytime before delivery
  // if isDelivered = true, must fail
  async cancel(id: uuid, claims: UserClaims): Promise<Purchase> {
    const now = new Date();
    let ledgerTransactionId;

    try {
      const purchase = await this.em.transactional(async (tx) => {
        const purchase = await tx.findOneOrFail(
          Purchase,
          { id: id },
          {
            populate: ['customerPortfolio.organization'],
            lockMode: LockMode.PESSIMISTIC_WRITE,
            // MikroORM writes the query in such a way that it is incompatible with the lock if we don't specify the table. Here `p0` is the purchase table
            lockTableAliases: ['p0'],
          },
        );
        if (purchase.isDelivered) {
          throw new UnprocessableEntityException(`Purchase already delivered and cannot be canceled`);
        } else if (purchase.status === PurchaseStatus.CANCELED) {
          throw new UnprocessableEntityException(`Purchase already canceled`);
        }

        const assetFlows = await tx.find(AssetFlow, { transaction: id });
        assetFlows.forEach((f) => {
          f.updatedAt = now;
          f.status = AssetFlowStatus.CANCELED;
        });

        tx.assign(purchase, {
          updatedAt: now,
          dateFinished: now,
          status: PurchaseStatus.CANCELED,
        });
        const ledgerResponse: ReleasedLedgerTransactionResponse =
          await this.ledgerPurchasesService.cancelPendingPurchaseTransaction(tx, purchase);
        ledgerTransactionId = ledgerResponse.id;

        await this.auditLogsService.create(tx, claims, AuditLogAction.PURCHASE_CANCELED, now, id, {
          status: PurchaseStatus.CANCELED,
          canceledAt: now,
        });

        await this.transactionStatusLogService.create(tx, PurchaseStatus.CANCELED, now, TransactionType.PURCHASE, id);

        return purchase;
      });

      // send email notifications
      await this.privateHandleNotificationsAndEmails(
        now,
        NotificationEvent.PURCHASE_STATUS_UPDATED,
        purchase,
        PurchaseStatus.CANCELED,
      );

      return purchase;
    } catch (e) {
      if (ledgerTransactionId) {
        await this.ledgerTransactionsService.revertTransactionsAfterFailedApi(
          id,
          now,
          [ledgerTransactionId],
          `canceling purchase ${id}`,
        );
      }
      throw e;
    }
  }

  // purchase is created
  // status is set to firm (firt step of a purchase)
  // assets ARE held in pending at this step
  async createFirm(data: AdminPurchaseRequestDTO, claims: UserClaims): Promise<Purchase> {
    const now = new Date();
    const purchaseId: uuid = uuid();
    let ledgerTransactionId;
    let totalAmountRequested = 0;

    try {
      const purchase = await this.em.transactional(async (tx) => {
        // get the destination book for the purchase
        const customerPortfolio = await tx.findOne(
          CustomerPortfolio,
          { organization: data.organizationId },
          { populate: ['book', 'organization'] },
        );
        if (!customerPortfolio) {
          throw new UnprocessableEntityException(`customer organizationId ${data.organizationId} must be a valid uuid`);
        } else if (!customerPortfolio.isEnabled || !customerPortfolio.organization.isEnabled) {
          throw new UnprocessableEntityException(
            `customer organization ${customerPortfolio.organization.name} must be enabled`,
          );
        } else if (!customerPortfolio.book.allowedActions.includes(BookAction.PURCHASE)) {
          throw new UnprocessableEntityException(
            `customerPortfolio book ${customerPortfolio.organization.name} must be allowed to purchase`,
          );
        }

        // get all the assets
        const assets = await tx.find(
          Asset,
          { id: data.assets.map((m) => m.assetId) },
          { populate: ['details.portfolioDetails', 'details.rrtDetails', 'details.vintageDetails'] },
        );
        const assetBooks = await tx.find(Book, { id: data.assets.map((m) => m.assetId) });
        const assetVintages = await tx.find(ProjectVintage, { id: data.assets.map((m) => m.assetId) });
        const assetIds = assets.map((m) => m.id);
        const missingAssets = data.assets.filter((f) => !assetIds.includes(f.assetId));

        if (missingAssets.length > 0) {
          throw new UnprocessableEntityException(
            `could not find assets [${missingAssets.map((m) => m.assetId).join(',')}]`,
          );
        }
        if (!assets.every((e) => e.type === data.assetType)) {
          throw new ConflictException(`assets must all be of the same type`);
        }
        // add check if assets are allowed
        const forbiddenAssets = assets.filter(
          (f) =>
            !f.details.isPublic && // public assets are allowed to anyone, non-public assets are limited to only the limitedOrgId
            f.details.portfolioDetails?.organization?.id !== customerPortfolio.organization.id, // if nonpublic asset's org id is not the purchaser org id, purchaser cannot buy it
        );
        if (forbiddenAssets.length > 0) {
          throw new UnprocessableEntityException(
            `purchaser cannot purchase [${forbiddenAssets.map((m) => m.details.name).join(',')}]`,
          );
        }

        // get assetAllocations
        const assetAllocations: InternalHolding[] = await this.assetsService.internalGetPurchaseHoldings(
          tx.getKnex(),
          data.assets,
        );

        // generate uiKey
        let key = generateRandomKey('PR');
        for (let i = 0; i < 1000; i++) {
          const notFound = (await tx.findOne(Purchase, { uiKey: key }, { fields: ['id', 'uiKey'] })) == null;
          if (notFound) break;
          /* istanbul ignore next */
          key = generateRandomKey('PR');
          // let if fail if we exhaust ids
        }

        // create transaction
        const transaction = tx.create(Transaction, {
          id: purchaseId,
          type: TransactionType.PURCHASE,
          showCustomer: true,
        });
        await tx.persistAndFlush(transaction);

        const flows: AssetFlow[] = [];
        for (const dataAsset of data.assets) {
          // get asset
          const asset = assets.find((a) => a.id === dataAsset.assetId);
          if (!asset) {
            throw new InternalServerErrorException(`could not find asset ${asset}`);
          }
          // get asset allocation
          const allocation = assetAllocations.find(
            (f) => f.asset_id === dataAsset.assetId && f.owner_id === dataAsset.sourceId,
          );

          // validate amount which is amountAllocated - amountPendingPurchase (since purchases are sourced from this book so it should be a negative as well) - amountPendingRetirement - amountPendingSell - amountPendingCustomerTransferOutflow
          if (!allocation || allocation.amount_available < dataAsset.amount) {
            throw new UnprocessableEntityException(
              `Book ${dataAsset.sourceId} must have amountAvaliable greater than or equal to ${dataAsset.amount} for asset ${dataAsset.assetId}`,
            );
          }

          // create asset flows
          flows.push(
            tx.create(AssetFlow, {
              id: uuid(),
              createdAt: now,
              updatedAt: now,
              amount: dataAsset.amount,
              asset: dataAsset.assetId,
              assetType: data.assetType,
              destination: customerPortfolio.book,
              // todo (3163) : fix
              detailedAsset:
                asset.type === AssetType.RCT
                  ? assetBooks.find((f) => f.id === asset.id)
                  : asset.type === AssetType.REGISTRY_VINTAGE
                    ? assetVintages.find((f) => f.id === asset.id)
                    : undefined,
              otherFee: dataAsset.otherFee,
              rawPrice: dataAsset.rawPrice,
              serviceFee: dataAsset.serviceFee,
              source: dataAsset.sourceId,
              status: AssetFlowStatus.PENDING,
              transaction: purchaseId,
              transactionType: TransactionType.PURCHASE,
            }),
          );

          totalAmountRequested += dataAsset.amount;
        }

        // create purchase
        const purchase = tx.create(Purchase, {
          ...data,
          id: purchaseId,
          createdAt: now,
          updatedAt: now,
          amount: totalAmountRequested,
          customerPortfolio,
          status: PurchaseStatus.FIRM,
          dateStarted: now,
          uiKey: key,
          assetFlows: [],
          isDelivered: false,
          isPaid: false,
          updatableStatusOrder: ['pending_payment', 'pending_delivery'],
        });
        purchase.assetFlows = flows;

        await this.auditLogsService.create(tx, claims, AuditLogAction.PURCHASE_FIRM, now, purchase.id, {
          ...data,
        });

        await this.transactionStatusLogService.create(
          tx,
          PurchaseStatus.FIRM,
          now,
          TransactionType.PURCHASE,
          purchase.id,
        );

        const ledgerResponse: PendingLedgerTransactionResponse =
          await this.ledgerPurchasesService.createPendingPurchaseTransaction(tx, purchase, flows);
        ledgerTransactionId = ledgerResponse.id;
        return purchase;
      });

      // send emails and notifications
      if (claims.name !== 'init-console-script') {
        await this.privateHandleNotificationsAndEmails(
          now,
          NotificationEvent.PURCHASE_CREATED,
          purchase,
          PurchaseStatus.FIRM,
        );
      }

      return purchase;
    } catch (e) {
      if (ledgerTransactionId) {
        await this.ledgerTransactionsService.revertTransactionsAfterFailedApi(
          purchaseId,
          now,
          [ledgerTransactionId],
          `creating firm purchase ${purchaseId}`,
        );
      }
      throw e;
    }
  }

  // during the executed status, a purchase can be marked as isDelivered
  // status stays "executed" until isDelivered and isPaid are both true
  // regardless of when customer puchase is settled, as soon as isDelivered is true, update ledger and balances and asset flows to settled
  async deliver(id: uuid, claims: UserClaims, data: AdminPurchaseDeliverRequestDTO): Promise<Purchase> {
    const now = new Date();
    let ledgerTransactionIds: uuid[] = [];

    try {
      return await this.em.transactional(async (tx) => {
        const purchase = await tx.findOneOrFail(
          Purchase,
          { id },
          {
            // MikroORM writes the query in such a way that it is incompatible with the lock if we don't specify the table. Here `p0` is the purchase table
            lockTableAliases: ['p0'],
            populate: ['customerPortfolio.organization'],
            lockMode: LockMode.PESSIMISTIC_WRITE,
          },
        );

        if (purchase.status !== PurchaseStatus.EXECUTED) {
          throw new UnprocessableEntityException(`Purchase ${id} must have status ${PurchaseStatus.EXECUTED}`);
        }
        if (purchase.isDelivered) {
          throw new UnprocessableEntityException(`Purchase ${id} already delivered`);
        }

        const assetFlows = await tx.find(
          AssetFlow,
          { transaction: purchase.id },
          { populate: ['asset', 'source', 'destination'] },
        );
        const assetBooks = await tx.find(Book, { id: assetFlows.map((m) => m.asset.id) });
        const assetVintages = await tx.find(ProjectVintage, { id: assetFlows.map((m) => m.asset.id) });

        // expect the destination book for all flows to be the same as the customer portfolio
        if (!assetFlows.map((m) => m.destination.id).every((e) => e === purchase.customerPortfolio.id)) {
          throw new InternalServerErrorException(
            `expected all destinations for purchase ${id} to be ${purchase.customerPortfolio.organization.name}`,
          );
        }

        // validate that all the source books are enabled, the customer portfolio is enabled, and the customer portfolio organization is enabled
        if (
          assetFlows.map((m) => m.source.isEnabled).includes(false) ||
          !purchase.customerPortfolio.isEnabled ||
          !purchase.customerPortfolio.organization.isEnabled
        ) {
          throw new UnprocessableEntityException(`customerPortfolio must be enabled to complete purchase`);
        }

        // get asset allocations for purchased assets and customer organization and validate amounts
        const assetAllocations = await this.assetsService.internalGetPurchaseHoldings(
          tx.getKnex(),
          assetFlows.map((flow) => {
            return { assetId: flow.asset.id, rawPrice: flow.rawPrice, sourceId: flow.source.id, amount: flow.amount };
          }),
        );
        for (const flow of assetFlows) {
          const allocation = assetAllocations.find(
            (allocation) => allocation.asset_id === flow.asset.id && allocation.owner_id === flow.source.id,
          );
          if (!allocation) {
            throw new InternalServerErrorException(
              `could not find asset allocation for owner ${flow.source.name} and asset ${flow.asset.id}`,
            );
          }
          if (allocation.pending_purchase_outflow < flow.amount) {
            throw new UnprocessableEntityException(
              `owner ${allocation.owner_id} must have ${flow.amount} pending for asset ${flow.asset.id} but only has ${allocation.pending_purchase_outflow}`,
            );
          }
        }

        // update purchase and assetFlow statuses
        assetFlows.forEach((af: AssetFlow) => {
          // todo (3163) : fix
          af.detailedAsset =
            af.assetType === AssetType.RCT
              ? assetBooks.find((f) => f.id === af.asset.id)
              : af.assetType === AssetType.REGISTRY_VINTAGE
                ? assetVintages.find((f) => f.id === af.asset.id)
                : undefined;
          af.settledAt = data.assetsDeliveredAt;
          af.status = AssetFlowStatus.SETTLED;
          af.updatedAt = now;
        });
        purchase.updatedAt = now;
        purchase.isDelivered = true;
        if (purchase.isDelivered && purchase.isPaid) {
          purchase.status = PurchaseStatus.SETTLED;
          purchase.dateFinished = now;
          await this.auditLogsService.create(tx, claims, AuditLogAction.PURCHASE_SETTLED, now, purchase.id, {});
          await this.transactionStatusLogService.create(
            tx,
            PurchaseStatus.SETTLED,
            now,
            TransactionType.PURCHASE,
            purchase.id,
          );
        }

        // call ledger
        const ledgerResponse: BulkLedgerTransactionsResponse =
          await this.ledgerPurchasesService.settlePurchaseTransaction(tx, purchase, assetFlows);
        ledgerTransactionIds = this.ledgerTransactionsService.getTransactionIdsFromBulkResponse(ledgerResponse);

        // create audit log
        await this.auditLogsService.create(tx, claims, AuditLogAction.PURCHASE_DELIVERED, now, purchase.id, {
          updatedAt: now,
          assetsDeliveredAt: data.assetsDeliveredAt,
        });

        purchase.assetFlows = assetFlows;

        // send email notifications
        if (claims.name !== 'init-console-script') {
          await this.privateHandleNotificationsAndEmails(
            now,
            NotificationEvent.PURCHASE_STATUS_UPDATED,
            purchase,
            'delivered',
          );
        }

        return purchase;
      });
    } catch (e) {
      if (ledgerTransactionIds.length > 0) {
        await this.ledgerTransactionsService.revertTransactionsAfterFailedApi(
          id,
          now,
          ledgerTransactionIds,
          `delivering Purchase ${id} assets`,
        );
      }
      throw e;
    }
  }

  // executing a purchase requires a contract document
  // also pending_delivery, pending_payment order determined
  // status stays executing as FE determines what the next "pending" step is
  // status stays executing throughout pendingPayment check and pendingDelivery check
  // and then auto changes to "settled" after isPaid and isDelivered are completed
  async execute(
    id: uuid,
    claims: UserClaims,
    data: AdminPurchaseExecuteRequestDTO,
    ignoreDocCheck: boolean = false,
  ): Promise<Purchase> {
    if (ignoreDocCheck && claims.name !== 'init-console-script') {
      throw new InternalServerErrorException('ignoreDocs cannot be true');
    }

    const now = new Date();
    const purchase = await this.em.transactional(async (tx) => {
      const purchase = await tx.findOneOrFail(Purchase, { id }, { populate: ['customerPortfolio.organization'] });
      if (purchase.status !== PurchaseStatus.BINDING) {
        throw new UnprocessableEntityException(`Purchase ${id} must have status ${PurchaseStatus.BINDING}`);
      }

      // Purchases require a document with type CONTRACT uploaded before moving to status EXECUTED
      if (!ignoreDocCheck) {
        const document = await tx.findOne(Document, {
          relatedUiKey: purchase.uiKey,
          type: [DocumentType.CONTRACT, DocumentType.PURCHASE_AGREEMENT],
          isUploaded: true,
        });
        if (!document) {
          throw new UnprocessableEntityException(
            `Purchase must have a document with type ${DocumentType.CONTRACT} or ${DocumentType.PURCHASE_AGREEMENT} uploaded before moving to status ${PurchaseStatus.EXECUTED}`,
          );
        }
      }

      purchase.updatableStatusOrder = data.updatableStatusOrder;
      purchase.status = PurchaseStatus.EXECUTED;
      purchase.updatedAt = now;

      await this.auditLogsService.create(tx, claims, AuditLogAction.PURCHASE_EXECUTED, now, id, {
        ...data,
      });
      await this.transactionStatusLogService.create(tx, PurchaseStatus.EXECUTED, now, TransactionType.PURCHASE, id);

      return purchase;
    });

    // send email notifications
    if (claims.name !== 'init-console-script') {
      await this.privateHandleNotificationsAndEmails(
        now,
        NotificationEvent.PURCHASE_STATUS_UPDATED,
        purchase,
        PurchaseStatus.EXECUTED,
      );
    }

    return purchase;
  }

  // only called in admin for now
  async findManyForAdmin(query: AdminPurchaseQueryDTO): Promise<InternalPurchaseQueryResponse> {
    const where: FilterQuery<Purchase> = {};

    if (query.statuses) {
      where.status = query.statuses;
    }
    if (query.assetId) {
      where.transactionDetails = { transactionAssets: { assetId: query.assetId } };
    }
    if (query.organizationId) {
      where.customerPortfolio = { organization: query.organizationId };
    }

    const count = query.includeTotalCount ? await this.em.count(Purchase, where) : undefined;
    const purchases = await this.em.find(Purchase, where, {
      limit: query.limit,
      offset: query.offset,
      orderBy: this.privateSetPurchaseOrderBys(query.orderBys),
      populate: ['transactionDetails'],
    });

    // populate customer portfolio
    if (query.includeRelations.includes(PurchaseRelations.CUSTOMER_PORTFOLIO)) {
      await this.em.populate(purchases, ['customerPortfolio.organization']);
    }
    // populate transaction assets
    if (query.includeRelations.includes(PurchaseRelations.ASSETS)) {
      await this.privatePopulateAssets(query.includeRelations, purchases);
    }

    return {
      data: purchases,
      page: {
        limit: query.limit,
        offset: query.offset,
        size: purchases.length,
        totalCount: count,
      },
    };
  }

  async findOne(id: uuid, includeRelations: PurchaseRelations[], claims?: UserClaims): Promise<Purchase> {
    const purchase = await this.em.findOneOrFail(Purchase, id, {
      populate: ['customerPortfolio.organization', 'transactionDetails'],
      connectionType: 'read',
    });

    // if claims, public endpoint so make sure user belongs to org of transaction
    if (claims) {
      await getPortalUserWithOrganizationFromClaims(this.em, claims, purchase.customerPortfolio.organization.id);
    }

    await this.privatePopulateAssets(includeRelations, [purchase]);
    return purchase;
  }

  // during the execute step, isPaid can be marked as true when prompted
  // status stays "executed" until both isPaid and isDelivered are marked true (then marked settled)
  async pay(id: uuid, claims: UserClaims): Promise<Purchase> {
    return await this.em.transactional(async (tx) => {
      const now = new Date();
      const user = await tx.findOneOrFail(User, { email: claims.email });
      const purchase = await tx.findOneOrFail(Purchase, { id }, { populate: ['customerPortfolio.organization'] });
      if (purchase.status !== PurchaseStatus.EXECUTED) {
        throw new UnprocessableEntityException(`Purchase ${id} must have status ${PurchaseStatus.EXECUTED}`);
      }
      if (purchase.isPaid) {
        throw new UnprocessableEntityException(`Purchase ${id} already paid`);
      }

      await this.auditLogsService.create(tx, claims, AuditLogAction.PURCHASE_PAID, now, id, {});

      purchase.updatedAt = now;
      purchase.isPaid = true;
      if (purchase.isDelivered && purchase.isPaid) {
        purchase.status = PurchaseStatus.SETTLED;
        purchase.dateFinished = now;
        await this.auditLogsService.create(tx, user, AuditLogAction.PURCHASE_SETTLED, now, purchase.id, {});
        await this.transactionStatusLogService.create(
          tx,
          PurchaseStatus.SETTLED,
          now,
          TransactionType.PURCHASE,
          purchase.id,
        );
      }

      // send email notifications
      if (claims.name !== 'init-console-script') {
        await this.privateHandleNotificationsAndEmails(
          now,
          NotificationEvent.PURCHASE_STATUS_UPDATED,
          purchase,
          'paid',
        );
      }

      return purchase;
    });
  }

  /* private */

  private async privateHandleNotificationsAndEmails(
    now: Date,
    event: NotificationEvent,
    purchase: Purchase,
    status: PurchaseStatus | 'paid' | 'delivered',
  ): Promise<void> {
    try {
      if (!purchase.customerPortfolio.organization) {
        throw new InternalServerErrorException(
          'purchase.customerPortfolio.organization required to send Purchase email',
        );
      }
      const assetFlows = await this.em.find(
        AssetFlow,
        { transaction: purchase.id },
        { populate: ['asset.details.portfolioDetails', 'asset.details.rrtDetails', 'asset.details.vintageDetails'] },
      );
      // handle internal event notifications
      const sendgridPayload: InternalTransactionBase = new InternalTransactionBase(
        await this.notificationsService.getNotificationUrlWithUuid(event, purchase.id, '', '?type=purchase'),
        purchase.customerPortfolio.organization.name,
        now,
        purchase.uiKey,
        status,
        purchase,
        assetFlows,
      );
      await this.notificationsService.handleEvent(this.em, event, sendgridPayload);

      // send customer email on creation
      if (status === PurchaseStatus.EXECUTED) {
        await this.sendgrid.createAndSendCustomerEmails(
          this.em,
          NotificationEvent.PURCHASE_CREATED,
          purchase.customerPortfolio.organization,
          environment.sendgrid.template.purchaseCreated,
          new PurchaseCreated(),
        );
      }
    } catch (e) {
      console.error(`error sending ${event} email for purchase ${purchase.uiKey} : ` + JSON.stringify(e));
    }
  }

  private async privatePopulateAssets(includeRelations: PurchaseRelations[], purchases: Purchase[]): Promise<void> {
    const flowRelations = [PurchaseRelations.ASSETS, PurchaseRelations.CUSTOMER_PORTFOLIO];

    if (includeRelations.some((s) => flowRelations.includes(s))) {
      const assetFlows: AssetFlow[] = await this.em.find(
        AssetFlow,
        { amount: { $ne: 0 }, transaction: purchases.map((m) => m.id) },
        {
          populate: [
            'asset.details.portfolioDetails',
            'asset.details.rrtDetails',
            'asset.details.vintageDetails',
            'destination',
            'source',
            'transaction',
          ],
        },
      );
      // const rcts = await this.em.find(Book, { id: assetFlows.map((m) => m.asset.id) });
      // const pvs = await this.em.find(
      //   ProjectVintage,
      //   { id: assetFlows.map((m) => m.asset.id) },
      //   { populate: ['project.country', 'project.projectType'] },
      // );

      for (const transaction of purchases) {
        transaction.assetFlows = assetFlows.filter((f) => f.transaction.id === transaction.id);
        // transaction.assetFlows.forEach((flow) => {
        //   if (flow.assetType === AssetType.RCT) {
        //     flow.detailedAsset = rcts.find((f) => f.id === flow.asset.id);
        //   } else if (flow.assetType === AssetType.REGISTRY_VINTAGE) {
        //     const found = pvs.find((f) => f.id === flow.asset.id);
        //     flow.detailedAsset = found;
        //   }
        // });
      }
    }
  }

  private privateSetPurchaseOrderBys(orderBys: BasePurchaseOrderBy[]): any {
    const x = [];
    for (const ob of orderBys) {
      if (ob.orderBy === PurchaseOrderByOptions.CREATED_AT) {
        x.push({ createdAt: OrderByDirectionSQL[ob.orderByDirection] });
      }
      if (ob.orderBy === PurchaseOrderByOptions.CUSTOMER_PORTFOLIO_ORGANIZATION_NAME) {
        x.push({ transactionDetails: { counterpartyName: OrderByDirectionSQL[ob.orderByDirection] } });
      }
      if (ob.orderBy === PurchaseOrderByOptions.DATE_FINISHED) {
        x.push({ dateFinished: OrderByDirectionSQL[ob.orderByDirection] });
      }
      if (ob.orderBy === PurchaseOrderByOptions.DATE_STARTED) {
        x.push({ dateStarted: OrderByDirectionSQL[ob.orderByDirection] });
      }
      if (ob.orderBy === PurchaseOrderByOptions.STATUS) {
        x.push({ status: OrderByDirectionSQL[ob.orderByDirection] });
      }
      if (ob.orderBy === PurchaseOrderByOptions.UI_KEY) {
        x.push({ uiKey: OrderByDirectionSQL[ob.orderByDirection] });
      }
      if (ob.orderBy === PurchaseOrderByOptions.UPDATED_AT) {
        x.push({ updatedAt: OrderByDirectionSQL[ob.orderByDirection] });
      }
    }

    return x;
  }
}
