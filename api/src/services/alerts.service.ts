/* third party */
import { EntityManager } from '@mikro-orm/postgresql';
import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { fromZonedTime } from 'date-fns-tz';
import Decimal from 'decimal.js';
/* rubicon */
import {
  AdminAlertPortfolioPolicyViolationResponse,
  AdminPortfolioViolationCountryConcentrationResponse,
  AdminPortfolioViolationProjectConcentrationResponse,
  AdminPortfolioViolationProjectVintageIneligibleResponse,
  AlertEvent,
  AuditLogAction,
  BookRelations,
  BookType,
  PortfolioPolicyViolationCategory,
  uuid,
} from '@rubiconcarbon/shared-types';
/* app */
import { UserClaims } from '@app/auth';
import { AdminAlertSubscriptionRequestDTO } from '@app/dtos/alert.dto';
import { AlertEntry, AlertSubscription, Book, Country, Project, ProjectVintage, User } from '@app/entities';
import { ProjectVintageGrouping } from '@app/entities/project-vintage-groupings';
import { groupBy } from '@app/helpers';
import { validateProjectVintageIsEligible } from '@app/helpers/project.helper';
import { InternalAllocationResponse, InternalGroupedAllocationsResponse } from '@app/interfaces/allocation.interface';
import { InternalBookResponse } from '@app/interfaces/book.interface';
import { AuditLogsService } from '@app/utility/audit-log';
import { InternalPopulateAllocationsService } from './allocations-populate.service';

@Injectable()
export class AlertsService {
  constructor(
    private em: EntityManager,
    private auditLogsService: AuditLogsService,
    private populateAllocationsService: InternalPopulateAllocationsService,
  ) {}

  // create alerts for stale price
  async alertPortfolioPolicyViolations(claims: UserClaims): Promise<void> {
    const now = new Date();

    await this.em.transactional(async (tx) => {
      const openAlerts = await tx.find(AlertEntry, { event: AlertEvent.PORTFOLIO_POLICY_VIOLATION, closedAt: null });

      // get and populate all current portfolios
      const enabledPublicPortfolios: InternalBookResponse[] = await tx.find(Book, {
        type: BookType.RCT_PUBLIC,
        isEnabled: true,
      });
      await this.populateAllocationsService.internalPopulateBooksAllocations(tx, enabledPublicPortfolios, undefined, [
        BookRelations.OWNER_ALLOCATIONS_NESTED,
      ]);

      // close all unnecessary alerts
      for (const openAlert of openAlerts) {
        if (!enabledPublicPortfolios.map((m) => m.id).includes(openAlert.relatedId)) {
          openAlert.closedAt = now;
        }
      }

      for (const portfolio of enabledPublicPortfolios) {
        let newViolationsCount = 0;
        const portfolioAllocations = portfolio.ownerAllocations?.allocations;
        if (!portfolio.ownerAllocations || !portfolioAllocations) {
          // should always exist even if it's empty array
          throw new InternalServerErrorException(
            'ownerAllocations and ownerAllocations.allocations should always exist here even if empty array',
          );
        }

        // get vintages by project
        const projectVintagesGroupings: ProjectVintageGrouping[] = await tx.find(
          ProjectVintageGrouping,
          { project_vintage_id: portfolioAllocations.map((m) => m.asset.id) },
          { populate: ['project.country'] },
        );

        // validate portfolio
        const countryViolations = this.privateValidateCountryConcentration(
          portfolio.ownerAllocations,
          portfolioAllocations,
          projectVintagesGroupings,
        );
        const projectViolations = this.privateValidateProjectConcentration(
          portfolio.ownerAllocations,
          portfolioAllocations,
          projectVintagesGroupings,
        );
        const projectVintageViolations = this.privateValidateProjectVintageEligibility(portfolioAllocations);

        // filter currently opened alerts
        const countryViolationOpenAlerts = openAlerts.filter(
          (f) =>
            f.relatedId === portfolio.id &&
            (f.details as AdminAlertPortfolioPolicyViolationResponse).violationCategory ===
              PortfolioPolicyViolationCategory.COUNTRY_CONCENTRATION,
        );
        const projectViolationOpenAlerts = openAlerts.filter(
          (f) =>
            f.relatedId === portfolio.id &&
            (f.details as AdminAlertPortfolioPolicyViolationResponse).violationCategory ===
              PortfolioPolicyViolationCategory.PROJECT_CONCENTRATION,
        );
        const projectVintageViolationOpenAlerts = openAlerts.filter(
          (f) =>
            f.relatedId === portfolio.id &&
            (f.details as AdminAlertPortfolioPolicyViolationResponse).violationCategory ===
              PortfolioPolicyViolationCategory.PROJECT_VINTAGE_INELIGIBLE,
        );

        // close all open countryViolation alerts
        countryViolationOpenAlerts.forEach((f) => (f.closedAt = now));
        // re-open and/or create new alerts per violation
        for (const violation of countryViolations) {
          const matchingCountryOpenAlert = countryViolationOpenAlerts.find(
            (f) =>
              (
                (f.details as AdminAlertPortfolioPolicyViolationResponse)
                  .violationDetails as AdminPortfolioViolationCountryConcentrationResponse
              ).country.alpha3 === violation.country.alpha3,
          );
          if (matchingCountryOpenAlert) {
            ((matchingCountryOpenAlert.details as AdminAlertPortfolioPolicyViolationResponse)
              .violationDetails as AdminPortfolioViolationCountryConcentrationResponse) = violation;
            matchingCountryOpenAlert.lastCheckedAt = now;
            matchingCountryOpenAlert.closedAt = undefined;
          } else {
            tx.create(AlertEntry, {
              relatedId: portfolio.id,
              openedAt: now,
              lastCheckedAt: now,
              event: AlertEvent.PORTFOLIO_POLICY_VIOLATION,
              details: {
                portfolio: {
                  id: portfolio.id,
                  name: portfolio.name,
                  isEnabled: portfolio.isEnabled,
                },
                violationCategory: PortfolioPolicyViolationCategory.COUNTRY_CONCENTRATION,
                violationDetails: violation,
              } as AdminAlertPortfolioPolicyViolationResponse,
            });
            newViolationsCount++;
          }
        }

        // close all open projectViolation alerts
        projectViolationOpenAlerts.forEach((f) => (f.closedAt = now));
        // re-open and/or create new alerts per violation
        for (const violation of projectViolations) {
          const matchingProjectOpenAlert = projectViolationOpenAlerts.find(
            (f) =>
              (
                (f.details as AdminAlertPortfolioPolicyViolationResponse)
                  .violationDetails as AdminPortfolioViolationProjectConcentrationResponse
              ).project.id === violation.project.id,
          );
          if (matchingProjectOpenAlert) {
            ((matchingProjectOpenAlert.details as AdminAlertPortfolioPolicyViolationResponse)
              .violationDetails as AdminPortfolioViolationProjectConcentrationResponse) = violation;
            matchingProjectOpenAlert.lastCheckedAt = now;
            matchingProjectOpenAlert.closedAt = undefined;
          } else {
            tx.create(AlertEntry, {
              relatedId: portfolio.id,
              openedAt: now,
              lastCheckedAt: now,
              event: AlertEvent.PORTFOLIO_POLICY_VIOLATION,
              details: {
                portfolio: {
                  id: portfolio.id,
                  name: portfolio.name,
                  isEnabled: portfolio.isEnabled,
                },
                violationCategory: PortfolioPolicyViolationCategory.PROJECT_CONCENTRATION,
                violationDetails: violation,
              } as AdminAlertPortfolioPolicyViolationResponse,
            });
            newViolationsCount++;
          }
        }

        // close all open projectVintageViolation alerts
        projectVintageViolationOpenAlerts.forEach((f) => (f.closedAt = now));
        // re-open and/or create new alerts per violation
        for (const violation of projectVintageViolations) {
          const matchingOpenAlert = projectVintageViolationOpenAlerts.find(
            (f) =>
              (
                (f.details as AdminAlertPortfolioPolicyViolationResponse)
                  .violationDetails as AdminPortfolioViolationProjectVintageIneligibleResponse
              ).projectVintage.id === violation.projectVintage.id,
          );
          if (matchingOpenAlert) {
            ((matchingOpenAlert.details as AdminAlertPortfolioPolicyViolationResponse)
              .violationDetails as AdminPortfolioViolationProjectVintageIneligibleResponse) = violation;
            // todo : also make sure the expected percentage didn't get updated
            matchingOpenAlert.lastCheckedAt = now;
            matchingOpenAlert.closedAt = undefined;
          } else {
            tx.create(AlertEntry, {
              relatedId: portfolio.id,
              openedAt: now,
              lastCheckedAt: now,
              event: AlertEvent.PORTFOLIO_POLICY_VIOLATION,
              details: {
                portfolio: {
                  id: portfolio.id,
                  name: portfolio.name,
                  isEnabled: portfolio.isEnabled,
                },
                violationCategory: PortfolioPolicyViolationCategory.PROJECT_VINTAGE_INELIGIBLE,
                violationDetails: violation,
              } as AdminAlertPortfolioPolicyViolationResponse,
            });
            newViolationsCount++;
          }
        }

        const closedViolations = openAlerts.filter((f) => f.closedAt != null).length;
        if (closedViolations > 0) {
          await this.auditLogsService.create(tx, claims, AuditLogAction.ALERTS_CLOSED, now, portfolio.id, {
            alertEvent: AlertEvent.PORTFOLIO_POLICY_VIOLATION,
            closedViolationsCount: openAlerts.filter((f) => f.closedAt != null).length,
          });
        }

        await this.auditLogsService.create(tx, claims, AuditLogAction.ALERTS_CREATED, now, portfolio.id, {
          alertEvent: AlertEvent.PORTFOLIO_POLICY_VIOLATION,
          newViolationsCount,
          totalViolationsCount: projectVintageViolations.length + projectViolations.length + countryViolations.length,
        });
      }
    });
  }

  // create alerts for stale price
  async alertPortfolioStalePrice(claims: UserClaims): Promise<void> {
    // get yesterday's date 8am Eastern
    const now = new Date();
    const yesterday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 8, 0, 0);
    const yesterdayToUtc = fromZonedTime(yesterday, 'America/New_York');

    await this.em.transactional(async (tx) => {
      const openAlerts = await tx.find(AlertEntry, { event: AlertEvent.PORTFOLIO_STALE_PRICE, closedAt: null });

      const stalePortfolios = await tx.find(Book, {
        type: [BookType.RCT_PUBLIC, BookType.RRT_PUBLIC],
        isEnabled: true,
        priceUpdatedAt: { $lt: yesterdayToUtc },
      });

      for (const openAlert of openAlerts) {
        // price is no longer stale, close alert
        if (!stalePortfolios.map((m) => m.id).includes(openAlert.relatedId)) {
          await this.closePortfolioStalePrice(claims, tx, now, openAlert.relatedId);
        }
      }

      for (const portfolio of stalePortfolios) {
        // if openAlerts does not already include stale portfolio, create new entry
        if (!openAlerts.map((m) => m.relatedId).includes(portfolio.id)) {
          tx.create(AlertEntry, {
            relatedId: portfolio.id,
            openedAt: now,
            lastCheckedAt: now,
            event: AlertEvent.PORTFOLIO_STALE_PRICE,
            details: {
              portfolio: {
                id: portfolio.id,
                name: portfolio.name,
                isEnabled: portfolio.isEnabled,
              },
              lastUpdatedAt: portfolio.priceUpdatedAt as Date,
              purchasePrice: portfolio.purchasePrice,
              purchasePriceWithBuffer: portfolio.purchasePriceWithBuffer,
            },
          });
          await this.auditLogsService.create(tx, claims, AuditLogAction.ALERTS_CREATED, now, portfolio.id, {
            alertEvent: AlertEvent.PORTFOLIO_STALE_PRICE,
            lastUpdatedAt: portfolio.priceUpdatedAt,
            purchasePrice: portfolio.purchasePrice,
            purchasePriceWithBuffer: portfolio.purchasePriceWithBuffer,
          });
        }
      }
    });
  }

  async closeAllPortfolioPolicyViolations(
    claims: UserClaims,
    tx: EntityManager,
    now: Date,
    portfolioId: uuid,
    notes?: string,
  ): Promise<void> {
    const openAlerts = await tx.find(AlertEntry, {
      event: AlertEvent.PORTFOLIO_POLICY_VIOLATION,
      closedAt: null,
      relatedId: portfolioId,
    });

    openAlerts.forEach((alert) => {
      alert.closedAt = now;
    });
    await this.auditLogsService.create(tx, claims, AuditLogAction.ALERTS_CLOSED, now, portfolioId, {
      alertEvent: AlertEvent.PORTFOLIO_POLICY_VIOLATION,
      closedViolationsCount: openAlerts.length,
      notes,
    });
  }

  async closePortfolioStalePrice(
    claims: UserClaims,
    tx: EntityManager,
    now: Date,
    portfolioId: uuid,
    notes?: string,
  ): Promise<void> {
    const openAlert = await tx.findOne(AlertEntry, {
      event: AlertEvent.PORTFOLIO_STALE_PRICE,
      closedAt: null,
      relatedId: portfolioId,
    });
    if (openAlert) {
      openAlert.closedAt = now;
      await this.auditLogsService.create(tx, claims, AuditLogAction.ALERTS_CLOSED, now, portfolioId, {
        alertEvent: AlertEvent.PORTFOLIO_STALE_PRICE,
        notes,
      });
    }
  }

  async findManyAlerts(claims: UserClaims): Promise<AlertEntry[]> {
    const alertSubscriptions = await this.em.find(AlertSubscription, { user: claims.id, deletedAt: null });
    return await this.em.find(AlertEntry, { event: alertSubscriptions.map((m) => m.event), closedAt: null });
  }

  async findManySubscriptions(claims: UserClaims): Promise<AlertSubscription[]> {
    const user = await this.em.findOneOrFail(User, { id: claims.id });
    return await this.em.find(AlertSubscription, { user, deletedAt: null });
  }

  async setAlertSubscriptions(
    claims: UserClaims,
    data: AdminAlertSubscriptionRequestDTO,
  ): Promise<AlertSubscription[]> {
    const now = new Date();

    await this.em.transactional(async (tx) => {
      const user = await tx.findOneOrFail(User, { id: claims.id });
      const subscriptions = await tx.find(AlertSubscription, { user });

      // delete all subscriptions not in data
      const deletedSubscriptions = subscriptions.filter(
        (f) => !data.subscribedEvents.includes(f.event) && f.deletedAt == null,
      );
      deletedSubscriptions.forEach((f) => {
        f.deletedAt = now;
      });

      // existing subscriptions
      const existingSubscriptions = subscriptions.filter((subscription) =>
        data.subscribedEvents.includes(subscription.event),
      );
      existingSubscriptions.forEach((f) => {
        f.deletedAt = undefined;
      });

      // add new subscriptions
      const newSubscriptions = data.subscribedEvents.filter(
        (newEvent) => !subscriptions.map((existingSubscription) => existingSubscription.event).includes(newEvent),
      );

      newSubscriptions.forEach((f) => {
        tx.create(AlertSubscription, {
          createdAt: now,
          updatedAt: now,
          event: f,
          user,
        });
      });

      await this.auditLogsService.create(tx, claims, AuditLogAction.ALERT_SUBSCRIPTION_UPDATED, now, user.id, {
        ...data,
      });
    });

    return await this.em.find(AlertSubscription, { user: claims.id, deletedAt: null });
  }

  /* private */

  private privateValidateCountryConcentration(
    totalAllocations: InternalGroupedAllocationsResponse,
    portfolioAllocations: InternalAllocationResponse[],
    groupings: ProjectVintageGrouping[],
  ): AdminPortfolioViolationCountryConcentrationResponse[] {
    const violatingCountries: AdminPortfolioViolationCountryConcentrationResponse[] = [];
    const byCountry: Map<Country | undefined, ProjectVintageGrouping[]> = groupBy(groupings, (x) => {
      return x.project.country;
    });

    for (const country of [...byCountry.keys()]) {
      // country is currently nullable though it shouldn't be. if it is null just skip
      if (!country) {
        continue;
      }
      const countryHoldings = portfolioAllocations.filter((f) =>
        (byCountry.get(country) || []).map((m) => m.project_vintage_id).includes(f.asset.id),
      );
      // the calculate the holdings available based on amountAllocated (as requested by product, but should confirm with FE)
      const countryHoldingsAmount = Decimal.sum(...countryHoldings.map((m) => m.amountAllocated), 0);
      const percentage = countryHoldingsAmount.div(totalAllocations.totalAmountAllocated);
      if (percentage.gt(0.35)) {
        violatingCountries.push({
          country: {
            alpha2: country.alpha2,
            alpha3: country.alpha3,
            name: country.name,
            region: country.region,
            subRegion: country.subRegion,
          },
          actualPercentage: percentage,
          expectedMaxPercentage: new Decimal(0.35),
        });
      }
    }

    return violatingCountries;
  }

  private privateValidateProjectConcentration(
    totalAllocations: InternalGroupedAllocationsResponse,
    portfolioAllocations: InternalAllocationResponse[],
    groupings: ProjectVintageGrouping[],
  ): AdminPortfolioViolationProjectConcentrationResponse[] {
    const violatingProjects: AdminPortfolioViolationProjectConcentrationResponse[] = [];
    const byProject: Map<Project, ProjectVintageGrouping[]> = groupBy(groupings, (x) => {
      return x.project;
    });

    for (const project of [...byProject.keys()]) {
      const countryHoldings = portfolioAllocations.filter((f) =>
        (byProject.get(project) || []).map((m) => m.project_vintage_id).includes(f.asset.id),
      );
      // the calculate the holdings available based on amountAllocated (as requested by product, but should confirm with FE)
      const projectHoldingsAmount = Decimal.sum(...countryHoldings.map((m) => m.amountAllocated), 0);
      const percentage = projectHoldingsAmount.div(totalAllocations.totalAmountAllocated);

      if (percentage.gt(0.25)) {
        violatingProjects.push({
          project: {
            id: project.id,
            name: project.name,
            registryProjectId: project.registryProjectId,
          },
          actualPercentage: percentage,
          expectedMaxPercentage: new Decimal(0.25),
        });
      }
    }

    return violatingProjects;
  }

  private privateValidateProjectVintageEligibility(
    portfolioAllocations: InternalAllocationResponse[],
  ): AdminPortfolioViolationProjectVintageIneligibleResponse[] {
    const violatingProjects: AdminPortfolioViolationProjectVintageIneligibleResponse[] = [];

    for (const allocation of portfolioAllocations) {
      const projectVintage = allocation.detailedAsset as ProjectVintage | undefined;
      if (!projectVintage) {
        // should never be nullable in this situation
        console.error(`projectVintage in allocation is null ${JSON.stringify(allocation)}`);
        throw new InternalServerErrorException('projectVintage for allocation is null');
      }

      const ineligibleReasons = validateProjectVintageIsEligible(projectVintage);
      if (ineligibleReasons.length > 0) {
        violatingProjects.push({
          projectVintage: {
            id: projectVintage.id,
            name: projectVintage.name(),
            project: {
              id: projectVintage.project.id,
              name: projectVintage.project.name,
              registryProjectId: projectVintage.project.registryProjectId,
            },
          },
          ineligibleReasons,
          isRctStandard: projectVintage.project.rctStandard,
          isScienceTeamApproved: projectVintage.project.isScienceTeamApproved,
          isSuspended: projectVintage.project.suspended,
          riskBufferPercentage: projectVintage.riskBufferPercentage,
        });
      }
    }

    return violatingProjects;
  }
}
