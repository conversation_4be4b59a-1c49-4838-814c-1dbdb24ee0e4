/* third party */
import { EntityManager, Knex } from '@mikro-orm/postgresql';
import { Injectable, InternalServerErrorException } from '@nestjs/common';
/* rubicon */
import {
  AdminAssetPurchaseRequest,
  AllAdminBookTypes,
  AllAssetTypes,
  AssetType,
  BaseAssetRetirementRequest,
  BookAction,
  BookType,
  uuid,
} from '@rubiconcarbon/shared-types';
/* env */
import { environment } from '@env/environment';
/* app */
import Decimal from 'decimal.js';
import { Asset, Book, CustomerPortfolio } from '@app/entities';
import {
  InternalAllocationResponse,
  InternalHolding,
  InternalHoldingsQuery,
} from '@app/interfaces/allocation.interface';
import { AssetDetail } from '@app/entities/asset-detail.entity';

@Injectable()
export class AssetsService {
  constructor(private readonly em: EntityManager) {}

  async internalGetAssetAllocation(
    tx: EntityManager,
    ownerId: uuid,
    assetId: uuid,
  ): Promise<InternalAllocationResponse> {
    // get the owner and the asset
    const owner = await tx.findOneOrFail(Book, ownerId);
    const asset = await tx.findOneOrFail(Asset, assetId);

    // get result from asset_composition_v2 for the owner_id and asset_id
    // only one  owner_id and asset_id entry should exist for level 1
    const assetHoldings: InternalHolding[] = await this.privateGetAssetHoldings(tx.getKnex(), {
      assetIds: [asset.id],
      ownerIds: [owner.id],
      includePrices: false,
    });

    if (assetHoldings.length > 1) {
      throw new InternalServerErrorException(`expected to get or or 1 result(s) but got ${assetHoldings.length}`);
    }
    // if no entries found, just return 0
    if (assetHoldings.length === 0) {
      return {
        amountAllocated: 0,
        amountAvailable: 0,
        amountCustomerTransferredOutflow: 0,
        amountPendingBuy: 0,
        amountPendingPurchase: 0,
        amountPendingRetirement: 0,
        amountPendingSell: 0,
        amountPendingCustomerTransferOutflow: 0,
        amountRetired: 0,
        amountSold: 0,
        asset,
        owner: owner,
      };
    }
    return {
      amountAllocated: assetHoldings[0].holdings,
      amountAvailable: assetHoldings[0].holdings - assetHoldings[0].pending_outflow,
      amountCustomerTransferredOutflow: assetHoldings[0].holdings_customer_transferred_outflow,
      amountPendingBuy: assetHoldings[0].pending_buy_inflow,
      amountPendingCustomerTransferOutflow: assetHoldings[0].pending_customer_transfer_outflow,
      amountPendingPurchase:
        owner.type === BookType.PORTFOLIO_CUSTOMER
          ? assetHoldings[0].pending_purchase_inflow
          : assetHoldings[0].pending_purchase_outflow,
      amountPendingRetirement: assetHoldings[0].pending_retirement_outflow,
      amountPendingSell: assetHoldings[0].pending_sell_outflow,
      amountRetired: assetHoldings[0].holdings_retired_outflow,
      amountSold: assetHoldings[0].holdings_sold_outflow,
      asset,
      owner: owner,
    };
  }

  // this is used for project/project-type/project-vintage response
  async internalGetAssetHoldings(knex: Knex, query: InternalHoldingsQuery): Promise<InternalHolding[]> {
    if (!query.assetIds) {
      throw new InternalServerErrorException(`assetIds must be passed in to get asset holdings`);
    }
    return await this.privateGetHoldings(knex, query);
  }

  // this is used internally so knex gets passed around
  async internalGetOwnerHoldings(knex: Knex, query: InternalHoldingsQuery): Promise<InternalHolding[]> {
    if (!query.ownerIds) {
      throw new InternalServerErrorException(`ownerIds must be passed in to get owner holdings`);
    }
    return await this.privateGetHoldings(knex, query);
  }

  async internalGetPurchaseHoldings(knex: Knex, assets: AdminAssetPurchaseRequest[]): Promise<InternalHolding[]> {
    // get all asset allocations
    const query: InternalHoldingsQuery = { includePrices: false };
    query.assetIds = assets.map((m) => m.assetId);
    query.ownerIds = assets.map((m) => m.sourceId);
    return await this.privateGetHoldings(knex, query);
  }

  async internalGetRetirementHoldings(
    knex: Knex,
    customerPortfolioBookId: uuid,
    assets: BaseAssetRetirementRequest[],
  ): Promise<InternalHolding[]> {
    // get all asset allocations
    const query: InternalHoldingsQuery = { includePrices: false };
    query.assetIds = assets.map((m) => m.assetId);
    query.ownerIds = [customerPortfolioBookId];
    return await this.privateGetHoldings(knex, query);
  }

  async internalGetPurchasableAssets(
    tx: EntityManager,
    purchaser: CustomerPortfolio,
    assetTypes: AssetType[] = AllAssetTypes,
  ): Promise<InternalHolding[]> {
    // if the purchaser is not allowed to purchase for some reason, then return no assets
    if (!purchaser.book.allowedActions.includes(BookAction.PURCHASE) || !purchaser.book.isEnabled) {
      return [];
    }

    // get assets purchasable by purchaser
    const purchasableAssets = await tx.find(
      AssetDetail,
      {
        type: assetTypes,
        $or: [{ isPublic: true }, { portfolioDetails: { organization: purchaser.organization.id } }],
      },
      { populate: ['portfolioDetails', 'rrtDetails', 'vintageDetails'] },
    );

    // get books that can be purchased from (RCTs now belon on PORTFOLIO_DEFAULT)
    const purchaseFromBookTypes = [
      BookType.AGED_DEFAULT,
      BookType.COMPLIANCE_DEFAULT,
      BookType.OPPORTUNISTIC_DEFAULT,
      BookType.PORTFOLIO_DEFAULT,
      BookType.REHABILITATION_DEFAULT,
    ];

    const purchasableFrom = await tx.find(Book, { type: purchaseFromBookTypes, isEnabled: true });

    // get asset allocations
    const query: InternalHoldingsQuery = { includePrices: false };
    query.assetIds = purchasableAssets.map((m) => m.id);
    query.ownerIds = purchasableFrom.map((m) => m.id);
    query.hasAmountAllocated = true;
    return await this.privateGetAssetHoldings(tx.getKnex(), query);
  }

  /* private */

  private async privateGetAssetHoldings(knex: Knex, query: InternalHoldingsQuery): Promise<InternalHolding[]> {
    // get the holdings (settled and pending) for an asset/owner pairing
    // ignoring reserves and transfers since those don't have pending
    const assetHoldings: InternalHolding[] = await knex
      .select([
        'ah.owner_id as owner_id',
        'b.type as owner_type',
        'ah.asset_id as asset_id',
        'a.asset_type as asset_type',
        'ah.holdings as holdings',
        'ah.holdings_customer_transferred_outflow as holdings_customer_transferred_outflow',
        'ah.holdings_retired_outflow as holdings_retired_outflow',
        'ah.holdings_sold_outflow as holdings_sold_outflow',
        'ah.pending_buy_inflow as pending_buy_inflow',
        'ah.pending_customer_transfer_outflow as pending_customer_transfer_outflow',
        'ah.pending_purchase_inflow as pending_purchase_inflow',
        'ah.pending_purchase_outflow as pending_purchase_outflow',
        'ah.pending_retirement_outflow as pending_retirement_outflow',
        'ah.pending_sell_outflow as pending_sell_outflow',
        'ah.pending_inflow as pending_inflow',
        'ah.pending_outflow as pending_outflow',
        knex.raw(`ah.holdings - ah.pending_outflow as amount_available`),
      ])
      .from(`${environment.db.schema.rubicon}.asset_holdings_v2 as ah`)
      .innerJoin(`${environment.db.schema.rubicon}.assets as a`, 'a.id', 'ah.asset_id')
      .innerJoin(`${environment.db.schema.rubicon}.books as b`, 'b.id', 'ah.owner_id')
      .where((builder) => {
        // only get holdings that have amounts != 0
        builder.whereRaw(`(ah.holdings != 0 or ah.pending_inflow != 0 or ah.pending_outflow != 0)`);
        if (query.ownerIds) {
          builder.whereIn('ah.owner_id', query.ownerIds);
        }
        if (query.ownerTypes) {
          builder.whereIn('b.type', query.ownerTypes);
        } else {
          builder.whereIn('b.type', AllAdminBookTypes);
        }
        if (query.assetIds) {
          builder.whereIn('ah.asset_id', query.assetIds);
        }
        if (query.assetTypes) {
          builder.whereIn('a.asset_type', query.assetTypes);
        }
        if (query.hasAmountAllocated) {
          builder.where('ah.holdings', '>', 0);
        }
      });

    // note: these values are bigints in the db and thus convert to strings.
    // if we ever get an actual bigint, this may be wrong
    assetHoldings.forEach((f) => {
      f.holdings = new Decimal(f.holdings).toNumber();
      f.holdings_customer_transferred_outflow = new Decimal(f.holdings_customer_transferred_outflow).toNumber();
      f.holdings_retired_outflow = new Decimal(f.holdings_retired_outflow).toNumber();
      f.holdings_sold_outflow = new Decimal(f.holdings_sold_outflow).toNumber();
      f.pending_buy_inflow = new Decimal(f.pending_buy_inflow).toNumber();
      f.pending_customer_transfer_outflow = new Decimal(f.pending_customer_transfer_outflow).toNumber();
      f.pending_purchase_inflow = new Decimal(f.pending_purchase_inflow).toNumber();
      f.pending_purchase_outflow = new Decimal(f.pending_purchase_outflow).toNumber();
      f.pending_retirement_outflow = new Decimal(f.pending_retirement_outflow).toNumber();
      f.pending_sell_outflow = new Decimal(f.pending_sell_outflow).toNumber();
      f.pending_inflow = new Decimal(f.pending_inflow).toNumber();
      f.pending_outflow = new Decimal(f.pending_outflow).toNumber();
      f.amount_available = new Decimal(f.amount_available).toNumber();
    });

    return assetHoldings;
  }

  private async privateGetAssetHoldingsWithPrice(knex: Knex, query: InternalHoldingsQuery): Promise<InternalHolding[]> {
    // get the holdings (settled and pending) for an asset/owner pairing
    // ignoring reserves and transfers since those don't have pending
    const assetHoldings: InternalHolding[] = await knex
      .select([
        'ah.owner_id as owner_id',
        'b.type as owner_type',
        'ah.asset_id as asset_id',
        'a.asset_type as asset_type',
        'ah.holdings as holdings',
        'ah.holdings_customer_transferred_outflow as holdings_customer_transferred_outflow',
        'ah.holdings_retired_outflow as holdings_retired_outflow',
        'ah.holdings_sold_outflow as holdings_sold_outflow',
        'ah.pending_buy_inflow as pending_buy_inflow',
        'ah.pending_customer_transfer_outflow as pending_customer_transfer_outflow',
        'ah.pending_purchase_inflow as pending_purchase_inflow',
        'ah.pending_purchase_outflow as pending_purchase_outflow',
        'ah.pending_retirement_outflow as pending_retirement_outflow',
        'ah.pending_sell_outflow as pending_sell_outflow',
        'ah.pending_inflow as pending_inflow',
        'ah.pending_outflow as pending_outflow',
        knex.raw(`ah.holdings - ah.pending_outflow as amount_available`),
        'pvp.price as price',
        'pvp.average_cost_basis',
      ])
      .from(`${environment.db.schema.rubicon}.asset_holdings_v2 as ah`)
      .innerJoin(`${environment.db.schema.rubicon}.assets as a`, 'a.id', 'ah.asset_id')
      .innerJoin(`${environment.db.schema.rubicon}.books as b`, 'b.id', 'ah.owner_id')
      .leftJoin(`${environment.db.schema.rubicon}.project_vintage_prices_v2 as pvp`, 'pvp.vintage_id', 'ah.asset_id')
      .where((builder) => {
        // only get holdings that have amounts != 0
        builder.whereRaw(`(ah.holdings != 0 or ah.pending_inflow != 0 or ah.pending_outflow != 0)`);
        if (query.ownerIds) {
          builder.whereIn('ah.owner_id', query.ownerIds);
        }
        if (query.ownerTypes) {
          builder.whereIn('b.type', query.ownerTypes);
        } else {
          builder.whereIn('b.type', AllAdminBookTypes);
        }
        if (query.assetIds) {
          builder.whereIn('ah.asset_id', query.assetIds);
        }
        if (query.assetTypes) {
          builder.whereIn('a.asset_type', query.assetTypes);
        }
        if (query.hasAmountAllocated) {
          builder.where('ah.holdings', '>', 0);
        }
      });

    // note: these values are bigints in the db and thus convert to strings.
    // if we ever get an actual bigint, this may be wrong
    assetHoldings.forEach((f) => {
      f.holdings = new Decimal(f.holdings).toNumber();
      f.holdings_customer_transferred_outflow = new Decimal(f.holdings_customer_transferred_outflow).toNumber();
      f.holdings_retired_outflow = new Decimal(f.holdings_retired_outflow).toNumber();
      f.holdings_sold_outflow = new Decimal(f.holdings_sold_outflow).toNumber();
      f.pending_buy_inflow = new Decimal(f.pending_buy_inflow).toNumber();
      f.pending_customer_transfer_outflow = new Decimal(f.pending_customer_transfer_outflow).toNumber();
      f.pending_purchase_inflow = new Decimal(f.pending_purchase_inflow).toNumber();
      f.pending_purchase_outflow = new Decimal(f.pending_purchase_outflow).toNumber();
      f.pending_retirement_outflow = new Decimal(f.pending_retirement_outflow).toNumber();
      f.pending_sell_outflow = new Decimal(f.pending_sell_outflow).toNumber();
      f.pending_inflow = new Decimal(f.pending_inflow).toNumber();
      f.pending_outflow = new Decimal(f.pending_outflow).toNumber();
      f.amount_available = new Decimal(f.amount_available).toNumber();
    });

    return assetHoldings;
  }

  private async privateGetHoldings(knex: Knex, query: InternalHoldingsQuery): Promise<InternalHolding[]> {
    // get the holdings (settled and pending) for an asset/owner pairing
    // ignoring reserves and transfers since those don't have pending
    if (query.includePrices) {
      return await this.privateGetAssetHoldingsWithPrice(knex, query);
    } else {
      return await this.privateGetAssetHoldings(knex, query);
    }
  }
}
