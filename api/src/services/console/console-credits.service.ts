/* third party */
import { EntityManager, UniqueConstraintViolationException } from '@mikro-orm/postgresql';
import { Command, Console } from 'nestjs-console';
import Decimal from 'decimal.js';
/* rubicon */
import {
  AdminPurchaseRequest,
  AssetFlowStatus,
  AssetType,
  BookType,
  CounterpartyRole,
  PurchaseFlowType,
  RetirementType,
  TradeType,
  TransactionSubtype,
  TransactionType,
  uuid,
} from '@rubiconcarbon/shared-types';
/* env */
import { environment } from '@env/environment';
/* app */
import { Asset, Book, CustomerPortfolio, Organization, ProjectVintage, Retirement, Trade, User } from '@app/entities';
import { TransfersService } from '../transfers.service';
import { AdminAssetTradeRequestDTO, AdminAssetTransferRequestDTO } from '@app/dtos/asset.dto';
import { AdminTransferRequestDTO } from '@app/dtos/transfer.dto';
import { UserClaims } from '@app/auth';
import { TradesService } from '../trades.service';
import { creditInflowSeederValues } from '@app/../seeders/values/trades.values';
import { bookVintageSeederValues } from '@app/../seeders/values/portfolio-composition.values';
import { ForbiddenException, UnprocessableEntityException } from '@nestjs/common';
import { copyToArrayFromCSV, groupBy } from '@app/helpers';
import { PurchasesService } from '../purchases.service';
import { InternalRetirementTransaction } from '@app/interfaces/retirement.interface';
import { RetirementsService } from '../retirements.service';
import { OrganizationsService } from '../organizations.service';
import { AssetsService } from '../assets.service';

interface AssetFlowInterface {
  id: uuid;
  created_at: Date;
  updated_at: Date;
  asset_id: uuid;
  asset_type: AssetType;
  status: AssetFlowStatus;
  amount: number;
  source_id: uuid;
  destination_id: uuid;
  transaction_id: uuid;
  settled_at?: Date;
  raw_price: Decimal;
  service_fee?: Decimal;
  other_fee?: Decimal;
  transaction_type: TransactionType;
  transaction_subtype?: TransactionSubtype;
}

@Console({
  command: 'credits',
  description: 'Setup environment with vintage and rct credits',
})
export class ConsoleCreditsService {
  constructor(
    private assetsService: AssetsService,
    private em: EntityManager,
    private organizationsService: OrganizationsService,
    private purchasesService: PurchasesService,
    private retirementsService: RetirementsService,
    private tradesService: TradesService,
    private transfersService: TransfersService,
  ) {}

  originalTradeVintages = creditInflowSeederValues;
  originalBookVintages = bookVintageSeederValues;

  @Command({
    command: 'create-settle-x-rct-purchase <customerPortfolioId> <amount>',
    description: 'create and settle purchase of x amount of each rct to customerPortfolioId',
  })
  async createXRctPurchases(customerPortfolioId: uuid, amount: number): Promise<void> {
    const now = new Date();
    if (environment.env === 'production') {
      throw new ForbiddenException(`this cannot be run in prod`);
    }
    const em = this.em.fork();
    const knex = em.getKnex();
    const claims = await this.privateGetDefaultAdminClaims(em);

    const customerPortfolio = await em.findOneOrFail(CustomerPortfolio, customerPortfolioId, {
      populate: ['organization'],
    });

    // get rct assets and balances
    const balances: { owner_id: uuid; asset_id: uuid; amount: number; registry_project_id: string; name: string }[] =
      await knex
        .raw(
          `select ac.owner_id, ac.component_id as asset_id, ac.holdings as amount
          from ${environment.db.schema.rubicon}.asset_composition_v2 as ac
          where ac.owner_id = '${environment.rubicon.books.portfolio.default}' and ac.asset_type = 'rct' and ac.holdings != 0 and ac.level = 1;`,
        )
        .then((r) => r.rows);

    let books = await em.find(Book, { id: balances.map((m) => m.asset_id), assetType: AssetType.RCT });
    books = books.filter((f) => !f.organization || f.organization.id === customerPortfolio.organization!.id);

    const request = {
      assetType: AssetType.RCT,
      flowType: PurchaseFlowType.PURCHASE_AND_HOLD,
      memo: 'initial setup',
      paymentDueDate: new Date(now.getTime() + 100000),
      organizationId: customerPortfolio.organization.id,
      needsRiskAdjustment: false,
      assets: books.map((b) => {
        const assetBalance = balances.find((f) => f.asset_id === b.id);
        if (!assetBalance) {
          throw new UnprocessableEntityException(`expected to find asset allocation for asset ${b.name}`);
        }
        return {
          amount: Decimal.min(new Decimal(amount).toNumber(), assetBalance?.amount).toNumber(),
          assetId: b.id,
          sourceId: uuid(environment.rubicon.books.portfolio.default),
          rawPrice: new Decimal(b.purchasePrice || 13.13).mul(amount),
          otherFee: new Decimal(0.13).mul(amount),
          serviceFee:
            b.purchasePriceWithBuffer && b.purchasePrice
              ? new Decimal(b.purchasePriceWithBuffer).sub(b.purchasePrice).mul(amount)
              : new Decimal(1.3).mul(amount),
        };
      }),
    };
    await this.privateCreateAndSettlePurchase(claims, now, request);
  }

  @Command({
    command: 'populate-original-compliance-credits',
    description: 'populate original seeder compliance credits into compliance default book',
  })
  async populateOriginalComplianceCredits(): Promise<void> {
    if (environment.env === 'production') {
      throw new ForbiddenException(`this cannot be run in prod`);
    }
    const em = this.em.fork();
    const claims = await this.privateGetDefaultAdminClaims(em);
    const knex = em.getKnex();

    const complianceVintages: { id: uuid; registry_project_id: string; name: string }[] = await knex
      .raw(
        `select pv.id, p.registry_project_id, pv.label as name from ${environment.db.schema.rubicon}.project_vintages as pv inner join ${environment.db.schema.rubicon}.projects as p on p.id = pv.project_id inner join ${environment.db.schema.rubicon}.project_types as pt on pt.id = p.project_type_id where pt.category = 'Compliance Permit'`,
      )
      .then((r) => r.rows);

    const complianceSeeders = this.originalTradeVintages.filter((li) =>
      complianceVintages.find((vintage) => vintage.name === li[6] && vintage.registry_project_id === li[5]),
    );

    for (const li of complianceSeeders) {
      const pv = complianceVintages.find((vintage) => vintage.name === li[6] && vintage.registry_project_id === li[5]);
      if (!pv) {
        continue;
      }
      await this.privateCreateAndSettleTrade(em, claims, {
        bookId: uuid(environment.rubicon.books.compliance),
        projectVintages: [{ assetId: uuid(pv.id), amount: li[3] }],
        type: TradeType.BUY,
        rawPrice: new Decimal(li[4]),
      });
    }

    // eslint-disable-next-line no-console
    console.log('this.tradeVintages : ' + this.originalTradeVintages.length);
    // eslint-disable-next-line no-console
    console.log('complianceSeeders: ' + complianceSeeders.length);
    this.originalTradeVintages = this.originalTradeVintages.filter((f) => !complianceSeeders.includes(f));
    // eslint-disable-next-line no-console
    console.log('this.tradeVintages - compliance : ' + this.originalTradeVintages.length);
  }

  @Command({
    command: 'populate-x-compliance-credits <amount>',
    description: 'populate compliance credits into compliance default book',
  })
  async populateXComplianceCredits(amount: number): Promise<void> {
    if (environment.env === 'production') {
      throw new ForbiddenException(`this cannot be run in prod`);
    }
    const em = this.em.fork();
    const knex = em.getKnex();
    const claims = await this.privateGetDefaultAdminClaims(em);

    const complianceVintages: { id: uuid; registry_project_id: string; name: string }[] = await knex
      .raw(
        `select pv.id, p.registry_project_id, pv.label as name from ${environment.db.schema.rubicon}.project_vintages as pv inner join ${environment.db.schema.rubicon}.projects as p on p.id = pv.project_id inner join ${environment.db.schema.rubicon}.project_types as pt on pt.id = p.project_type_id where pt.category = 'Compliance Permit'`,
      )
      .then((r) => r.rows);

    for (const vintage of complianceVintages) {
      await this.privateCreateAndSettleTrade(em, claims, {
        bookId: uuid(environment.rubicon.books.compliance),
        projectVintages: [{ assetId: uuid(vintage.id), amount: amount }],
        type: TradeType.BUY,
        rawPrice: new Decimal((Math.random() * (13 - 5 + 1) + 5) * amount),
      });
    }
  }

  @Command({
    command: 'populate-original-rct-credits',
    description: 'populate original amount of rct credits into portfolio default book',
  })
  async populateOriginalRctCredits(): Promise<void> {
    if (environment.env === 'production') {
      throw new ForbiddenException(`this cannot be run in prod`);
    }
    const em = this.em.fork();
    const knex = em.getKnex();
    const claims = await this.privateGetDefaultAdminClaims(em);

    const rctVintages: { id: uuid; registry_project_id: string; name: string }[] = await knex
      .raw(
        `select pv.id, p.registry_project_id, pv.label as name from ${environment.db.schema.rubicon}.project_vintages as pv inner join ${environment.db.schema.rubicon}.projects as p on p.id = pv.project_id where p.rct_standard = true and (pv.risk_buffer_percentage is null or pv.risk_buffer_percentage < 1)`,
      )
      .then((r) => r.rows);

    const rctSeeders = this.originalTradeVintages.filter((li) =>
      rctVintages.find((vintage) => vintage.name === li[6] && vintage.registry_project_id === li[5]),
    );

    for (const li of rctSeeders) {
      const pv = rctVintages.find((vintage) => vintage.name === li[6] && vintage.registry_project_id === li[5]);
      if (!pv) {
        continue;
      }
      await this.privateCreateAndSettleTrade(em, claims, {
        bookId: uuid(environment.rubicon.books.portfolio.default),
        projectVintages: [{ assetId: uuid(pv.id), amount: li[3] }],
        type: TradeType.BUY,
        rawPrice: new Decimal(li[4]),
      });
    }

    // eslint-disable-next-line no-console
    console.log('this.tradeVintages : ' + this.originalTradeVintages.length);
    // eslint-disable-next-line no-console
    console.log('rctSeeders: ' + rctSeeders.length);
    this.originalTradeVintages = this.originalTradeVintages.filter((f) => !rctSeeders.includes(f));
    // eslint-disable-next-line no-console
    console.log('this.tradeVintages - rctSeeders : ' + this.originalTradeVintages.length);
  }

  @Command({
    command: 'populate-x-rct-credits <amount>',
    description: 'populate specified amount rct credits into portfolio default book',
  })
  async populateXRctCredits(amount: number): Promise<void> {
    if (environment.env === 'production') {
      throw new ForbiddenException(`this cannot be run in prod`);
    }
    const em = this.em.fork();
    const knex = em.getKnex();
    const claims = await this.privateGetDefaultAdminClaims(em);

    const rctVintages: { id: uuid; registry_project_id: string; name: string }[] = await knex
      .raw(
        `select pv.id, p.registry_project_id, pv.label as name from ${environment.db.schema.rubicon}.project_vintages as pv inner join ${environment.db.schema.rubicon}.projects as p on p.id = pv.project_id where p.rct_standard = true and (pv.risk_buffer_percentage is null or pv.risk_buffer_percentage < 1)`,
      )
      .then((r) => r.rows);

    for (const vintage of rctVintages) {
      await this.privateCreateAndSettleTrade(em, claims, {
        bookId: uuid(environment.rubicon.books.portfolio.default),
        projectVintages: [{ assetId: uuid(vintage.id), amount: amount }],
        type: TradeType.BUY,
        rawPrice: new Decimal((Math.random() * (13 - 5 + 1) + 5) * amount),
      });
    }
  }

  @Command({
    command: 'populate-original-rehab-credits',
    description: 'populate original rehab credit amounts into rehab book',
  })
  async populateOriginalRehabCredits(): Promise<void> {
    if (environment.env === 'production') {
      throw new ForbiddenException(`this cannot be run in prod`);
    }
    const em = this.em.fork();
    const claims = await this.privateGetDefaultAdminClaims(em);
    const rehabSeeders = this.originalTradeVintages;

    for (const li of rehabSeeders) {
      const pv = await em.findOneOrFail(
        ProjectVintage,
        { label: li[6], project: { registryProjectId: li[5] } },
        { populate: ['project'] },
      );
      if (!pv) {
        continue;
      }
      await this.privateCreateAndSettleTrade(em, claims, {
        bookId: uuid(environment.rubicon.books.rehabilitation),
        projectVintages: [{ assetId: uuid(pv.id), amount: li[3] }],
        type: TradeType.BUY,
        rawPrice: new Decimal(li[4]),
      });
    }

    // eslint-disable-next-line no-console
    console.log('this.tradeVintages : ' + this.originalTradeVintages.length);
    // eslint-disable-next-line no-console
    console.log('rehabSeeders: ' + rehabSeeders.length);
    this.originalTradeVintages = this.originalTradeVintages.filter((f) => !rehabSeeders.includes(f));
    // eslint-disable-next-line no-console
    console.log('this.tradeVintages - rehabSeeders : ' + this.originalTradeVintages.length);
  }

  @Command({
    command: 'populate-x-rehab-credits <amount>',
    description: 'populate specified amount of rehab credits into rehab book',
  })
  async populateXRehabCredits(amount: number): Promise<void> {
    if (environment.env === 'production') {
      throw new ForbiddenException(`this cannot be run in prod`);
    }
    const em = this.em.fork();
    const knex = em.getKnex();
    const claims = await this.privateGetDefaultAdminClaims(em);

    const rehabVintages: { id: uuid; registry_project_id: string; name: string }[] = await knex
      .raw(
        `select pv.id, p.registry_project_id, pv.label as name 
        from ${environment.db.schema.rubicon}.project_vintages as pv 
        inner join ${environment.db.schema.rubicon}.projects as p on p.id = pv.project_id 
        inner join ${environment.db.schema.rubicon}.project_types as pt on pt.id = p.project_type_id 
        where pt.category != 'Compliance Permit' and (p.rct_standard = false or (pv.risk_buffer_percentage != null and pv.risk_buffer_percentage > 1))`,
      )
      .then((r) => r.rows);

    for (const vintage of rehabVintages) {
      await this.privateCreateAndSettleTrade(em, claims, {
        bookId: uuid(environment.rubicon.books.rehabilitation),
        projectVintages: [{ assetId: uuid(vintage.id), amount: amount }],
        type: TradeType.BUY,
        rawPrice: new Decimal((Math.random() * (13 - 5 + 1) + 5) * amount),
      });
    }
  }

  @Command({
    command: 'transfer-default-public-portfolios-credits',
    description: 'transfer credits from portfolio default to public portfolios',
  })
  async transferDefaultPublicPortfolioCredits(): Promise<void> {
    if (environment.env === 'production') {
      throw new ForbiddenException(`this cannot be run in prod`);
    }
    const em = this.em.fork();
    const knex = em.getKnex();
    const claims = await this.privateGetDefaultAdminClaims(em);

    const balances: { owner_id: uuid; asset_id: uuid; amount: number; registry_project_id: string; name: string }[] =
      await knex
        .raw(
          `select ac.owner_id, ac.component_id as asset_id, ac.holdings as amount, p.registry_project_id, pv.label as name
          from ${environment.db.schema.rubicon}.asset_composition_v2 as ac inner join ${environment.db.schema.rubicon}.project_vintages as pv on pv.id = ac.component_id inner join ${environment.db.schema.rubicon}.projects as p on p.id = pv.project_id where ac.owner_id = '${environment.rubicon.books.portfolio.default}' and ac.level = 1;`,
        )
        .then((r) => r.rows);

    const assetTransfers: AdminAssetTransferRequestDTO[] = [];
    for (const bv of this.originalBookVintages) {
      const book = await em.findOneOrFail(Book, { name: bv[2] });
      const balance = balances.find((f) => f.registry_project_id === bv[3] && f.name === bv[4]);
      const amount = Decimal.min(bv[1], balance?.amount || 0);
      if (balance && amount.greaterThan(0)) {
        assetTransfers.push({
          amount: amount.toNumber(),
          assetId: balance.asset_id,
          destinationId: book.id,
          sourceId: uuid(environment.rubicon.books.portfolio.default),
        });
      }
    }
    if (assetTransfers.length === 0) {
      // eslint-disable-next-line no-console
      console.log('no rct assets to transfer.');
      return;
    }

    const transferRequest: AdminTransferRequestDTO = {
      memo: `one-time transfer of credits to rct books`,
      assetTransfers,
    };

    await this.transfersService.executeInternal(claims, transferRequest);
  }

  @Command({
    command: 'transfer-x-credits-custom-portfolios <amount>',
    description: 'transfer x credits to custom-portfolios',
  })
  async transferXCreditsCustomPortfolio(amount: number): Promise<void> {
    if (environment.env === 'production') {
      throw new ForbiddenException(`this cannot be run in prod`);
    }
    const em = this.em.fork();
    const knex = em.getKnex();
    const claims = await this.privateGetDefaultAdminClaims(em);

    const books = await em.find(Book, { type: BookType.RCT_CUSTOM });
    if (books.length === 0) {
      console.error('no custom portfolios found');
      return;
    }

    const balances: { owner_id: uuid; asset_id: uuid; amount: number; registry_project_id: string; name: string }[] =
      await knex
        .raw(
          `select ac.owner_id, ac.component_id as asset_id, ac.holdings as amount, p.registry_project_id, pv.label as name
          from ${environment.db.schema.rubicon}.asset_composition_v2 as ac 
          inner join ${environment.db.schema.rubicon}.project_vintages as pv on pv.id = ac.component_id 
          inner join ${environment.db.schema.rubicon}.projects as p on p.id = pv.project_id 
          where ac.owner_id = '${environment.rubicon.books.portfolio.default}' and ac.level = 1;`,
        )
        .then((r) => r.rows);

    for (const customPortfolio of books) {
      const assetTransfers: AdminAssetTransferRequestDTO[] = [];
      for (const balance of balances) {
        const transferAmount = Decimal.min(amount, new Decimal(balance.amount).divToInt(books.length));
        if (transferAmount.greaterThan(0)) {
          assetTransfers.push({
            amount: transferAmount.toNumber(),
            assetId: balance.asset_id,
            destinationId: customPortfolio.id,
            sourceId: uuid(environment.rubicon.books.portfolio.default),
          });
        }
      }
      if (assetTransfers.length === 0) {
        console.error('no rct assets to transfer.');
        return;
      }

      await this.transfersService.executeInternal(claims, {
        memo: `one-time transfer of credits to custom portfolio ${customPortfolio.name}`,
        assetTransfers,
      });
    }
  }

  /* bulk credits commands */

  @Command({
    command: 'populate-original-credits',
    description: 'populate original credits into proper books (this is not part of the initial setup)',
  })
  async populateOriginalCredits(): Promise<void> {
    if (environment.env === 'production') {
      throw new ForbiddenException(`this cannot be run in prod`);
    }
    await this.populateOriginalComplianceCredits();
    await this.populateOriginalRctCredits();
    await this.populateOriginalRehabCredits();
    await this.transferDefaultPublicPortfolioCredits();
  }

  @Command({
    command: 'populate-x-credits <amount>',
    description: 'populate a chosen number of credits  into proper books (this is not part of the initial setup)',
  })
  async populateXAssetsEachVintage(amount: number): Promise<void> {
    if (environment.env === 'production') {
      throw new ForbiddenException(`this cannot be run in prod`);
    }
    await this.populateXComplianceCredits(amount);
    await this.populateXRctCredits(amount);
    await this.populateXRehabCredits(amount);
  }

  @Command({
    command: 'populate-current-credits <customerPortfolioId>',
    description:
      'populate most recently updated csvs into proper books (this is not part of the initial setup) and purchases and retirements from the customerPortfolioId',
  })
  async populateCurrentCredits(customerPortfolioId: uuid): Promise<void> {
    if (environment.env === 'production') {
      throw new ForbiddenException(`this cannot be run in prod`);
    }
    const em = this.em.fork();
    const claims = await this.privateGetDefaultAdminClaims(em);

    // validate customerPortfolio exists
    const customerPortfolio = await em.findOneOrFail(CustomerPortfolio, customerPortfolioId, {
      populate: ['organization'],
    });

    // get asset flows from csv
    // note : currently ignoring cancelled but can add that in if necessary
    // also currently does not do marketing agreements or forwards
    const assetFlows: AssetFlowInterface[] = await copyToArrayFromCSV('./seeders/csv/asset-flows.csv');
    const byTransactionId: Map<uuid, AssetFlowInterface[]> = groupBy(assetFlows, (x) => {
      return x.transaction_id;
    });

    // get existing books and assets
    const assetIds: uuid[] = (await em.find(Asset, {})).map((m) => m.id);
    const bookIds: uuid[] = (await em.find(Book, {})).map((m) => m.id);

    for (const af of assetFlows) {
      if ([AssetType.RRT, AssetType.RRT_VINTAGE].includes(af.asset_type)) {
        console.warn('RRT asset found, should be fixed in RBC-3175');
        continue;
      }
      // eslint-disable-next-line no-console
      console.log('af : ' + JSON.stringify(af));
      // trade buys
      if (af.transaction_type === TransactionType.TRADE && af.transaction_subtype === TradeType.BUY) {
        const transactionAssetFlows: AssetFlowInterface[] | undefined = byTransactionId
          .get(af.transaction_id)
          ?.filter((f) => assetIds.includes(f.asset_id) && bookIds.includes(f.destination_id));

        if (!transactionAssetFlows || transactionAssetFlows.length === 0) {
          continue;
        }

        await this.privateCreateTradeBuy(em, claims, transactionAssetFlows);
        byTransactionId.delete(af.transaction_id);
        continue;
      }
      // trade sells
      if (af.transaction_type === TransactionType.TRADE && af.transaction_subtype === TradeType.SELL) {
        const transactionAssetFlows: AssetFlowInterface[] | undefined = byTransactionId
          .get(af.transaction_id)
          ?.filter((f) => assetIds.includes(f.asset_id) && bookIds.includes(f.source_id));

        if (!transactionAssetFlows || transactionAssetFlows.length === 0) {
          continue;
        }

        await this.privateCreateTradeSell(em, claims, transactionAssetFlows);
        byTransactionId.delete(af.transaction_id);
        continue;
      }
      // transfers
      if (af.transaction_type === TransactionType.INTERNAL_TRANSFER) {
        const transactionAssetFlows: AssetFlowInterface[] | undefined = byTransactionId
          .get(af.transaction_id)
          ?.filter(
            (f) =>
              assetIds.includes(f.asset_id) &&
              f.asset_type === AssetType.REGISTRY_VINTAGE &&
              bookIds.includes(f.source_id) &&
              bookIds.includes(f.destination_id),
          );

        if (!transactionAssetFlows || transactionAssetFlows.length === 0) {
          continue;
        }

        await this.privateCreateTransfer(claims, transactionAssetFlows);
        byTransactionId.delete(af.transaction_id);
        continue;
      }
      // purchases
      if (af.transaction_type === TransactionType.PURCHASE) {
        const transactionAssetFlows: AssetFlowInterface[] | undefined = byTransactionId
          .get(af.transaction_id)
          ?.filter((f) => assetIds.includes(f.asset_id) && bookIds.includes(f.source_id));

        if (!transactionAssetFlows || transactionAssetFlows.length === 0) {
          continue;
        }

        await this.privateCreatePurchase(claims, customerPortfolio.organization.id, transactionAssetFlows);
        byTransactionId.delete(af.transaction_id);
        continue;
      }
      // retirements
      if (af.transaction_type === TransactionType.RETIREMENT) {
        // get current customer portfolio holdings to ignore retirements that do not have a matching purchase for whatever reason
        const customerPortfolioAllocations = await this.assetsService.internalGetOwnerHoldings(em.getKnex(), {
          ownerIds: [customerPortfolio.id],
          includePrices: false,
        });
        let transactionAssetFlows: AssetFlowInterface[] | undefined = byTransactionId.get(af.transaction_id);
        if (!transactionAssetFlows || transactionAssetFlows.length === 0) {
          continue;
        }
        const rctAssetFlow = transactionAssetFlows?.find((s) => s.asset_type === AssetType.RCT);
        if (rctAssetFlow) {
          if (!customerPortfolioAllocations.find((f) => f.asset_id === af.asset_id)) {
            continue;
          }
        } else {
          transactionAssetFlows = transactionAssetFlows?.filter((f) =>
            customerPortfolioAllocations.map((m) => m.asset_id).includes(f.asset_id),
          );
        }

        await this.privateCreateRetirement(claims, customerPortfolio, assetIds, transactionAssetFlows);
        byTransactionId.delete(af.transaction_id);
        continue;
      }
    }
  }

  /* private */

  private async privateCreatePurchase(
    claims: UserClaims,
    organizationId: uuid,
    assetFlows: AssetFlowInterface[],
  ): Promise<void> {
    const now = new Date();
    if (environment.env === 'production') {
      throw new ForbiddenException(`this cannot be run in prod`);
    }

    assetFlows = assetFlows.filter((f) => ![AssetType.RRT, AssetType.RRT_VINTAGE].includes(f.asset_type)); // todo (RBC-3175) : fix

    if (assetFlows[0].status === AssetFlowStatus.PENDING) {
      await this.purchasesService.createFirm(
        {
          assetType: assetFlows[0].asset_type,
          flowType: PurchaseFlowType.PURCHASE_AND_HOLD,
          memo: 'initial setup',
          paymentDueDate: new Date(now.getTime() + 100000),
          organizationId,
          needsRiskAdjustment: false,
          assets: assetFlows.map((m) => {
            return {
              amount: new Decimal(m.amount).toNumber(),
              assetId: m.asset_id,
              sourceId:
                m.asset_type === AssetType.RCT ? uuid(environment.rubicon.books.portfolio.default) : m.source_id,
              rawPrice: new Decimal(m.raw_price),
              otherFee: m.other_fee ? new Decimal(m.other_fee) : undefined,
              serviceFee: m.service_fee ? new Decimal(m.service_fee) : undefined,
            };
          }),
        },
        claims,
      );
    } else {
      await this.privateCreateAndSettlePurchase(claims, now, {
        assetType: assetFlows[0].asset_type,
        flowType: PurchaseFlowType.PURCHASE_AND_HOLD,
        memo: 'initial setup',
        paymentDueDate: new Date(now.getTime() + 100000),
        organizationId,
        needsRiskAdjustment: false,
        assets: assetFlows.map((m) => {
          return {
            amount: new Decimal(m.amount).toNumber(),
            assetId: m.asset_id,
            sourceId: m.asset_type === AssetType.RCT ? uuid(environment.rubicon.books.portfolio.default) : m.source_id,
            rawPrice: new Decimal(m.raw_price),
            otherFee: m.other_fee ? new Decimal(m.other_fee) : undefined,
            serviceFee: m.service_fee ? new Decimal(m.service_fee) : undefined,
          };
        }),
      });
    }
  }

  private async privateCreateRetirement(
    claims: UserClaims,
    customerPortfolio: CustomerPortfolio,
    assetIds: uuid[],
    assetFlows: AssetFlowInterface[],
  ): Promise<void> {
    if (environment.env === 'production') {
      throw new ForbiddenException(`this cannot be run in prod`);
    }
    assetFlows = assetFlows.filter((f) => ![AssetType.RRT, AssetType.RRT_VINTAGE].includes(f.asset_type)); // todo (TD-3175) : fix

    const rctRetirement: AssetFlowInterface | undefined = assetFlows.find((f) => f.asset_type === AssetType.RCT);
    if (!rctRetirement || assetIds.includes(rctRetirement.asset_id)) {
      if (assetFlows[0].status === AssetFlowStatus.PENDING) {
        await this.privateCreateAndCalculateRetirement(
          claims,
          {
            assetType: rctRetirement?.asset_type || AssetType.REGISTRY_VINTAGE,
            beneficiary: 'initiate',
            organizationId: customerPortfolio.organization.id,
            isPublic: false,
            memo: 'initial setup',
            type: assetFlows[0].transaction_subtype as RetirementType,
            assets: rctRetirement
              ? [
                  {
                    amount: new Decimal(rctRetirement.amount).toNumber(),
                    assetId: rctRetirement.asset_id,
                    rawPrice: new Decimal(rctRetirement.raw_price),
                    otherFee: rctRetirement.other_fee ? new Decimal(rctRetirement.other_fee) : undefined,
                    serviceFee: rctRetirement.service_fee ? new Decimal(rctRetirement.service_fee) : undefined,
                  },
                ]
              : assetFlows.map((m) => {
                  return {
                    amount: new Decimal(m.amount).toNumber(),
                    assetId: m.asset_id,
                    rawPrice: new Decimal(m.raw_price),
                    otherFee: m.other_fee ? new Decimal(m.other_fee) : undefined,
                    serviceFee: m.service_fee ? new Decimal(m.service_fee) : undefined,
                  };
                }),
          },
          assetFlows,
        );
      } else {
        await this.privateCreateAndSettleRetirement(
          claims,
          {
            assetType: rctRetirement?.asset_type || AssetType.REGISTRY_VINTAGE,
            beneficiary: 'initiate',
            organizationId: customerPortfolio.organization.id,
            isPublic: false,
            memo: 'initial setup',
            type: assetFlows[0].transaction_subtype as RetirementType,
            assets: rctRetirement
              ? [
                  {
                    amount: new Decimal(rctRetirement.amount).toNumber(),
                    assetId: rctRetirement.asset_id,
                    rawPrice: new Decimal(rctRetirement.raw_price),
                    otherFee: rctRetirement.other_fee ? new Decimal(rctRetirement.other_fee) : undefined,
                    serviceFee: rctRetirement.service_fee ? new Decimal(rctRetirement.service_fee) : undefined,
                  },
                ]
              : assetFlows.map((m) => {
                  return {
                    amount: new Decimal(m.amount).toNumber(),
                    assetId: m.asset_id,
                    rawPrice: new Decimal(m.raw_price),
                    otherFee: m.other_fee ? new Decimal(m.other_fee) : undefined,
                    serviceFee: m.service_fee ? new Decimal(m.service_fee) : undefined,
                  };
                }),
          },
          assetFlows,
        );
      }
    }
  }

  private async privateCreateTradeBuy(
    tx: EntityManager,
    claims: UserClaims,
    assetFlows: AssetFlowInterface[],
  ): Promise<void> {
    if (environment.env === 'production') {
      throw new ForbiddenException(`this cannot be run in prod`);
    }
    const accountingAssetFlows = assetFlows.filter((f) => new Decimal(f.amount).equals(0));
    const tradeAssetFlows = assetFlows.filter((f) => new Decimal(f.amount).greaterThan(0));
    if (tradeAssetFlows.length === 0) {
      return;
    }
    if (
      tradeAssetFlows.some((e) => e.status !== tradeAssetFlows[0].status) ||
      tradeAssetFlows.some((e) => e.destination_id !== tradeAssetFlows[0].destination_id)
    ) {
      console.error('trade buy : ' + JSON.stringify(assetFlows));
      console.error(
        `trade buy ${tradeAssetFlows[0].transaction_id} asset flows did not match as expected. skipping trade.`,
      );
      return;
    }

    let triesLeft = 10;
    while (triesLeft > 0) {
      try {
        if (tradeAssetFlows[0].status === AssetFlowStatus.PRE_PENDING) {
          await this.privateCreateTrade(tx, claims, {
            bookId: tradeAssetFlows[0].destination_id,
            projectVintages: tradeAssetFlows.map((m) => {
              return { amount: new Decimal(m.amount).toNumber(), assetId: m.asset_id };
            }),
            type: TradeType.BUY,
            rawPrice: Decimal.sum(...tradeAssetFlows.map((m) => m.raw_price), 0),
            otherFee: Decimal.sum(...tradeAssetFlows.map((m) => m.other_fee || 0), 0),
            serviceFee: Decimal.sum(...tradeAssetFlows.map((m) => m.service_fee || 0), 0),
          });
        } else if (tradeAssetFlows[0].status === AssetFlowStatus.PENDING) {
          await this.privateCreateAndFirmTrade(tx, claims, {
            bookId: tradeAssetFlows[0].destination_id,
            projectVintages: tradeAssetFlows.map((m) => {
              return { amount: new Decimal(m.amount).toNumber(), assetId: m.asset_id };
            }),
            type: TradeType.BUY,
            rawPrice: Decimal.sum(...tradeAssetFlows.map((m) => m.raw_price), 0),
            otherFee: Decimal.sum(...tradeAssetFlows.map((m) => m.other_fee || 0), 0),
            serviceFee: Decimal.sum(...tradeAssetFlows.map((m) => m.service_fee || 0), 0),
          });
        } else if (tradeAssetFlows[0].status === AssetFlowStatus.SETTLED) {
          const tradeId: uuid = await this.privateCreateAndSettleTrade(tx, claims, {
            bookId: tradeAssetFlows[0].destination_id,
            projectVintages: tradeAssetFlows.map((m) => {
              return { amount: new Decimal(m.amount).toNumber(), assetId: m.asset_id };
            }),
            type: TradeType.BUY,
            rawPrice: Decimal.sum(...tradeAssetFlows.map((m) => m.raw_price), 0),
            otherFee: Decimal.sum(...tradeAssetFlows.map((m) => m.other_fee || 0), 0),
            serviceFee: Decimal.sum(...tradeAssetFlows.map((m) => m.service_fee || 0), 0),
          });

          for (const ae of accountingAssetFlows) {
            await this.tradesService.internalCreateAccountingEntry(tx, tradeId, { amount: new Decimal(ae.raw_price) });
          }
        }
        triesLeft = 0;
      } catch (e) {
        if (!(e instanceof UniqueConstraintViolationException)) {
          console.error(e);
        }
        triesLeft--;
      }
    }
  }

  private async privateCreateTradeSell(
    tx: EntityManager,
    claims: UserClaims,
    assetFlows: AssetFlowInterface[],
  ): Promise<void> {
    if (environment.env === 'production') {
      throw new ForbiddenException(`this cannot be run in prod`);
    }

    if (
      assetFlows.some((e) => e.status !== assetFlows[0].status) ||
      assetFlows.some((e) => e.destination_id !== assetFlows[0].destination_id)
    ) {
      console.error('trade buy : ' + JSON.stringify(assetFlows));
      console.error(
        `trade sell ${assetFlows[0].transaction_id} asset flows did not match as expected. skipping trade.`,
      );
      return;
    }

    let triesLeft = 10;
    while (triesLeft > 0) {
      try {
        if (assetFlows[0].status === AssetFlowStatus.PRE_PENDING) {
          await this.privateCreateTrade(tx, claims, {
            bookId: assetFlows[0].source_id,
            projectVintages: assetFlows.map((m) => {
              return { amount: new Decimal(m.amount).toNumber(), assetId: m.asset_id };
            }),
            type: TradeType.SELL,
            rawPrice: Decimal.sum(...assetFlows.map((m) => m.raw_price), 0),
            otherFee: Decimal.sum(...assetFlows.map((m) => m.other_fee || 0), 0),
            serviceFee: Decimal.sum(...assetFlows.map((m) => m.service_fee || 0), 0),
          });
        } else if (assetFlows[0].status === AssetFlowStatus.PENDING) {
          await this.privateCreateAndFirmTrade(tx, claims, {
            bookId: assetFlows[0].source_id,
            projectVintages: assetFlows.map((m) => {
              return { amount: new Decimal(m.amount).toNumber(), assetId: m.asset_id };
            }),
            type: TradeType.SELL,
            rawPrice: Decimal.sum(...assetFlows.map((m) => m.raw_price), 0),
            otherFee: Decimal.sum(...assetFlows.map((m) => m.other_fee || 0), 0),
            serviceFee: Decimal.sum(...assetFlows.map((m) => m.service_fee || 0), 0),
          });
        } else if (assetFlows[0].status === AssetFlowStatus.SETTLED) {
          await this.privateCreateAndSettleTrade(tx, claims, {
            bookId: assetFlows[0].source_id,
            projectVintages: assetFlows.map((m) => {
              return { amount: new Decimal(m.amount).toNumber(), assetId: m.asset_id };
            }),
            type: TradeType.SELL,
            rawPrice: Decimal.sum(...assetFlows.map((m) => m.raw_price), 0),
            otherFee: Decimal.sum(...assetFlows.map((m) => m.other_fee || 0), 0),
            serviceFee: Decimal.sum(...assetFlows.map((m) => m.service_fee || 0), 0),
          });
        }
        triesLeft = 0;
      } catch (e) {
        if (!(e instanceof UniqueConstraintViolationException)) {
          console.error(e);
        }
        triesLeft--;
      }
    }
  }

  private async privateCreateTransfer(claims: UserClaims, assetFlows: AssetFlowInterface[]): Promise<void> {
    if (environment.env === 'production') {
      throw new ForbiddenException(`this cannot be run in prod`);
    }

    await this.transfersService.executeInternal(claims, {
      assetTransfers: assetFlows.map((m) => {
        return {
          amount: new Decimal(m.amount).toNumber(),
          assetId: m.asset_id,
          sourceId: m.source_id,
          destinationId: m.destination_id,
        };
      }),
    });
  }

  private async privateCreateAndFirmTrade(
    tx: EntityManager,
    claims: UserClaims,
    data: {
      bookId: uuid;
      projectVintages: AdminAssetTradeRequestDTO[];
      type: TradeType;
      rawPrice: Decimal;
      serviceFee?: Decimal;
      otherFee?: Decimal;
    },
  ): Promise<Trade> {
    if (environment.env === 'production') {
      throw new ForbiddenException(`this cannot be run in prod`);
    }
    const trade = await this.privateCreateTrade(tx, claims, data);
    return await this.tradesService.firm(claims, trade.id);
  }

  private async privateCreateAndSettlePurchase(
    claims: UserClaims,
    now: Date,
    request: AdminPurchaseRequest,
  ): Promise<void> {
    if (environment.env === 'production') {
      throw new ForbiddenException(`this cannot be run in prod`);
    }

    const purchase = await this.purchasesService.createFirm(request, claims);
    await this.purchasesService.bind(purchase.id, claims, true);
    await this.purchasesService.execute(
      purchase.id,
      claims,
      {
        updatableStatusOrder: ['pending_payment', 'pending_delivery'],
      },
      true,
    );
    await this.purchasesService.pay(purchase.id, claims);
    await this.purchasesService.deliver(purchase.id, claims, { assetsDeliveredAt: now });
  }

  private async privateCreateAndCalculateRetirement(
    claims: UserClaims,
    request: InternalRetirementTransaction,
    transactionAssetFlows: AssetFlowInterface[],
  ): Promise<Retirement> {
    if (environment.env === 'production') {
      throw new ForbiddenException(`this cannot be run in prod`);
    }

    let retirement = await this.retirementsService.createRetirement(request, claims);
    if (request.assetType === AssetType.RCT && transactionAssetFlows.length > 1) {
      retirement = await this.retirementsService.calculateRetirementAmounts(retirement.id, claims);
      const updatedAmounts = transactionAssetFlows
        .filter((f) => f.asset_type === AssetType.REGISTRY_VINTAGE)
        .map((m) => {
          return {
            amountTransacted: new Decimal(m.amount).toNumber(),
            projectVintageId: m.asset_id,
            sourceId: m.source_id,
          };
        });

      retirement = await this.retirementsService.updateRetirementAmounts(retirement.id, claims, [
        ...updatedAmounts,
        ...retirement.assetFlows
          .filter((f) => !updatedAmounts.map((m) => m.projectVintageId).includes(f.asset.id))
          .map((m) => {
            return {
              amountTransacted: 0,
              projectVintageId: m.asset.id,
              sourceId: m.source.id,
            };
          }),
      ]);
    }

    return retirement;
  }

  private async privateCreateAndSettleRetirement(
    claims: UserClaims,
    request: InternalRetirementTransaction,
    transactionAssetFlows: AssetFlowInterface[],
  ): Promise<void> {
    if (environment.env === 'production') {
      throw new ForbiddenException(`this cannot be run in prod`);
    }

    const retirement = await this.privateCreateAndCalculateRetirement(claims, request, transactionAssetFlows);
    await this.retirementsService.processRetirement(retirement.id, claims);
    await this.retirementsService.completeRetirement(retirement.id, claims);
  }

  private async privateCreateAndSettleTrade(
    tx: EntityManager,
    claims: UserClaims,
    data: {
      bookId: uuid;
      projectVintages: AdminAssetTradeRequestDTO[];
      type: TradeType;
      rawPrice: Decimal;
      serviceFee?: Decimal;
      otherFee?: Decimal;
    },
  ): Promise<uuid> {
    if (environment.env === 'production') {
      throw new ForbiddenException(`this cannot be run in prod`);
    }

    const trade = await this.privateCreateAndFirmTrade(tx, claims, data);
    await this.tradesService.bind(claims, trade.id, undefined, true);
    await this.tradesService.execute(
      claims,
      trade.id,
      { updatableStatusOrder: ['pending_payment', 'pending_delivery'] },
      true,
      false,
    );
    await this.tradesService.pay(claims, trade.id);
    await this.tradesService.deliver(claims, trade.id, { assetsDeliveredAt: new Date() }, true);

    return trade.id;
  }

  private async privateCreateTrade(
    tx: EntityManager,
    claims: UserClaims,
    data: {
      bookId: uuid;
      projectVintages: AdminAssetTradeRequestDTO[];
      type: TradeType;
      rawPrice: Decimal;
      serviceFee?: Decimal;
      otherFee?: Decimal;
    },
  ): Promise<Trade> {
    if (environment.env === 'production') {
      throw new ForbiddenException(`this cannot be run in prod`);
    }
    const seederOrganization = await this.privateGetOrCreateSeederCounterparty(claims, tx);

    const trade = await this.tradesService.indicate(
      claims,
      {
        ...data,
        counterparties: [
          {
            actualServiceFee: data.serviceFee || new Decimal(0),
            organizationId: seederOrganization.id,
            isPrimary: true,
            role: CounterpartyRole.CUSTOMER,
          },
        ],
        poid: '',
        memo: 'initial setup',
      },
      false,
    );

    return trade;
  }

  private async privateGetOrCreateSeederCounterparty(claims: UserClaims, tx: EntityManager): Promise<Organization> {
    const organizationCounterparty = await tx.findOne(
      Organization,
      { name: 'Seeder Counterparty' },
      { populate: ['counterparty'] },
    );
    if (organizationCounterparty && organizationCounterparty.counterparty) {
      return organizationCounterparty;
    }

    return await this.organizationsService.create(claims, {
      isEnabled: true,
      name: 'Seeder Counterparty',
      counterparty: {
        tradeConfirmEmails: ['<EMAIL>'],
        isEnabled: true,
        isOnboarded: true,
      },
    });
  }

  private async privateGetDefaultAdminClaims(em: EntityManager): Promise<UserClaims> {
    const user: User = await em.findOneOrFail(User, {
      email: environment.rubicon.defaultAdminEmail.toLowerCase() as Lowercase<string>,
    });

    return {
      id: user.id,
      email: user.email,
      type: 'jwt',
      name: 'init-console-script',
    };
  }
}

/* istanbul ignore file */
