/* third party */
import { RedisService } from '@liaoliaots/nestjs-redis';
import { EntityManager } from '@mikro-orm/postgresql';
import { Injectable, UnprocessableEntityException } from '@nestjs/common';
import Decimal from 'decimal.js';
import { Request } from 'express';
import { Redis } from 'ioredis';
import { sortBy, sum } from 'lodash';
import pl from 'nodejs-polars';
import { Float64, Int64, List, String } from 'nodejs-polars/bin/datatypes';
/* rubicon */
import {
  AdminPricingEstimateRequest,
  AdminPricingEstimateResponse,
  AllocationAdjustmentEnum,
  AssetType,
  BookType,
  BufferComputationEnum,
  ErrorResponse,
  MobileCustomPricingResponse,
  uuid,
} from '@rubiconcarbon/shared-types';
/* env*/
import { environment } from '@env/environment';
/* app */
import { UserClaims } from '@app/auth';
import { addToLog, getPortalUserWithOrganizationFromClaims } from '@app/helpers';
import { BaseModelPortfolioComponentCreateDTO, ModelPortfolioVintageComponentCreateDTO } from '@app/dtos/model-portfolio.dto';
import { PortalCustomPricingRequestDTO } from '@app/dtos/pricing.dto';
import { ByorctPriceRequest } from '@app/dtos/sendgrid-payload.dto';
import { SendgridService } from './sendgrid.service';
import { InternalCachedBYOQuote } from '@app/interfaces/model-portfolio.interface';

enum BYOVintageDistributionMethod {
  OLDER_FIRST,
  PROPORTIONAL,
}

const DEFAULT_BUFFER_COMPUTATION_TYPE: BufferComputationEnum = BufferComputationEnum.CATEGORIES;
const DEFAULT_BYO_VINTAGE_DISTRIBUTION: BYOVintageDistributionMethod = BYOVintageDistributionMethod.PROPORTIONAL;

function adjustmentName(adj: AllocationAdjustmentEnum): string {
  switch (adj) {
    case AllocationAdjustmentEnum.None:
      return 'none';
    case AllocationAdjustmentEnum.Clip:
      return 'clip';
    case AllocationAdjustmentEnum.ClipAndRedistribute:
      return 'clip and redistribute';
    case AllocationAdjustmentEnum.Scale:
      return 'scale';
  }
}

interface AdjustedAllocationEntry {
  projectId: uuid;
  quantity: number;
  delta: number;
  price: Decimal;
  riskBufferPercentage: Decimal | null;
  projectName: string;
  registryProjectId: string;
  vintageId: uuid;
}

interface AllocationPrice {
  priceEstimate: Decimal;
  unitPrice: Decimal;
  totalQuantity: number;
  markup: Decimal;
  discount: Decimal;
  bufferPrice: Decimal;
}

interface AllocationCost {
  costEstimate: Decimal;
  unitCost: Decimal;
  totalQuantity: number;
  markup: Decimal;
  bufferCost: Decimal;
}

@Injectable()
export class PricingService {
  private redis: Redis;
  constructor(
    private em: EntityManager,
    private readonly redisService: RedisService,
    private readonly sendgrid: SendgridService,
  ) {
    this.redis = this.redisService.getClient();
  }

  /**
   * generate a custom price for a set of allocations for a User
   *
   * steps:
   * 1. retrieve Project, Vintage, and Buffer data for each allocation from the database
   * 2. cache the raw payload in Redis
   * 3. validate that all required prices and buffers are present
   * 4. check Project limits and zero-quantity requests
   * 5. retrieve the Organization for the User
   * 6. send a sales email with the request details
   * 7. if allocation is within limits, calculate and cache the price, then return it
   * 8. if not, generate alternative allocations using each AllocationAdjustmentEnum, cache, and return them
   * 9. remove duplicate alternatives and return the result
   *
   * @param {UserClaims} claims
   * @param {PortalCustomPricingRequestDTO} data
   * @param {Request} req
   * @returns {Promise<MobileCustomPricingResponse | ErrorResponse>}
   * @throws {UnprocessableEntityException}
   */
  async generateCustomPrice(
    claims: UserClaims,
    data: PortalCustomPricingRequestDTO,
    req: Request,
  ): Promise<MobileCustomPricingResponse | ErrorResponse> {
    // get prices and check limits
    const knex = this.em.getKnex();

    let df = pl.readRecords(
      await knex
        .raw(
          `
          SELECT
            projects.id as project_id,
            projects.name as project_name,
            projects.registry_project_id,
            project_vintage_prices_v2.vintage,
            project_vintage_prices_v2.price::double precision,
            project_vintages.id as project_vintage_id,
            project_vintages.label as project_vintage_label,
            risk_buffer_percentage::double precision,
            min_percentage::double precision,
            max_percentage::double precision,
            min_quantity::double precision,
            max_quantity::double precision, 
            project_amounts.holdings::double precision as project_holdings,
            buffer_categories.id as buffer_category_id,
            project_vintage_amounts_v3.holdings::double precision
          FROM "${environment.db.schema.rubicon}".projects
          LEFT JOIN "${environment.db.schema.rubicon}".project_vintage_prices_v2
            ON projects.id = project_vintage_prices_v2.project_id
              AND source != 'cost_basis'
		      LEFT JOIN (
            SELECT P.id, SUM(holdings) as holdings
            FROM "${environment.db.schema.rubicon}".project_vintage_amounts_v3 PVA
            JOIN "${environment.db.schema.rubicon}".project_vintages PV ON PV.id = PVA.asset_id
            JOIN "${environment.db.schema.rubicon}".projects P ON PV.project_id = P.id
            WHERE PV.id IN (${data.allocation.map(() => '?').join(',')})
            GROUP BY P.id
          ) project_amounts ON project_amounts.id=projects.id
          LEFT JOIN "${environment.db.schema.rubicon}".project_vintages
            ON project_vintages.id = project_vintage_prices_v2.vintage_id
          LEFT JOIN "${environment.db.schema.rubicon}".project_vintage_amounts_v3
            ON project_vintage_amounts_v3.asset_id=project_vintages.id
          LEFT JOIN "${environment.db.schema.rubicon}".project_types
            ON project_types.id = projects.project_type_id
          LEFT JOIN "${environment.db.schema.rubicon}".buffer_categories
            ON buffer_categories.id = COALESCE(projects.buffer_category_id, project_types.buffer_category_id)
          WHERE
            project_vintages.id IN (${data.allocation.map(() => '?').join(',')})
          `,
          data.allocation.map((x) => x.vintageId).concat(data.allocation.map((x) => x.vintageId)),
        )
        .then((r) =>
          r.rows.map((x: any) => ({
            vintage: x.vintage,
            price: x.price,
            riskBufferPercentage: x.risk_buffer_percentage,
            vintageId: x.project_vintage_id,
            vintageName: x.project_vintage_label,
            limitsQuantityLow: x.min_quantity || 0,
            limitsQuantityHigh: x.max_quantity == null ? Number.MAX_VALUE : x.max_quantity,
            limitsPercentageLow: x.min_percentage || 0,
            limitsPercentageHigh: x.max_percentage == null ? 1 : x.max_percentage,
            projectHoldings: x.project_holdings,
            registryProjectId: x.registry_project_id,
            projectName: x.project_name,
            projectId: x.project_id,
            bufferCategoryId: x.buffer_category_id,
            vintageHoldings: x.holdings,
          })),
        ),
      {
        schema: {
          vintage: new String(),
          price: new Float64(),
          riskBufferPercentage: new Float64(),
          vintageId: new String(),
          limitsQuantityLow: new Float64(),
          limitsQuantityHigh: new Float64(),
          limitsPercentageLow: new Float64(),
          limitsPercentageHigh: new Float64(),
          projectHoldings: new Int64(),
          registryProjectId: new String(),
          projectName: new String(),
          vintageName: new String(),
          projectId: new String(),
          bufferCategoryId: new String(),
          vintageHoldings: new Int64(),
        },
      },
    );

    const rawPayloadCacheId = uuid();
    const rawCacheData: InternalCachedBYOQuote = {
      id: rawPayloadCacheId,
      includeRiskAdjustment: !!data.riskAdjusted,
      isPurchaseToRetire: !!data.forRetirement,
      components: data.allocation.map((x) => {
        return Object.assign(new ModelPortfolioVintageComponentCreateDTO(), {
          amountAllocated: x.quantity,
          isBufferComponent: false,
          vintageId: x.vintageId,
        });
      }),
    };

    // add main cached data to Redis
    await this.redis.set(
      `BYO-${rawPayloadCacheId}`,
      JSON.stringify(rawCacheData),
      'EX',
      3600, // 1 hour
    );

    // break if we retrieved less prices than what we were expecting
    if (df.height !== data.allocation.length)
      return { error: 'Cannot generate estimate: Missing prices', details: JSON.stringify({ id: rawPayloadCacheId }) };

    // buffer prices
    const bufferCategoryAmounts = await this.privateGetBufferCategoryPrices();

    // compute limits and join inputs and buffer info
    df = df
      .withColumns(
        pl
          .maxHorizontal([pl.col('limitsQuantityLow'), pl.col('projectHoldings').mul('limitsPercentageLow')])
          .alias('limitLow'),
        pl
          .minHorizontal([pl.col('limitsQuantityHigh'), pl.col('projectHoldings').mul('limitsPercentageHigh')])
          .alias('limitHigh'),
      )
      .join(pl.readRecords(data.allocation, { schema: { vintageId: new String(), quantity: new Float64() } }), {
        on: 'vintageId',
      })
      .join(bufferCategoryAmounts, { on: 'bufferCategoryId', how: 'left' });

    // check buffers
    if (
      data.riskAdjusted &&
      (df.getColumn('riskBufferPercentage').nullCount() > 0 ||
        df.getColumn('bufferCategoryId').nullCount() > 0 ||
        df.getColumn('bufferUnitPrice').nullCount() > 0)
    )
      return { error: 'Cannot generate estimate: Missing buffers', details: JSON.stringify({ id: rawPayloadCacheId }) };

    // check prices
    if (df.getColumn('price').nullCount() > 0)
      return { error: 'Cannot generate estimate: Missing prices', details: JSON.stringify({ id: rawPayloadCacheId }) };

    // check limits
    const aggByProject = df
      .groupBy('projectId')
      .agg(pl.col('quantity').sum(), pl.first('limitLow'), pl.first('limitHigh'));
    if (
      aggByProject.select(pl.col('limitLow').greaterThan(pl.col('quantity'))).filter(pl.col('limitLow').eq(true))
        .height > 0
    )
      return { error: 'Cannot generate estimate: Project Limits', details: JSON.stringify({ id: rawPayloadCacheId }) };

    // check for zero payloads
    if (df.getColumn('quantity').sum() === 0)
      return {
        error: 'Cannot generate estimate: Zero-credits request',
        details: JSON.stringify({ id: rawPayloadCacheId }),
      };

    // get the org
    const portalUser = await getPortalUserWithOrganizationFromClaims(this.em, claims);
    await this.em.populate(portalUser, ['organizationUser.organization.customerPortfolio.rubiconManager']);
    if (!portalUser.organizationUser.organization.customerPortfolio) {
      throw new UnprocessableEntityException(
        `user ${claims.email} must be attached to an organization with a customer portfolios`,
      );
    }

    // send sales email
    const salesRequestEmailPayload = new ByorctPriceRequest(
      portalUser.organizationUser,
      df.select('projectName', 'vintageName', 'quantity').toRecords() as {
        projectName: string;
        vintageName: string;
        quantity: number;
      }[],
      !!data.riskAdjusted,
      !!data.forRetirement,
    );

    const internalRecipients = [
      environment.sendgrid.receiver.byoPriceRequested,
      portalUser.organizationUser.organization.customerPortfolio?.rubiconManager.email || '',
    ];
    if (environment.sendgrid.receiver.asana) {
      internalRecipients.push(environment.sendgrid.receiver.asana);
    }
    this.sendgrid.createAndSendEmailTemplate(
      internalRecipients,
      environment.sendgrid.sender.alerts,
      environment.sendgrid.template.byoPriceRequested,
      salesRequestEmailPayload,
    );

    // allocation adjustment (if needed)
    const ok =
      aggByProject.select(pl.col('quantity').lessThanEquals(pl.col('limitHigh'))).filter(pl.col('quantity').eq(true))
        .height === aggByProject.height;

    // account for risk-adjustment
    if (!data.riskAdjusted) {
      df = df.withColumn(pl.lit(0).alias('riskBufferPercentage'));
    }

    // get the prices
    const unmodifiedEstimateId = uuid();
    const cachedData: InternalCachedBYOQuote = {
      id: unmodifiedEstimateId,
      includeRiskAdjustment: !!data.riskAdjusted,
      isPurchaseToRetire: !!data.forRetirement,
      components: df.toRecords().map((x) => {
        return Object.assign(new ModelPortfolioVintageComponentCreateDTO(), {
          amountAllocated: x.quantity,
          isBufferComponent: false,
          projectId: x.projectId,
          projectName: x.projectName,
          vintageId: x.vintageId,
          registryProjectId: x.registryProjectId,
        });
      }),
    };

    // return a price if the allocation is ok
    if (ok) {
      const allocPrice = this.privateAllocationPrice(df, {
        includeCosts: false,
        forRetirement: !!data.forRetirement,
        bufferCalculationType: data.buffer ?? DEFAULT_BUFFER_COMPUTATION_TYPE,
      });

      addToLog(req, { ...allocPrice, forRetirement: data.forRetirement, riskAdjusted: data.riskAdjusted });

      cachedData.priceEstimate = allocPrice.priceEstimate;

      // add to Redis
      await this.redis.set(
        `BYO-${unmodifiedEstimateId}`,
        JSON.stringify(cachedData),
        'EX',
        3600, // 1 hour
      );

      return { id: unmodifiedEstimateId, price: allocPrice.priceEstimate, alternatives: [] };
    }

    // add main cached data to Redis
    await this.redis.set(
      `BYO-${unmodifiedEstimateId}`,
      JSON.stringify(cachedData),
      'EX',
      3600, // 1 hour
    );

    // run alternatives if here
    const alternatives: {
      id: uuid;
      pricing: AllocationPrice;
      allocations: AdjustedAllocationEntry[];
      type: AllocationAdjustmentEnum;
      df: pl.DataFrame;
    }[] = [];

    for (const adjType of [
      AllocationAdjustmentEnum.Scale,
      AllocationAdjustmentEnum.ClipAndRedistribute,
      AllocationAdjustmentEnum.Clip,
    ]) {
      const alternative = this.privateAdjustedAllocations(df, adjType);

      if (alternative.height === 0 || alternative.getColumn('quantity').sum() <= 0) continue;

      const estimateId = uuid();
      const altPrice = this.privateAllocationPrice(alternative, {
        includeCosts: false,
        forRetirement: !!data.forRetirement,
        bufferCalculationType: data.buffer ?? DEFAULT_BUFFER_COMPUTATION_TYPE,
      });
      alternatives.push({
        id: estimateId,
        pricing: altPrice,
        allocations: alternative.toRecords() as AdjustedAllocationEntry[],
        type: adjType,
        df: alternative,
      });

      // add to Redis
      const cachedData: InternalCachedBYOQuote = {
        id: estimateId,
        priceEstimate: new Decimal(altPrice.priceEstimate),
        includeRiskAdjustment: !!data.riskAdjusted,
        isPurchaseToRetire: !!data.forRetirement,
        components: alternative.toRecords().map((x) => {
          return Object.assign(new ModelPortfolioVintageComponentCreateDTO(), {
            amountAllocated: x.quantity,
            isBufferComponent: false,
            projectId: x.projectId,
            projectName: x.projectName,
            vintageId: x.vintageId,
            registryProjectId: x.registryProjectId,
          });
        }),
      };

      await this.redis.set(
        `BYO-${estimateId}`,
        JSON.stringify(cachedData),
        'EX',
        3600, // 1 hour
      );
    }

    addToLog(req, {
      forRetirement: data.forRetirement,
      riskAdjusted: data.riskAdjusted,
      alternatives: alternatives.map((x) => ({ price: x.pricing, alloc: x.allocations, type: adjustmentName(x.type) })),
    });

    // remove alternatives that are identical
    const vintageQuantities = alternatives.map((alt) => alt.df.select('vintageId', 'quantity').sort('vintageId'));
    const selectedAlternatives: typeof alternatives = [];
    for (let i = 0; i < vintageQuantities.length; i++) {
      let foundMatch = false;
      for (let j = 0; j < i; j++) {
        if (vintageQuantities[i].frameEqual(vintageQuantities[j])) {
          foundMatch = true;
          break;
        }
      }
      if (!foundMatch) selectedAlternatives.push(alternatives[i]);
    }

    return {
      price: undefined,
      id: unmodifiedEstimateId,
      alternatives: selectedAlternatives.map((alt) => ({
        id: alt.id,
        type: alt.type,
        price: alt.pricing.priceEstimate.toFixed(4) as any,
        allocation: alt.allocations.map((x) => ({
          projectId: x.projectId,
          vintageId: x.vintageId,
          quantity: x.quantity,
          delta: x.delta,
        })),
      })),
    };
  }

  /**
   * generate a price estimate for a set of PricingEstimateAllocation for a User
   *
   * steps:
   * 1. validate that allocations are present and have required price and buffer information
   * 2. retrieve price and buffer data for platform Vintages from the database
   * 3. join user-provided and database data into a DataFrame
   * 4. validate that all required prices and buffers are present
   * 5. compute the price estimate using privateAllocationPrice
   * 6. log and return the computed estimate
   *
   * @param {AdminPricingEstimateRequest[]} data
   * @param {Request} req
   * @param {BufferComputationEnum} bufferCalculationType
   * @returns {Promise<AdminPricingEstimateResponse | ErrorResponse>}
   */
  async generateEstimate(
    data: AdminPricingEstimateRequest[],
    req: Request,
    bufferCalculationType: BufferComputationEnum = DEFAULT_BUFFER_COMPUTATION_TYPE,
  ): Promise<AdminPricingEstimateResponse | ErrorResponse> {
    if (data.length === 0) {
      return {
        error: 'Cannot generate estimate: No vintages selected',
      };
    }

    // make sure every item has a vintage id or a price and a buffer (can be zero)
    if (data.some((x) => x.vintageId == null && x.price === undefined)) {
      return {
        error: 'Cannot generate estimate: Missing price information for non-platform vintage',
      };
    }
    if (data.some((x) => x.vintageId == null && (x.buffer === undefined || x.bufferCategoryId === undefined))) {
      return {
        error: 'Cannot generate estimate: Missing buffer information for non-platform vintage',
      };
    }

    // get the vintage information
    const knex = this.em.getKnex();

    // platform vintages
    const vintages = data.filter((x) => x.vintageId !== undefined);

    let df = pl.readRecords([], {
      schema: {
        vintageId: new String(),
        dbPrice: new Float64(),
        dbCost: new Float64(),
        dbRiskBufferPercentage: new Float64(),
        dbBufferCategoryId: new String(),
      },
    });

    if (vintages.length > 0) {
      df = pl.readRecords(
        await knex
          .raw(
            `
          SELECT
            project_vintages.id,
            project_vintage_prices_v2.price::double precision,
            project_vintage_prices_v2.average_cost_basis::double precision as cost_basis,
            risk_buffer_percentage::double precision,
            buffer_categories.id as buffer_category_id,
            projects.id as project_id
          FROM ${environment.db.schema.rubicon}.project_vintages
          JOIN ${environment.db.schema.rubicon}.project_vintage_prices_v2
	          ON project_vintage_prices_v2.vintage_id = project_vintages.id
          JOIN ${environment.db.schema.rubicon}.projects ON project_vintages.project_id = projects.id
          JOIN ${environment.db.schema.rubicon}.project_types ON project_types.id = projects.project_type_id
          LEFT JOIN ${environment.db.schema.rubicon}.buffer_categories
              ON buffer_categories.id = COALESCE(projects.buffer_category_id, project_types.buffer_category_id)
          WHERE
            project_vintages.id IN (${vintages.map(() => '?').join(',')})
          `,
            vintages.map((x) => x.vintageId),
          )
          .then((r) =>
            r.rows.map((x: any) => ({
              vintageId: x.id,
              dbPrice: x.price,
              dbCost: x.cost_basis,
              dbRiskBufferPercentage: x.risk_buffer_percentage,
              dbBufferCategoryId: x.buffer_category_id,
              projectId: x.project_id,
            })),
          ),
        {
          schema: {
            vintageId: new String(),
            dbPrice: new Float64(),
            dbCost: new Float64(),
            dbRiskBufferPercentage: new Float64(),
            dbBufferCategoryId: new String(),
            projectId: new String(),
          },
        },
      );
    }

    // check for missing prices and buffers
    if (vintages.length != df.height) return { error: 'Cannot generate estimate: Missing vintage information' };

    // buffer prices
    const bufferCategoryAmounts = await this.privateGetBufferCategoryPrices();

    df = pl
      .readRecords(
        data.map((x) => ({ ...x, price: x.price?.toNumber() })),
        {
          schema: {
            bufferCategoryId: new String(),
            price: new Float64(),
            cost: new Float64(),
            buffer: new Float64(),
            quantity: new Int64(),
            vintageId: new String(),
          },
        },
      )
      .join(df, { on: 'vintageId', how: 'left' })
      .select(
        'vintageId',
        'quantity',
        'projectId',
        pl.when(pl.col('price').isNotNull()).then(pl.col('price')).otherwise(pl.col('dbPrice')).alias('price'),
        pl.when(pl.col('cost').isNotNull()).then(pl.col('cost')).otherwise(pl.col('dbCost')).alias('cost'),
        pl
          .when(pl.col('buffer').isNotNull())
          .then(pl.col('buffer'))
          .otherwise(pl.col('dbRiskBufferPercentage'))
          .alias('riskBufferPercentage'),
        pl
          .when(pl.col('bufferCategoryId').isNotNull())
          .then(pl.col('bufferCategoryId'))
          .otherwise(pl.col('dbBufferCategoryId'))
          .alias('bufferCategoryId'),
      )
      .join(bufferCategoryAmounts, { on: 'bufferCategoryId', how: 'left' });

    if (df.getColumn('price').nullCount() > 0)
      return { error: 'Cannot generate estimate: Missing vintage prices information' };
    if (
      df.getColumn('riskBufferPercentage').nullCount() > 0 ||
      (bufferCalculationType === BufferComputationEnum.CATEGORIES &&
        df
          .select(pl.col('bufferUnitPrice').isNull().and(pl.col('riskBufferPercentage').greaterThan(0)).alias('bad'))
          .filter(pl.col('bad').eq(true)).height > 0)
    )
      return { error: 'Cannot generate estimate: Missing buffers' };

    const computed = this.privateAllocationPrice(df, {
      includeCosts: true,
      forRetirement: true,
      bufferCalculationType,
    });
    addToLog(req, computed);

    return computed;
  }

  /**
   * seeded random number generator (sfc32)
   *
   * steps:
   * 1. initialize state with four numbers
   * 2. update state using bitwise and arithmetic operations
   * 3. return a function that produces a pseudo-random number between 0 and 1
   *
   * @param {number} a
   * @param {number} b
   * @param {number} c
   * @param {number} d
   * @returns {() => number}
   */
  sfc32(a: number, b: number, c: number, d: number) {
    return function (): number {
      a >>>= 0;
      b >>>= 0;
      c >>>= 0;
      d >>>= 0;
      let t = (a + b) | 0;
      a = b ^ (b >>> 9);
      b = (c + (c << 3)) | 0;
      c = (c << 21) | (c >>> 11);
      d = (d + 1) | 0;
      t = (t + d) | 0;
      c = (c + t) | 0;
      return (t >>> 0) / 4294967296;
    };
  }

  /* private */

  /**
   * adjust allocations for a DataFrame using a given AllocationAdjustmentEnum
   *
   * steps:
   * 1. group allocations by Project and sum quantities
   * 2. determine adjustment method based on AllocationAdjustmentEnum
   * 3. apply adjustment (clip, scale, or clip and redistribute) to each Project
   * 4. allocate adjusted quantities to Vintages using the default BYOVintageDistributionMethod
   * 5. return a DataFrame with adjusted allocations
   *
   * @param {pl.DataFrame} df
   * @param {AllocationAdjustmentEnum} adjustmentType
   * @returns {pl.DataFrame}
   */
  private privateAdjustedAllocations(df: pl.DataFrame, adjustmentType: AllocationAdjustmentEnum): pl.DataFrame {
    // this code assumes that the limits are breached
    const dfProjects = df.groupBy('projectId').agg(pl.col('quantity').sum(), pl.first('limitHigh'));

    const total = dfProjects.getColumn('quantity').sum();

    const alloc = ((): { projectId: string; quantity: number; delta: number }[] => {
      let adjusted = dfProjects.toRecords().map((x) => ({
        projectId: x.projectId,
        quantity: x.quantity,
        delta: 0,
        initial: x.quantity,
        max: Math.floor(x.limitHigh),
      }));

      switch (adjustmentType) {
        case AllocationAdjustmentEnum.None:
          return [];

        case AllocationAdjustmentEnum.Clip:
          adjusted = adjusted.map((x) => ({
            ...x,
            quantity: Math.min(x.quantity, x.max),
            delta: Math.min(x.quantity, x.max) - x.quantity,
          }));
          return adjusted;

        case AllocationAdjustmentEnum.ClipAndRedistribute:
          // check if we have enough, otherwise pass nothing
          if (sum(adjusted.map((x) => x.max)) < total) return [];

          // clip
          adjusted = sortBy(
            adjusted.map((x) => ({
              ...x,
              quantity: Math.min(x.quantity, x.max),
            })),
            (x) => x.projectId,
          );
          let diff = total - sum(adjusted.map((x) => x.quantity));
          let canAdjust = adjusted.filter((x) => x.quantity < x.max).length;

          while (canAdjust > 0 && diff > 0) {
            // scale up and clip
            adjusted = adjusted.map((x) => ({
              ...x,
              quantity: Math.ceil(Math.min(x.quantity + diff / canAdjust, x.max)),
            }));

            diff = total - sum(adjusted.map((x) => x.quantity));
            canAdjust = adjusted.filter((x) => x.quantity < x.max).length;
          }

          let idx = 0;
          while (diff < 0) {
            idx = (idx + 1) % adjusted.length;
            if (adjusted[idx].quantity > adjusted[idx].initial)
              adjusted[idx].quantity = Math.max(adjusted[idx].quantity - 1, 0);
            diff = total - sum(adjusted.map((x) => x.quantity));
          }

          adjusted.forEach((x) => {
            x.quantity = Math.min(x.quantity, x.max);
            x.delta = x.quantity - x.initial;
          });

          return adjusted;

        case AllocationAdjustmentEnum.Scale:
          const ratio = Math.min(...adjusted.map((x) => x.max / x.quantity), 1.0);
          adjusted = adjusted.map((x) => ({
            ...x,
            quantity: Math.min(Math.ceil(x.quantity * ratio), x.max),
            delta: Math.min(Math.ceil(x.quantity * ratio), x.max) - x.quantity,
          }));
          return adjusted;
      }
    })();

    // allocate to vintages, older first
    const d = df
      .join(
        pl
          .readRecords(alloc, { schema: { projectId: new String(), quantity: new Int64() } })
          .select('projectId', 'quantity')
          .withColumnRenamed('quantity', 'projectTotal'),
        { on: ['projectId'] },
      )
      .sort(['projectId', 'vintage'])
      .select('projectId', 'vintageId', 'vintageHoldings', 'projectTotal', 'quantity')
      .withColumnRenamed('quantity', 'wanted')
      .withColumnRenamed('vintageHoldings', 'vintageMax')
      .groupBy('projectId')
      .aggList();

    const clipVintages = ((): pl.DataFrame => {
      switch (DEFAULT_BYO_VINTAGE_DISTRIBUTION) {
        case BYOVintageDistributionMethod.PROPORTIONAL: {
          return pl
            .readRecords(
              d.toRecords().map((x) => {
                const m: number = x.projectTotal.at(0);

                // limit to what we wanted initially, starting on older vintages
                let start: number[] = x.vintageMax.map((vM: number, i: number) => Math.min(vM, x.wanted.at(i)));

                // proportions
                const s = sum(start);
                const proportions = start.map((x) => x / s);

                // scale to ensure we don't have more than the total
                if (s > m) {
                  const factor = s / m;
                  start = start.map((x) => Math.floor(x / factor));
                }

                // fill proportionally
                while (sum(start) < m - start.length) {
                  const gap = m - sum(start);
                  start = start.map((x, i) => Math.floor(x + gap * proportions[i]));
                }
                for (let i = 0; i < start.length; i++) {
                  if (sum(start) < m) start[i] += 1;
                }

                x.quantity = start;

                return x;
              }),
              {
                schema: { projectId: new String(), vintageId: new List(new String()), quantity: new List(new Int64()) },
              },
            )
            .explode(['vintageId', 'quantity'])
            .withColumnRenamed('quantity', 'quantityNew');
        }
        case BYOVintageDistributionMethod.OLDER_FIRST: {
          return pl
            .readRecords(
              d.toRecords().map((x) => {
                const m: number = x.projectTotal.at(0);
                let runningSum = 0;
                // limit to what we wanted initially, starting on older vintages
                const start: number[] = x.vintageMax.map((vM: number, i: number) => Math.min(vM, x.wanted.at(i)));
                x.quantity = start.map((q: number) => {
                  const r = Math.min(q, Math.max(m - runningSum, 0));
                  runningSum += q;
                  return r;
                });
                // if there's still a gap, start filling from the older vintages
                x.quantity.forEach((q: number, i: number) => {
                  const total = sum(x.quantity);
                  const gap = m - total;
                  x.quantity[i] = Math.min(q + gap, x.vintageMax[i]);
                });
                return x;
              }),
              {
                schema: { projectId: new String(), vintageId: new List(new String()), quantity: new List(new Int64()) },
              },
            )
            .explode(['vintageId', 'quantity'])
            .withColumnRenamed('quantity', 'quantityNew');
        }
      }
    })();

    return df
      .join(clipVintages, { on: 'vintageId' })
      .withColumns(pl.col('quantity').alias('originalQuantity'), pl.col('quantityNew').alias('quantity'))
      .drop('quantityNew');
  }

  /**
   * calculate allocation price and buffer for a DataFrame
   *
   * steps:
   * 1. sum total price and cost for all allocations
   * 2. calculate buffer price and cost based on BufferComputationEnum
   * 3. compute unit price, unit cost, and markup
   * 4. apply markup and rounding to final price
   * 5. return AllocationPrice (and AllocationCost if includeCosts is true)
   *
   * @param {pl.DataFrame} df
   * @param {{ includeCosts: IC; bufferCalculationType: BufferComputationEnum; forRetirement: boolean }} options
   * @returns {IC extends true ? AllocationPrice & AllocationCost : AllocationPrice}
   */
  private privateAllocationPrice<IC extends boolean>(
    df: pl.DataFrame,
    options: { includeCosts: IC; bufferCalculationType: BufferComputationEnum; forRetirement: boolean },
  ): IC extends true ? AllocationPrice & AllocationCost : AllocationPrice {
    return this.privateAllocationPriceNew(df, options);
  }

  /**
   * calculate allocation price and buffer for a DataFrame
   *
   * steps:
   * 1. sum total price and cost for all allocations
   * 2. calculate buffer price and cost based on BufferComputationEnum
   * 3. compute unit price, unit cost, and markup
   * 4. apply markup and rounding to final price
   * 5. return AllocationPrice (and AllocationCost if includeCosts is true)
   *
   * @param {pl.DataFrame} df
   * @param {{ includeCosts: IC; bufferCalculationType: BufferComputationEnum; forRetirement: boolean }} options
   * @returns {IC extends true ? AllocationPrice & AllocationCost : AllocationPrice}
   */
  private privateAllocationPriceNew<IC extends boolean>(
    df: pl.DataFrame,
    options: { includeCosts: IC; bufferCalculationType: BufferComputationEnum; forRetirement: boolean },
  ): IC extends true ? AllocationPrice & AllocationCost : AllocationPrice {
    // pl.Config.setTblCols(-1);

    switch (options.bufferCalculationType) {
      case BufferComputationEnum.PROPORTIONAL_TO_ALLOCATION:
        df = df.withColumn(
          pl
            .col('riskBufferPercentage')
            .mul(pl.col('price').mul(pl.col('quantity')))
            .alias('riskBufferTotalPrice'),
        );
        if (options.includeCosts) {
          df = df.withColumn(
            pl
              .col('cost')
              .mul(pl.col('quantity').mul(pl.col('riskBufferPercentage')))
              .alias('riskBufferCost'),
          );
        }
        break;

      case BufferComputationEnum.CATEGORIES:
        df = df.withColumn(
          pl
            .col('riskBufferPercentage')
            .mul(pl.col('quantity'))
            .mul(pl.col('bufferUnitPrice').fillNull(0))
            .alias('riskBufferTotalPrice'),
        );
        if (options.includeCosts) {
          df = df.withColumn(
            pl
              .col('riskBufferPercentage')
              .mul(pl.col('quantity'))
              .mul(pl.col('bufferUnitCost').fillNull(0))
              .alias('riskBufferCost'),
          );
        }
        break;
    }

    df = df
      .withColumn(pl.col('price').mul(pl.col('quantity')).add(pl.col('riskBufferTotalPrice')).alias('totalPrice'))
      .withColumn(pl.col('totalPrice').div(pl.col('quantity')).alias('unitPrice'));
    if (options.includeCosts) {
      df = df
        .withColumn(pl.col('cost').mul(pl.col('quantity')).alias('totalCost'))
        .withColumn(pl.col('totalCost').add('riskBufferCost').div(pl.col('quantity')).alias('unitCost'));
    }

    const totalQuantity = df.getColumn('quantity').sum();

    df = this.privateAddMarkupsColumn(df, options);

    if (options.includeCosts) {
      const result: AllocationPrice & AllocationCost = {
        priceEstimate: new Decimal(
          df
            .select(pl.col('totalPrice').mul(pl.col('markup')).div(pl.col('discount')).alias('value'))
            .getColumn('value')
            .sum(),
        ),
        costEstimate: new Decimal(df.getColumn('totalCost').sum() + df.getColumn('riskBufferCost').sum()),
        unitPrice: new Decimal(df.select(pl.col('totalPrice').alias('value')).getColumn('value').sum() / totalQuantity),
        unitCost: new Decimal((df.getColumn('totalCost').sum() + df.getColumn('riskBufferCost').sum()) / totalQuantity),
        totalQuantity: totalQuantity,
        markup: new Decimal(
          df
            .select(pl.col('quantity').mul(pl.col('markup').sub(1.0)).alias('value'))
            .getColumn('value')
            .sum() / totalQuantity,
        ),
        discount: new Decimal(
          df
            .select(pl.col('quantity').mul(pl.col('discount').sub(1.0)).alias('value'))
            .getColumn('value')
            .sum() / totalQuantity,
        ),
        bufferPrice: new Decimal(df.getColumn('riskBufferTotalPrice').sum()),
        bufferCost: new Decimal(df.getColumn('riskBufferCost').sum()),
      };
      return result;
    } else {
      const result: AllocationPrice = {
        priceEstimate: new Decimal(
          df
            .select(pl.col('totalPrice').mul(pl.col('markup')).div(pl.col('discount')).alias('value'))
            .getColumn('value')
            .sum(),
        ),
        unitPrice: new Decimal(df.select(pl.col('totalPrice').alias('value')).getColumn('value').sum() / totalQuantity),
        totalQuantity: totalQuantity,
        markup: new Decimal(
          df
            .select(pl.col('quantity').mul(pl.col('markup').sub(1.0)).alias('value'))
            .getColumn('value')
            .sum() / totalQuantity,
        ),
        discount: new Decimal(
          df
            .select(pl.col('quantity').mul(pl.col('discount').sub(1.0)).alias('value'))
            .getColumn('value')
            .sum() / totalQuantity,
        ),
        bufferPrice: new Decimal(df.getColumn('riskBufferTotalPrice').sum()),
      };
      return result as any;
    }
  }

  /**
   * generate a seeded hash array from a string for random number generation
   *
   * steps:
   * 1. initialize four hash values
   * 2. iterate over each character in the string and update hash values
   * 3. perform final mixing of hash values
   * 4. return an array of four numbers
   *
   * @param {string} str
   * @returns {number[]}
   */
  // see https://stackoverflow.com/questions/521295/seeding-the-random-number-generator-in-javascript
  private privateCyrb128(str: string): number[] {
    let h1 = 1779033703,
      h2 = 3144134277,
      h3 = 1013904242,
      h4 = 2773480762;
    for (let i = 0, k; i < str.length; i++) {
      k = str.charCodeAt(i);
      h1 = h2 ^ Math.imul(h1 ^ k, 597399067);
      h2 = h3 ^ Math.imul(h2 ^ k, 2869860233);
      h3 = h4 ^ Math.imul(h3 ^ k, 951274213);
      h4 = h1 ^ Math.imul(h4 ^ k, 2716044179);
    }
    h1 = Math.imul(h3 ^ (h1 >>> 18), 597399067);
    h2 = Math.imul(h4 ^ (h2 >>> 22), 2869860233);
    h3 = Math.imul(h1 ^ (h3 >>> 17), 951274213);
    h4 = Math.imul(h2 ^ (h4 >>> 19), 2716044179);
    h1 ^= h2 ^ h3 ^ h4;
    h2 ^= h1;
    h3 ^= h1;
    h4 ^= h1;
    return [h1 >>> 0, h2 >>> 0, h3 >>> 0, h4 >>> 0];
  }

  /**
   * retrieve buffer category prices from the database as a DataFrame
   *
   * steps:
   * 1. query the database for BufferCategory holdings, prices, and costs
   * 2. aggregate and join BufferCategory data
   * 3. return a DataFrame with BufferCategory prices and costs
   *
   * @returns {Promise<pl.DataFrame>}
   */
  private async privateGetBufferCategoryPrices(): Promise<pl.DataFrame> {
    const knex = this.em.getKnex();

    return pl.readRecords(
      await knex
        .raw(
          `
          SELECT DISTINCT ON (buffer_category_id)
            *    
          FROM (
            WITH holdings_by_buffer_category AS (
            SELECT
                project_vintages.id,
                COALESCE(projects.buffer_category_id, project_types.buffer_category_id) AS buffer_category_id,
                asset_composition_v2.holdings,
                price,
                asset_composition_v2.holdings * price AS value,
                average_cost_basis as cost_basis,
                asset_composition_v2.holdings * average_cost_basis AS cost_value,
                books.type as source
              FROM ${environment.db.schema.rubicon}.books
            JOIN ${environment.db.schema.rubicon}.asset_composition_v2 ON owner_id = books.id
            JOIN ${environment.db.schema.rubicon}.project_vintages ON asset_composition_v2.component_id = project_vintages.id
            JOIN ${environment.db.schema.rubicon}.projects ON projects.id = project_vintages.project_id
            JOIN ${environment.db.schema.rubicon}.project_types ON project_types.id = projects.project_type_id
            LEFT JOIN ${environment.db.schema.rubicon}.project_vintage_prices_v2 ON project_vintage_prices_v2.vintage_id = project_vintages.id
            WHERE
                books.type IN (?, ?) AND asset_composition_v2.asset_type = ?
            )
            SELECT
              buffer_category_id,
              buffer_categories.name,
              SUM(value)::double precision AS total_value,
              (SUM(value) / SUM(holdings))::double precision AS unit_value,
              SUM(cost_value)::double precision as total_cost,
              (SUM(cost_value) / SUM(holdings))::double precision AS unit_cost,
              source
            FROM holdings_by_buffer_category
            LEFT JOIN ${environment.db.schema.rubicon}.buffer_categories
                  ON buffer_categories.id = holdings_by_buffer_category.buffer_category_id
            GROUP BY buffer_category_id, buffer_categories.name, source
          ) Q
          ORDER BY buffer_category_id, source
          `,
          [BookType.PORTFOLIO_DEFAULT, BookType.REHABILITATION_DEFAULT, AssetType.REGISTRY_VINTAGE],
        )
        .then((r) =>
          r.rows.map((x: any) => ({
            bufferCategoryId: x.buffer_category_id,
            bufferUnitPrice: x.unit_value,
            bufferUnitCost: x.unit_cost,
          })),
        ),
      { schema: { bufferCategoryId: new String(), bufferUnitPrice: new Float64(), bufferUnitCost: new Float64() } },
    );
  }

  markupUnitPrice(unitPrice: number, forRetirement: boolean): number {
    let markup = 0.5;
    if (unitPrice > 40) {
      markup = 0.2;
    } else if (unitPrice > 10) {
      markup = (50 - (unitPrice - 10)) / 100.0;
    }

    if (!forRetirement) {
      markup += 0.05;
    }

    return markup;
  }

  private privateAddMarkupsColumn(df: pl.DataFrame, options: { forRetirement: boolean }): pl.DataFrame {
    df = df.withColumn(
      pl
        //MTM < $10 → 50% markup
        .when(pl.col('unitPrice').lt(10))
        .then(pl.lit(0.5))
        .otherwise(
          // MTM $10 – $40 →  50% - (MTM - 10)%
          pl
            .when(pl.col('unitPrice').lt(40))
            .then(pl.lit(50).sub(pl.col('unitPrice').sub(10)).div(100.0))
            // MTM ≥ $40 → 20% markup
            .otherwise(pl.lit(0.2)),
        )
        .add(pl.lit(1))
        .alias('markup'),
    );

    const nProjects = df.getColumn('projectId').unique().length;

    if (nProjects < 5) {
      df = df.withColumn(pl.lit(1).alias('discount'));
    } else {
      df = df.withColumn(
        pl
          .when(pl.col('unitPrice').mul(pl.col('markup')).lt(10))
          .then(pl.lit(0.1))
          .otherwise(
            pl
              .when(pl.col('unitPrice').mul(pl.col('markup')).lt(40))
              .then(pl.lit(0.08))
              .otherwise(pl.lit(0.06)),
          )
          .add(pl.lit(1))
          .alias('discount'),
      );
    }

    // NOTE: WAIT FOR APPROVAL ON DISCOUNT >>>
    df = df.withColumn(pl.lit(1).alias('discount'));
    // NOTE: WAIT FOR APPROVAL ON DISCOUNT <<<

    df = df.withColumn(pl.col('markup').div(pl.col('discount')).alias('multiplier'));

    if (!options.forRetirement) {
      df = df.withColumn(pl.col('markup').add(0.05).alias('markup'));
    }

    return df;
  }
}
