/* third party */
import { RedisService } from '@liaoliaots/nestjs-redis';
import { FilterQuery, FindOptions, LockMode } from '@mikro-orm/core';
import { EntityManager } from '@mikro-orm/postgresql';
import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { Redis } from 'ioredis';
import { sortBy, sum } from 'lodash';
/* rubicon */
import {
  AssetFlowStatus,
  AssetType,
  AuditLogAction,
  BaseRetirementOrderBy,
  BookAction,
  BookRelations,
  BookType,
  BulkLedgerTransactionsResponse,
  ErrorMessageEnum,
  NotificationEvent,
  OrganizationUserRole,
  PendingLedgerTransactionResponse,
  PermissionEnum,
  RetirementOrderByOptions,
  RetirementRelations,
  RetirementStatus,
  RetirementType,
  TransactionType,
  uuid,
} from '@rubiconcarbon/shared-types';
/* env */
import { environment } from '@env/environment';
/* app */
import { AuthService } from '@app/auth';
import { UserClaims } from '@app/auth/interfaces';
import {
  InternalTransactionBase,
  NewRetirementRequest,
  RetirementVerificationCode,
} from '@app/dtos/sendgrid-payload.dto';
import {
  Asset,
  AssetFlow,
  Book,
  CreditFlow,
  CustomerPortfolio,
  Project,
  ProjectVintage,
  Retirement,
  RetirementLink,
  Transaction,
  User,
} from '@app/entities';
import { getPortalUserWithOrganizationFromClaims, groupBy } from '@app/helpers';
import { generateRandomKey, randomNumberString } from '@app/helpers/random.helper';
import {
  CachedRetirementTransaction,
  CalculateEstimateRetirementAmount,
  InternalRetirementQueryResponse,
  InternalRetirementTransaction,
} from '@app/interfaces/retirement.interface';
import { LedgerRetirementsService } from '@app/ledger/services/ledger-retirements.service';
import { LedgerTransactionsService } from '@app/ledger/services/ledger-transactions.service';
import { AuditLogsService } from '@app/utility/audit-log';
import { NotificationsService } from './notifications.service';
import { SendgridService } from './sendgrid.service';
import Decimal from 'decimal.js';
import { AssetsService } from './assets.service';
import { InternalAllocationResponse, InternalHolding } from '@app/interfaces/allocation.interface';
import { PurchasesRetirementsService } from './purchases-retirements.service';
import {
  AdminRetirementLinkUpdateRequestDTO,
  AdminRetirementQueryDTO,
  AdminRetirementRelationsQueryDTO,
  AdminSuggestedBufferElementDTO,
  AdminSuggestedBufferRetirementResponseDTO,
  AdminUpdateRetirementAmountsRequestDTO,
  PortalRetirementRequestDTO,
} from '@app/dtos/retirement.dto';
import { OrderByDirectionSQL } from '@app/enums/orderby.enum';
import { InternalPopulateAllocationsService } from './allocations-populate.service';
import { InternalBookResponse } from '@app/interfaces/book.interface';
import { CreditFlowsService } from './credit-flows.service';
import { BufferCategory } from '@app/entities/buffer-category.entity';
import { toTrimmedProjectVintageResponseDTO } from '@app/dtos/project-vintage.dto';
import { InternalPortalUser } from '@app/interfaces/user.interface';

@Injectable()
export class RetirementsService {
  private redis: Redis;

  constructor(
    private assetsService: AssetsService,
    private auditLogsService: AuditLogsService,
    private authService: AuthService,
    private creditFlowsService: CreditFlowsService,
    private readonly em: EntityManager,
    private ledgerRetirementsService: LedgerRetirementsService,
    private ledgerTransactionsService: LedgerTransactionsService,
    private notificationsService: NotificationsService,
    private populateAllocationsService: InternalPopulateAllocationsService,
    private purchasesRetirementsService: PurchasesRetirementsService,
    private readonly redisService: RedisService,
    private readonly sendgrid: SendgridService,
  ) {
    this.redis = this.redisService.getClient();
  }

  /**
   * call from portal to request a Retirement entity for the user's Organization
   *
   * steps:
   * 1. generate a random token for verification
   * 2. find the OrganizationUser entity for the user by email
   * 3. check that the OrganizationUser exists and has the correct OrganizationUserRole
   * 4. validate that transfer outflows are only for AssetType.REGISTRY_VINTAGE assets
   * 5. find the CustomerPortfolio entity for the Organization
   * 6. check that the CustomerPortfolio and its Book allow BookAction.RETIRE
   * 7. retrieve all InternalHolding allocations for the requested assets
   * 8. for each asset, validate AssetAllocation existence, type, and available amount
   * 9. save the Retirement request to Redis with a 10-minute expiration
   * 10. send a verification email using SendgridService
   * 11. return the original RetirementRequestDTO data
   *
   * @param {PortalRetirementRequestDTO} data
   * @param {UserClaims} claims
   * @returns {Promise<PortalRetirementRequestDTO>}
   * @throws UnprocessableEntityException, ForbiddenException, BadRequestException, ConflictException
   */
  async portalCacheCreateRetirementRequest(
    data: PortalRetirementRequestDTO,
    claims: UserClaims,
  ): Promise<PortalRetirementRequestDTO> {
    const token = randomNumberString(6);
    const portalUser: InternalPortalUser = await getPortalUserWithOrganizationFromClaims(this.em, claims);
    if (
      !portalUser.organizationUser.roles.some((s) =>
        [OrganizationUserRole.TRANSACTOR, OrganizationUserRole.MANAGER].includes(s),
      )
    ) {
      throw new ForbiddenException('user must have correct permissions to request a retirement');
    }
    if (data.type === RetirementType.TRANSFER_OUTFLOW && data.assetType !== AssetType.REGISTRY_VINTAGE) {
      // transfer outflows can only be done on registry vintage assets
      throw new UnprocessableEntityException(
        `transfers to customer registry account can only be done on ${AssetType.REGISTRY_VINTAGE} assets`,
      );
    }

    // get single source
    const customerPortfolio = await this.em.findOne(
      CustomerPortfolio,
      { organization: portalUser.organizationUser.organization.id },
      { populate: ['book', 'organization'] },
    );

    // should never happen
    if (!customerPortfolio || !customerPortfolio.book.allowedActions.includes(BookAction.RETIRE)) {
      throw new BadRequestException([`customer organization must have allowedAction for retirement`]);
    }

    // get all asset allocations
    const allocations: InternalHolding[] = await this.assetsService.internalGetRetirementHoldings(
      this.em.getKnex(),
      customerPortfolio.book.id,
      data.assets,
    );

    for (const asset of data.assets) {
      // get asset allocation
      const assetAllocation = allocations.find((f) => f.asset_id === asset.assetId);
      if (!assetAllocation) {
        throw new ConflictException(`AssetAllocation not found for asset ${asset.assetId}`);
      } else if (assetAllocation.asset_type !== data.assetType) {
        throw new UnprocessableEntityException(`all assets must be of the assetType ${data.assetType}`);
      }

      // validate amount for asset
      if (assetAllocation.amount_available < asset.amount) {
        throw new UnprocessableEntityException(
          `customer organization ${customerPortfolio.organization.name} must have amountAvaliable greater than or equal to ${asset.amount} for ${assetAllocation.asset_type} asset ${assetAllocation.asset_id}`,
        );
      }
    }

    const cache: CachedRetirementTransaction = {
      ...data,
      organizationId: customerPortfolio.organization.id,
      requestedBy: portalUser,
      token,
    };

    // save to redis
    await this.redis.set(
      `RETIREMENT-${claims.email}:${customerPortfolio.organization.id}`,
      JSON.stringify(cache),
      'EX',
      600, // 10 minutes
    );

    await this.sendgrid.createAndSendEmailTemplate(
      portalUser.email,
      environment.sendgrid.sender.verification,
      environment.sendgrid.template.retirementVerification,
      new RetirementVerificationCode(token),
    );

    return data;
  }

  /**
   * estimate what the Retirement would look like on a Book entity, only does calculations
   *
   * steps:
   * 1. find the Book entity by bookId
   * 2. check that the Book assetType is AssetType.RCT
   * 3. call the privateRetirementAlgorithm to calculate allocations for the requested amount
   * 4. filter out allocations with zero amountTransacted
   * 5. return the calculated allocations
   *
   * @param {uuid} bookId
   * @param {number} amountRequested
   * @returns {Promise<CalculateEstimateRetirementAmount[]>}
   * @throws UnprocessableEntityException
   */
  // todo : needs to be rewritten
  // this method is just to estimate what the retirement would look like on a book
  // it only does calculations and should not affect the db in any way
  async calculateEstimateRetirementAmounts(
    bookId: uuid,
    amountRequested: number,
  ): Promise<CalculateEstimateRetirementAmount[]> {
    const rctBook = await this.em.findOneOrFail(Book, bookId);
    if (rctBook.assetType !== AssetType.RCT) {
      throw new UnprocessableEntityException(`Retirement cannot be made for ${rctBook.name}`);
    }

    // calculate amounts
    let allocations: CalculateEstimateRetirementAmount[] = await this.privateRetirementAlgorithm(
      this.em,
      rctBook.id,
      amountRequested,
    );

    // filter out vintages with a zero amount
    allocations = allocations.filter((x) => x.amountTransacted > 0);
    return allocations;
  }

  /**
   * calculate vintages to retire for the Retirement entity, only allowed if Retirement has RCT assets
   *
   * steps:
   * 1. start a database transaction
   * 2. find the Retirement entity by id and type RetirementType.RETIREMENT
   * 3. check that the Retirement status is RetirementStatus.ADMIN_REVIEW
   * 4. update the Retirement status to RetirementStatus.PORTFOLIO_MANAGER_REVIEW
   * 5. find AssetFlow entities for the Retirement and filter for AssetType.RCT
   * 6. for each RCT AssetFlow, calculate allocations and create new AssetFlow entities for vintages
   * 7. validate that the sum of vintage flows matches the Retirement amount
   * 8. create an AuditLog entry for the calculation
   * 9. send RCT vintage amounts to the ledger for pending status
   * 10. update the Retirement assetFlows
   * 11. send notification emails if needed
   * 12. handle errors and revert ledger transactions if necessary
   *
   * @param {uuid} id
   * @param {UserClaims} claims
   * @returns {Promise<Retirement>}
   * @throws BadRequestException, InternalServerErrorException, ConflictException
   */
  // this method calculates vintages to retire for the rct
  // if retirement doesn't have rcts, calculateRetirementAmounts is not allowed
  async calculateRetirementAmounts(id: uuid, claims: UserClaims): Promise<Retirement> {
    const now = new Date();
    let ledgerTransactionIds: uuid[] = [];

    try {
      const retirement = await this.em.transactional(async (tx) => {
        const retirement = await tx.findOneOrFail(
          Retirement,
          { id, type: RetirementType.RETIREMENT },
          {
            populate: ['customerPortfolio.organization'],
            lockMode: LockMode.PESSIMISTIC_WRITE,
            // MikroORM writes the query in such a way that it is incompatible with the lock if we don't specify the table. Here `r0` is the retirement table
            lockTableAliases: ['r0'],
          },
        );

        // validate retirement is the right status
        if (retirement.status !== RetirementStatus.ADMIN_REVIEW) {
          throw new BadRequestException(ErrorMessageEnum.STATUS_NOT_ADMIN_REVIEW);
        }

        // update retirement
        tx.assign(retirement, {
          updatedAt: now,
          status: RetirementStatus.PORTFOLIO_MANAGER_REVIEW,
        });

        // calculate get the assetFlows for each rct of the retirement
        const assetFlows: AssetFlow[] = await tx.find(
          AssetFlow,
          { transaction: id },
          { populate: ['asset', 'destination', 'source', 'transaction'] },
        );
        const rctFlows: AssetFlow[] = assetFlows.filter((f) => f.assetType === AssetType.RCT);

        // if no RctFlows, throw error since calculation is not allowed
        if (rctFlows.length > 0) {
          const vintageFlows: AssetFlow[] = [];
          for (const rctFlow of rctFlows) {
            const newAllocations: CalculateEstimateRetirementAmount[] = await this.privateRetirementAlgorithm(
              tx,
              rctFlow.asset.id,
              rctFlow.amount,
            );

            const amountTransacted = sum(newAllocations.map((m) => m.amountTransacted));
            if (amountTransacted !== rctFlow.amount) {
              // this should never happen but if it does, we want to fail so we don't accidentally retire too much or too little
              throw new InternalServerErrorException(
                `expected allocations amount ${amountTransacted} to match amount ${retirement.amount} for rct ${rctFlow.asset.id}`,
              );
            }
            for (const allocation of newAllocations) {
              if (allocation.amountTransacted === 0) {
                continue;
              }
              vintageFlows.push(
                tx.create(AssetFlow, {
                  id: uuid(),
                  createdAt: now,
                  updatedAt: now,
                  amount: allocation.amountTransacted,
                  asset: allocation.vintage.id,
                  assetType: AssetType.REGISTRY_VINTAGE,
                  destination: uuid(environment.rubicon.books.offsets),
                  rawPrice: new Decimal(0),
                  source: rctFlow.asset.id, // the source of the vintage must be from the rct book
                  status: AssetFlowStatus.PENDING,
                  transaction: id,
                  transactionSubtype: retirement.type,
                  transactionType: TransactionType.RETIREMENT,
                }),
              );
            }
          }
          const vintagesSum = Decimal.sum(...vintageFlows.map((m) => m.amount), 0).toNumber();
          if (vintagesSum !== retirement.amount) {
            throw new InternalServerErrorException(
              `vintage flow amounts ${vintagesSum} did not match retirement amount ${retirement.amount}`,
            );
          }

          await this.auditLogsService.create(tx, claims, AuditLogAction.RETIREMENT_WAITING_REVIEW, now, id, {
            vintageFlows,
          });

          // send rct vintage amounts to ledger to be held in pending
          const ledgerResponse: BulkLedgerTransactionsResponse =
            await this.ledgerRetirementsService.calculateRetirementAmountsTransaction(
              tx,
              now,
              retirement,
              vintageFlows,
            );
          ledgerTransactionIds = this.ledgerTransactionsService.getTransactionIdsFromBulkResponse(ledgerResponse);

          retirement.assetFlows = vintageFlows;
        } else {
          await this.auditLogsService.create(tx, claims, AuditLogAction.RETIREMENT_WAITING_REVIEW, now, id, {
            vintageFlows: assetFlows,
          });
        }
        return retirement;
      });

      // send email notifications
      if (claims.name !== 'init-console-script' && retirement.type === RetirementType.RETIREMENT) {
        await this.privateHandleNotificationsAndEmails(now, NotificationEvent.RETIREMENT_STATUS_UPDATED, retirement);
      }

      return retirement;
    } catch (e) {
      if (ledgerTransactionIds.length > 0) {
        await this.ledgerTransactionsService.revertTransactionsAfterFailedApi(
          id,
          now,
          ledgerTransactionIds,
          `calculating retirement ${id} amounts`,
        );
      }
      throw e;
    }
  }

  /**
   * cancels a retirement
   * @param {uuid} id
   * @param {UserClaims} claims
   * @returns {Promise<Retirement>}
   * @throws ConflictException, ForbiddenException
   */
  /**
   * cancel a Retirement entity
   *
   * steps:
   * 1. start a database transaction
   * 2. find the Retirement entity by id
   * 3. check that the Retirement status is valid for cancellation
   * 4. check user permissions for the current Retirement status
   * 5. cancel all AssetFlow entities for the Retirement
   * 6. delete related PurchasesRetirements entry
   * 7. update the Retirement status to RetirementStatus.CANCELED
   * 8. create an AuditLog entry for the cancellation
   * 9. call the ledger to cancel the Retirement transaction
   * 10. send notification emails if needed
   * 11. handle errors and revert ledger transactions if necessary
   *
   * @param {uuid} id
   * @param {UserClaims} claims
   * @returns {Promise<Retirement>}
   * @throws ConflictException, ForbiddenException
   */
  async cancelRetirement(id: uuid, claims: UserClaims): Promise<Retirement> {
    const now = new Date();
    let ledgerTransactionIds: uuid[] = [];

    try {
      const retirement = await this.em.transactional(async (tx) => {
        const knex = tx.getKnex();
        const retirement = await tx.findOneOrFail(
          Retirement,
          { id },
          {
            populate: ['customerPortfolio.organization'],
            lockMode: LockMode.PESSIMISTIC_WRITE,
            // MikroORM writes the query in such a way that it is incompatible with the lock if we don't specify the table. Here `r0` is the retirement table
            lockTableAliases: ['r0'],
          },
        );

        // validate current retirement status
        if (
          ![`${RetirementStatus.ADMIN_REVIEW}`, `${RetirementStatus.PORTFOLIO_MANAGER_REVIEW}`].includes(
            retirement.status,
          )
        ) {
          throw new ConflictException(
            `Retirement status must be one of [${RetirementStatus.ADMIN_REVIEW}, ${RetirementStatus.PORTFOLIO_MANAGER_REVIEW}]`,
          );
        }

        // validate user permissions depending on current status of retirement
        if (
          (retirement.status === RetirementStatus.ADMIN_REVIEW &&
            !(await this.authService.check(claims, PermissionEnum.RETIREMENTS_CLEAR_ADMIN_REVIEW, {
              route: 'retirements',
            }))) ||
          (retirement.status === RetirementStatus.PORTFOLIO_MANAGER_REVIEW &&
            !(await this.authService.check(claims, PermissionEnum.RETIREMENTS_CLEAR_PM_REVIEW, {
              route: 'retirements',
            })))
        ) {
          throw new ForbiddenException();
        }

        // cancel all asset flows (rcts and vintages)
        const assetFlows: AssetFlow[] = await tx.find(AssetFlow, { transaction: id }, { populate: ['source'] });
        assetFlows.forEach((f) => {
          f.updatedAt = now;
          f.status = AssetFlowStatus.CANCELED;
        });

        // delete related purchases_retirements entry
        await knex.raw(
          `delete from "${environment.db.schema.rubicon}".purchases_retirements where retirement_id='${retirement.id}';`,
        );

        tx.assign(retirement, {
          updatedAt: now,
          dateFinished: now,
          status: RetirementStatus.CANCELED,
        });

        await this.auditLogsService.create(tx, claims, AuditLogAction.RETIREMENT_CANCELED, now, id, {});

        const ledgerResponse: BulkLedgerTransactionsResponse =
          await this.ledgerRetirementsService.cancelRetirementTransaction(tx, retirement);
        ledgerTransactionIds = this.ledgerTransactionsService.getTransactionIdsFromBulkResponse(ledgerResponse);
        return retirement;
      });

      // send email notifications
      if (retirement.type === RetirementType.RETIREMENT) {
        await this.privateHandleNotificationsAndEmails(now, NotificationEvent.RETIREMENT_STATUS_UPDATED, retirement);
      }

      return retirement;
    } catch (e) {
      if (ledgerTransactionIds.length > 0) {
        await this.ledgerTransactionsService.revertTransactionsAfterFailedApi(
          id,
          now,
          ledgerTransactionIds,
          `canceling retirement ${id}`,
        );
      }
      throw e;
    }
  }

  /**
   * completes a retirement
   * @param {uuid} id
   * @param {UserClaims} claims
   * @returns {Promise<Retirement>}
   * @throws BadRequestException, InternalServerErrorException, ConflictException
   */
  /**
   * complete a Retirement entity
   *
   * steps:
   * 1. start a database transaction
   * 2. find the Retirement entity by id
   * 3. check that the Retirement status is RetirementStatus.PROCESSING
   * 4. find AssetFlow, Book, and ProjectVintage entities for the Retirement
   * 5. check that CustomerPortfolio and Organization are enabled
   * 6. create CreditFlow outflow entries for registry vintage AssetFlows
   * 7. validate that the sum of CreditFlow amounts matches the Retirement amount
   * 8. update the Retirement status to RetirementStatus.COMPLETED
   * 9. update AssetFlow entities to settled status
   * 10. create an AuditLog entry for completion
   * 11. call the ledger to settle the Retirement transaction
   * 12. send notification emails if needed
   * 13. handle errors and revert ledger transactions if necessary
   *
   * @param {uuid} id
   * @param {UserClaims} claims
   * @returns {Promise<Retirement>}
   * @throws BadRequestException, InternalServerErrorException, ConflictException
   */
  async completeRetirement(id: uuid, claims: UserClaims): Promise<Retirement> {
    const now = new Date();
    let ledgerTransactionIds: uuid[] = [];

    try {
      const retirement = await this.em.transactional(async (tx) => {
        const retirement = await tx.findOneOrFail(
          Retirement,
          { id },
          {
            populate: ['customerPortfolio.organization'],
            lockMode: LockMode.PESSIMISTIC_WRITE,
            // MikroORM writes the query in such a way that it is incompatible with the lock if we don't specify the table. Here `r0` is the retirement table
            lockTableAliases: ['r0'],
          },
        );

        if (retirement.status !== RetirementStatus.PROCESSING) {
          throw new BadRequestException(ErrorMessageEnum.STATUS_NOT_PROCESSING);
        }

        const assetFlows: AssetFlow[] = await tx.find(
          AssetFlow,
          { transaction: id },
          { populate: ['asset', 'destination', 'source', 'transaction'] },
        );
        const assetBooks = await tx.find(Book, { id: assetFlows.map((m) => m.id) });
        const assetVintages = await tx.find(ProjectVintage, { id: assetFlows.map((m) => m.id) });

        const projectVintages = await tx.find(
          ProjectVintage,
          { id: assetFlows.map((m) => m.asset.id) },
          { populate: ['creditInflows'] },
        );

        // get orgs related to a retirement
        if (!retirement.customerPortfolio.isEnabled || !retirement.customerPortfolio.organization?.isEnabled) {
          // or maybe we just have a warning? maybe we want to retire for them without enabling them
          throw new ConflictException(`CustomerPortfolio and Organization must be enabled to complete retirement`);
        }

        // update credit outflows
        const lits: CreditFlow[] = await this.creditFlowsService.createCreditOutflowEntry(
          tx,
          assetFlows.filter((f) => f.assetType === AssetType.REGISTRY_VINTAGE),
          projectVintages,
        );

        // sanity check
        // note (TD-58) : this is sometimes flaky in e2e line 583 AdminRetirementsController
        if (sum(lits.map((x) => -x.amount)) != retirement.amount) {
          throw new InternalServerErrorException(
            `expected credit inflows sum ${sum(lits.map((x) => -x.amount))} to match total amount requested ${
              retirement.amount
            }`,
          );
        }

        tx.assign(retirement, {
          updatedAt: now,
          dateFinished: now,
          status: RetirementStatus.COMPLETED,
        });
        assetFlows.forEach((af) => {
          af.detailedAsset =
            // todo (3163) : fix
            af.assetType === AssetType.RCT
              ? assetBooks.find((asset) => asset.id === af.asset.id)
              : af.assetType === AssetType.REGISTRY_VINTAGE
                ? assetVintages.find((asset) => asset.id === af.asset.id)
                : undefined;
          af.settledAt = now;
          af.status = AssetFlowStatus.SETTLED;
          af.updatedAt = now;
        });

        await this.auditLogsService.create(tx, claims, AuditLogAction.RETIREMENT_COMPLETED, now, id, {});

        const ledgerResponse: BulkLedgerTransactionsResponse =
          await this.ledgerRetirementsService.settleRetirementTransaction(tx, retirement, assetFlows);
        ledgerTransactionIds = this.ledgerTransactionsService.getTransactionIdsFromBulkResponse(ledgerResponse);

        return retirement;
      });

      // send email notifications
      if (claims.name !== 'init-console-script' && retirement.type === RetirementType.RETIREMENT) {
        await this.privateHandleNotificationsAndEmails(now, NotificationEvent.RETIREMENT_STATUS_UPDATED, retirement);
      }

      return retirement;
    } catch (e) {
      if (ledgerTransactionIds.length > 0) {
        await this.ledgerTransactionsService.revertTransactionsAfterFailedApi(
          id,
          now,
          ledgerTransactionIds,
          `completing retirement ${id}`,
        );
      }
      throw e;
    }
  }

  /**
   * public endpoint for user to confirm Retirement entity based on token sent in email
   *
   * steps:
   * 1. find the OrganizationUser entity for the user by email
   * 2. check that the OrganizationUser exists and has the correct OrganizationUserRole
   * 3. build the Redis key for the Retirement request
   * 4. retrieve the cached Retirement request from Redis
   * 5. parse the cached data and validate the token
   * 6. validate that transfer outflows are only for AssetType.REGISTRY_VINTAGE assets
   * 7. create the Retirement entity using the cached data
   * 8. delete the cached Retirement request from Redis
   * 9. return the created Retirement entity
   *
   * @param {PortalRetirementRequestDTO} data
   * @param {UserClaims} claims
   * @returns {Promise<Retirement>}
   * @throws UnprocessableEntityException, BadRequestException, ForbiddenException
   */
  async portalConfirmCreateRetirement(data: PortalRetirementRequestDTO, claims: UserClaims): Promise<Retirement> {
    const portalUser: InternalPortalUser = await getPortalUserWithOrganizationFromClaims(this.em, claims);
    if (
      !portalUser.organizationUser.roles.some((s) =>
        [OrganizationUserRole.TRANSACTOR, OrganizationUserRole.MANAGER].includes(s),
      )
    ) {
      throw new ForbiddenException('user must have correct permissions to request a retirement');
    }

    const key = `RETIREMENT-${claims.email}:${portalUser.organizationUser.organization.id}`;
    const cachedString = await this.redis.get(key);
    if (!cachedString) {
      throw new UnprocessableEntityException(`Retirement request not found`);
    }

    // parse the cached string into the expected object
    const cached: CachedRetirementTransaction = JSON.parse(cachedString);
    if (cached.token !== data.token) {
      throw new BadRequestException([`token not valid`]);
    }

    // transfer outflows can only be done on registry vintage assets
    if (data.type === RetirementType.TRANSFER_OUTFLOW && data.assetType !== AssetType.REGISTRY_VINTAGE) {
      throw new UnprocessableEntityException(
        `transfers to customer registry account can only be done on ${AssetType.REGISTRY_VINTAGE} assets`,
      );
    }

    const retirement = await this.createRetirement(cached, claims);
    await this.redis.del(key);
    return retirement;
  }

  /**
   * create a Retirement entity
   *
   * steps:
   * 1. start a database transaction
   * 2. validate that only one RCT asset is allowed for AssetType.RCT
   * 3. validate that transfer outflows are only for AssetType.REGISTRY_VINTAGE assets
   * 4. find the CustomerPortfolio entity for the Organization
   * 5. retrieve all InternalHolding allocations for the requested assets
   * 6. for each asset, validate AssetAllocation existence, type, and available amount
   * 7. generate a unique uiKey for the Retirement
   * 8. create a Transaction entity for the Retirement
   * 9. create AssetFlow entities for the Retirement
   * 10. create the Retirement entity with all related data
   * 11. set default PurchasesRetirements for the Retirement
   * 12. update the ledger with pending Retirement amounts
   * 13. create an AuditLog entry for creation
   * 14. send notification emails if needed
   * 15. handle errors and revert ledger transactions if necessary
   *
   * @param {InternalRetirementTransaction} data
   * @param {UserClaims} claims
   * @returns {Promise<Retirement>}
   * @throws UnprocessableEntityException, InternalServerErrorException, ConflictException
   */
  async createRetirement(data: InternalRetirementTransaction, claims: UserClaims): Promise<Retirement> {
    const now = new Date();
    const retirementId: uuid = uuid();
    let ledgerTransactionId;

    try {
      const retirement = await this.em.transactional(async (tx) => {
        // only one RCT is allowed to be retired at a time to avoid issues with project vintage calculations.
        // vintages can have multiple assets
        if (data.assetType === AssetType.RCT && data.assets.length !== 1) {
          throw new UnprocessableEntityException(`only 1 RCT asset is allowed to be retired at a time`);
        }
        // transfer outflows can only be done on registry vintage assets
        if (data.type === RetirementType.TRANSFER_OUTFLOW && data.assetType !== AssetType.REGISTRY_VINTAGE) {
          throw new UnprocessableEntityException(
            `transfers to customer registry account can only be done on ${AssetType.REGISTRY_VINTAGE} assets`,
          );
        }
        // get the sourceBooks which should all be the same book for now
        // this will need to change when we do buffer retirements that are from different sources
        const customerPortfolio = await tx.findOne(
          CustomerPortfolio,
          { organization: data.organizationId },
          {
            populate: ['book', 'organization'],
          },
        );
        if (!customerPortfolio) {
          throw new InternalServerErrorException(`could not find customer organization ${data.organizationId}`);
        }

        // get all asset allocations
        const allocations: InternalHolding[] = await this.assetsService.internalGetRetirementHoldings(
          tx.getKnex(),
          customerPortfolio.book.id,
          data.assets,
        );

        for (const asset of data.assets) {
          // get asset allocation
          const assetAllocation = allocations.find((f) => f.asset_id === asset.assetId);
          if (!assetAllocation) {
            throw new ConflictException(`AssetAllocation not found for asset ${asset.assetId}`);
          } else if (assetAllocation.asset_type !== data.assetType) {
            throw new UnprocessableEntityException(`all assets must be of the assetType ${data.assetType}`);
          }

          // validate amount for asset
          if (assetAllocation.amount_available < asset.amount) {
            throw new UnprocessableEntityException(
              `customer organization ${customerPortfolio.organization.name} must have amountAvaliable greater than or equal to ${asset.amount} for ${assetAllocation.asset_type} asset ${assetAllocation.asset_id}`,
            );
          }
        }

        // generate a unique id -- potential for clashes at the current usage is very low
        //                         if we ever need, we can use something like the Hull–Dobell Theorem
        //                         to generate clash free numbers and convert them to codes
        let key: string = generateRandomKey('RR');
        for (let i = 0; i < 1000; i++) {
          const notFound = (await tx.findOne(Retirement, { uiKey: key }, { fields: ['id', 'uiKey'] })) == null;
          if (notFound) break;
          /* istanbul ignore next */
          key = generateRandomKey('RR');
          // let fail if we exhaust ids
        }

        // generate transaction
        const transaction = tx.create(Transaction, {
          id: retirementId,
          type: TransactionType.RETIREMENT,
          showCustomer: true,
        });
        await tx.persistAndFlush(transaction);

        // create asset flows
        const assets = await tx.find(
          Asset,
          { id: data.assets.map((m) => m.assetId) },
          { populate: ['details.portfolioDetails', 'details.rrtDetails', 'details.vintageDetails'] },
        );
        const rcts = await tx.find(Book, { id: data.assets.map((m) => m.assetId) });
        const pvs = await tx.find(ProjectVintage, { id: data.assets.map((m) => m.assetId) });
        const assetFlows = data.assets.map((dataAsset) =>
          tx.create(AssetFlow, {
            createdAt: now,
            updatedAt: now,
            amount: dataAsset.amount,
            asset: assets.find((f) => f.id === dataAsset.assetId) || dataAsset.assetId, // hack since asset should always exist
            assetType: data.assetType,
            destination: uuid(environment.rubicon.books.offsets),
            detailedAsset:
              // todo (3163) : fix
              data.assetType === AssetType.RCT
                ? rcts.find((f) => f.id === dataAsset.assetId)
                : data.assetType === AssetType.REGISTRY_VINTAGE
                  ? pvs.find((f) => f.id === dataAsset.assetId)
                  : undefined,
            otherFee: dataAsset.otherFee,
            rawPrice: dataAsset.rawPrice,
            serviceFee: dataAsset.serviceFee,
            source: customerPortfolio.book.id,
            status: AssetFlowStatus.PENDING,
            transaction: retirementId,
            transactionSubtype: data.type,
            transactionType: TransactionType.RETIREMENT,
          }),
        );

        const retirement = tx.create(Retirement, {
          ...data,
          id: retirementId,
          createdAt: now,
          updatedAt: now,
          amount: Decimal.sum(...data.assets.map((m) => m.amount), 0).toNumber(),
          assetFlows,
          customerPortfolio,
          dateStarted: now,
          requestedBy: data.requestedBy ? tx.getReference(User, data.requestedBy.id) : undefined,
          status:
            // todo (3166) : fix
            data.assetType === AssetType.RCT
              ? RetirementStatus.ADMIN_REVIEW
              : RetirementStatus.PORTFOLIO_MANAGER_REVIEW,
          uiKey: key,
        });

        // set default purchases-retirements
        // note only one of rctFlows or assetFlows will be passed through since the other will be empty, but we're also combining the flows (TD-75)
        await this.purchasesRetirementsService.defaultRetirementPurchases(tx, now, assetFlows);

        // update ledger with pending retirement amounts
        const ledgerResponse: PendingLedgerTransactionResponse =
          await this.ledgerRetirementsService.createRetirementTransaction(tx, retirement, assetFlows);
        ledgerTransactionId = ledgerResponse.id;

        await this.auditLogsService.create(tx, claims, AuditLogAction.RETIREMENT_CREATED, now, retirement.id, {
          ...data,
        });

        return retirement;
      });

      // send email notifications
      if (claims.name !== 'init-console-script' && retirement.type === RetirementType.RETIREMENT) {
        await this.privateHandleNotificationsAndEmails(now, NotificationEvent.RETIREMENT_CREATED, retirement);
      }

      return retirement;
    } catch (e) {
      if (ledgerTransactionId) {
        await this.ledgerTransactionsService.revertTransactionsAfterFailedApi(
          retirementId,
          now,
          [ledgerTransactionId],
          `creating retirement ${retirementId}`,
        );
      }
      throw e;
    }
  }

  /**
   * finds many retirements for admin
   * @param {AdminRetirementQueryDTO} query
   * @returns {Promise<InternalRetirementQueryResponse>}
   */
  // only used in Admin
  async findManyForAdmin(query: AdminRetirementQueryDTO): Promise<InternalRetirementQueryResponse> {
    const where: FilterQuery<Retirement> = {
      type: query.types || [RetirementType.RETIREMENT, RetirementType.TRANSFER_OUTFLOW],
    };

    if (query.statuses) {
      where.status = query.statuses;
    }
    if (query.assetId) {
      where.transactionDetails = { transactionAssets: { assetId: query.assetId } };
    }
    if (query.organizationId) {
      where.customerPortfolio = { organization: query.organizationId };
    }
    if (query.types) {
      where.type = { $in: query.types };
    }

    const count = query.includeTotalCount ? await this.em.count(Retirement, where) : undefined;
    const retirements = await this.em.find(Retirement, where, {
      limit: query.limit,
      offset: query.offset,
      orderBy: this.privateSetRetirementOrderBys(query.orderBys),
      populate: this.privatePopulateRelations(query.includeRelations),
    });

    // populate assets if necessary
    await this.privatePopulateAssets(query.includeRelations, retirements);

    return {
      data: retirements,
      page: {
        limit: query.limit,
        offset: query.offset,
        size: retirements.length,
        totalCount: count,
      },
    };
  }

  /**
   * finds a single retirement by id
   * @param {uuid} id -- returement id
   * @param {AdminRetirementRelationsQueryDTO} query
   * @param {UserClaims} claims
   * @returns {Promise<Retirement>}
   * @throws NotFoundException
   */
  async findOne(id: uuid, query: AdminRetirementRelationsQueryDTO, claims?: UserClaims): Promise<Retirement> {
    const retirement = await this.em.findOneOrFail(Retirement, id, {
      populate: this.privatePopulateRelations(query.includeRelations),
      connectionType: 'read',
    });

    // if claims, public endpoint so make sure user belongs to org of transaction
    if (claims) {
      try {
        const portalUser: InternalPortalUser = await getPortalUserWithOrganizationFromClaims(this.em, claims);
        if (portalUser.organizationUser.organization !== retirement.customerPortfolio.organization) {
          throw new NotFoundException();
        }
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (e) {
        throw new NotFoundException(`Retirement ${id} not found`);
      }
    }

    await this.privatePopulateAssets(query.includeRelations, [retirement]);
    return retirement;
  }

  /**
   * processes a retirement (update status)
   * @param {uuid} id
   * @param {UserClaims} claims
   * @returns {Promise<Retirement>}
   * @throws ConflictException, ForbiddenException
   */
  async processRetirement(id: uuid, claims: UserClaims): Promise<Retirement> {
    const now = new Date();
    const retirement = await this.em.transactional(async (tx) => {
      const retirement = await tx.findOneOrFail(
        Retirement,
        { id, type: [RetirementType.RETIREMENT, RetirementType.TRANSFER_OUTFLOW] },
        {
          populate: ['customerPortfolio.organization'],
          lockMode: LockMode.PESSIMISTIC_WRITE,
          // MikroORM writes the query in such a way that it is incompatible with the lock if we don't specify the table. Here `r0` is the Transaction table
          lockTableAliases: ['r0'],
        },
      );

      if (retirement.status !== RetirementStatus.PORTFOLIO_MANAGER_REVIEW) {
        throw new ConflictException(`Retirement must have status ${RetirementStatus.PORTFOLIO_MANAGER_REVIEW}`);
      }

      if (
        claims.name !== 'init-console-script' &&
        !(await this.authService.check(claims, PermissionEnum.RETIREMENTS_CLEAR_PM_REVIEW, { route: 'retirements' }))
      ) {
        throw new ForbiddenException();
      }

      retirement.status = RetirementStatus.PROCESSING;
      retirement.updatedAt = now;

      await this.auditLogsService.create(tx, claims, AuditLogAction.RETIREMENT_PROCESSING, now, id, {});

      return retirement;
    });

    // send email notifications
    if (claims.name !== 'init-console-script' && retirement.type === RetirementType.RETIREMENT) {
      await this.privateHandleNotificationsAndEmails(now, NotificationEvent.RETIREMENT_STATUS_UPDATED, retirement);
    }

    return retirement;
  }

  /**
   * updates retirement amounts
   * @param {uuid} id
   * @param {UserClaims} claims
   * @param {AdminUpdateRetirementAmountsRequestDTO[]} data
   * @returns {Promise<Retirement>}
   * @throws ConflictException, BadRequestException, InternalServerErrorException
   */
  async updateRetirementAmounts(
    id: uuid,
    claims: UserClaims,
    data: AdminUpdateRetirementAmountsRequestDTO[],
  ): Promise<Retirement> {
    const now = new Date();
    let ledgerTransactionIds: uuid[] = [];

    try {
      const retirement = await this.em.transactional(async (tx) => {
        const retirement = await tx.findOneOrFail(
          Retirement,
          { id, type: RetirementType.RETIREMENT },
          {
            populate: ['customerPortfolio.organization'],
            lockMode: LockMode.PESSIMISTIC_WRITE,
            // MikroORM writes the query in such a way that it is incompatible with the lock if we don't specify the table. Here `r0` is the retirement table
            lockTableAliases: ['r0'],
          },
        );
        // only retirements with status RetirementStatus.PORTFOLIO_MANAGER_REVIEW can be updated
        if (retirement.status !== RetirementStatus.PORTFOLIO_MANAGER_REVIEW) {
          throw new ConflictException(`Retirement must have status ${RetirementStatus.PORTFOLIO_MANAGER_REVIEW}`);
        }
        // only retirements that have RctFlows can have updated VintageFlow amounts
        const rctFlows = await tx.find(AssetFlow, { transaction: id, assetType: AssetType.RCT });
        if (rctFlows.length === 0) {
          throw new ConflictException(`Retirements must have an RCT asset to update retirement amounts`);
        }

        // the new sum must match the old amount
        if (sum(data.map((m) => m.amountTransacted)) !== retirement.amount) {
          throw new BadRequestException([
            `total amountTransacted ${sum(
              data.map((m) => m.amountTransacted),
            )} must equal the initial amount ${retirement.amount}`,
          ]);
        }

        const vintageFlows: AssetFlow[] = await tx.find(
          AssetFlow,
          { transaction: id, assetType: AssetType.REGISTRY_VINTAGE },
          { populate: ['asset'] },
        );
        const rctAssetBooks: InternalBookResponse[] = await tx.find(Book, { id: vintageFlows.map((m) => m.source.id) });
        await this.populateAllocationsService.internalPopulateBooksAllocations(tx, rctAssetBooks, undefined, [
          BookRelations.OWNER_ALLOCATIONS_NESTED,
        ]);

        for (const updateAmount of data) {
          // get the rct book associated to the vintage for the updated amount
          const rctBook = rctAssetBooks.find((f) => f.id === updateAmount.sourceId);
          // get the vintage amount in the rct book
          const rctVintage = rctBook
            ? rctBook.ownerAllocations?.allocations?.find((f) => f.asset.id === updateAmount.projectVintageId)
            : undefined;
          const rctAmountAvailable = rctVintage ? rctVintage.amountAvailable : 0;

          // create or get the flow
          const flow =
            vintageFlows.find(
              (f) => f.asset.id === updateAmount.projectVintageId && f.source.id === updateAmount.sourceId,
            ) ||
            tx.create(AssetFlow, {
              id: uuid(),
              createdAt: now,
              updatedAt: now,
              amount: 0,
              asset: updateAmount.projectVintageId,
              assetType: AssetType.REGISTRY_VINTAGE,
              destination: uuid(environment.rubicon.books.offsets),
              rawPrice: new Decimal(0),
              source: updateAmount.sourceId,
              status: AssetFlowStatus.PENDING,
              transaction: id,
              transactionSubtype: retirement.type,
              transactionType: TransactionType.RETIREMENT,
            });
          // if the flowAmount + rct available amount is less than the updated amount,
          // it means the rct book does not have enough credits of that vintagew
          // for the updated amount, and thus should throw an error
          if (flow.amount + rctAmountAvailable < updateAmount.amountTransacted) {
            throw new ConflictException(
              `vintage ${updateAmount.projectVintageId} amount ${updateAmount.amountTransacted} must be less than or equal to ${rctAmountAvailable}`,
            );
          }

          flow.updatedAt = now;
          flow.amount = updateAmount.amountTransacted;

          // if the flow doesn't already exist (i.e. creating a new flow for the retirement)
          // add flow to asset flows so that we can update ledger.
          if (!vintageFlows.find((f) => f.asset.id === updateAmount.projectVintageId)) {
            vintageFlows.push(flow);
          }
        }

        const assetFlowsSum = Decimal.sum(...vintageFlows.map((m) => m.amount), 0).toNumber();
        if (assetFlowsSum !== retirement.amount) {
          throw new InternalServerErrorException(
            `updated assetFlows amounts ${assetFlowsSum} did not match retirement amount ${retirement.amount}`,
          );
        }

        await this.auditLogsService.create(tx, claims, AuditLogAction.RETIREMENT_AMOUNTS_UPDATED, now, id, {
          ...data,
        });

        const ledgerResponse: BulkLedgerTransactionsResponse =
          await this.ledgerRetirementsService.calculateRetirementAmountsTransaction(tx, now, retirement, vintageFlows);
        ledgerTransactionIds = this.ledgerTransactionsService.getTransactionIdsFromBulkResponse(ledgerResponse);

        retirement.assetFlows = vintageFlows;
        return retirement;
      });

      // send email notifications
      if (claims.name !== 'init-console-script' && retirement.type === RetirementType.RETIREMENT) {
        await this.privateHandleNotificationsAndEmails(now, 'amounts_updated', retirement);
      }

      return retirement;
    } catch (e) {
      if (ledgerTransactionIds.length > 0) {
        await this.ledgerTransactionsService.revertTransactionsAfterFailedApi(
          id,
          now,
          ledgerTransactionIds,
          `updating retirement ${id} amounts`,
        );
      }
      throw e;
    }
  }

  /**
   * update retirement links
   * @param {uuid} retirementId
   * @param {UserClaims} claims
   * @param {AdminRetirementLinkUpdateRequestDTO} data
   * @returns {Promise<Retirement>}
   */
  async updateRetirementLinks(
    retirementId: uuid,
    claims: UserClaims,
    data: AdminRetirementLinkUpdateRequestDTO,
  ): Promise<Retirement> {
    const now = new Date();
    // fetch old links for retirement
    return await this.em.transactional(async (tx) => {
      const retirement: Retirement = await tx.findOneOrFail(Retirement, retirementId);
      const assetFlow = await tx.findOneOrFail(
        AssetFlow,
        { transaction: retirementId, asset: data.projectVintageId },
        { populate: ['asset'] },
      );

      const existingLinks = await tx.find(RetirementLink, { assetFlow }, { lockMode: LockMode.PESSIMISTIC_WRITE });

      // delete old links if any
      tx.remove(existingLinks);
      // write new links
      const user = await tx.findOneOrFail(User, { email: claims.email });
      const retirementLinks: RetirementLink[] = [];
      data.links.forEach((l) => {
        retirementLinks.push(
          tx.create(RetirementLink, {
            updatedAt: now,
            createdAt: now,
            url: l.url,
            label: l.label,
            createdBy: user,
            assetFlow,
          }),
        );
      });

      return retirement;
    });
  }

  /**
   * calculate suggested buffer retirement
   * @param {uuid[]} retirementIds
   * @returns {Promise<AdminSuggestedBufferRetirementResponseDTO>}
   * @throws UnprocessableEntityException
   */
  async calculateSuggestedBufferRetirement(retirementIds: uuid[]): Promise<AdminSuggestedBufferRetirementResponseDTO> {
    // ): Promise<SuggestedBufferComposition> {
    // Get all retirements
    const retirements = await this.em.find(Retirement, { id: retirementIds }, { populate: ['transactionDetails'] });
    // Ensure all exist and are completed
    const uncompletedRetirements = retirements.filter((f) => f.status !== RetirementStatus.COMPLETED);
    if (uncompletedRetirements.length > 0) {
      throw new UnprocessableEntityException(
        `Retirements [${uncompletedRetirements.map((r) => r.id).join(', ')}] must be completed`,
      );
    }
    const vintageFlows: AssetFlow[] = await this.em.find(
      AssetFlow,
      { amount: { $ne: 0 }, transaction: retirements.map((m) => m.id), assetType: AssetType.REGISTRY_VINTAGE },
      { populate: ['source', 'transaction', 'detailedAsset'] },
    );
    const pvs = await this.em.find(
      ProjectVintage,
      { id: vintageFlows.map((m) => m.asset.id) },
      {
        populate: [
          'project.country',
          'project.projectType',
          'project.projectType.bufferCategory',
          'project.bufferCategory',
        ],
      },
    );
    const projects = pvs.map((p) => p.project);
    const uncategorizedProjects = projects.filter((p) => !p.bufferCategory && !p.projectType.bufferCategory);
    if (uncategorizedProjects.length > 0) {
      throw new UnprocessableEntityException(
        `Projects ${uncategorizedProjects.map((p) => p.id).join(', ')} do not have a buffer category defined`,
      );
    }
    // Group them by Nature/Industrial/removal/renewables
    // (by buffer category)
    const bufferCategories = await this.em.find(BufferCategory, {});
    // Sum of pv amount * buffer percentage
    // Return a zero for categories with no retirements
    // return the suggested buffer composition
    // look for buffer category on project types if project buffer category is empty
    const composition = bufferCategories.map(async (b) => {
      const categoryProjectIds = projects
        .filter((p) => {
          const projectCategory = p.bufferCategory || p.projectType.bufferCategory;
          return projectCategory!.id === b.id;
        })
        .map((p) => p.id);
      const relaventFlows = vintageFlows.filter((f) => {
        const pv = pvs.find((p) => p.id === f.asset.id);
        if (!pv) {
          throw new UnprocessableEntityException(
            `ProjectVintage with id ${f.asset.id} not found in database, cannot calculate risk buffer retirement`,
          );
        }
        f.detailedAsset = pv;
        return categoryProjectIds.includes(pv.project.id);
      });
      const values = relaventFlows.map((f) => {
        const bufferPercentage = (f.detailedAsset as unknown as ProjectVintage).riskBufferPercentage || new Decimal(0);
        return bufferPercentage.mul(f.amount);
      });
      const targetCredits = Decimal.round(Decimal.sum(...values, 0)).toNumber();
      return {
        category: b.name,
        suggestedBufferRetirements:
          targetCredits === 0 ? [] : await this.calculateCategoryRiskBufferComposition(b, targetCredits),
      };
    });
    return { suggestedAllocations: await Promise.all(composition) };
  }

  /**
   * calculate category risk buffer composition
   * @param {BufferCategory} category
   * @param {number} amount
   * @returns {Promise<AdminSuggestedBufferElementDTO[]>}
   * @throws InternalServerErrorException, UnprocessableEntityException
   */
  async calculateCategoryRiskBufferComposition(
    category: BufferCategory,
    amount: number,
  ): Promise<AdminSuggestedBufferElementDTO[]> {
    // Per total, make composition
    // Weighted representation of unallocated credits in same category
    // Get all projects in category with unallocated inventory
    return await this.em.transactional(async (tx) => {
      // get the rct book
      const defaultBook: InternalBookResponse | null = await tx.findOne(Book, {
        id: environment.rubicon.books.portfolio.default,
        type: BookType.PORTFOLIO_DEFAULT,
      });
      if (!defaultBook) {
        throw new InternalServerErrorException(`defaultBook not found`);
      }
      // populate vintage allocations for the rct book
      await this.populateAllocationsService.internalPopulateBookAllocations(
        tx,
        defaultBook,
        [AssetType.REGISTRY_VINTAGE],
        [BookRelations.OWNER_ALLOCATIONS_NESTED],
      );
      if (!defaultBook.ownerAllocations?.allocations) {
        // should never happen
        throw new InternalServerErrorException(`could not get allocated vintages for book ${defaultBook.id}`);
      }
      // get vintages allocated of the defaultBook and filter to match category
      const projectVintages = (
        await tx.find(
          ProjectVintage,
          { id: defaultBook.ownerAllocations.allocations.map((m) => m.asset.id) },
          { populate: ['project.country', 'project.projectType.bufferCategory', 'project.bufferCategory'] },
        )
      ).filter((pv) => {
        return pv.project.bufferCategory
          ? pv.project.bufferCategory.id === category.id
          : pv.project.projectType.bufferCategory?.id === category.id;
      });
      const projects = [...new Set(projectVintages.map((m) => m.project))];
      const categoryVintageAllocations = defaultBook.ownerAllocations.allocations.filter((f) => {
        f.detailedAsset = projectVintages.find((pv) => pv.id === f.asset.id);
        return f.asset.type === AssetType.REGISTRY_VINTAGE && f.detailedAsset !== undefined;
      });

      // get the total amount of vintages available in the defaultBook
      const totalVintagesAvailableInBook =
        Decimal.sum(...categoryVintageAllocations.map((m) => Decimal.max(m.amountAvailable, 0))).toNumber() || 0;
      if (amount > totalVintagesAvailableInBook) {
        throw new UnprocessableEntityException(
          `amount to retire ${amount} must be less than or equal to ${totalVintagesAvailableInBook}`,
        );
      }

      // step 1 - calculate proportions, step 2 - round down
      const totalByProject = new Map<uuid, { amountRemaining: number; transactionAmount: number }>();
      const byProject: Map<uuid, InternalAllocationResponse[]> = groupBy(categoryVintageAllocations, (x) => {
        return (x.detailedAsset as ProjectVintage).project.id;
      });
      byProject.forEach((vintage: InternalAllocationResponse[], projectId: uuid) => {
        const totalAmountAvailableToRetire = sum(
          vintage.map((value) => {
            return (
              value.amountAllocated -
              value.amountPendingCustomerTransferOutflow -
              value.amountPendingRetirement -
              value.amountPendingSell
            );
          }),
        );
        const transactionAmount = Math.floor(amount * (totalAmountAvailableToRetire / totalVintagesAvailableInBook));
        // ignore amounts under 10
        if (transactionAmount >= 10 || ([...totalByProject].length < 10 && totalAmountAvailableToRetire > 0)) {
          totalByProject.set(projectId, {
            transactionAmount: transactionAmount >= 10 ? transactionAmount : 0,
            amountRemaining: totalAmountAvailableToRetire - transactionAmount,
          });
        }
      });
      // step 3 - fill gap with the largest project
      let remainder = amount - sum([...totalByProject].map((value) => value[1].transactionAmount));
      const filled: {
        projectId: uuid;
        amountTransacted: number;
        amountRemaining: number;
        projectName: Project | undefined;
      }[] = sortBy(
        [...totalByProject].map(([projectId, amountsByProject]) => ({
          projectId: projectId,
          amountTransacted: amountsByProject.transactionAmount,
          amountRemaining: amountsByProject.amountRemaining,
          projectName: projects.find((f) => f.id === projectId),
        })),
        ['amountRemaining', 'projectName'],
      )
        .reverse()
        .map((tx) => {
          if (remainder > 0) {
            if (remainder <= tx.amountRemaining) {
              tx.amountTransacted += remainder;
              remainder = 0;
            } else {
              remainder = remainder - tx.amountRemaining;
              tx.amountTransacted += tx.amountRemaining;
            }
          }
          return tx;
        });

      // step 4 - redistribute to older vintages within same project
      // note : the following relies on ordering the vintages by name. We should change this
      // by adding a timestamp to the vintage model

      return filled
        .map(
          (tx: {
            projectId: uuid;
            amountTransacted: number;
            amountRemaining: number;
            projectName: Project | undefined;
          }) => {
            const thisComponents: InternalAllocationResponse[] = byProject
              .get(tx.projectId)! // not null by construction of byProject
              .sort((a, b) =>
                (a.detailedAsset as ProjectVintage).name().localeCompare((b.detailedAsset as ProjectVintage).name()),
              );
            let remaining = tx.amountTransacted;
            return thisComponents.map((x: InternalAllocationResponse) => {
              const amountAvailableToRetire =
                x.amountAllocated -
                x.amountPendingCustomerTransferOutflow -
                x.amountPendingRetirement -
                x.amountPendingSell;
              const amountTransacted = Math.min(remaining, amountAvailableToRetire);
              remaining -= amountTransacted;
              return {
                projectVintage: toTrimmedProjectVintageResponseDTO(x.detailedAsset as ProjectVintage),
                amount: amountTransacted,
              };
            });
          },
        )
        .flat()
        .filter((p) => p.amount > 0);
    });
  }

  /* private */

  /**
   * handle notifications and emails for Retirement entity events
   *
   * steps:
   * 1. determine the NotificationEvent to use
   * 2. retrieve AssetFlow entities for the Retirement
   * 3. build the InternalTransactionBase payload for notifications
   * 4. call NotificationsService to handle the event
   * 5. if event is RETIREMENT_CREATED, send customer emails using SendgridService
   * 6. handle and log any errors during notification sending
   *
   * @param {Date} now
   * @param {NotificationEvent | 'amounts_updated'} event
   * @param {Retirement} retirement
   * @returns {Promise<void>}
   */
  private async privateHandleNotificationsAndEmails(
    now: Date,
    event: NotificationEvent | 'amounts_updated',
    retirement: Retirement,
  ): Promise<void> {
    const notificationEvent = event === 'amounts_updated' ? NotificationEvent.RETIREMENT_STATUS_UPDATED : event;
    const assetFlows = await this.em.find(
      AssetFlow,
      { transaction: retirement.id },
      { populate: ['asset.details.portfolioDetails', 'asset.details.rrtDetails', 'asset.details.vintageDetails'] },
    );

    try {
      // handle internal event notifications
      const sendgridPayload: InternalTransactionBase = new InternalTransactionBase(
        await this.notificationsService.getNotificationUrlWithUuid(notificationEvent, retirement.id),
        retirement.customerPortfolio.organization!.name,
        now,
        retirement.uiKey,
        event === 'amounts_updated' ? 'vintage_amounts_updated' : retirement.status,
        retirement,
        assetFlows,
      );
      await this.notificationsService.handleEvent(this.em, notificationEvent, sendgridPayload);

      // send customer emails
      if (notificationEvent === NotificationEvent.RETIREMENT_CREATED) {
        if (retirement.customerPortfolio.organization) {
          await this.sendgrid.createAndSendCustomerEmails(
            this.em,
            NotificationEvent.RETIREMENT_CREATED,
            retirement.customerPortfolio.organization,
            environment.sendgrid.template.retirementCreated,
            new NewRetirementRequest(),
          );
        } else {
          // purchases should only be created from customer portfolios, which should always have an organization attached
          console.error(`expected to find a customer organization for ${event}`);
        }
      }
    } catch (e) {
      console.error(`error sending ${event} email for retirement ${retirement.uiKey} : ` + JSON.stringify(e));
    }
  }

  /**
   * populate AssetFlow and related assets for Retirement entities based on requested relations
   *
   * steps:
   * 1. check if any requested relations require AssetFlow population
   * 2. retrieve AssetFlow entities for the retirements
   * 3. retrieve Book and ProjectVintage entities for AssetFlows
   * 4. assign detailedAsset to each AssetFlow based on assetType
   * 5. if RetirementRelations.LINKS is included, populate links for AssetFlows
   *
   * @param {RetirementRelations[]} includeRelations
   * @param {Retirement[]} retirements
   * @returns {Promise<void>}
   */
  private async privatePopulateAssets(
    includeRelations: RetirementRelations[],
    retirements: Retirement[],
  ): Promise<void> {
    const flowRelations = [RetirementRelations.ASSETS, RetirementRelations.LINKS, RetirementRelations.RCT_VINTAGES];

    if (includeRelations.some((s) => flowRelations.includes(s))) {
      const assetFlows: AssetFlow[] = await this.em.find(
        AssetFlow,
        { amount: { $ne: 0 }, transaction: retirements.map((m) => m.id) },
        {
          populate: [
            'asset.details.portfolioDetails',
            'asset.details.rrtDetails',
            'asset.details.vintageDetails',
            'source',
            'transaction',
          ],
        },
      );
      const rcts = await this.em.find(Book, { id: assetFlows.map((m) => m.asset.id) });
      const pvs = await this.em.find(
        ProjectVintage,
        { id: assetFlows.map((m) => m.asset.id) },
        { populate: ['grouping', 'project.country', 'project.projectType'] },
      );

      for (const transaction of retirements) {
        transaction.assetFlows = assetFlows.filter((f) => f.transaction.id === transaction.id);
        transaction.assetFlows.forEach((flow) => {
          // todo (3166) : fix
          if (flow.assetType === AssetType.RCT) {
            flow.detailedAsset = rcts.find((f) => f.id === flow.asset.id);
          } else if (flow.assetType === AssetType.REGISTRY_VINTAGE) {
            const found = pvs.find((f) => f.id === flow.asset.id);
            flow.detailedAsset = found;
          }
        });
        if (includeRelations.includes(RetirementRelations.LINKS)) {
          await this.em.populate(transaction.assetFlows, ['links']);
        }
      }
    }
  }

  /**
   * build the relations to populate for Retirement entity queries
   *
   * steps:
   * 1. initialize the populateString array with 'transactionDetails'
   * 2. add 'customerPortfolio.organization' if RetirementRelations.CUSTOMER_PORTFOLIO is included
   * 3. add 'requestedBy' if RetirementRelations.REQUESTED_BY is included
   * 4. return the populateString array as the FindOptions populate type
   *
   * @param {RetirementRelations[]} relations
   * @returns {FindOptions<Retirement, S>['populate']}
   */
  private privatePopulateRelations<S extends string>(
    relations: RetirementRelations[],
  ): FindOptions<Retirement, S>['populate'] {
    const populateString: string[] = ['transactionDetails'];
    if (relations.includes(RetirementRelations.CUSTOMER_PORTFOLIO)) {
      populateString.push('customerPortfolio.organization');
    }
    if (relations.includes(RetirementRelations.REQUESTED_BY)) {
      populateString.push('requestedBy');
    }
    return populateString as unknown as FindOptions<Retirement, S>['populate'];
  }

  /**
   * calculate allocations for Retirement of RCT assets using the private retirement algorithm
   *
   * steps:
   * 1. find the Book entity for the given rctAssetId
   * 2. populate vintage allocations for the Book
   * 3. filter allocations for AssetType.REGISTRY_VINTAGE
   * 4. group allocations by Project id
   * 5. calculate transaction amounts for each project based on available and requested amount
   * 6. fill the gap with smallest project vintages
   * 7. redistribute to older vintages within each project
   * 8. return the calculated allocations
   *
   * @param {EntityManager} tx
   * @param {uuid} rctAssetId
   * @param {number} amount
   * @returns {Promise<CalculateEstimateRetirementAmount[]>}
   * @throws InternalServerErrorException, UnprocessableEntityException
   */
  private async privateRetirementAlgorithm(
    tx: EntityManager,
    rctAssetId: uuid,
    amount: number,
  ): Promise<CalculateEstimateRetirementAmount[]> {
    // get the rct book
    const rctBook: InternalBookResponse | null = await tx.findOne(Book, { id: rctAssetId });
    if (!rctBook) {
      throw new InternalServerErrorException(`rctBook not found for ${rctAssetId}`);
    }
    // populate vintage allocations for the rct book
    await this.populateAllocationsService.internalPopulateBookAllocations(
      tx,
      rctBook,
      [AssetType.REGISTRY_VINTAGE],
      [BookRelations.OWNER_ALLOCATIONS_NESTED],
    );
    if (!rctBook.ownerAllocations?.allocations) {
      // should never happen
      throw new InternalServerErrorException(`could not get allocated vintages for book ${rctBook.id}`);
    }
    // get vintages allocated of the rctBook
    const projectVintages = await tx.find(
      ProjectVintage,
      { id: rctBook.ownerAllocations.allocations.map((m) => m.asset.id) },
      { populate: ['project.country', 'project.projectType'] },
    );
    const projects = [...new Set(projectVintages.map((m) => m.project))];
    const rctVintageAllocations = rctBook.ownerAllocations.allocations.filter(
      (f) => f.asset.type === AssetType.REGISTRY_VINTAGE,
    );
    rctVintageAllocations.forEach(
      (vintageAllocation) =>
        (vintageAllocation.detailedAsset = projectVintages.find((pv) => pv.id === vintageAllocation.asset.id)),
    );

    // get the total amount of vintages available in the rctBook
    const totalVintagesAvailableInBook =
      Decimal.sum(...rctVintageAllocations.map((m) => m.amountAvailable)).toNumber() || 0;
    /* istanbul ignore if: we should never get here, this check is already enforced when the retirement is created */
    if (amount > totalVintagesAvailableInBook) {
      throw new UnprocessableEntityException(
        `amount to retire ${amount} must be less than or equal to ${totalVintagesAvailableInBook}`,
      );
    }

    // step 1 - calculate proportions, step 2 - round down
    const totalByProject = new Map<uuid, { amountRemaining: number; transactionAmount: number }>();
    const byProject: Map<uuid, InternalAllocationResponse[]> = groupBy(rctVintageAllocations, (x) => {
      return (x.detailedAsset as ProjectVintage).project.id;
    });
    byProject.forEach((vintage: InternalAllocationResponse[], projectId: uuid) => {
      const totalAmountAvailableToRetire = sum(
        vintage.map((value) => {
          return (
            value.amountAllocated -
            value.amountPendingCustomerTransferOutflow -
            value.amountPendingRetirement -
            value.amountPendingSell
          );
        }),
      );
      const transactionAmount = Math.floor(totalAmountAvailableToRetire * (amount / totalVintagesAvailableInBook));
      if (totalAmountAvailableToRetire < 0) {
        // throw an error? or just skip? this should never happen but just in case
        console.error(`project ${projectId} has a negative sum ${totalAmountAvailableToRetire}`);
      } else {
        totalByProject.set(projectId, {
          transactionAmount,
          amountRemaining: totalAmountAvailableToRetire - transactionAmount,
        });
      }
    });
    // step 3 - fill gap with the largest project vintages
    let remainder = amount - sum([...totalByProject].map((value) => value[1].transactionAmount));
    const filled: {
      projectId: uuid;
      amountTransacted: number;
      amountRemaining: number;
      projectName: Project | undefined;
    }[] = sortBy(
      [...totalByProject].map(([projectId, amountsByProject]) => ({
        projectId: projectId,
        amountTransacted: amountsByProject.transactionAmount,
        amountRemaining: amountsByProject.amountRemaining,
        projectName: projects.find((f) => f.id === projectId),
      })),
      ['amountRemaining', 'projectName'],
    )
      .reverse()
      .map((tx) => {
        if (remainder > 0) {
          if (remainder <= tx.amountRemaining) {
            tx.amountTransacted += remainder;
            remainder = 0;
          } else {
            remainder = remainder - tx.amountRemaining;
            tx.amountTransacted += tx.amountRemaining;
          }
        }
        return tx;
      });

    // step 4 - redistribute to older vintages within same project
    // note : the following relies on ordering the vintages by name. We should change this
    //       by adding a timestamp to the vintage model

    return filled
      .map(
        (tx: {
          projectId: uuid;
          amountTransacted: number;
          amountRemaining: number;
          projectName: Project | undefined;
        }) => {
          const thisComponents: InternalAllocationResponse[] = byProject
            .get(tx.projectId)! // not null by construction of byProject
            .sort((a, b) =>
              (a.detailedAsset as ProjectVintage).name().localeCompare((b.detailedAsset as ProjectVintage).name()),
            );
          let remaining = tx.amountTransacted;
          return thisComponents.map((x: InternalAllocationResponse) => {
            const amountAvailableToRetire =
              x.amountAllocated -
              x.amountPendingCustomerTransferOutflow -
              x.amountPendingRetirement -
              x.amountPendingSell;
            const amountTransacted = Math.min(remaining, amountAvailableToRetire);
            remaining -= amountTransacted;
            return {
              vintage: x.detailedAsset as ProjectVintage,
              amountAllocated: amountAvailableToRetire,
              amountTransacted,
            };
          });
        },
      )
      .flat();
  }

  private privateSetRetirementOrderBys(orderBys: BaseRetirementOrderBy[]): any {
    const x = [];
    for (const ob of orderBys) {
      if (ob.orderBy === RetirementOrderByOptions.CREATED_AT) {
        x.push({ createdAt: OrderByDirectionSQL[ob.orderByDirection] });
      }
      if (ob.orderBy === RetirementOrderByOptions.CUSTOMER_PORTFOLIO_ORGANIZATION_NAME) {
        x.push({ transactionDetails: { counterpartyName: OrderByDirectionSQL[ob.orderByDirection] } });
      }
      if (ob.orderBy === RetirementOrderByOptions.DATE_FINISHED) {
        x.push({ dateFinished: OrderByDirectionSQL[ob.orderByDirection] });
      }
      if (ob.orderBy === RetirementOrderByOptions.DATE_STARTED) {
        x.push({ dateStarted: OrderByDirectionSQL[ob.orderByDirection] });
      }
      if (ob.orderBy === RetirementOrderByOptions.STATUS) {
        x.push({ status: OrderByDirectionSQL[ob.orderByDirection] });
      }
      if (ob.orderBy === RetirementOrderByOptions.TYPE) {
        x.push({ type: OrderByDirectionSQL[ob.orderByDirection] });
      }
      if (ob.orderBy === RetirementOrderByOptions.UI_KEY) {
        x.push({ uiKey: OrderByDirectionSQL[ob.orderByDirection] });
      }
      if (ob.orderBy === RetirementOrderByOptions.UPDATED_AT) {
        x.push({ updatedAt: OrderByDirectionSQL[ob.orderByDirection] });
      }
    }

    return x;
  }
}
