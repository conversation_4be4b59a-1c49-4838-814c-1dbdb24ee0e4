/* third party */
import { EntityManager } from '@mikro-orm/postgresql';
import { Injectable, InternalServerErrorException, UnprocessableEntityException } from '@nestjs/common';
import Decimal from 'decimal.js';
/* rubicon */
import {
  BulkLedgerTransactionsRequest,
  BulkLedgerTransactionsResponse,
  BulkReleasedLedgerTransactionRequest,
  EntryRequest,
  LedgerTransactionAction,
  LedgerTransactionType,
  PendingLedgerTransactionRequest,
  PendingLedgerTransactionResponse,
  SettledLedgerTransactionRequest,
} from '@rubiconcarbon/shared-types';
/* app */
import { AssetFlow, Retirement } from '@app/entities';
import { findAvailablePendingLedgerTransactions } from '@app/helpers/ledger.helper';
import { LedgerFlowsService } from './ledger-flows.service';
import { LedgerTransactionsService } from './ledger-transactions.service';

@Injectable()
export class LedgerRetirementsService {
  constructor(
    private ledgerFlowsService: LedgerFlowsService,
    private ledgerTransactionsService: LedgerTransactionsService,
  ) {}

  async calculateRetirementAmountsTransaction(
    tx: EntityManager,
    now: Date,
    retirement: Retirement,
    assetFlows: AssetFlow[],
  ): Promise<BulkLedgerTransactionsResponse> {
    // get pending RETIREMENT_CREATE transaction
    const knex = tx.getKnex();
    const matchingLedgerTransactions = await findAvailablePendingLedgerTransactions(knex, retirement.id);
    const createdLedgerTransaction = matchingLedgerTransactions.find(
      (f) => f.action === LedgerTransactionAction.RETIREMENT_CREATE,
    );
    if (!createdLedgerTransaction) {
      throw new UnprocessableEntityException(
        `expected to find 1 pending ledger transaction with action ${LedgerTransactionAction.RETIREMENT_CREATE} for ${retirement.id}`,
      );
    }

    // add amounts for all vintages
    const entries: EntryRequest[] = await this.ledgerFlowsService.createEntryRequestFromAssetFlows(tx, assetFlows);
    const entriesSum = Decimal.sum(
      ...entries.filter((f) => new Decimal(f.amount).greaterThan(0)).map((m) => m.amount),
      0,
    ).toNumber();
    if (entriesSum !== retirement.amount) {
      throw new InternalServerErrorException(
        `new entries amounts ${entriesSum} did not match transaction amount ${retirement.amount}`,
      );
    }

    const body: BulkLedgerTransactionsRequest = {
      pendingTransactions: [
        {
          action: LedgerTransactionAction.RETIREMENT_AMOUNT_UPDATE,
          executedAt: now,
          externalId: retirement.id,
          notes: `calulating project vintage amounts for retirement ${retirement.id}`,
          entries,
        },
      ],
    };

    const updatedTransaction = matchingLedgerTransactions.find(
      (f) => f.type === LedgerTransactionType.PENDING && f.action === LedgerTransactionAction.RETIREMENT_AMOUNT_UPDATE,
    );

    if (updatedTransaction) {
      body.releasedTransactions = [
        {
          releasedId: updatedTransaction.id,
          action: LedgerTransactionAction.RETIREMENT_AMOUNT_UPDATE,
          executedAt: now,
          externalId: retirement.id,
          notes: `releasing original amounts for retirement ${retirement.id}`,
        },
      ];
    }

    const response: BulkLedgerTransactionsResponse = await this.ledgerTransactionsService.postBulkTransactions(body);
    // note : this should never happen
    if (response.pendingTransactions?.length !== 1) {
      this.ledgerTransactionsService.revertUnexpectedBulkResponse(
        new Date(),
        `expected 1 pending transaction for calculating retiremetn amounts but got ${response.pendingTransactions?.length}`,
        response,
      );
      throw new InternalServerErrorException(
        `expected 1 pending transactions for calculating retirement amounts but got ${response.pendingTransactions?.length}. reverting transactions`,
      );
    }

    return response;
  }

  async cancelRetirementTransaction(
    tx: EntityManager,
    retirement: Retirement,
  ): Promise<BulkLedgerTransactionsResponse> {
    // get pending transaction
    const knex = tx.getKnex();
    const pendingTransactions = await findAvailablePendingLedgerTransactions(knex, retirement.id);
    if (pendingTransactions.length < 1) {
      throw new InternalServerErrorException(
        `expected to find at least 1 pending ledgerTransactions for ${retirement.id} but found ${pendingTransactions.length}`,
      );
    }

    const releasedTransactions: BulkReleasedLedgerTransactionRequest[] = [];
    pendingTransactions.forEach((pt) => {
      releasedTransactions.push({
        releasedId: pt.id,
        action: LedgerTransactionAction.RETIREMENT_CANCEL,
        executedAt: retirement.updatedAt,
        externalId: retirement.id,
        notes: `cancel retirement ${retirement.id}`,
      });
    });

    const response: BulkLedgerTransactionsResponse = await this.ledgerTransactionsService.postBulkTransactions({
      releasedTransactions,
    });

    return response;
  }

  async createRetirementTransaction(
    tx: EntityManager,
    retirement: Retirement,
    assetFlows: AssetFlow[],
  ): Promise<PendingLedgerTransactionResponse> {
    const assetEntries: EntryRequest[] = await this.ledgerFlowsService.createEntryRequestFromAssetFlows(tx, assetFlows);

    const body: PendingLedgerTransactionRequest = {
      action: LedgerTransactionAction.RETIREMENT_CREATE,
      executedAt: retirement.dateStarted,
      externalId: retirement.id,
      notes: `create pending retirement transaction for ${retirement.uiKey}`,
      entries: assetEntries,
    };

    const response: PendingLedgerTransactionResponse = await this.ledgerTransactionsService.pendingTransaction(body);

    return response;
  }

  async settleRetirementTransaction(
    tx: EntityManager,
    retirement: Retirement,
    assetFlows: AssetFlow[],
  ): Promise<BulkLedgerTransactionsResponse> {
    // add entries for asset flows
    const entries: EntryRequest[] = await this.ledgerFlowsService.createEntryRequestFromAssetFlows(tx, assetFlows);

    const body: BulkLedgerTransactionsRequest = {};
    const settleTransactionRequest: SettledLedgerTransactionRequest = {
      action: LedgerTransactionAction.RETIREMENT_COMPLETE,
      executedAt: retirement.dateFinished || retirement.updatedAt,
      externalId: retirement.id,
      notes: `complete retirement transaction ${retirement.uiKey}`,
      entries,
    };

    body.settledTransactions = [settleTransactionRequest];

    // get pending retirement transaction
    const knex = tx.getKnex();
    const ledgerTransactions = await findAvailablePendingLedgerTransactions(knex, retirement.id);
    const releasedTransactions: BulkReleasedLedgerTransactionRequest[] = [];
    ledgerTransactions.forEach((lt) => {
      releasedTransactions.push({
        releasedId: lt.id,
        action: LedgerTransactionAction.RETIREMENT_COMPLETE,
        executedAt: retirement.updatedAt,
        externalId: retirement.id,
        notes: `releasing pending transactions to complete retirement ${retirement.id}`,
      });
    });
    body.releasedTransactions = releasedTransactions;

    // create settled transaction in ledger, releasing the pending-transaction
    const response: BulkLedgerTransactionsResponse = await this.ledgerTransactionsService.postBulkTransactions(body);

    return response;
  }
}
