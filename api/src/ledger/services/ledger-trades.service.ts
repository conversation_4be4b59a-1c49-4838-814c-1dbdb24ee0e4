/* third party */
import { EntityManager } from '@mikro-orm/postgresql';
import { Injectable, InternalServerErrorException } from '@nestjs/common';
/* rubicon */
import {
  BulkLedgerTransactionsResponse,
  BulkReleasedLedgerTransactionRequest,
  EntryRequest,
  LedgerTransactionAction,
  PendingLedgerTransactionRequest,
  PendingLedgerTransactionResponse,
  ReleasedLedgerTransactionResponse,
  SettledLedgerTransactionRequest,
} from '@rubiconcarbon/shared-types';
/* env */
/* app */
import { Trade } from '@app/entities';
import { findAvailablePendingLedgerTransactions } from '@app/helpers/ledger.helper';
import { LedgerFlowsService } from './ledger-flows.service';
import { LedgerTransactionsService } from './ledger-transactions.service';

@Injectable()
export class LedgerTradesService {
  constructor(
    private ledgerFlowsService: LedgerFlowsService,
    private ledgerTransactionsService: LedgerTransactionsService,
  ) {}

  async cancelPendingTradeTransaction(tx: EntityManager, trade: Trade): Promise<ReleasedLedgerTransactionResponse> {
    // get pending transaction
    const knex = tx.getKnex();
    const ledgerTransactions = await findAvailablePendingLedgerTransactions(knex, trade.id, {
      action: LedgerTransactionAction.TRADES_PLACE,
    });
    if (ledgerTransactions.length !== 1) {
      throw new InternalServerErrorException(
        `expected to find 1 ledgerTransaction for ${trade.id} to cancel but found ${ledgerTransactions.length}`,
      );
    }

    // else release transaction
    const releasedTransaction: ReleasedLedgerTransactionResponse =
      await this.ledgerTransactionsService.releaseTransaction(ledgerTransactions[0].id, {
        action: LedgerTransactionAction.TRADES_CANCEL,
        executedAt: trade.updatedAt,
        notes: `cancel trade ${trade.id}`,
      });

    return releasedTransaction;
  }

  async createPendingTradeTransaction(tx: EntityManager, trade: Trade): Promise<PendingLedgerTransactionResponse> {
    const entries: EntryRequest[] = await this.ledgerFlowsService.createEntryRequestFromAssetFlows(
      tx,
      trade.assetFlows,
    );
    const body: PendingLedgerTransactionRequest = {
      action: LedgerTransactionAction.TRADES_PLACE,
      executedAt: trade.createdAt,
      externalId: trade.id,
      notes: `trade pending transaction ${trade.uiKey || ''} ${trade.type} ${trade.amount}`,
      entries,
    };

    const response: PendingLedgerTransactionResponse = await this.ledgerTransactionsService.pendingTransaction(body);

    return response;
  }

  async settleTradeTransaction(tx: EntityManager, trade: Trade): Promise<BulkLedgerTransactionsResponse> {
    // create settle transaction
    const entries: EntryRequest[] = await this.ledgerFlowsService.createEntryRequestFromAssetFlows(
      tx,
      trade.assetFlows,
    );
    const settleTransactionRequest: SettledLedgerTransactionRequest = {
      action: LedgerTransactionAction.TRADES_SETTLE,
      executedAt: trade.settledAt || trade.updatedAt, // trade.settledAt should be defined by this point
      externalId: trade.id,
      notes: `trade settle transaction ${trade.uiKey || ''} ${trade.type} ${trade.amount}`,
      entries,
    };

    // get pending transaction to release
    const knex = tx.getKnex();
    const ledgerTransactions = await findAvailablePendingLedgerTransactions(knex, trade.id, {
      action: LedgerTransactionAction.TRADES_PLACE,
    });

    if (ledgerTransactions.length !== 1) {
      throw new InternalServerErrorException(
        `expected to find 1 pending ledgerTransactions for ${trade.id} to complete but found ${ledgerTransactions.length}`,
      );
    }

    const releaseTransactionRequest: BulkReleasedLedgerTransactionRequest = {
      releasedId: ledgerTransactions[0].id,
      action: LedgerTransactionAction.TRADES_SETTLE,
      executedAt: trade.settledAt || trade.updatedAt,
      externalId: trade.id,
      notes: `releasing pending transaction for trade ${trade.id} : ${trade.uiKey || ''} ${trade.type} ${trade.amount}`,
    };

    // create settled transaction in ledger, releasing the pending-transaction
    const transactions = await this.ledgerTransactionsService.postBulkTransactions({
      releasedTransactions: [releaseTransactionRequest],
      settledTransactions: [settleTransactionRequest],
    });

    return transactions;
  }

  async updateTradeAmountsTransaction(tx: EntityManager, trade: Trade): Promise<BulkLedgerTransactionsResponse> {
    // create settle transaction
    const entries: EntryRequest[] = await this.ledgerFlowsService.createEntryRequestFromAssetFlows(
      tx,
      trade.assetFlows,
    );

    const pendingTransactionRequest: PendingLedgerTransactionRequest = {
      action: LedgerTransactionAction.TRADES_PLACE,
      executedAt: trade.createdAt,
      externalId: trade.id,
      notes: `trade pending transaction ${trade.uiKey || ''} ${trade.type} ${trade.amount}`,
      entries,
    };

    // get pending transaction to release
    const knex = tx.getKnex();
    const ledgerTransactions = await findAvailablePendingLedgerTransactions(knex, trade.id, {
      action: LedgerTransactionAction.TRADES_PLACE,
    });
    if (ledgerTransactions.length !== 1) {
      throw new InternalServerErrorException(
        `expected to find 1 pending ledgerTransactions for ${trade.id} to complete but found ${ledgerTransactions.length}`,
      );
    }

    const releaseTransactionRequest: BulkReleasedLedgerTransactionRequest = {
      releasedId: ledgerTransactions[0].id,
      action: LedgerTransactionAction.TRADES_SETTLE,
      executedAt: trade.settledAt || trade.updatedAt,
      externalId: trade.id,
      notes: `releasing pending transaction for trade ${trade.id} : ${trade.uiKey || ''} ${trade.type} ${trade.amount}`,
    };

    // create settled transaction in ledger, releasing the pending-transaction
    const transactions = await this.ledgerTransactionsService.postBulkTransactions({
      releasedTransactions: [releaseTransactionRequest],
      pendingTransactions: [pendingTransactionRequest],
    });

    return transactions;
  }
}
