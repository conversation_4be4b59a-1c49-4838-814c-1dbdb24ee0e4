/* third party */
import { EntityManager } from '@mikro-orm/core';
import { Injectable, InternalServerErrorException } from '@nestjs/common';
import Decimal from 'decimal.js';
/* rubicon */
import { EntryRequest } from '@rubiconcarbon/shared-types';
/* app */
import { AssetFlow } from '@app/entities';
import { toBookOwnerType } from '@app/helpers';

@Injectable()
export class LedgerFlowsService {
  async createEntryRequestFromAssetFlows(tx: EntityManager, flows: AssetFlow[]): Promise<EntryRequest[]> {
    // populate just in case
    await tx.populate(flows, ['asset', 'destination', 'source']);

    // create entries, same for both RCT and vintages
    const entries: EntryRequest[] = [];
    for (const flow of flows) {
      if (flow.amount !== 0) {
        // debit
        entries.push({
          assetId: flow.asset.id,
          assetType: flow.assetType,
          ownerId: flow.source.id,
          ownerType: toBookOwnerType(flow.source.type),
          amount: new Decimal(flow.amount).mul(-1),
        });
        // credit
        entries.push({
          assetId: flow.asset.id,
          assetType: flow.assetType,
          ownerId: flow.destination.id,
          ownerType: toBookOwnerType(flow.destination.type),
          amount: new Decimal(flow.amount),
        });
      }
    }

    // validate sums
    const entriesSum = Decimal.sum(
      ...entries.filter((f) => new Decimal(f.amount).greaterThan(0)).map((m) => m.amount),
      0,
    ).toNumber();
    const flowsSum = Decimal.sum(...flows.map((m) => m.amount), 0).toNumber();
    if (entriesSum !== flowsSum) {
      throw new InternalServerErrorException(`new entries amounts ${entriesSum} did not match amount ${flowsSum}`);
    }
    return entries;
  }
}
