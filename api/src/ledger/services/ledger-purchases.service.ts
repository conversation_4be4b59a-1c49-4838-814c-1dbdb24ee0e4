/* third party */
import { EntityManager } from '@mikro-orm/postgresql';
import { Injectable, InternalServerErrorException } from '@nestjs/common';
/* rubicon */
import {
  BulkLedgerTransactionsResponse,
  BulkReleasedLedgerTransactionRequest,
  EntryRequest,
  LedgerTransactionAction,
  LedgerTransactionType,
  PendingLedgerTransactionRequest,
  PendingLedgerTransactionResponse,
  ReleasedLedgerTransactionResponse,
  SettledLedgerTransactionRequest,
} from '@rubiconcarbon/shared-types';
/* app */
import { AssetFlow, Purchase } from '@app/entities';
import { findAvailablePendingLedgerTransactions } from '@app/helpers/ledger.helper';
import { LedgerFlowsService } from './ledger-flows.service';
import { LedgerTransactionsService } from './ledger-transactions.service';

@Injectable()
export class LedgerPurchasesService {
  constructor(
    private ledgerFlowsService: LedgerFlowsService,
    private ledgerTransactionsService: LedgerTransactionsService,
  ) {}

  async createPendingPurchaseTransaction(
    tx: EntityManager,
    purchase: Purchase,
    assetFlows: AssetFlow[],
  ): Promise<PendingLedgerTransactionResponse> {
    const entries: EntryRequest[] = await this.ledgerFlowsService.createEntryRequestFromAssetFlows(tx, assetFlows);
    const body: PendingLedgerTransactionRequest = {
      action: LedgerTransactionAction.PURCHASE_CREATE,
      executedAt: purchase.dateStarted,
      externalId: purchase.id,
      notes: `purchase transaction for ${assetFlows[0].destination.name}`,
      entries,
    };

    const response: PendingLedgerTransactionResponse = await this.ledgerTransactionsService.pendingTransaction(body);

    return response;
  }

  async cancelPendingPurchaseTransaction(
    tx: EntityManager,
    purchase: Purchase,
  ): Promise<ReleasedLedgerTransactionResponse> {
    // get pending transaction
    const knex = tx.getKnex();
    const ledgerTransactions = await findAvailablePendingLedgerTransactions(knex, purchase.id, {
      action: LedgerTransactionAction.PURCHASE_CREATE,
    });

    if (ledgerTransactions.length !== 1) {
      throw new InternalServerErrorException(
        `expected to find 1 ledgerTransaction for ${purchase.id} to cancel but found ${ledgerTransactions.length}`,
      );
    }

    // else release transaction
    const releasedTransaction: ReleasedLedgerTransactionResponse =
      await this.ledgerTransactionsService.releaseTransaction(ledgerTransactions[0].id, {
        action: LedgerTransactionAction.PURCHASE_CANCEL,
        executedAt: purchase.updatedAt,
        notes: `cancel purchase ${purchase.id}`,
      });

    return releasedTransaction;
  }

  async settlePurchaseTransaction(
    tx: EntityManager,
    purchase: Purchase,
    assetFlows: AssetFlow[],
  ): Promise<BulkLedgerTransactionsResponse> {
    const entries: EntryRequest[] = await this.ledgerFlowsService.createEntryRequestFromAssetFlows(tx, assetFlows);
    const settleTransactionRequest: SettledLedgerTransactionRequest = {
      action: LedgerTransactionAction.PURCHASE_COMPLETE,
      executedAt: purchase.dateFinished || purchase.updatedAt,
      externalId: purchase.id,
      notes: `complete purchase transaction ${purchase.id}`,
      entries,
    };

    // get pending purchase transaction
    const knex = tx.getKnex();
    const ledgerTransactions = await findAvailablePendingLedgerTransactions(knex, purchase.id, {
      action: LedgerTransactionAction.PURCHASE_CREATE,
    });

    if (ledgerTransactions.length !== 1) {
      throw new InternalServerErrorException(
        `expected to find 1 pending ledgerTransactions for ${purchase.id} to complete but found ${ledgerTransactions.length}`,
      );
    }

    const releaseTransactionRequest: BulkReleasedLedgerTransactionRequest = {
      releasedId: ledgerTransactions[0].id,
      action: LedgerTransactionAction.PURCHASE_COMPLETE,
      executedAt: purchase.updatedAt,
      externalId: purchase.id,
      notes: `releasing pending transaction for purchase ${purchase.id}`,
    };

    // create settled transaction in ledger, releasing the pending-transaction
    const transactions = await this.ledgerTransactionsService.postBulkTransactions({
      releasedTransactions: [releaseTransactionRequest],
      settledTransactions: [settleTransactionRequest],
    });

    return transactions;
  }
}
