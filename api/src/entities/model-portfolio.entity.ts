/* third party */
import { Collection, Entity, ManyToOne, OneToMany, OptionalProps, Property } from '@mikro-orm/core';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsBoolean, IsEnum, IsOptional } from 'class-validator';
/* env */
import { environment } from '@env/environment';
/* app */
import { BaseEntity } from './base.entity';
import { ModelPortfolioComponent } from './model-portfolio-component.entity';
import { User } from './user.entity';
import { IsNonEmptyString } from '@app/helpers/validations.helper';
import { ModelPortfolioStatus, uuid } from '@rubiconcarbon/shared-types';
import Decimal from 'decimal.js';
import { DecimalType } from '@app/types';
import { IsDecimalGreaterThanOrEqualTo } from '@app/validators';
import { TransformDecimal } from '@app/helpers/transforms.helper';
import { generateRandomKey } from '@app/helpers';
import { Organization } from './organization.entity';

@Entity({ tableName: `model_portfolios`, schema: environment.db.schema.rubicon })
export class ModelPortfolio extends BaseEntity {
  [OptionalProps]?: 'createdAt' | 'isDeleted' | 'organization' | 'updatedAt';

  ///////////////////////////////////////// Fields //////////////////////////////////////////

  @IsBoolean()
  @Property({ index: true })
  isDeleted = false;

  @Expose()
  @IsNonEmptyString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ index: true, nullable: true })
  memo?: string;

  @Expose()
  @ApiProperty({ type: String })
  @Property({ unique: true })
  name!: string;

  @Expose()
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean })
  @Property({ index: true, default: false })
  showCustomer = false;

  @Expose()
  @TransformDecimal()
  @IsDecimalGreaterThanOrEqualTo(new Decimal(0))
  @IsOptional()
  @ApiPropertyOptional({ type: uuid })
  @Property({ nullable: true, type: DecimalType })
  priceEstimate?: Decimal;

  @Expose()
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean })
  @Property({ default: false })
  includeRiskAdjustment = false;

  @Expose()
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean })
  @Property({ default: false })
  rfp = false;

  @Expose()
  @IsEnum(ModelPortfolioStatus)
  @IsOptional()
  @ApiPropertyOptional({ enum: ModelPortfolioStatus, nullable: true })
  @Property({
    nullable: true,
    index: true,
    customOrder: [
      ModelPortfolioStatus.QUOTE_REQUESTED,
      ModelPortfolioStatus.QUOTE_READY,
      ModelPortfolioStatus.CLIENT_REVIEWING,
      ModelPortfolioStatus.ENCUMBER_LITE,
      ModelPortfolioStatus.ORDER_CREATED,
      ModelPortfolioStatus.LOST,
      ModelPortfolioStatus.EXPIRED_REPLACED,
    ],
  })
  status?: ModelPortfolioStatus;

  @Expose()
  @ApiProperty({
    type: String,
    readOnly: true,
    example: generateRandomKey('BYO'),
  })
  @Property({ unique: true })
  uiKey!: string;

  @Expose()
  @ApiProperty({
    type: String,
    format: 'uuid',
    readOnly: true,
    nullable: false,
  })
  @Property({ nullable: false })
  groupingId!: uuid;

  ////////////////////////////////////// Relationships //////////////////////////////////////
  @ManyToOne(() => Organization, { index: true, nullable: true })
  organization?: Organization;

  @OneToMany(() => ModelPortfolioComponent, (component) => component.modelPortfolio)
  modelPortfolioComponents = new Collection<ModelPortfolioComponent>(this);

  @ManyToOne(() => User, { index: true })
  createdBy!: User;

  @ManyToOne(() => User, { index: true })
  updatedBy!: User;
}
