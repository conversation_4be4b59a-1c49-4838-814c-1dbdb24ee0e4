/* third party */
import { Entity, ManyToOne, OneToOne, Property, Unique } from '@mikro-orm/core';
import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import Decimal from 'decimal.js';
/* env */
import { environment } from '@env/environment';
/* app */
import { TransformDecimal } from '@app/helpers/transforms.helper';
import { IsIntGreaterThan } from '@app/validators';
import { IsDecimalGreaterThanOrEqualTo } from '@app/validators/decimal.validator';
import { Asset } from './asset.entity';
import { BaseEntity } from './base.entity';
import { Book } from './book.entity';
import { ProjectVintage } from './project-vintage.entity';

@Entity({ tableName: `rrt_assets`, schema: environment.db.schema.rubicon })
@Unique({ properties: ['rrt', 'projectVintage'] })
export class RrtAsset extends BaseEntity {
  ///////////////////////////////////////// Fields //////////////////////////////////////////

  @Expose()
  @TransformDecimal()
  @IsDecimalGreaterThanOrEqualTo(new Decimal(0))
  @ApiProperty({ type: Number, format: 'decimal', description: 'BeZero percentage of vintage in RRT' })
  @Property()
  beZeroFactor!: Decimal;

  @Property()
  beZeroRatingAtCreation!: string; // not shown to FE or anything for now

  @Expose()
  @IsIntGreaterThan(0)
  @ApiProperty({ type: Number, format: 'integer', description: 'net amount of vintage in RRT' })
  @Property()
  currentNetQuantity!: number;

  @Property()
  initialGrossQuantity!: number; // not shown to FE or anything for now

  @Property()
  initialNetQuantity!: number; // not shown to FE or anything for now

  @Property()
  totalGrossQuantity!: number; // not shown to FE or anything for now

  @Property()
  totalNetQuantity!: number; // not shown to FE or anything for now

  ////////////////////////////////////// Relationships //////////////////////////////////////

  @OneToOne(() => Asset, {
    referenceColumnName: 'id',
    joinColumn: 'id',
    persist: false, // must be false to avoid duplicate "id" field
  })
  asset!: Asset;

  /**
   * {@link ProjectVintage}.
   * @category Relationship
   */
  @Expose()
  @ManyToOne(() => ProjectVintage, { index: true })
  projectVintage!: ProjectVintage;

  /**
   * {@link Book}.
   * @category Relationship
   */
  @Expose()
  @ManyToOne(() => Book, { index: true })
  rrt!: Book;
}
