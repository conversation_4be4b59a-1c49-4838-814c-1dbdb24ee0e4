/* third party */
import { Collection, Entity, ManyToMany, ManyToOne, OneToMany, OptionalProps, Property } from '@mikro-orm/core';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsBoolean, IsOptional } from 'class-validator';
import Decimal from 'decimal.js';
/* rubicon */
import { AssetType, BaseBookLimit, BookAction, BookType } from '@rubiconcarbon/shared-types';
/* env */
import { environment } from '@env/environment';
/* app */
import { TransformDateISO, TransformDecimal } from '@app/helpers/transforms.helper';
import { IsNonEmptyString } from '@app/helpers/validations.helper';
import { DecimalType } from '@app/types';
import { BaseEntity } from './base.entity';
import { Organization } from './organization.entity';
import { ProjectType } from './project-type.entity';
import { Grouping } from './grouping.entity';

@Entity({ tableName: 'books', schema: environment.db.schema.rubicon })
export class Book extends BaseEntity {
  [OptionalProps]!:
    | 'assetType'
    | 'createdAt'
    | 'description'
    | 'organization'
    | 'priceUpdatedAt'
    | 'purchasePrice'
    | 'purchasePriceWithBuffer'
    | 'updatedAt';

  @Property({ nullable: true })
  assetType?: AssetType;

  @Expose()
  @ApiProperty({
    type: [BookAction],
    enum: BookAction,
    description: 'relations to include',
    example: [BookAction.BUY, BookAction.SELL],
  })
  @Property({ type: 'array' })
  allowedActions: BookAction[] = [];

  @Expose()
  @IsNonEmptyString()
  @ApiProperty({ type: String, example: 'Book Name' })
  @Property({ unique: true })
  name!: string;

  @Expose()
  @IsNonEmptyString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true, example: 'compliance credits only' })
  @Property({ nullable: true })
  description?: string;

  @Expose()
  @IsBoolean()
  @ApiProperty({ type: Boolean })
  @Property({ index: true })
  isEnabled!: boolean;

  @Expose()
  @ApiProperty({ type: 'jsonb', readOnly: true, default: '{}' })
  @Property({ type: 'jsonb' })
  limit!: BaseBookLimit;

  @Expose()
  @TransformDateISO()
  @ApiPropertyOptional({ type: Date, format: 'iso', nullable: true, readOnly: true })
  @Property({ defaultRaw: 'CURRENT_TIMESTAMP', index: true, nullable: true })
  priceUpdatedAt?: Date;

  @Expose()
  @TransformDecimal()
  @ApiPropertyOptional({ type: Number, format: 'decimal', nullable: true })
  @Property({ type: DecimalType, nullable: true })
  purchasePrice?: Decimal;

  @Expose()
  @TransformDecimal()
  @ApiPropertyOptional({ type: Number, format: 'decimal', nullable: true })
  @Property({ type: DecimalType, nullable: true })
  purchasePriceWithBuffer?: Decimal;

  @Property({ type: 'string' })
  type!: BookType;

  /// relations ///

  /**
   * {@link Grouping}.
   * @category Relationship
   */
  @OneToMany(() => Grouping, (g) => g.book)
  groupings = new Collection<Grouping>(this);

  /**
   * {@link Organization}.
   * @category Relationship
   * this is the org that OWNS the book (aka customer portfolio).
   */
  @ManyToOne(() => Organization, { index: true, nullable: true })
  organization?: Organization;

  /**
   * {@link ProjectType}.
   * @category Relationship
   * project-types of vintage credits that are allowed to be in the owner book
   * mainly for rct:public and rct:custom
   */
  @ManyToMany({ entity: () => ProjectType, pivotTable: 'books_project_types' })
  projectTypes = new Collection<ProjectType>(this);
}
