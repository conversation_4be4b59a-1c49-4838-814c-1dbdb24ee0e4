/* third party */
import { Entity, IntegerType, ManyToOne, OneToOne, OptionalProps, Property } from '@mikro-orm/core';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsDate, IsEnum, IsIn, IsOptional } from 'class-validator';
/* rubicon */
import {
  AllAssetTypes,
  AssetType,
  PurchaseFlowType,
  PurchaseStatus,
  PurchaseUpdatableStatus,
} from '@rubiconcarbon/shared-types';
/* env */
import { environment } from '@env/environment';
/* app */
import { generateRandomKey } from '@app/helpers/random.helper';
import { TransformDateISO } from '@app/helpers/transforms.helper';
import { IsNonEmptyString } from '@app/helpers/validations.helper';
import { IsIntGreaterThan } from '@app/validators';
import { AssetFlow } from './asset-flow.entity';
import { BaseEntity } from './base.entity';
import { CustomerPortfolio } from './customer-portfolio.entity';
import { TransactionDetail } from './transaction-detail.entity';

@Entity({ tableName: `purchases`, schema: environment.db.schema.rubicon })
export class Purchase extends BaseEntity {
  [OptionalProps]?: 'createdAt' | 'updatedAt' | 'amounts' | 'transactionDetails';

  ///////////////////////////////////////// Fields //////////////////////////////////////////

  @Expose()
  @IsIntGreaterThan(0)
  @ApiProperty({ type: 'integer' })
  @Property({ type: IntegerType })
  amount!: number;

  @Expose()
  // @IsEnum(AssetType) // todo RBC-3163 : add back in
  @IsIn(AllAssetTypes)
  @ApiProperty({ enum: AssetType })
  @Property({ type: 'string' })
  assetType!: AssetType;

  @Expose()
  @TransformDateISO()
  @ApiPropertyOptional({ type: Date, format: 'iso', readOnly: true, nullable: true })
  @Property({ nullable: true, index: true })
  dateFinished?: Date;

  @Expose()
  @TransformDateISO()
  @ApiProperty({ type: Date, format: 'iso', readOnly: true })
  @Property({ index: true })
  dateStarted!: Date;

  @Expose()
  @IsEnum(PurchaseFlowType)
  @ApiProperty({ enum: PurchaseFlowType })
  @Property({ type: 'string' })
  flowType!: PurchaseFlowType;

  @Expose()
  @ApiProperty({ type: Boolean })
  @Property({ index: true })
  isDelivered!: boolean;

  @Expose()
  @ApiProperty({ type: Boolean })
  @Property({ index: true })
  isPaid!: boolean;

  @Expose()
  @IsNonEmptyString()
  @IsOptional()
  @ApiPropertyOptional({
    type: String,
    nullable: true,
  })
  @Property({ nullable: true })
  memo?: string;

  @Expose()
  @ApiProperty({
    type: Boolean,
    readOnly: true,
    description: 'determines if riskAdjustment is required on a purchase transaction',
  })
  @Property({ nullable: true })
  needsRiskAdjustment?: boolean;

  @Expose()
  @TransformDateISO()
  @IsDate()
  @IsOptional()
  @ApiPropertyOptional({
    type: Date,
    format: 'iso',
    nullable: true,
  })
  @Property({ nullable: true })
  paymentDueDate?: Date;

  @Expose()
  @ApiProperty({
    enum: PurchaseStatus,
    readOnly: true,
  })
  @Property({ type: 'string' })
  status!: PurchaseStatus;

  @Expose()
  @ApiProperty({
    type: String,
    readOnly: true,
    example: generateRandomKey('RR'),
  })
  @Property({ unique: true })
  uiKey!: string;

  @Expose()
  @ApiProperty({
    type: [String],
    readOnly: true,
    example: ['pending_payment', 'pending_delivery'],
  })
  @Property({ type: 'array' })
  updatableStatusOrder!: PurchaseUpdatableStatus[];

  /// non-persisted fields ///

  /**
   * {@link VintageFlow}.
   * @category Relationship
   */
  @Property({ persist: false })
  assetFlows: AssetFlow[] = [];

  /// relations ///

  /**
   * {@link CustomerPortfolio}.
   * @category Relationship
   */
  @ManyToOne(() => CustomerPortfolio, { index: true })
  customerPortfolio!: CustomerPortfolio;

  @OneToOne(() => TransactionDetail, {
    referenceColumnName: 'id',
    joinColumn: 'id',
    persist: false,
  })
  transactionDetails!: TransactionDetail;
}
