/* third party */
import { <PERSON><PERSON><PERSON>, ManyToOne, PrimaryKey, Property } from '@mikro-orm/core';
import Decimal from 'decimal.js';
/* rubicon */
import { ProjectTypeCategory, uuid } from '@rubiconcarbon/shared-types';
/* env */
import { environment } from '@env/environment';
/* app */
import { DateRangeType, DecimalType, InternalDateRange } from '@app/types';
import { Project } from './project.entity';
import { ProjectType } from './project-type.entity';

@Entity({ tableName: `project_vintage_groupings_v2`, schema: environment.db.schema.rubicon, readonly: true })
export class ProjectVintageGrouping {
  @PrimaryKey({ type: 'uuid' })
  readonly project_vintage_id!: uuid;

  @Property({ type: 'uuid' })
  buffer_category_id!: uuid;

  @Property()
  buffer_category_name!: string;

  @Property()
  is_rct_eligible!: boolean;

  @Property({ nullable: true })
  project_be_zero_rating?: string;

  @Property({ nullable: true })
  project_be_zero_updated_date?: string;

  @Property({ nullable: true })
  project_country_alpha3?: string;

  @Property({ nullable: true })
  project_country_name?: string;

  @Property({ nullable: true })
  project_integrity_grade_score?: number;

  @Property({ nullable: true })
  project_integrity_grade_score_risk_adjusted?: number;

  @Property()
  project_is_science_team_approved!: boolean;

  @Property()
  project_is_byorct_approved!: boolean;

  @Property()
  project_is_rct_standard!: boolean;

  @Property()
  project_is_suspended!: boolean;

  @Property()
  project_name!: string;

  @Property()
  project_registry_name?: string;

  @Property()
  project_type_category!: ProjectTypeCategory;

  @Property()
  project_type_type!: string;

  @Property({ type: DecimalType, nullable: true })
  project_vintage_high_buffer_percentage?: Decimal;

  @Property({ type: DateRangeType })
  project_vintage_interval!: InternalDateRange;

  @Property({ nullable: true })
  project_vintage_label?: string;

  @Property({ type: DecimalType, nullable: true })
  project_vintage_low_buffer_percentage?: Decimal;

  @Property({ type: DecimalType, nullable: true })
  project_vintage_risk_buffer_percentage?: Decimal;

  @Property()
  registry_project_id!: string;

  /// relations ///

  /**
   * {@link Project}.
   * @category Relationship
   */
  @ManyToOne(() => Project, { name: 'project_id', index: true })
  project!: Project;

  /**
   * {@link ProjectType}.
   * @category Relationship
   */
  @ManyToOne(() => ProjectType, { name: 'project_type_id', index: true })
  projectType!: ProjectType;
}
