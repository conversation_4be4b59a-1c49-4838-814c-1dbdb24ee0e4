import { Agreement } from './agreement.entity';
import { AlertEntry } from './alert-entry.entity';
import { AlertSubscription } from './alert-subscription.entity';
import { Asset } from './asset.entity';
import { AssetDetail } from './asset-detail.entity';
import { AssetFlow } from './asset-flow.entity';
import { AuditLog } from './audit-log.entity';
import { Book } from './book.entity';
import { Counterparty } from './counterparty.entity';
import { Country } from './country.entity';
import { CreditFlow } from './credit-flow.entity';
import { CustomerPortfolio } from './customer-portfolio.entity';
import { Document } from './document.entity';
import { DefaultFeeStructure } from './default-fee-structure.entity';
import { FeatureFlag } from './feature-flags.entity';
import { Forward } from './forward.entity';
import { ForwardLineItem } from './forward-line-item.entity';
import { Grouping } from './grouping.entity';
import { GroupingParent } from './grouping-parent.entity';
import { HistoricalBookPrice } from '@app/entities/historical-book-price.entity';
import { HistoricalRiskBuffer } from '@app/entities/historical-risk-buffer.entity';
import { MarketNews } from './market-news.entity';
import { MarketingAgreement } from './marketing-agreement.entity';
import { MarketingAgreementLineItem } from './marketing-agreement-line-item.entity';
import { ModelPortfolio } from './model-portfolio.entity';
import { ModelPortfolioComponent } from './model-portfolio-component.entity';
import { NotificationEntry } from './notification-data.entity';
import { NotificationSubscription } from './notification-subscription.entity';
import { Organization } from './organization.entity';
import { OrganizationUser } from './organization-user.entity';
import { Project } from './project.entity';
import { ProjectFlags } from './project-flags.entity';
import { ProjectSDG } from './project-sdg.entity';
import { ProjectType } from './project-type.entity';
import { ProjectUpdateLog } from './project-update-log.entity';
import { ProjectVintage } from './project-vintage.entity';
import { Purchase } from './purchase.entity';
import { PurchaseRetirement } from './purchase-retirement.entity';
import { Quote } from './quote.entity';
import { Reserve } from './reserve.entity';
import { Registry } from './registry.entity';
import { Retirement } from './retirement.entity';
import { RetirementLink } from './retirement-link.entity';
import { RrtAsset } from './rrt-asset.entity';
import { SDGType } from './sdg-type.entity';
import { ShortURL } from './short-url.entity';
import { Trade } from './trade.entity';
import { TradeConfirmation } from './trade-confirmation.entity';
import { TradeConfirmationProduct } from './trade-confirmation-product.entity';
import { TradeCounterparty } from './trade-counterparty.entity';
import { Transaction } from './transaction.entity';
import { TransactionAsset } from './transaction-asset.entity';
import { TransactionDetail } from './transaction-detail.entity';
import { TransactionStatusLog } from './transaction-status-log.entity';
import { Transfer } from './transfer.entity';
import { UserCookieConsentLog } from './user-cookie-consent-log.entity';
import { User } from './user.entity';
import { UserAction } from './user-action.entity';

export { Agreement } from './agreement.entity';
export { AlertEntry } from './alert-entry.entity';
export { AlertSubscription } from './alert-subscription.entity';
export { Asset } from './asset.entity';
export { AssetDetail } from './asset-detail.entity';
export { AssetFlow } from './asset-flow.entity';
export { AuditLog } from './audit-log.entity';
export { Book } from './book.entity';
export { Counterparty } from './counterparty.entity';
export { Country } from './country.entity';
export { CreditFlow } from './credit-flow.entity';
export { CustomerPortfolio } from './customer-portfolio.entity';
export { Document } from './document.entity';
export { DefaultFeeStructure } from './default-fee-structure.entity';
export { FeatureFlag } from './feature-flags.entity';
export { Forward } from './forward.entity';
export { ForwardLineItem } from './forward-line-item.entity';
export { Grouping } from './grouping.entity';
export { GroupingParent } from './grouping-parent.entity';
export { HistoricalBookPrice } from '@app/entities/historical-book-price.entity';
export { HistoricalRiskBuffer } from '@app/entities/historical-risk-buffer.entity';
export { MarketNews } from './market-news.entity';
export { MarketingAgreement } from './marketing-agreement.entity';
export { MarketingAgreementLineItem } from './marketing-agreement-line-item.entity';
export { ModelPortfolio } from './model-portfolio.entity';
export { ModelPortfolioComponent } from './model-portfolio-component.entity';
export { NotificationEntry } from './notification-data.entity';
export { NotificationSubscription } from './notification-subscription.entity';
export { Organization } from './organization.entity';
export { OrganizationUser } from './organization-user.entity';
export { Project } from './project.entity';
export { ProjectFlags } from './project-flags.entity';
export { ProjectSDG } from './project-sdg.entity';
export { ProjectType } from './project-type.entity';
export { ProjectUpdateLog } from './project-update-log.entity';
export { ProjectVintage } from './project-vintage.entity';
export { Purchase } from './purchase.entity';
export { PurchaseRetirement } from './purchase-retirement.entity';
export { Quote } from './quote.entity';
export { Registry } from './registry.entity';
export { Reserve } from './reserve.entity';
export { Retirement } from './retirement.entity';
export { RetirementLink } from './retirement-link.entity';
export { RrtAsset } from './rrt-asset.entity';
export { SDGType } from './sdg-type.entity';
export { ShortURL } from './short-url.entity';
export { Trade } from './trade.entity';
export { TradeConfirmation } from './trade-confirmation.entity';
export { TradeConfirmationProduct } from './trade-confirmation-product.entity';
export { TradeCounterparty } from './trade-counterparty.entity';
export { Transaction } from './transaction.entity';
export { TransactionAsset } from './transaction-asset.entity';
export { TransactionDetail } from './transaction-detail.entity';
export { TransactionStatusLog } from './transaction-status-log.entity';
export { Transfer } from './transfer.entity';
export { UserCookieConsentLog } from './user-cookie-consent-log.entity';
export { User } from './user.entity';

export const Entities = [
  Agreement,
  AlertEntry,
  AlertSubscription,
  Asset,
  AssetDetail,
  AssetFlow,
  AuditLog,
  Book,
  Country,
  Counterparty,
  CreditFlow,
  CustomerPortfolio,
  Document,
  DefaultFeeStructure,
  FeatureFlag,
  Forward,
  ForwardLineItem,
  Grouping,
  GroupingParent,
  HistoricalBookPrice,
  HistoricalRiskBuffer,
  MarketNews,
  MarketingAgreement,
  MarketingAgreementLineItem,
  ModelPortfolio,
  ModelPortfolioComponent,
  NotificationSubscription,
  NotificationEntry,
  Organization,
  OrganizationUser,
  Project,
  ProjectFlags,
  ProjectSDG,
  ProjectType,
  ProjectUpdateLog,
  ProjectVintage,
  Purchase,
  PurchaseRetirement,
  Quote,
  Registry,
  Reserve,
  Retirement,
  RetirementLink,
  RrtAsset,
  SDGType,
  ShortURL,
  Trade,
  TradeConfirmation,
  TradeConfirmationProduct,
  TradeCounterparty,
  Transaction,
  TransactionAsset,
  TransactionDetail,
  TransactionStatusLog,
  Transfer,
  UserCookieConsentLog,
  User,
  UserAction,
];

/* istanbul ignore file */
