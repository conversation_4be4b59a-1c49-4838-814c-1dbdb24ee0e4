/* third party */
import {
  Collection,
  Entity,
  IntegerType,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  OptionalProps,
  PrimaryKey,
  Property,
} from '@mikro-orm/core';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsEnum,
  IsInt,
  IsLatitude,
  IsLongitude,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
/* rubicon */
import {
  ProjectBeZeroRating,
  ProjectEligibilityAccreditation,
  ProjectEmissionsImpactType,
  ProjectRiskScore,
  uuid,
} from '@rubiconcarbon/shared-types';
/* env */
import { environment } from '@env/environment';
/* app */
import { TransformDateISO } from '@app/helpers/transforms.helper';
import { IsNonEmptyString } from '@app/helpers/validations.helper';
import { IsIntGreaterThanOrEqualTo, IsNumberBetween } from '@app/validators';
import { BufferCategory } from './buffer-category.entity';
import { Country } from './country.entity';
import { MarketNews } from './market-news.entity';
import { ProjectFlags } from './project-flags.entity';
import { ProjectSDG } from './project-sdg.entity';
import { ProjectType } from './project-type.entity';
import { ProjectVintage } from './project-vintage.entity';
import { Registry } from './registry.entity';

@Entity({ tableName: `projects`, schema: environment.db.schema.rubicon })
export class Project {
  [OptionalProps]?: 'createdAt' | 'updatedAt' | 'flags';

  /**
   * Unique database object id.
   */
  @Expose()
  @IsUUID()
  @ApiProperty({ type: 'string', format: 'uuid' })
  @PrimaryKey({ type: 'uuid', defaultRaw: 'gen_random_uuid()' })
  readonly id!: uuid;

  /**
   * Created at timestamp.
   */
  @Expose()
  @Property({ defaultRaw: 'CURRENT_TIMESTAMP', index: true })
  createdAt: Date = new Date();

  /**
   * Last updated at timestamp.
   */
  @Expose()
  @Property({
    defaultRaw: 'CURRENT_TIMESTAMP',
    onUpdate: /* istanbul ignore next */ () => new Date(),
    index: true,
  })
  updatedAt: Date = new Date();

  ///////////////////////////////////////// Fields //////////////////////////////////////////

  @Expose()
  @IsString()
  @ApiProperty({ type: String, description: 'unique name for project' })
  @Property({ unique: true })
  name!: string;

  @Expose()
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true, deprecated: true })
  @Property({ nullable: true, index: true })
  projectLocation?: string;

  @Expose()
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true, index: true })
  countryRegion?: string;

  @Expose()
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true, index: true, length: 3 })
  countryCode?: string;

  @Expose()
  @IsString()
  @ApiPropertyOptional({ type: String })
  @Property({ unique: true })
  registryProjectId!: string;

  @Expose()
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true, index: true })
  registryName?: string;

  @Expose()
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true, index: true })
  independentVerifierName?: string;

  @Expose()
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true, index: true })
  projectDeveloperName?: string;

  @Expose()
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true })
  registryLink?: string;

  @Expose()
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true })
  pddReportLink?: string;

  @Expose()
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true })
  vvbReportLink?: string;

  @Expose()
  @TransformDateISO()
  @IsDate()
  @IsOptional()
  @ApiPropertyOptional({ type: Date, format: 'iso', nullable: true })
  @Property({ nullable: true, index: true })
  dateOfLatestVerification?: Date; // for consistency, either lastVerifiedAt or lastVerificationDate

  @Expose()
  @TransformDateISO()
  @IsDate()
  @IsOptional()
  @ApiPropertyOptional({ type: Date, format: 'iso', nullable: true })
  @Property({ nullable: true, index: true })
  startDate?: Date;

  @Expose()
  @TransformDateISO()
  @IsDate()
  @IsOptional()
  @ApiPropertyOptional({ type: Date, format: 'iso', nullable: true })
  @Property({ nullable: true, index: true })
  endDate?: Date;

  @Expose()
  @IsIntGreaterThanOrEqualTo(0)
  @IsOptional()
  @ApiPropertyOptional({ type: 'integer', minimum: 0, exclusiveMinimum: false, nullable: true })
  @Property({ type: IntegerType, nullable: true })
  registryCreditsIssued?: number;

  @Expose()
  @IsIntGreaterThanOrEqualTo(0)
  @IsOptional()
  @ApiPropertyOptional({ type: 'integer', minimum: 0, exclusiveMinimum: false, nullable: true })
  @Property({ type: IntegerType, nullable: true })
  registryCreditsRetired?: number;

  @Expose()
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true })
  projectDescription?: string;

  @Expose()
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true })
  additionalityBlurb?: string;

  @Expose()
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true })
  permanenceBlurb?: string;

  @Expose()
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true })
  previewImageUrl?: string;

  @Expose()
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true })
  squareImage1Url?: string;

  @Expose()
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true })
  squareImage2Url?: string;

  @Expose()
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true })
  mapImageUrl?: string;

  @Expose()
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true })
  illustrationImageUrl?: string;

  @Expose()
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ nullable: true })
  @Property({ nullable: true })
  otherCoBenefitsBlurb?: string;

  @Expose()
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true })
  bookChartDisplayGroup?: string;

  @Expose()
  @IsEnum(ProjectRiskScore)
  @IsOptional()
  @ApiPropertyOptional({ enum: ProjectRiskScore, nullable: true })
  @Property({ nullable: true })
  categoryRiskScore?: ProjectRiskScore;

  @Expose()
  @IsEnum(ProjectRiskScore)
  @IsOptional()
  @ApiPropertyOptional({ enum: ProjectRiskScore, nullable: true })
  @Property({ nullable: true })
  overallRiskScore?: ProjectRiskScore;

  @Expose()
  @IsNonEmptyString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true })
  methodologyBlurb?: string;

  @Expose()
  @IsNonEmptyString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true })
  carbonAccountingBlurb?: string;

  @Expose()
  @IsNonEmptyString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true })
  waterBlurb?: string;

  @Expose()
  @IsLatitude()
  @IsOptional()
  @ApiPropertyOptional({ type: Number, format: 'iso latitude', nullable: true })
  @Property({ nullable: true })
  kmlLatitude?: number;

  @Expose()
  @IsLongitude()
  @IsOptional()
  @ApiPropertyOptional({ type: Number, format: 'iso longitude', nullable: true })
  @Property({ nullable: true })
  kmlLongitude?: number;

  @Expose()
  @IsArray()
  @IsEnum(ProjectEligibilityAccreditation, { each: true })
  @IsOptional()
  @ApiPropertyOptional({ enum: ProjectEligibilityAccreditation, isArray: true, nullable: true, default: [] })
  @Property({ type: 'array' })
  eligibilityAccreditations: ProjectEligibilityAccreditation[] = [];

  @Expose()
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean, nullable: true, default: false })
  @Property({ default: false })
  isScienceTeamApproved = false; // or science_signoff_website_ready, but I think that's too wordy

  @Expose()
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean, nullable: true, default: false })
  @Property({ default: false })
  rctStandard = false;

  @Expose()
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean, nullable: true, default: false })
  @Property({ default: false })
  suspended = false;

  @Expose()
  @IsNonEmptyString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true })
  kmlUrl?: string;

  @Expose()
  @IsEnum(ProjectEmissionsImpactType)
  @IsOptional()
  @ApiPropertyOptional({ enum: ProjectEmissionsImpactType, nullable: true })
  @Property({ nullable: true })
  emissionsImpactType?: ProjectEmissionsImpactType;

  @Expose()
  @IsNumber()
  @IsOptional()
  @ApiPropertyOptional({ type: Number, nullable: true })
  @Property({ nullable: true })
  minPercentage?: number;

  @Expose()
  @IsNumber()
  @IsOptional()
  @ApiPropertyOptional({ type: Number, nullable: true })
  @Property({ nullable: true })
  maxPercentage?: number;

  @Expose()
  @IsInt()
  @IsOptional()
  @ApiPropertyOptional({ type: 'integer', nullable: true })
  @Property({ nullable: true })
  minQuantity?: number;

  @Expose()
  @IsInt()
  @IsOptional()
  @ApiPropertyOptional({ type: 'integer', nullable: true })
  @Property({ nullable: true })
  maxQuantity?: number;

  @Expose()
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean, nullable: true, default: false })
  @Property({ default: false })
  isByorctApproved = false;

  @Expose()
  @IsNonEmptyString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true })
  analystInsightsBlurb?: string;

  @Expose()
  @IsNonEmptyString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true })
  analystName?: string;

  @Expose()
  @IsNonEmptyString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true })
  analystRole?: string;

  @Expose()
  @IsNumber()
  @IsOptional()
  @IsNumberBetween(0, 100)
  @ApiPropertyOptional({ type: Number, minimum: 0, maximum: 100, nullable: true })
  @Property({ nullable: true })
  additionalityScore?: number;

  @Expose()
  @IsNonEmptyString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true })
  certificationBlurb?: string;

  @Expose()
  @IsNumber()
  @IsOptional()
  @IsNumberBetween(0, 100)
  @ApiPropertyOptional({ type: Number, minimum: 0, maximum: 100, nullable: true })
  @Property({ nullable: true })
  certificationScore?: number;

  @Expose()
  @IsNumber()
  @IsOptional()
  @IsNumberBetween(0, 100)
  @ApiPropertyOptional({ type: Number, minimum: 0, maximum: 100, nullable: true })
  @Property({ nullable: true })
  climateImpact?: number;

  @Expose()
  @IsNonEmptyString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true })
  climateImpactBlurb?: string;

  @Expose()
  @IsNumber()
  @IsOptional()
  @IsNumberBetween(0, 100)
  @ApiPropertyOptional({ type: Number, minimum: 0, maximum: 100, nullable: true })
  @Property({ nullable: true })
  climateImpactRiskAdjusted?: number;

  @Expose()
  @IsNumber()
  @IsOptional()
  @IsNumberBetween(0, 100)
  @ApiPropertyOptional({ type: Number, minimum: 0, maximum: 100, nullable: true })
  @Property({ nullable: true })
  durabilityScore?: number;

  @Expose()
  @IsNonEmptyString()
  @IsOptional()
  @ApiPropertyOptional({ type: String, nullable: true })
  @Property({ nullable: true })
  futureDeliveryBlurb?: string;

  @Expose()
  @IsNumber()
  @IsOptional()
  @IsNumberBetween(0, 100)
  @ApiPropertyOptional({ type: Number, minimum: 0, maximum: 100, nullable: true })
  @Property({ nullable: true })
  futureDeliveryRisk?: number;

  @Expose()
  @IsNumber()
  @IsOptional()
  @IsNumberBetween(0, 100)
  @ApiPropertyOptional({ type: Number, minimum: 0, maximum: 100, nullable: true })
  @Property({ nullable: true })
  integrityGradeScore?: number;

  @Expose()
  @IsNumber()
  @IsOptional()
  @IsNumberBetween(0, 100)
  @ApiPropertyOptional({ type: Number, minimum: 0, maximum: 100, nullable: true })
  @Property({ nullable: true })
  integrityGradeScoreRiskAdjusted?: number;

  @Expose()
  @TransformDateISO()
  @IsDate()
  @IsOptional()
  @ApiPropertyOptional({ type: Date, format: 'iso', nullable: true })
  @Property({ nullable: true, index: true })
  lastReviewDate?: Date;

  @Expose()
  @IsEnum(ProjectBeZeroRating)
  @IsOptional()
  @ApiPropertyOptional({ enum: ProjectBeZeroRating, nullable: true })
  @Property({ nullable: true })
  beZeroRating?: ProjectBeZeroRating;

  @Expose()
  @TransformDateISO()
  @IsDate()
  @IsOptional()
  @ApiPropertyOptional({ type: Date, format: 'iso', nullable: true })
  @Property({ nullable: true, index: true })
  beZeroUpdatedDate?: Date;

  ////////////////////////////////////// Relationships //////////////////////////////////////

  /**
   * {@link ProjectType}.
   * @category Relationship
   */
  @ManyToOne(() => ProjectType, { index: true })
  projectType!: ProjectType;

  @ManyToOne(() => Country, {
    index: true,
    joinColumn: 'country_code',
    referenceColumnName: 'alpha3',
    nullable: true,
    persist: false,
  })
  country?: Country;

  @OneToMany(() => ProjectVintage, (obj) => obj.project)
  projectVintages = new Collection<ProjectVintage>(this);

  @ManyToMany(() => MarketNews, (mn) => mn.projects)
  marketNews = new Collection<MarketNews>(this);

  @OneToMany(() => ProjectSDG, (obj) => obj.project, { fieldName: 'project_sdgs', orphanRemoval: true })
  projectSDGs = new Collection<ProjectSDG>(this);

  @ManyToOne(() => Registry, { index: true })
  registry!: Registry;

  @OneToOne(() => ProjectFlags, { persist: false, joinColumn: 'id' })
  flags?: ProjectFlags;

  @ManyToOne(() => BufferCategory, { index: true, nullable: true })
  bufferCategory?: BufferCategory;
}
