/* third party */
import { BigIntType, Entity, ManyToOne, Property } from '@mikro-orm/core';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
/* rubicon */
import { AssetType, TransactionSubtype, TransactionType, uuid } from '@rubiconcarbon/shared-types';
/* env */
import { environment } from '@env/environment';
/* app */
import { BaseEntity } from './base.entity';
import Decimal from 'decimal.js';
import { DecimalType } from '@app/types';
import { TransformDecimal } from '@app/helpers/transforms.helper';
import { TransactionDetail } from './transaction-detail.entity';

@Entity({ tableName: `transaction_assets_v2`, schema: environment.db.schema.rubicon, readonly: true })
export class TransactionAsset extends BaseEntity {
  /// Fields ///

  @Expose()
  @ApiProperty({ type: String, format: 'uuid', readOnly: true })
  @Property({ type: 'uuid' })
  assetId!: uuid;

  @Expose()
  @ApiProperty({ type: String, readOnly: true })
  @Property({ type: 'string' })
  assetName!: string;

  @Expose()
  @TransformDecimal()
  @ApiProperty({ type: Number, format: 'decimal' })
  @Property({ type: DecimalType })
  assetOtherFee!: Decimal;

  @Expose()
  @ApiProperty({ type: String, readOnly: true })
  @Property({ type: 'string' })
  assetPortfolioName!: string;

  @Expose()
  @ApiProperty({ type: String, readOnly: true })
  @Property({ type: 'string' })
  assetProjectName!: string;

  @Expose()
  @ApiProperty({ type: Number, format: 'integer' })
  @Property({ type: BigIntType })
  assetQuantity!: number;

  @Expose()
  @TransformDecimal()
  @ApiProperty({ type: Number, format: 'decimal' })
  @Property({ type: DecimalType })
  assetRawPrice!: Decimal;

  @Expose()
  @ApiPropertyOptional({ type: String, readOnly: true, nullable: true })
  @Property({ type: 'string', nullable: true })
  assetRegistryProjectId?: string;

  @Expose()
  @TransformDecimal()
  @ApiProperty({ type: Number, format: 'decimal' })
  @Property({ type: DecimalType })
  assetServiceFee!: Decimal;

  @Expose()
  @ApiProperty({ enum: AssetType, readOnly: true })
  @Property({ type: 'string' })
  assetType!: AssetType;

  @Expose()
  @ApiProperty({ type: Boolean, readOnly: true })
  @Property()
  hasAccountingEntries!: boolean;

  @ApiPropertyOptional({ type: String, format: 'uuid', readOnly: true, nullable: true })
  @Property({ type: 'uuid', nullable: true })
  parentTransactionId?: uuid;

  @ApiProperty({ type: String, format: 'uuid', readOnly: true })
  @Property({ type: 'uuid' })
  transactionId!: uuid;

  @Expose()
  @ApiPropertyOptional({ type: String, readOnly: true, nullable: true })
  @Property({ type: 'string', nullable: true })
  transactionSubtype?: TransactionSubtype;

  @Expose()
  @ApiProperty({ enum: TransactionType, readOnly: true })
  @Property({ type: 'string' })
  transactionType!: TransactionType;

  /* relationships */

  /**
   * {@link TransactionDetail}.
   * @category Relationship
   */
  @ManyToOne(() => TransactionDetail, {
    nullable: false,
    persist: false,
  })
  transaction!: TransactionDetail;
}
