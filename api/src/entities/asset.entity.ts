/* third party */
import { Entity, OneToOne, OptionalProps, Property } from '@mikro-orm/core';
import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
/* rubicon */
import { AssetType } from '@rubiconcarbon/shared-types';
/* env */
import { environment } from '@env/environment';
/* app */
import { AssetDetail } from './asset-detail.entity';
import { BaseEntity } from './base.entity';

@Entity({ tableName: `assets`, schema: environment.db.schema.rubicon })
export class Asset extends BaseEntity {
  [OptionalProps]!: 'id' | 'createdAt' | 'updatedAt' | 'details';

  /// Fields ///

  @Expose()
  @ApiProperty({ enum: AssetType, readOnly: true })
  @Property({ type: 'string', fieldName: 'asset_type' })
  type!: AssetType;

  /// relations ///

  @OneToOne(() => AssetDetail, {
    referenceColumnName: 'id',
    joinColumn: 'id',
    persist: false,
  })
  details!: AssetDetail;
}
