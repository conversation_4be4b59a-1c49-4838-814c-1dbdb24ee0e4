/* third party */
import { <PERSON>ti<PERSON>, OneToOne, PrimaryKey, Property } from '@mikro-orm/core';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
/* rubicon */
import { AssetType, uuid } from '@rubiconcarbon/shared-types';
/* env */
import { environment } from '@env/environment';
import { Book } from './book.entity';
import { ProjectVintageGrouping } from './project-vintage-groupings';
import { RrtAsset } from './rrt-asset.entity';

@Entity({ tableName: `asset_details_v2`, schema: environment.db.schema.rubicon, readonly: true })
export class AssetDetail {
  /// Fields ///

  @PrimaryKey({ type: 'uuid' })
  readonly id!: uuid;

  @Expose()
  @ApiProperty({ type: Boolean, readOnly: true })
  @Property({ type: 'boolean' })
  isPublic!: boolean;

  @Expose()
  @ApiProperty({ type: String, readOnly: true })
  @Property({ type: 'string' })
  name!: string;

  @Expose()
  @ApiPropertyOptional({ type: String, format: 'uuid', readOnly: true, nullable: true })
  @Property({ type: 'uuid', nullable: true })
  portfolioId?: uuid;

  @Expose()
  @ApiPropertyOptional({ type: String, format: 'uuid', readOnly: true, nullable: true })
  @Property({ type: 'uuid', nullable: true })
  projectVintageId?: uuid;

  @Expose()
  @ApiProperty({ enum: AssetType, readOnly: true })
  @Property({ type: 'string', fieldName: 'asset_type' })
  type!: AssetType;

  /// relations ///

  @OneToOne(() => Book, {
    referenceColumnName: 'id',
    joinColumn: 'portfolio_id',
    nullable: true,
    persist: false,
  })
  portfolioDetails?: Book;

  @OneToOne(() => RrtAsset, {
    referenceColumnName: 'id',
    joinColumn: 'id',
    nullable: true,
    persist: false,
  })
  rrtDetails?: RrtAsset;

  @OneToOne(() => ProjectVintageGrouping, {
    referenceColumnName: 'project_vintage_id',
    joinColumn: 'project_vintage_id',
    nullable: true,
    persist: false,
  })
  vintageDetails?: ProjectVintageGrouping;
}
