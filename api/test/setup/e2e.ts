/* third party */
import { createMock } from '@golevelup/ts-jest';
import { RedisService } from '@liaoliaots/nestjs-redis';
import { MikroORM } from '@mikro-orm/core';
import { EntityManager, Knex, PostgreSqlConnection } from '@mikro-orm/postgresql';
import { ExecutionContext, INestApplication, Logger, ValidationPipe } from '@nestjs/common';
import { HttpAdapterHost, Reflector } from '@nestjs/core';
import { Test, TestingModule } from '@nestjs/testing';
import { MailDataRequired } from '@sendgrid/mail';
import * as supertest from 'supertest'; // eslint-disable-line import/no-namespace
import Decimal from 'decimal.js';
/* rubicon */
import {
  AssetFlowStatus,
  AssetType,
  BulkLedgerTransactionsRequest,
  BulkLedgerTransactionsResponse,
  EntryRequest,
  LedgerBalanceType,
  LedgerTransactionAction,
  LedgerTransactionType,
  OwnerType,
  PendingLedgerTransactionRequest,
  PendingLedgerTransactionResponse,
  Permission,
  ReleasedLedgerTransactionRequest,
  ReleasedLedgerTransactionResponse,
  SettledLedgerTransactionRequest,
  SettledLedgerTransactionResponse,
  TradeStatus,
  TradeType,
  TransactionType,
  UserStatus,
  uuid,
} from '@rubiconcarbon/shared-types';
/* env */
import { environment } from '@env/environment';
/* app */
import { Auth0Service, AuthService } from '@app/auth';
import { UserClaims } from '@app/auth/interfaces';
import { PERMISSIONS_KEY } from '@app/auth/permissions/permissions.decorator';
import { PermissionsGuard } from '@app/auth/permissions/permissions.guard';
import { PermitService } from '@app/auth/services/permit.service';
import { AppAuthGuard } from '@app/auth/strategies';
import { SendgridPayload } from '@app/dtos/sendgrid-payload.dto';
import { AssetFlow, Book, CreditFlow, ProjectVintage, Trade, Transaction, Transfer, User } from '@app/entities';
import { AllExceptionsFilter } from '@app/helpers/exception-filter.helper';
import { LedgerTransactionsService } from '@app/ledger/services/ledger-transactions.service';
import { LedgerService } from '@app/ledger/services/ledger.service';
import { S3Service, SendgridService } from '@app/services';
import { AppModule } from '@app/app.module';
import { bookVintageSeederValues } from '../../seeders/values/portfolio-composition.values';
import { creditInflowSeederValues } from '../../seeders/values/trades.values';
import { TestSeeder } from '../../seeders/TestSeeder';
import { toBookOwnerType } from '@app/helpers';

export type Request = supertest.Agent;
export type Response = supertest.Response;

export class AuthInfo {
  permissions: Permission[] = [];
  roles: string[] = [];
  email: string | undefined;
  extra: Partial<UserClaims> = {};
  id?: uuid;
}

export class ApplicationTest {
  module!: TestingModule;
  app!: INestApplication;
  request!: Request;
  orm!: MikroORM;
  auth!: AuthInfo;
  authService!: AuthService;
  sendgridService!: SendgridService;
  s3Service!: S3Service;
  permitService!: PermitService;
  redisService!: RedisService;
  reflector!: Reflector;
  ledgerTransactionsService!: LedgerTransactionsService;

  private redisClientMock = createMock({
    get: (() => undefined) as () => any,
    set: (() => undefined) as () => any,
    del: (() => undefined) as () => any,
  });

  async setup(): Promise<{ request: Request; auth: AuthInfo; module: TestingModule; orm: MikroORM }> {
    const postgresHost =
      (global as any)['__TESTCONTAINERS_POSTGRES-1_IP__'] ?? process.env['E2E_POSTGRES_HOST'] ?? 'postgres';
    const postgresPort =
      (global as any)['__TESTCONTAINERS_POSTGRES-1_PORT_5432__'] ?? process.env['E2E_POSTGRES_PORT'] ?? '5432';
    const postgresDB = process.env['E2E_POSTGRES_DB'] ?? 'rubicon-e2e';

    environment.db.uri = `postgres://postgres:password@${postgresHost}:${postgresPort}/${postgresDB}`;
    // environment.db.replicas = [environment.db.uri, environment.db.uri];

    this.auth = new AuthInfo();

    this.module = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(RedisService)
      .useValue({
        getClient: () => this.redisClientMock,
      })
      .overrideProvider(AppAuthGuard)
      .useValue(
        createMock<AppAuthGuard>({
          canActivate: (context: ExecutionContext): boolean => {
            const req = context.switchToHttp().getRequest();

            req.user = Object.assign(
              new UserClaims({
                email: this.auth.email?.toLowerCase() as Lowercase<string>,
                type: 'jwt',
              }),
              {
                permissions: this.auth.permissions,
                ...this.auth.extra,
                id: this.auth.id,
              },
            );
            return true;
          },
        }),
      )
      .overrideProvider(PermissionsGuard)
      .useValue(
        createMock<PermissionsGuard>({
          canActivate: async (context: ExecutionContext): Promise<boolean> => {
            const requiredPermissions =
              this.reflector.getAllAndOverride<Permission[]>(PERMISSIONS_KEY, [
                context.getHandler(),
                context.getClass(),
              ]) || [];
            const permitted = requiredPermissions.every((x) => this.auth.permissions.includes(x));
            return permitted;
          },
        }),
      )
      //TODO : @taylor override s3 SDK instead to properly test s3service
      .overrideProvider(S3Service)
      .useValue(
        createMock<S3Service>({
          getSignedUploadLink: async (key: string, prefix: string = ''): Promise<string> => prefix + key + '/upload/',
          getSignedDownloadLink: async (key: string, prefix: string = ''): Promise<string> =>
            prefix + key + '/download/',
          getSignedDownloadLinkFromFullUrl: async (key: string, prefix: string = ''): Promise<string> =>
            prefix + key + '/download/',
        }),
      )
      .overrideProvider(PermitService)
      .useValue(
        createMock<PermitService>({
          check: async (user: { email: string }, perm: Permission): Promise<boolean> => {
            return this.auth.permissions.includes(perm);
          },
          getUser: async (email: string) => {
            return Object.assign(
              new UserClaims({ email: email.toLowerCase() as Lowercase<string>, ...this.auth.extra }),
              { roles: this.auth.roles },
            );
          },
          findUser: async (email: string) => {
            return new UserClaims({ email: email.toLowerCase() as Lowercase<string>, ...this.auth.extra });
          },
        }),
      )
      .overrideProvider(Auth0Service)
      .useValue(
        createMock<Auth0Service>({
          getLastLogins: async (users: User[]) => {
            const lastLogins = users.map((user): { email: string; last_login: string } => {
              return { email: user.email, last_login: '' };
            });
            return lastLogins;
          },
        }),
      )
      .overrideProvider(SendgridService)
      .useValue(
        createMock<SendgridService>({
          createAndSendEmailTemplate: async (
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            to: string | string[],
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            from: string,
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            template: string,
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            variables: SendgridPayload,
          ): Promise<number> => {
            return 0;
          },

          createEmailTemplate: (
            to: string | string[],
            from: string,
            template: string,
            variables: SendgridPayload,
          ): MailDataRequired => {
            return { to, from, templateId: template, dynamicTemplateData: variables };
          },
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          sendEmails: async (mail: MailDataRequired[]): Promise<number> => {
            return 0;
          },
        }),
      )
      .overrideProvider(LedgerService)
      .useValue(
        createMock<LedgerService>({
          createBulkTransactions: async (
            body: BulkLedgerTransactionsRequest,
          ): Promise<BulkLedgerTransactionsResponse> => {
            const tx: EntityManager = this.orm.em as EntityManager;
            const knex = tx.getKnex();
            const bulkId = uuid();

            const response: BulkLedgerTransactionsResponse = {
              pendingTransactions: [],
              settledTransactions: [],
              releasedTransactions: [],
              revertedTransactions: [], // todo (TD-58) : REVERTED NOT ACTUALLY COVERED
              bulkId,
              count:
                (body.pendingTransactions?.length || 0) +
                (body.settledTransactions?.length || 0) +
                (body.releasedTransactions?.length || 0),
            };

            for (const pt of body.pendingTransactions || []) {
              const transactionId = uuid();

              await knex(`${environment.db.schema.ledger}.transactions`).insert({
                id: transactionId,
                action: pt.action,
                external_id: pt.externalId,
                is_fully_released: false,
                is_reverted: false,
                notes: pt.notes,
                type: LedgerTransactionType.PENDING,
              });

              const uniqueEntries: EntryRequest[] = [];
              pt.entries.forEach((entry) => {
                const ue = uniqueEntries.find((f) => f.assetId === entry.assetId && f.ownerId === entry.ownerId);
                if (!ue) {
                  uniqueEntries.push(entry);
                } else {
                  ue.amount = new Decimal(ue.amount).plus(entry.amount);
                }
              });

              for (const entry of uniqueEntries) {
                const balances: { asset_id: uuid; owner_id: uuid; balance: Decimal }[] = await knex
                  .select(['asset_id', 'owner_id', 'balance'])
                  .from(`${environment.db.schema.ledger}.balances`)
                  .where({ asset_id: entry.assetId, owner_id: entry.ownerId, type: LedgerBalanceType.PENDING });

                await knex(`${environment.db.schema.ledger}.pending_entries`).insert({
                  transaction_id: transactionId,
                  amount: new Decimal(entry.amount).toNumber(),
                  asset_id: entry.assetId,
                  asset_type: entry.assetType,
                  balance_at_created_time: 0, // this is wrong but idk if it matters
                  executed_at: pt.executedAt,
                  owner_id: entry.ownerId,
                  owner_type: entry.ownerType,
                });

                await knex(`${environment.db.schema.ledger}.balances`)
                  .insert({
                    type: LedgerBalanceType.PENDING,
                    asset_id: entry.assetId,
                    asset_type: entry.assetType,
                    owner_id: entry.ownerId,
                    owner_type: entry.ownerType,
                    created_at: pt.executedAt,
                    updated_at: pt.executedAt,
                    balance: new Decimal(
                      balances.find((f) => f.asset_id === entry.assetId && f.owner_id === entry.ownerId)?.balance || 0,
                    )
                      .plus(entry.amount)
                      .toNumber(),
                  })
                  .onConflict(['asset_id', 'owner_id', 'type'])
                  .merge(['balance', 'updated_at']);
              }

              response.pendingTransactions!.push({
                ...pt,
                id: transactionId,
                createdAt: pt.executedAt,
                updatedAt: pt.executedAt,
                pendingEntries: pt.entries.map((m) => {
                  return {
                    ...m,
                    executedAt: pt.executedAt,
                    id: uuid(), // this is wrong but idk if it matters
                    createdAt: pt.executedAt,
                    updatedAt: pt.executedAt,
                  };
                }),
                type: LedgerTransactionType.PENDING,
              });
            }

            for (const st of body.settledTransactions || []) {
              const transactionId = uuid();

              await knex(`${environment.db.schema.ledger}.transactions`).insert({
                id: transactionId,
                action: st.action,
                external_id: st.externalId,
                notes: st.notes,
                type: LedgerTransactionType.SETTLED,
              });

              const uniqueEntries: EntryRequest[] = [];
              st.entries.forEach((entry) => {
                const ue = uniqueEntries.find((f) => f.assetId === entry.assetId && f.ownerId === entry.ownerId);
                if (!ue) {
                  uniqueEntries.push(entry);
                } else {
                  ue.amount = new Decimal(ue.amount).plus(entry.amount);
                }
              });
              for (const entry of uniqueEntries) {
                const balances: { asset_id: uuid; owner_id: uuid; balance: Decimal }[] = await knex
                  .select(['asset_id', 'owner_id', 'balance'])
                  .from(`${environment.db.schema.ledger}.balances`)
                  .where({ asset_id: entry.assetId, owner_id: entry.ownerId, type: LedgerBalanceType.SETTLED });

                await knex(`${environment.db.schema.ledger}.settled_entries`).insert({
                  transaction_id: transactionId,
                  amount: new Decimal(entry.amount).toNumber(),
                  asset_id: entry.assetId,
                  asset_type: entry.assetType,
                  balance_at_created_time: 0, // this is wrong but idk if it matters
                  executed_at: st.executedAt,
                  owner_id: entry.ownerId,
                  owner_type: entry.ownerType,
                });

                await knex(`${environment.db.schema.ledger}.balances`)
                  .insert({
                    type: LedgerBalanceType.SETTLED,
                    asset_id: entry.assetId,
                    asset_type: entry.assetType,
                    owner_id: entry.ownerId,
                    owner_type: entry.ownerType,
                    created_at: st.executedAt,
                    updated_at: st.executedAt,
                    balance: new Decimal(
                      balances.find((f) => f.asset_id === entry.assetId && f.owner_id === entry.ownerId)?.balance || 0,
                    )
                      .plus(entry.amount)
                      .toNumber(),
                  })
                  .onConflict(['asset_id', 'owner_id', 'type'])
                  .merge(['balance', 'updated_at']);
              }

              response.settledTransactions!.push({
                ...st,
                id: transactionId,
                createdAt: st.executedAt,
                updatedAt: st.executedAt,
                settledEntries: st.entries.map((m) => {
                  return {
                    ...m,
                    executedAt: st.executedAt,
                    id: uuid(), // this is wrong but idk if it matters
                    createdAt: st.executedAt,
                    updatedAt: st.executedAt,
                  };
                }),
                type: LedgerTransactionType.SETTLED,
              });
            }

            for (const rt of body.releasedTransactions || []) {
              const transactionId = uuid();

              // get existing pending entries
              const pendingTransactionEntries: {
                amount: Decimal;
                asset_id: uuid;
                asset_type: AssetType;
                owner_id: uuid;
                owner_type: OwnerType;
              }[] = await knex
                .select(['amount', 'asset_id', 'asset_type', 'owner_id', 'owner_type'])
                .from(`${environment.db.schema.ledger}.pending_entries`)
                .where({ transaction_id: rt.releasedId });

              // create release transaction
              await knex(`${environment.db.schema.ledger}.transactions`).insert({
                id: transactionId,
                action: rt.action,
                external_id: rt.externalId,
                notes: rt.notes,
                type: LedgerTransactionType.RELEASE,
              });
              // update pending transaction
              await knex(`${environment.db.schema.ledger}.transactions`)
                .update({
                  is_fully_released: true,
                })
                .where({ id: rt.releasedId });

              // create reversal entries of pending entries
              for (const entry of pendingTransactionEntries) {
                const balances: { asset_id: uuid; owner_id: uuid; balance: Decimal }[] = await knex
                  .select(['asset_id', 'owner_id', 'balance'])
                  .from(`${environment.db.schema.ledger}.balances`)
                  .where({ asset_id: entry.asset_id, owner_id: entry.owner_id, type: LedgerBalanceType.PENDING });

                await knex(`${environment.db.schema.ledger}.pending_entries`).insert({
                  transaction_id: transactionId,
                  amount: new Decimal(entry.amount).mul(-1).toNumber(),
                  asset_id: entry.asset_id,
                  asset_type: entry.asset_type,
                  balance_at_created_time: 0, // this is wrong but idk if it matters
                  executed_at: rt.executedAt,
                  owner_id: entry.owner_id,
                  owner_type: entry.owner_type,
                });

                await knex(`${environment.db.schema.ledger}.balances`)
                  .insert({
                    type: LedgerBalanceType.PENDING,
                    asset_id: entry.asset_id,
                    asset_type: entry.asset_type,
                    owner_id: entry.owner_id,
                    owner_type: entry.owner_type,
                    created_at: rt.executedAt,
                    updated_at: rt.executedAt,
                    balance: new Decimal(
                      balances.find((f) => f.asset_id === entry.asset_id && f.owner_id === entry.owner_id)?.balance ||
                        0,
                    )
                      .plus(new Decimal(entry.amount).mul(-1))
                      .toNumber(),
                  })
                  .onConflict(['asset_id', 'owner_id', 'type'])
                  .merge(['balance', 'updated_at']);
              }

              response.releasedTransactions!.push({
                id: transactionId,
                action: rt.action,
                externalId: rt.externalId || rt.releasedId,
                createdAt: rt.executedAt,
                updatedAt: rt.executedAt,
                pendingEntries: [],
                pendingTransaction: {
                  action: LedgerTransactionAction.OTHER,
                  isFullyReleased: true,
                  isReverted: false,
                  type: LedgerTransactionType.PENDING,
                  id: rt.releasedId,
                  createdAt: rt.executedAt,
                  externalId: rt.releasedId,
                },
                type: LedgerTransactionType.PENDING,
              });
            }

            return response;
          },
          createPendingTransaction: async (
            body: PendingLedgerTransactionRequest,
          ): Promise<PendingLedgerTransactionResponse> => {
            const tx: EntityManager = this.orm.em as EntityManager;
            const knex = tx.getKnex();
            const transactionId = uuid();

            await knex(`${environment.db.schema.ledger}.transactions`).insert({
              id: transactionId,
              action: body.action,
              external_id: body.externalId,
              is_fully_released: false,
              is_reverted: false,
              notes: body.notes,
              type: LedgerTransactionType.PENDING,
            });

            const uniqueEntries: EntryRequest[] = [];
            body.entries.forEach((entry) => {
              const ue = uniqueEntries.find((f) => f.assetId === entry.assetId && f.ownerId === entry.ownerId);
              if (!ue) {
                uniqueEntries.push(entry);
              } else {
                ue.amount = new Decimal(ue.amount).plus(entry.amount);
              }
            });
            for (const entry of uniqueEntries) {
              const balances: { asset_id: uuid; owner_id: uuid; balance: Decimal }[] = await knex
                .select(['asset_id', 'owner_id', 'balance'])
                .from(`${environment.db.schema.ledger}.balances`)
                .where({ asset_id: entry.assetId, owner_id: entry.ownerId, type: LedgerBalanceType.PENDING });

              await knex(`${environment.db.schema.ledger}.pending_entries`).insert({
                transaction_id: transactionId,
                amount: new Decimal(entry.amount).toNumber(),
                asset_id: entry.assetId,
                asset_type: entry.assetType,
                balance_at_created_time: 0, // this is wrong but idk if it matters
                executed_at: body.executedAt,
                owner_id: entry.ownerId,
                owner_type: entry.ownerType,
              });

              await knex(`${environment.db.schema.ledger}.balances`)
                .insert({
                  type: LedgerBalanceType.PENDING,
                  asset_id: entry.assetId,
                  asset_type: entry.assetType,
                  owner_id: entry.ownerId,
                  owner_type: entry.ownerType,
                  created_at: body.executedAt,
                  updated_at: body.executedAt,
                  balance: new Decimal(
                    balances.find((f) => f.asset_id === entry.assetId && f.owner_id === entry.ownerId)?.balance || 0,
                  )
                    .plus(entry.amount)
                    .toNumber(),
                })
                .onConflict(['asset_id', 'owner_id', 'type'])
                .merge(['balance', 'updated_at']);
            }

            return {
              ...body,
              id: transactionId,
              createdAt: body.executedAt,
              updatedAt: body.executedAt,
              pendingEntries: body.entries.map((m) => {
                return {
                  ...m,
                  executedAt: body.executedAt,
                  id: uuid(), // this is wrong but idk if it matters
                  createdAt: body.executedAt,
                  updatedAt: body.executedAt,
                };
              }),
              type: LedgerTransactionType.PENDING,
            };
          },
          createSettledTransaction: async (
            body: SettledLedgerTransactionRequest,
          ): Promise<SettledLedgerTransactionResponse> => {
            const tx: EntityManager = this.orm.em as EntityManager;
            const knex = tx.getKnex();
            const transactionId = uuid();

            await knex(`${environment.db.schema.ledger}.transactions`).insert({
              id: transactionId,
              action: body.action,
              external_id: body.externalId,
              notes: body.notes,
              type: LedgerTransactionType.SETTLED,
            });

            const uniqueEntries: EntryRequest[] = [];
            body.entries.forEach((entry) => {
              const ue = uniqueEntries.find((f) => f.assetId === entry.assetId && f.ownerId === entry.ownerId);
              if (!ue) {
                uniqueEntries.push(entry);
              } else {
                ue.amount = new Decimal(ue.amount).plus(entry.amount);
              }
            });
            for (const entry of uniqueEntries) {
              const balances: { asset_id: uuid; owner_id: uuid; balance: Decimal }[] = await knex
                .select(['asset_id', 'owner_id', 'balance'])
                .from(`${environment.db.schema.ledger}.balances`)
                .where({ asset_id: entry.assetId, owner_id: entry.ownerId, type: LedgerBalanceType.SETTLED });

              await knex(`${environment.db.schema.ledger}.settled_entries`).insert({
                transaction_id: transactionId,
                amount: new Decimal(entry.amount).toNumber(),
                asset_id: entry.assetId,
                asset_type: entry.assetType,
                balance_at_created_time: 0, // this is wrong but idk if it matters
                executed_at: body.executedAt,
                owner_id: entry.ownerId,
                owner_type: entry.ownerType,
              });

              await knex(`${environment.db.schema.ledger}.balances`)
                .insert({
                  type: LedgerBalanceType.SETTLED,
                  asset_id: entry.assetId,
                  asset_type: entry.assetType,
                  owner_id: entry.ownerId,
                  owner_type: entry.ownerType,
                  created_at: body.executedAt,
                  updated_at: body.executedAt,
                  balance: new Decimal(
                    balances.find((f) => f.asset_id === entry.assetId && f.owner_id === entry.ownerId)?.balance || 0,
                  )
                    .plus(entry.amount)
                    .toNumber(),
                })
                .onConflict(['asset_id', 'owner_id', 'type'])
                .merge(['balance', 'updated_at']);
            }

            return {
              ...body,
              id: transactionId,
              createdAt: body.executedAt,
              updatedAt: body.executedAt,
              settledEntries: body.entries.map((m) => {
                return {
                  ...m,
                  executedAt: body.executedAt,
                  id: uuid(), // this is wrong but idk if it matters
                  createdAt: body.executedAt,
                  updatedAt: body.executedAt,
                };
              }),
              type: LedgerTransactionType.SETTLED,
            };
          },
          releaseTransaction: async (
            id: uuid,
            body: ReleasedLedgerTransactionRequest,
          ): Promise<ReleasedLedgerTransactionResponse> => {
            const tx: EntityManager = this.orm.em as EntityManager;
            const knex = tx.getKnex();
            const transactionId = uuid();

            // get existing pending entries
            const pendingTransactionEntries: {
              amount: Decimal;
              asset_id: uuid;
              asset_type: AssetType;
              owner_id: uuid;
              owner_type: OwnerType;
            }[] = await knex
              .select(['amount', 'asset_id', 'asset_type', 'owner_id', 'owner_type'])
              .from(`${environment.db.schema.ledger}.pending_entries`)
              .where({ transaction_id: id });

            // create release transaction
            await knex(`${environment.db.schema.ledger}.transactions`).insert({
              id: transactionId,
              action: body.action,
              external_id: body.externalId || id, // technically not right since it should be pendingTransaction.external_id but it shouldn't matter
              notes: body.notes,
              type: LedgerTransactionType.RELEASE,
            });
            // update pending transaction
            await knex(`${environment.db.schema.ledger}.transactions`)
              .update({
                is_fully_released: true,
              })
              .where({ id });

            // create reversal entries of pending entries
            for (const entry of pendingTransactionEntries) {
              const balances: { asset_id: uuid; owner_id: uuid; balance: Decimal }[] = await knex
                .select(['asset_id', 'owner_id', 'balance'])
                .from(`${environment.db.schema.ledger}.balances`)
                .where({ asset_id: entry.asset_id, owner_id: entry.owner_id, type: LedgerBalanceType.PENDING });

              await knex(`${environment.db.schema.ledger}.pending_entries`).insert({
                transaction_id: transactionId,
                amount: new Decimal(entry.amount).mul(-1).toNumber(),
                asset_id: entry.asset_id,
                asset_type: entry.asset_type,
                balance_at_created_time: 0, // this is wrong but idk if it matters
                executed_at: body.executedAt,
                owner_id: entry.owner_id,
                owner_type: entry.owner_type,
              });

              await knex(`${environment.db.schema.ledger}.balances`)
                .insert({
                  type: LedgerBalanceType.PENDING,
                  asset_id: entry.asset_id,
                  asset_type: entry.asset_type,
                  owner_id: entry.owner_id,
                  owner_type: entry.owner_type,
                  created_at: body.executedAt,
                  updated_at: body.executedAt,
                  balance: new Decimal(
                    balances.find((f) => f.asset_id === entry.asset_id && f.owner_id === entry.owner_id)?.balance || 0,
                  )
                    .plus(new Decimal(entry.amount).mul(-1))
                    .toNumber(),
                })
                .onConflict(['asset_id', 'owner_id', 'type'])
                .merge(['balance', 'updated_at']);
            }

            return {
              id: transactionId,
              action: body.action,
              externalId: body.externalId || id,
              createdAt: body.executedAt,
              updatedAt: body.executedAt,
              pendingEntries: [],
              pendingTransaction: {
                action: LedgerTransactionAction.OTHER,
                isFullyReleased: true,
                isReverted: false,
                type: LedgerTransactionType.PENDING,
                id,
                createdAt: body.executedAt,
                externalId: id,
              },
              type: LedgerTransactionType.PENDING,
            };
          },
        }),
      )
      .compile();

    this.redisService = this.module.get(RedisService);
    this.authService = this.module.get(AuthService);
    this.sendgridService = this.module.get(SendgridService);
    this.permitService = this.module.get(PermitService);
    this.s3Service = this.module.get(S3Service);
    this.reflector = this.module.get(Reflector);
    this.ledgerTransactionsService = this.module.get(LedgerTransactionsService);

    this.module.useLogger(false);

    // initialize db
    this.orm = this.module.get<MikroORM>(MikroORM);

    // fake migrations
    const tx = this.orm.em as EntityManager;
    const knex = tx.getKnex();
    await knex.raw(`CREATE SCHEMA IF NOT EXISTS "${environment.db.schema.marketData}";`);
    await knex.raw(`CREATE SCHEMA IF NOT EXISTS "${environment.db.schema.reports}";`);
    await knex.raw(`CREATE TABLE IF NOT EXISTS "${environment.db.schema.marketData}".internal_prices(
    source text NOT NULL,
    project_id text NOT NULL,
    vintage text NOT NULL,
    "timestamp" timestamp with time zone NOT NULL,
    price numeric NOT NULL,
    id bigint PRIMARY KEY);`);

    // migrations and seeders
    const migrator = this.orm.getMigrator();
    await migrator.up(); // runs migrations up to the latest
    const seeder = this.orm.getSeeder();
    await seeder.seed(TestSeeder); // seed the database
    // seed trades and portfolio compositions
    await this.seedTrades();
    await this.seedPortfolioCompositions();

    // app
    this.app = this.module.createNestApplication();
    this.app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        whitelist: true,
        transformOptions: { exposeUnsetFields: false },
        forbidUnknownValues: false,
      }),
    );

    const testLogger = new Logger('E2E Test Logger');
    this.app.useGlobalFilters(new AllExceptionsFilter(this.app.get(HttpAdapterHost), testLogger));

    await this.app.init();

    this.request = supertest(this.app.getHttpServer());

    return { request: this.request, auth: this.auth, module: this.module, orm: this.orm };
  }

  async cleanup(): Promise<void> {
    // clean db resources
    try {
      const orm = this.module.get<MikroORM>(MikroORM);
      const connection = orm.em.getConnection();
      for (const schema of Object.values(environment.db.schema)) {
        await connection.execute(`DROP SCHEMA IF EXISTS "${schema}" CASCADE;`);
      }
      await orm.close();
    } catch (err: unknown) {
      console.error(err);
    }

    // issue the app close hook
    await this.app.close();
  }

  getDBConnection(): PostgreSqlConnection {
    return this.orm.em.getConnection() as PostgreSqlConnection;
  }

  getKnex(): Knex {
    return (this.orm.em as EntityManager).getKnex();
  }

  // seeder methods

  private async seedTrades(): Promise<void> {
    const now = new Date();
    const tx = (this.orm.em as EntityManager).fork();

    for (const i in creditInflowSeederValues) {
      const x = creditInflowSeederValues[i];
      const registryProjectId: string = x[5];
      const pvName: string = x[6];

      const pv = await tx.findOneOrFail(
        ProjectVintage,
        { label: pvName, project: { registryProjectId: registryProjectId } },
        { populate: ['project'] },
      );

      const transaction = tx.create(Transaction, { id: x[0], type: TransactionType.TRADE });
      await tx.persistAndFlush(transaction);
      const vintageFlowId: uuid = uuid();
      const assetFlow = tx.create(AssetFlow, {
        id: vintageFlowId,
        updatedAt: now,
        createdAt: now,
        amount: x[3],
        asset: pv.id,
        assetType: AssetType.REGISTRY_VINTAGE,
        destination: uuid(environment.rubicon.books.portfolio.default),
        rawPrice: new Decimal(x[4]),
        transaction: transaction.id,
        source: uuid(environment.rubicon.books.offsets),
        status: AssetFlowStatus.SETTLED,
        transactionSubtype: TradeType.BUY,
        transactionType: TransactionType.TRADE,
      });
      tx.create(Trade, {
        id: transaction.id,
        uiKey: `TB-${x[1]}`,
        settledAt: x[2],
        status: TradeStatus.SETTLED,
        assetsDeliveredAt: x[2],
        isPaid: true,
        isDelivered: true,
        amount: x[3],
        updatedAt: now,
        createdAt: now,
        poid: `poid-${x[1]}`,
        type: TradeType.BUY,
        updatableStatusOrder: ['pending_payment', 'pending_delivery'],
        updatedBy: await tx.findOneOrFail(User, {
          email: environment.rubicon.defaultAdminEmail.toLowerCase() as Lowercase<string>,
        }),
        book: uuid(environment.rubicon.books.portfolio.default),
      });
      tx.create(CreditFlow, {
        type: 'inflow',
        id: x[0],
        acquiredAt: x[2],
        amount: x[3],
        updatedAt: now,
        createdAt: now,
        projectVintage: pv,
        assetFlow,
      });

      await this.ledgerTransactionsService.settleTransaction({
        action: LedgerTransactionAction.TRADES_SETTLE,
        externalId: x[0],
        notes: 'seeding trades for e2e',
        executedAt: now,
        entries: [
          {
            assetId: pv.id,
            assetType: AssetType.REGISTRY_VINTAGE,
            ownerId: uuid(environment.rubicon.books.offsets),
            ownerType: OwnerType.OFFSETS,
            amount: new Decimal(x[3]).mul(-1),
          },
          {
            assetId: pv.id,
            assetType: AssetType.REGISTRY_VINTAGE,
            ownerId: uuid(environment.rubicon.books.portfolio.default),
            ownerType: OwnerType.BOOK_PORTFOLIO_DEFAULT,
            amount: new Decimal(x[3]),
          },
        ],
      });
    }

    await tx.flush();
  }

  private async seedPortfolioCompositions(): Promise<void> {
    const tx = (this.orm.em as EntityManager).fork();
    const now = new Date();
    const transferId: uuid = uuid();
    let totalAmount = 0;

    for (const i in bookVintageSeederValues) {
      const x = bookVintageSeederValues[i];
      const bookName = x[2];
      const registryProjectId: string = x[3];
      const pvName: string = x[4];

      const b = await tx.findOneOrFail(Book, { name: bookName });
      const pv = await tx.findOneOrFail(ProjectVintage, {
        label: pvName,
        project: { registryProjectId: registryProjectId },
      });

      const transaction = tx.create(Transaction, { id: transferId, type: TransactionType.INTERNAL_TRANSFER });
      await tx.persistAndFlush(transaction);
      tx.create(AssetFlow, {
        id: x[0],
        createdAt: now,
        updatedAt: now,
        amount: x[1],
        asset: pv.id,
        assetType: AssetType.REGISTRY_VINTAGE,
        destination: b.id,
        rawPrice: new Decimal(0),
        transaction: transferId,
        source: uuid(environment.rubicon.books.portfolio.default),
        status: AssetFlowStatus.SETTLED,
        transactionType: TransactionType.INTERNAL_TRANSFER,
      });

      tx.create(AssetFlow, {
        id: uuid(),
        createdAt: now,
        updatedAt: now,
        amount: x[1],
        asset: b.id,
        assetType: AssetType.RCT,
        destination: uuid(environment.rubicon.books.portfolio.default),
        rawPrice: new Decimal(0),
        transaction: transferId,
        source: uuid(environment.rubicon.books.offsets),
        status: AssetFlowStatus.SETTLED,
        transactionType: TransactionType.INTERNAL_TRANSFER,
      });

      await this.ledgerTransactionsService.settleTransaction({
        action: LedgerTransactionAction.TRANSFERS_EXECUTE,
        externalId: transferId,
        notes: 'seeding portfolio compositions for e2e',
        executedAt: now,
        entries: [
          {
            assetId: pv.id,
            assetType: AssetType.REGISTRY_VINTAGE,
            ownerId: uuid(environment.rubicon.books.portfolio.default),
            ownerType: OwnerType.BOOK_PORTFOLIO_DEFAULT,
            amount: new Decimal(x[1]).mul(-1),
          },
          {
            assetId: pv.id,
            assetType: AssetType.REGISTRY_VINTAGE,
            ownerId: b.id,
            ownerType: toBookOwnerType(b.type), // todo (RBC-3163) : this needs to be fixed to account for RRTs
            amount: new Decimal(x[1]),
          },
          {
            assetId: b.id,
            assetType: AssetType.RCT,
            ownerId: uuid(environment.rubicon.books.offsets),
            ownerType: OwnerType.OFFSETS,
            amount: new Decimal(x[1]).mul(-1),
          },
          {
            assetId: b.id,
            assetType: AssetType.RCT,
            ownerId: uuid(environment.rubicon.books.portfolio.default),
            ownerType: OwnerType.BOOK_PORTFOLIO_DEFAULT,
            amount: new Decimal(x[1]),
          },
        ],
      });
      totalAmount += x[1];

      await tx.flush();
    }

    const defaultManager = await tx.findOneOrFail(User, {
      email: environment.rubicon.defaultAdminEmail.toLowerCase() as Lowercase<string>,
      status: UserStatus.ENABLED,
    });
    tx.create(Transfer, {
      id: transferId,
      createdAt: now,
      updatedAt: now,
      totalAmount,
      user: defaultManager,
      assetFlows: [],
    });

    const knex = tx.getKnex();
    await knex
      .raw(
        `insert into "${environment.db.schema.rubicon}"."books_project_types" ("book_id", "project_type_id") 
      select b.id, p.project_type_id from "${environment.db.schema.rubicon}".asset_flows as vf
      inner join "${environment.db.schema.rubicon}"."books" as b on b.id = vf.destination_id
      inner join "${environment.db.schema.rubicon}"."project_vintages" as pv on pv.id = vf.asset_id
      inner join "${environment.db.schema.rubicon}"."projects" as p on p.id = pv.project_id on conflict do nothing;`,
      )
      .then((r) => r.rows);
  }
}
