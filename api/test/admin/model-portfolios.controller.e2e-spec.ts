/* third party */
import { TestingModule } from '@nestjs/testing';
import Decimal from 'decimal.js';
/* rubicon */
import {
  ModelPortfolioOrderByOption,
  ModelPortfolioRelations,
  OrderByDirection,
  PermissionEnum,
  uuid,
  zeroUUID,
} from '@rubiconcarbon/shared-types';
/* app */
import { AdminModelPortfolioComponentResponseDTO, AdminModelPortfolioResponseDTO } from '@app/dtos/model-portfolio.dto';
import { NotificationsService, URLShortenerService } from '@app/services';
/* test */
import { createOrganizationWithCustomerPortfolio } from '../helpers/organizations.helper';
import { createUser } from '../helpers/users.helper';
import { ApplicationTest, AuthInfo, Request } from '../setup/e2e';

describe('AdminModelPortfoliosController (e2e)', () => {
  const appTest = new ApplicationTest();
  let request: Request;
  let auth: AuthInfo;
  let module: TestingModule;
  let notificationsService: NotificationsService;
  let shortenerService: URLShortenerService;

  let vintageIds: uuid[] = [];
  let modelPortfolioComponents: AdminModelPortfolioComponentResponseDTO[] = [];
  const user1email = `<EMAIL>`;
  let user1id = uuid();
  const user2email = `<EMAIL>`;
  let user2id = uuid();
  let user1ModelPortfolioId = uuid();
  let user2ModelPortfolioId = uuid();
  let user3ModelPortfolioId = uuid();
  let orgId: uuid;

  beforeAll(async () => {
    ({ request, auth, module } = await appTest.setup());

    // create admin users
    user1id = (await createUser(request, auth, user1email)).id;
    user2id = (await createUser(request, auth, user2email)).id;

    // create org
    orgId = (await createOrganizationWithCustomerPortfolio(request, auth)).id;

    // set auth permissions
    auth.permissions = [
      PermissionEnum.MODEL_PORTFOLIOS_COMPONENTS_WRITE,
      PermissionEnum.MODEL_PORTFOLIOS_READ,
      PermissionEnum.MODEL_PORTFOLIOS_CREATE,
      PermissionEnum.MODEL_PORTFOLIOS_DELETE,
      PermissionEnum.MODEL_PORTFOLIOS_UPDATE,
      PermissionEnum.MODEL_PORTFOLIOS_SHARE,
      PermissionEnum.MODEL_PORTFOLIOS_PRICE,
      PermissionEnum.MODEL_PORTFOLIOS_RFP,
      PermissionEnum.VERIFIED,
      PermissionEnum.VINTAGES_READ,
    ];

    notificationsService = module.get(NotificationsService);
    shortenerService = module.get(URLShortenerService);
    jest.spyOn(notificationsService, 'handleEvent');
    jest.spyOn(shortenerService, 'create');
  });

  afterAll(async () => await appTest.cleanup());

  test('/admin/model-portfolios (POST)', async () => {
    auth.email = user1email;
    const sameName = 'Default #1';
    await request
      .post('/admin/model-portfolios')
      .send({})
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body.name).toEqual(sameName);
        expect(res.body as AdminModelPortfolioResponseDTO).toMatchObject({
          updatedBy: { email: user1email },
        });
        user1ModelPortfolioId = res.body.id;
      });

    auth.email = user2email;
    await request
      .post('/admin/model-portfolios')
      .send({
        name: sameName,
        organizationId: orgId,
      })
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body as AdminModelPortfolioResponseDTO).toMatchObject({
          updatedBy: { email: user2email },
        });
        user2ModelPortfolioId = res.body.id;
        expect(shortenerService.create).toHaveBeenCalledWith(expect.stringContaining('edit-portfolio-sandbox'));
        expect(notificationsService.handleEvent).toHaveBeenCalledWith(expect.anything(), expect.anything(), {
          adminBaseURL: '',
          baseURL: '',
          createdBy: '<EMAIL>',
          ctaLink: expect.anything(),
          customerName: 'Not linked',
          dateFormat: 'MM/DD/YYYY',
          environmentPrefix: 'e2e:',
          portalBaseURL: '',
          portfolioName: 'Default #1',
          status: undefined,
        });
      });

    await request
      .post('/admin/model-portfolios')
      .send({
        name: sameName,
        organizationId: orgId,
      })
      .then((res) => {
        expect(res.status).toEqual(409);
        expect(res.body.message).toEqual(`portfolio name ${sameName} must be unique per organization`);
      });

    await request
      .post('/admin/model-portfolios')
      .send({
        organizationId: orgId,
      })
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body.name).toEqual(`Test Organization #2`);
      });

    await request
      .post('/admin/model-portfolios')
      .send({
        name: 'Test Organization #4',
        organizationId: orgId,
      })
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body.uiKey).toBeDefined();
      });

    await request
      .post('/admin/model-portfolios')
      .send({
        organizationId: orgId,
      })
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body.name).toEqual(`Test Organization #5`);
      });

    await request
      .post('/admin/model-portfolios')
      .send({
        memo: 'memo test',
      })
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body.name).toEqual('Default #2');
        expect(res.body.memo).toEqual('memo test');
      });

    await request
      .post('/admin/model-portfolios')
      .send({
        organizationId: orgId,
      })
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body.name).toEqual('Test Organization #6');
        user3ModelPortfolioId = res.body.id;
      });
  });

  test('/admin/model-portfolios/generate-name (GET)', async () => {
    await request
      .get('/admin/model-portfolios/generate-name')
      .query({ organizationId: orgId })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.text).toEqual('Test Organization #7');
      });
  });

  test('/admin/model-portfolios (GET)', async () => {
    await request.get('/admin/model-portfolios').then((res) => {
      if (res.status !== 200) {
        console.warn('get model-portfolios expected status to be 200 : ' + JSON.stringify(res));
      }
      expect(res.status).toEqual(200);
      expect((res.body.data as AdminModelPortfolioResponseDTO[]).map((x) => x.id)).toContain(user1ModelPortfolioId);
      expect((res.body.data as AdminModelPortfolioResponseDTO[]).map((x) => x.id)).toContain(user2ModelPortfolioId);
    });

    await request
      .get('/admin/model-portfolios')
      .query({
        limit: 1,
        offset: 0,
        orderBy: ModelPortfolioOrderByOption.CREATED_AT,
        orderByDirection: OrderByDirection.ASC,
      })
      .then((res) => {
        if (res.status !== 200) {
          console.warn('get modelPortfolios expected status to be 200 : ' + JSON.stringify(res));
        }
        expect(res.status).toEqual(200);
        expect(res.body.data.length).toEqual(1);
        expect(res.body.data[0].uiKey).toBeDefined();
        expect((res.body.data as AdminModelPortfolioResponseDTO[]).map((x) => x.id)).toContain(user1ModelPortfolioId);
      });

    await request
      .get('/admin/model-portfolios')
      .query({
        limit: 1,
        offset: 1,
        orderBy: ModelPortfolioOrderByOption.CREATED_AT,
        orderByDirection: OrderByDirection.ASC,
      })
      .then((res) => {
        if (res.status !== 200) {
          console.warn('get modelPortfolios expected status to be 200 : ' + JSON.stringify(res));
        }
        expect(res.status).toEqual(200);
        expect(res.body.data.length).toEqual(1);
        expect(res.body.data[0].uiKey).toBeDefined();
        expect((res.body.data as AdminModelPortfolioResponseDTO[]).map((x) => x.id)).toContain(user2ModelPortfolioId);
      });

    await request
      .get('/admin/model-portfolios')
      .query({ includeTotalCount: true })
      .then((res) => {
        if (res.status !== 200) {
          console.warn('get modelPortfolios expected status to be 200 : ' + JSON.stringify(res));
        }
        expect(res.status).toEqual(200);
        expect(res.body.page.totalCount).toEqual(7);
      });

    await request
      .get('/admin/model-portfolios')
      .query({ updatedById: user1id })
      .then((res) => {
        if (res.status !== 200) {
          console.warn('get modelPortfolios expected status to be 200 : ' + JSON.stringify(res));
        }
        expect(res.status).toEqual(200);
        expect(res.body.data.length).toEqual(1);
        expect((res.body.data as AdminModelPortfolioResponseDTO[]).map((x) => x.id)).toContain(user1ModelPortfolioId);
      });

    await request
      .get('/admin/model-portfolios')
      .query({ updatedById: user2id })
      .then((res) => {
        if (res.status !== 200) {
          console.warn('get modelPortfolios expected status to be 200 : ' + JSON.stringify(res));
        }
        expect(res.status).toEqual(200);
        expect(res.body.data.length).toEqual(6);
        expect((res.body.data as AdminModelPortfolioResponseDTO[]).map((x) => x.id)).toContain(user2ModelPortfolioId);
      });
  });

  test('/admin/model-portfolios/:id (GET)', async () => {
    await request.get(`/admin/model-portfolios/${zeroUUID}`).then((res) => {
      expect(res.status).toEqual(404);
      expect(res.body.message).toEqual('ModelPortfolio not found');
      expect(res.body.isDeleted).toBeUndefined();
    });

    await request.get(`/admin/model-portfolios/${user1ModelPortfolioId}`).then((res) => {
      expect(res.status).toEqual(200);
      expect(res.body.name).toEqual('Default #1');
      expect(res.body.uiKey).toBeDefined();
      expect(res.body.isDeleted).toBeUndefined();
    });

    await request.get(`/admin/model-portfolios/${user2ModelPortfolioId}`).then((res) => {
      expect(res.status).toEqual(200);
      expect(res.body.name).toEqual('Default #1');
      expect(res.body.uiKey).toBeDefined();
      expect(res.body.isDeleted).toBeUndefined();
    });
  });

  // todo (TD-58) : why is this test here and not in test/model-portfolios
  test('/model-portfolios (GET) empty', async () => {
    await request.get('/model-portfolios').then((res) => {
      expect(res.status).toEqual(200);
      expect(res.body.data).toEqual([]);
    });
  });

  test('/admin/model-portfolios/:id (PATCH)', async () => {
    await request
      .patch(`/admin/model-portfolios/${zeroUUID}`)
      .send({ name: 'New Name' })
      .then((res) => {
        expect(res.status).toEqual(404);
        expect(res.body.message).toEqual('ModelPortfolio not found');
      });

    await request
      .patch(`/admin/model-portfolios/${user1ModelPortfolioId}`)
      .send({
        showCustomer: true,
        organizationId: orgId,
        priceEstimate: new Decimal(5.2),
        includeRiskAdjustment: false,
      })
      .then((res) => {
        expect(res.status).toEqual(422);
        expect(res.body.message).toEqual(
          'model portfolio with name Default #1 already exists for organization Test Organization',
        );
      });

    await request
      .patch(`/admin/model-portfolios/${user1ModelPortfolioId}`)
      .send({
        name: 'ModelPortfolio 1 -- new name',
        showCustomer: true,
        organizationId: orgId,
        priceEstimate: new Decimal(5.2),
        includeRiskAdjustment: false,
      })
      .then((res) => {
        if (res.status !== 200) console.warn(JSON.stringify(res));
        expect(res.status).toEqual(200);
        expect(res.body.name).toEqual('ModelPortfolio 1 -- new name');
        expect(res.body.uiKey).toBeDefined();
        expect(res.body.showCustomer).toEqual(true);
        expect(res.body.priceEstimate).toEqual(5.2);
        expect(res.body.includeRiskAdjustment).toEqual(false);
        expect(res.body.organization.id).toEqual(orgId);
        expect(res.body.isDeleted).toBeUndefined();
      });
  });

  test('/admin/model-portfolios/:id (PATCH) showCustomer undefined', async () => {
    await request
      .patch(`/admin/model-portfolios/${user1ModelPortfolioId}`)
      .send({ name: 'ModelPortfolio 1 -- new name' })
      .then((res) => {
        if (res.status !== 200) console.warn(JSON.stringify(res));
        expect(res.status).toEqual(200);
        expect(res.body.name).toEqual('ModelPortfolio 1 -- new name');
        expect(res.body.showCustomer).toEqual(true);
        expect(res.body.organization.id).toEqual(orgId);
        expect(res.body.isDeleted).toBeUndefined();
      });
  });

  test('/admin/model-portfolios/:id (PATCH) showCustomer false', async () => {
    await request
      .patch(`/admin/model-portfolios/${user1ModelPortfolioId}`)
      .send({ showCustomer: false })
      .then((res) => {
        if (res.status !== 200) console.warn(JSON.stringify(res));
        expect(res.status).toEqual(200);
        expect(res.body.name).toEqual('ModelPortfolio 1 -- new name');
        expect(res.body.showCustomer).toEqual(false);
        expect(res.body.organization.id).toEqual(orgId);
        expect(res.body.isDeleted).toBeUndefined();
      });
  });

  test('/admin/model-portfolios/:id (PATCH) rfp undefined', async () => {
    await request
      .patch(`/admin/model-portfolios/${user1ModelPortfolioId}`)
      .send({ name: 'ModelPortfolio 1 -- another new name' })
      .then((res) => {
        if (res.status !== 200) console.warn(JSON.stringify(res));
        expect(res.status).toEqual(200);
        expect(res.body.name).toEqual('ModelPortfolio 1 -- another new name');
        expect(res.body.rfp).toEqual(false);
      });
  });

  test('/admin/model-portfolios/:id (PATCH) rfp true', async () => {
    await request
      .patch(`/admin/model-portfolios/${user1ModelPortfolioId}`)
      .send({ rfp: true })
      .then((res) => {
        if (res.status !== 200) console.warn(JSON.stringify(res));
        expect(res.status).toEqual(200);
        expect(res.body.name).toEqual('ModelPortfolio 1 -- another new name');
        expect(res.body.rfp).toEqual(true);
      });
  });

  test('/admin/model-portfolios/:id/components (PATCH)', async () => {
    await request
      .patch(`/admin/model-portfolios/${user1ModelPortfolioId}/components`)
      .send({})
      .then((res) => {
        expect(res.status).toEqual(200);
      });

    await request
      .patch(`/admin/model-portfolios/${user1ModelPortfolioId}/components`)
      .send({ create: [{}], update: [{}], delete: [{}] })
      .then((res) => {
        expect(res.status).toEqual(200);
      });

    const start = new Date();
    const end = new Date(start.getTime() + 1);

    auth.email = user1email;
    await request
      .patch(`/admin/model-portfolios/${user1ModelPortfolioId}/components`)
      .send({
        create: [
          {
            amountAllocated: 10,
            bufferPercentage: '12.33',
            costBasis: '2.54',
            isBufferComponent: false,
            projectId: 'd4aa9b81-75dd-421c-a552-f43e356ccca7',
            vintageInterval: `${start.toISOString()} - ${end.toISOString()}`,
          },
        ],
      })
      .then((res) => {
        expect(res.status).toEqual(200);
      });

    await request
      .get(`/admin/model-portfolios/${user1ModelPortfolioId}`)
      .query({
        includeRelations: [
          ModelPortfolioRelations.MODEL_PORTFOLIO_COMPONENTS,
          ModelPortfolioRelations.PROJECT,
          ModelPortfolioRelations.PROJECT_VINTAGE,
        ],
      })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.modelPortfolioComponents).toHaveLength(1);
        expect(res.body.modelPortfolioComponents[0]).toMatchObject({
          amountAllocated: 10,
          bufferPercentage: 12.33,
          // costBasis: 2.54,
          isBufferComponent: false,
          // registryProjectId: 'PNGN123IRENE',
          vintageInterval: `${start.toISOString()} - ${end.toISOString()}`,
          project: { id: 'd4aa9b81-75dd-421c-a552-f43e356ccca7', bufferCategory: {} },
        });
        modelPortfolioComponents = res.body.modelPortfolioComponents;
      });

    await request
      .get(`/admin/project-vintages`)
      .query({ limit: 2 })
      .then((res) => {
        expect(res.status).toEqual(200);
        vintageIds = [res.body.data[0].id, res.body.data[1].id];
      });

    auth.email = user2email;
    await request
      .patch(`/admin/model-portfolios/${user1ModelPortfolioId}/components`)
      .send({
        update: [
          {
            id: modelPortfolioComponents[0].id,
            vintageId: vintageIds[1],
            amountAllocated: 1000,
            isBufferComponent: true,
            projectId: 'd4aa9b81-75dd-421c-a552-f43e356ccca7',
          },
        ],
      })
      .then((res) => {
        expect(res.status).toEqual(200);
      });

    await request
      .get(`/admin/model-portfolios/${user1ModelPortfolioId}`)
      .query({
        includeRelations: [
          ModelPortfolioRelations.MODEL_PORTFOLIO_COMPONENTS,
          ModelPortfolioRelations.PROJECT,
          ModelPortfolioRelations.PROJECT_VINTAGE,
        ],
      })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.modelPortfolioComponents).toHaveLength(1);
        const updatedMbc = res.body.modelPortfolioComponents.find(
          (f: AdminModelPortfolioComponentResponseDTO) => f.id === modelPortfolioComponents[0].id,
        );
        expect(updatedMbc).toBeDefined();
        expect(updatedMbc.createdBy.email).toEqual(user1email);
        expect(updatedMbc.updatedBy.email).toEqual(user2email);
        expect(updatedMbc.amountAllocated).toEqual(1000);
        expect(updatedMbc.isBufferComponent).toEqual(true);
        modelPortfolioComponents = res.body.modelPortfolioComponents;
      });

    await request.get(`/admin/model-portfolios/${user1ModelPortfolioId}`).then((res) => {
      expect((res.body as AdminModelPortfolioResponseDTO).modelPortfolioComponents).toHaveLength(1);
    });

    await request
      .patch(`/admin/model-portfolios/${user3ModelPortfolioId}/components`)
      .send({
        create: [
          {
            amountAllocated: 42,
            overrideMTM: '2.54',
            bookId: '0a22034b-0ba2-4eec-a8d6-c9bbc6429ba1',
          },
        ],
      })
      .then((res) => {
        expect(res.status).toEqual(200);
      });

    const deletedMbcId = modelPortfolioComponents[0].id;
    await request
      .patch(`/admin/model-portfolios/${user1ModelPortfolioId}/components`)
      .send({
        delete: [{ id: deletedMbcId }],
      })
      .then((res) => {
        expect(res.status).toEqual(200);
      });

    await request
      .get(`/admin/model-portfolios/${user1ModelPortfolioId}`)
      .query({ includeRelations: [ModelPortfolioRelations.MODEL_PORTFOLIO_COMPONENTS] })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.modelPortfolioComponents).toHaveLength(0);
      });
  });

  test('/admin/model-portfolios/:id (DELETE)', async () => {
    await request.delete(`/admin/model-portfolios/${zeroUUID}`).then((res) => {
      expect(res.status).toEqual(404);
      expect(res.body.message).toEqual('ModelPortfolio not found');
    });

    const deletedModelPortfolio = user1ModelPortfolioId;
    await request.delete(`/admin/model-portfolios/${deletedModelPortfolio}`).then(async (res) => {
      if (res.status !== 200) console.warn(JSON.stringify(res));
      expect(res.status).toEqual(200);

      await request.get(`/admin/model-portfolios/${deletedModelPortfolio}`).then((res) => {
        expect(res.status).toEqual(404);
      });

      await request
        .patch(`/admin/model-portfolios/${deletedModelPortfolio}`)
        .send({ name: 'Deleted Model Portfolio' })
        .then((res) => {
          expect(res.status).toEqual(404);
        });
    });
  });
});
