/* third party */
import { TestingModule } from '@nestjs/testing';
import Decimal from 'decimal.js';
/* rubicon */
import {
  BookAction,
  BookRelations,
  BookType,
  NotificationCadence,
  NotificationEvent,
  PermissionEnum,
  uuid,
  zeroUUID,
} from '@rubiconcarbon/shared-types';
/* env */
import { environment } from '@env/environment';
/* app */
import { AdminProjectTypeGroupedAllocationResponseDTO } from '@app/dtos/allocation.dto';
import { AdminBookEstimateRetirementResponseDTO, AdminBookResponseDTO } from '@app/dtos/book.dto';
import { AdminOrganizationResponseDTO } from '@app/dtos/organization.dto';
import { NotificationsService } from '@app/services';
/* test */
import { createOrganizationWithCustomerPortfolio } from '../helpers/organizations.helper';
import { createUser } from '../helpers/users.helper';
import { ApplicationTest, AuthInfo, Request } from '../setup/e2e';
import { AdminNotificationSubscriptionRequestDTO } from '@app/dtos/notification.dto';

describe('AdminBooksController (e2e)', () => {
  const appTest = new ApplicationTest();
  let request: Request;
  let auth: AuthInfo;
  let customPortfolioWithOrgId: uuid;
  let customPortfolioWithoutOrgId: uuid;
  const publicPortfolioIds: uuid[] = [];
  let module: TestingModule;
  let notificationsService: NotificationsService;
  let organization: AdminOrganizationResponseDTO;
  let notificationsCount = 0;

  beforeAll(async () => {
    ({ request, auth, module } = await appTest.setup());

    // create org
    organization = await createOrganizationWithCustomerPortfolio(request, auth);

    // create admin user
    await createUser(request, auth, '<EMAIL>');

    // set auth permissions
    auth.permissions = [
      PermissionEnum.ADMIN,
      PermissionEnum.BOOKS_CALCULATE_RETIREMENT,
      PermissionEnum.BOOKS_CREATE_RCT_PORTFOLIOS,
      PermissionEnum.BOOKS_READ,
      PermissionEnum.BOOKS_UPDATE,
      PermissionEnum.ORGANIZATIONS_CREATE,
    ];

    notificationsService = module.get(NotificationsService);
    await request
      .put('/admin/notifications/subscriptions')
      .send({
        subscriptions: [
          { event: NotificationEvent.BASKET_COMPOSITION_UPDATED, cadence: NotificationCadence.REALTIME },
          { event: NotificationEvent.BOOK_CREATED, cadence: NotificationCadence.REALTIME },
          { event: NotificationEvent.BOOK_PRICE_UPDATED, cadence: NotificationCadence.REALTIME },
          { event: NotificationEvent.INVENTORY_UPDATED, cadence: NotificationCadence.REALTIME },
        ],
      } as AdminNotificationSubscriptionRequestDTO)
      .expect(200);
    jest.spyOn(notificationsService, 'handleEvent');
  });

  afterAll(async () => {
    await appTest.cleanup();
  });

  test('/admin/books/rct-custom (POST)', async () => {
    await request.post('/admin/books/rct-custom').then((res) => {
      expect(res.status).toEqual(400);
      expect(res.body.message).toContain('purchasePrice must be a decimal greater than or equal to 0');
      expect(res.body.message).toContain('purchasePriceWithBuffer must be a decimal greater than or equal to 0');
      expect(res.body.message).toContain('name must be a non-empty string');
      expect(res.body.message).toContain('isEnabled must be a boolean value');
    });

    await request
      .post('/admin/books/rct-custom')
      .send({})
      .then((res) => {
        expect(res.status).toEqual(400);
        expect(res.body.message).toContain('purchasePriceWithBuffer must be a decimal greater than or equal to 0');
        expect(res.body.message).toContain('purchasePrice must be a decimal greater than or equal to 0');
        expect(res.body.message).toContain('name must be a non-empty string');
        expect(res.body.message).toContain('isEnabled must be a boolean value');
      });

    await request
      .post('/admin/books/rct-custom')
      .send({
        organizationId: zeroUUID,
        name: 'Test Custom Portfolio Book',
        isEnabled: true,
        purchasePrice: '13.13',
      })
      .then((res) => {
        expect(res.status).toEqual(400);
        expect(res.body.message).toEqual(['organizationId must be a valid uuid']);
      });

    await request
      .post('/admin/books/rct-custom')
      .send({
        name: 'Test Custom Portfolio Book',
        isEnabled: true,
        purchasePrice: '13.13',
        purchasePriceWithBuffer: '12.12',
      })
      .then((res) => {
        expect(res.status).toEqual(400);
        expect(res.body.message).toEqual([
          'purchasePriceWithBuffer 12.12 must be greater than or equal to purchasePrice 13.13',
        ]);
      });

    await request
      .post('/admin/books/rct-custom')
      .send({
        name: 'Test Custom Portfolio Book No Org',
        isEnabled: true,
        purchasePrice: '13.13',
      })
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body.name).toEqual('Test Custom Portfolio Book No Org');
        expect(res.body.isEnabled).toBeTruthy();
        expect(res.body.limit).toBeDefined();
        expect(res.body.purchasePrice).toEqual(13.13);
        expect(res.body.purchasePriceWithBuffer).toBeNull();
        expect(res.body.type).toEqual(BookType.RCT_CUSTOM);
        customPortfolioWithoutOrgId = res.body.id;
      });
    expect(notificationsService.handleEvent).toHaveBeenCalledTimes((notificationsCount += 1));

    await request
      .post('/admin/books/rct-custom')
      .send({
        organizationId: organization.id,
        name: 'Test Custom Portfolio Book With Org',
        isEnabled: true,
        purchasePriceWithBuffer: '13.13',
      })
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body.name).toEqual('Test Custom Portfolio Book With Org');
        expect(res.body.isEnabled).toBeTruthy();
        expect(res.body.limit).toBeDefined();
        expect(res.body.purchasePrice).toBeNull();
        expect(res.body.purchasePriceWithBuffer).toEqual(13.13);
        expect(res.body.type).toEqual(BookType.RCT_CUSTOM);
        customPortfolioWithOrgId = res.body.id;
      });
    expect(notificationsService.handleEvent).toHaveBeenCalledTimes((notificationsCount += 1));
  });

  test('/admin/books/rct-public (POST)', async () => {
    await request.post('/admin/books/rct-public').then((res) => {
      expect(res.status).toEqual(400);
      expect(res.body.message).toContain('name must be a non-empty string');
      expect(res.body.message).toContain('isEnabled must be a boolean value');
      expect(res.body.message).toContain('purchasePrice must be a decimal greater than or equal to 0');
      expect(res.body.message).toContain('purchasePriceWithBuffer must be a decimal greater than or equal to 0');
    });

    await request
      .post('/admin/books/rct-public')
      .send({})
      .then((res) => {
        expect(res.status).toEqual(400);
        expect(res.body.message).toContain('name must be a non-empty string');
        expect(res.body.message).toContain('isEnabled must be a boolean value');
        expect(res.body.message).toContain('purchasePrice must be a decimal greater than or equal to 0');
        expect(res.body.message).toContain('purchasePriceWithBuffer must be a decimal greater than or equal to 0');
      });

    await request
      .post('/admin/books/rct-public')
      .send({
        name: 'Public Portfolio Book Name',
        isEnabled: true,
      })
      .then((res) => {
        expect(res.status).toEqual(400);
        expect(res.body.message).toContain('purchasePrice must be a decimal greater than or equal to 0');
        expect(res.body.message).toContain('purchasePriceWithBuffer must be a decimal greater than or equal to 0');
      });

    await request
      .post('/admin/books/rct-public')
      .send({
        name: 'Public Portfolio Book',
        isEnabled: true,
        purchasePrice: '13.13',
      })
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body.name).toEqual('Public Portfolio Book');
        expect(res.body.isEnabled).toBeTruthy();
        expect(res.body.limit).toBeDefined();
        expect(res.body.purchasePrice).toEqual(13.13);
        expect(res.body.purchasePriceWithBuffer).toBeNull();
        expect(res.body.type).toEqual(BookType.RCT_PUBLIC);
        publicPortfolioIds.push(res.body.id);
      });
    expect(notificationsService.handleEvent).toHaveBeenCalledTimes((notificationsCount += 1));

    await request
      .post('/admin/books/rct-public')
      .send({
        name: 'Public Portfolio Book',
        isEnabled: true,
        purchasePriceWithBuffer: '13.13',
      })
      .then((res) => {
        expect(res.status).toEqual(409);
      });

    await request
      .post('/admin/books/rct-public')
      .send({
        name: 'Public Portfolio Book With Buffer',
        isEnabled: false,
        purchasePriceWithBuffer: '13.15',
      })
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body.name).toEqual('Public Portfolio Book With Buffer');
        expect(res.body.isEnabled).toBeFalsy();
        expect(res.body.limit).toBeDefined();
        expect(res.body.purchasePrice).toBeNull();
        expect(res.body.purchasePriceWithBuffer).toEqual(13.15);
        expect(res.body.type).toEqual(BookType.RCT_PUBLIC);
        publicPortfolioIds.push(res.body.id);
      });
    expect(notificationsService.handleEvent).toHaveBeenCalledTimes((notificationsCount += 1));

    await request
      .post('/admin/books/rct-public')
      .send({
        name: 'Public Portfolio Book With Both',
        isEnabled: false,
        purchasePrice: '15',
        purchasePriceWithBuffer: '13.15',
      })
      .then((res) => {
        expect(res.status).toEqual(400);
        expect(res.body.message).toEqual([
          'purchasePriceWithBuffer 13.15 must be greater than or equal to purchasePrice 15',
        ]);
      });

    await request
      .post('/admin/books/rct-public')
      .send({
        name: 'Public Portfolio Book With Both',
        isEnabled: false,
        purchasePrice: '13.13',
        purchasePriceWithBuffer: '13.13',
      })
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body.name).toEqual('Public Portfolio Book With Both');
        expect(res.body.isEnabled).toBeFalsy();
        expect(res.body.limit).toBeDefined();
        expect(res.body.purchasePrice).toEqual(13.13);
        expect(res.body.purchasePriceWithBuffer).toEqual(13.13);
        expect(res.body.type).toEqual(BookType.RCT_PUBLIC);
        publicPortfolioIds.push(res.body.id);
      });
    expect(notificationsService.handleEvent).toHaveBeenCalledTimes((notificationsCount += 1));
  });

  test('/admin/books/rrt-public (POST)', async () => {
    await request.post('/admin/books/rrt-public').then((res) => {
      expect(res.status).toEqual(400);
      expect(res.body.message.sort()).toEqual([
        'isEnabled must be a boolean value',
        'name must be a non-empty string',
        'purchasePrice must be a decimal greater than or equal to 0',
      ]);
    });

    await request
      .post('/admin/books/rrt-public')
      .send({
        name: 'RRT Portfolio',
        isEnabled: true,
        purchasePrice: '13.13',
      })
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body.name).toEqual('RRT Portfolio');
        expect(res.body.isEnabled).toBeTruthy();
        expect(res.body.limit).toBeDefined();
        expect(res.body.purchasePrice).toEqual(13.13);
        expect(res.body.type).toEqual(BookType.RRT_PUBLIC);
        publicPortfolioIds.push(res.body.id);
      });
    expect(notificationsService.handleEvent).toHaveBeenCalledTimes((notificationsCount += 1));
  });

  test('/admin/books (GET)', async () => {
    await request
      .get('/admin/books')
      .query({ includeTotalCount: true })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.data.length).toEqual(17);
        expect(res.body.page.size).toEqual(17);
        expect(res.body.page.totalCount).toEqual(17);
        expect(res.body.data.find((f: AdminBookResponseDTO) => f.id === customPortfolioWithOrgId)).toBeDefined();
        expect(res.body.data.find((f: AdminBookResponseDTO) => f.id === customPortfolioWithoutOrgId)).toBeDefined();
        publicPortfolioIds.forEach((id) =>
          expect(res.body.data.find((f: AdminBookResponseDTO) => f.id === id)).toBeDefined(),
        );
        expect(
          res.body.data.filter((f: AdminBookResponseDTO) => f.type === BookType.PORTFOLIO_CUSTOMER).length,
        ).toEqual(1);
        expect(
          res.body.data.filter((f: AdminBookResponseDTO) => f.type === BookType.RRT_PUBLIC).length,
        ).toBeGreaterThanOrEqual(1);
        expect(res.body.data.find((f: AdminBookResponseDTO) => f.id === environment.rubicon.books.aged)).toBeDefined();
        expect(
          res.body.data.find((f: AdminBookResponseDTO) => f.id === environment.rubicon.books.compliance),
        ).toBeDefined();
        expect(
          res.body.data.find((f: AdminBookResponseDTO) => f.id === environment.rubicon.books.opportunistic),
        ).toBeDefined();
        expect(
          res.body.data.find((f: AdminBookResponseDTO) => f.id === environment.rubicon.books.portfolio.reserves),
        ).toBeDefined();
        expect(
          res.body.data.find((f: AdminBookResponseDTO) => f.id === environment.rubicon.books.portfolio.default),
        ).toBeDefined();
        expect(
          res.body.data.find((f: AdminBookResponseDTO) => f.id === environment.rubicon.books.rehabilitation),
        ).toBeDefined();
      });

    await request
      .get('/admin/books')
      .query({ offset: 7 })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.data.length).toEqual(10);
        expect(res.body.page.size).toEqual(10);
      });

    await request
      .get('/admin/books')
      .query({ allowedActions: [BookAction.BUY] })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.data.length).toEqual(5);
        expect(res.body.page.size).toEqual(5);
      });

    await request
      .get('/admin/books')
      .query({ allowedActions: [BookAction.BUY, BookAction.PURCHASE] })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.data.length).toEqual(6);
        expect(res.body.page.size).toEqual(6);
      });

    await request
      .get('/admin/books')
      .query({ limit: 1 })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.data.length).toEqual(1);
        expect(res.body.page.size).toEqual(1);
        expect(res.body.data[0].organization).toBeUndefined();
      });

    await request
      .get('/admin/books')
      .query({
        limit: 1,
        includeRelations: [BookRelations.ORGANIZATION],
        includeTotalCount: true,
      })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.data.length).toEqual(1);
        expect(res.body.page.size).toEqual(1);
        expect(res.body.page.totalCount).toEqual(17);
        expect(res.body.data[0].organization).toBeDefined();
      });

    await request
      .get('/admin/books')
      .query({ organizationId: organization.id, includeTotalCount: true })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.data.length).toEqual(2);
        expect(res.body.page.size).toEqual(2);
        expect(res.body.page.totalCount).toEqual(2);
      });

    await request
      .get('/admin/books')
      .query({ isEnabled: false })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.data.length).toEqual(2);
        expect(res.body.page.size).toEqual(2);
      });

    await request
      .get('/admin/books')
      .query({ isEnabled: true, limit: 3, offset: 13, includeTotalCount: true })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.data.length).toEqual(2);
        expect(res.body.page.size).toEqual(2);
        expect(res.body.page.totalCount).toEqual(15);
      });

    await request
      .get('/admin/books')
      .query({ name: 'Battlestar Galactica', limit: 3, offset: 11, includeTotalCount: true })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.data.length).toEqual(0);
        expect(res.body.page.size).toEqual(0);
        expect(res.body.page.totalCount).toEqual(0);
      });

    await request
      .get('/admin/books')
      .query({ name: 'Opportunistic' })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.data.length).toEqual(1);
        expect(res.body.page.size).toEqual(1);
      });

    await request
      .get('/admin/books')
      .query({ types: [BookType.COMPLIANCE_DEFAULT, BookType.OPPORTUNISTIC_DEFAULT] })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.data.length).toEqual(2);
        expect(res.body.page.size).toEqual(2);
      });

    await request
      .get('/admin/books')
      .query({
        types: [
          BookType.RCT_CUSTOM,
          BookType.PORTFOLIO_CUSTOMER,
          BookType.PORTFOLIO_DEFAULT,
          BookType.RCT_PUBLIC,
          BookType.RRT_PUBLIC,
          BookType.PORTFOLIO_RESERVES,
        ],
      })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.data.length).toEqual(13);
        expect(res.body.page.size).toEqual(13);
      });
  });

  test('/admin/books/:id (GET)', async () => {
    await request.get(`/admin/books/${zeroUUID}`).then((res) => {
      expect(res.status).toEqual(404);
      expect(res.body.message).toEqual('Book not found');
    });

    await request.get('/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974').then((res) => {
      expect(res.status).toEqual(200);
      expect(res.body.id).toEqual('f4cf07b2-04ba-4ee9-94b3-d1f860fe8974');
      expect(res.body.name).toEqual('Nature-Based Emissions Reductions RCT');
      expect(res.body.isEnabled).toBeTruthy();
      expect(res.body.limit).toBeDefined();
      expect(res.body.purchasePrice).toBeDefined();
      expect(res.body.purchasePriceWithBuffer).toBeDefined();
      expect(res.body.type).toEqual(BookType.RCT_PUBLIC);
      expect(res.body.organization).toBeUndefined();
      expect(res.body.projectTypes).toBeUndefined();
      expect(res.body.allocatedProjectVintages).toBeUndefined();
    });

    await request
      .get('/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974')
      .query({ includeRelations: [BookRelations.OWNER_ALLOCATIONS_NESTED] })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.id).toEqual('f4cf07b2-04ba-4ee9-94b3-d1f860fe8974');
        expect(res.body.name).toEqual('Nature-Based Emissions Reductions RCT');
        expect(res.body.isEnabled).toBeTruthy();
        expect(res.body.limit).toBeDefined();
        expect(res.body.purchasePrice).toBeDefined();
        expect(res.body.purchasePriceWithBuffer).toBeDefined();
        expect(res.body.type).toEqual(BookType.RCT_PUBLIC);
        expect(res.body.organization).toBeUndefined();
        expect(res.body.projectTypes).toBeUndefined();
        expect(res.body.ownerAllocations.allocations.length).toBeGreaterThan(0);
      });

    await request
      .get('/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974')
      .query({
        includeRelations: [
          BookRelations.ASSET_ALLOCATIONS,
          BookRelations.ASSET_ALLOCATIONS_NESTED,
          BookRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE,
          BookRelations.ORGANIZATION,
          BookRelations.OWNER_ALLOCATIONS,
          BookRelations.OWNER_ALLOCATIONS_NESTED,
          BookRelations.OWNER_ALLOCATIONS_BY_ASSET_TYPE,
          BookRelations.OWNER_ALLOCATIONS_BY_PROJECT,
          BookRelations.OWNER_ALLOCATIONS_BY_PROJECT_TYPE,
        ],
      })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.id).toEqual('f4cf07b2-04ba-4ee9-94b3-d1f860fe8974');
        expect(res.body.name).toEqual('Nature-Based Emissions Reductions RCT');
        expect(res.body.isEnabled).toBeTruthy();
        expect(res.body.limit).toBeDefined();
        expect(res.body.purchasePrice).toBeDefined();
        expect(res.body.purchasePriceWithBuffer).toBeDefined();
        expect(res.body.type).toEqual(BookType.RCT_PUBLIC);
        expect(res.body.organization).toBeUndefined();
        expect(res.body.assetAllocations.allocations).toBeDefined();
        expect(res.body.assetAllocationsByBookType).toBeDefined();
        expect(res.body.ownerAllocations.allocations).toBeDefined();
        expect(res.body.ownerAllocationsByAssetType).toBeDefined();
        expect(res.body.ownerAllocationsByProject).toBeDefined();
        expect(res.body.ownerAllocationsByProjectType).toBeDefined();
      });

    await request
      .get('/admin/books/3b50b813-c390-4935-b8a6-2d812509bb07')
      .query({
        includeRelations: [BookRelations.OWNER_ALLOCATIONS_BY_PROJECT],
      })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.id).toEqual('3b50b813-c390-4935-b8a6-2d812509bb07');
        expect(res.body.name).toEqual('Super Pollutant Elimination RCT');
        expect(res.body.portfolioScore).toBeGreaterThan(0);
      });

    await request
      .get(`/admin/books/${customPortfolioWithOrgId}`)
      .query({
        includeRelations: [
          BookRelations.ASSET_ALLOCATIONS,
          BookRelations.ASSET_ALLOCATIONS_NESTED,
          BookRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE,
          BookRelations.ORGANIZATION,
          BookRelations.OWNER_ALLOCATIONS,
          BookRelations.OWNER_ALLOCATIONS_NESTED,
          BookRelations.OWNER_ALLOCATIONS_BY_ASSET_TYPE,
          BookRelations.OWNER_ALLOCATIONS_BY_PROJECT,
          BookRelations.OWNER_ALLOCATIONS_BY_PROJECT_TYPE,
        ],
      })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.id).toEqual(customPortfolioWithOrgId);
        expect(res.body.name).toBeDefined();
        expect(res.body.isEnabled).toBeDefined();
        expect(res.body.limit).toBeDefined();
        expect(res.body.purchasePrice).toBeDefined();
        expect(res.body.type).toEqual(BookType.RCT_CUSTOM);
        expect(res.body.organization).toBeDefined();
        expect(res.body.assetAllocations.allocations).toBeDefined();
        expect(res.body.assetAllocationsByBookType).toBeDefined();
        expect(res.body.ownerAllocations.allocations).toBeDefined();
        expect(res.body.ownerAllocationsByAssetType).toBeDefined();
        expect(res.body.ownerAllocationsByProject).toBeDefined();
        expect(res.body.ownerAllocationsByProjectType).toBeDefined();
      });
  });

  test('/admin/books/:id/retirement-calcluations (GET)', async () => {
    await request.get(`/admin/books/${zeroUUID}/retirement-calculations/1000`).then((res) => {
      expect(res.status).toEqual(404);
    });

    await request.get(`/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974/retirement-calculations/-10`).then((res) => {
      expect(res.status).toEqual(400);
    });

    await request
      .get(`/admin/books/${environment.rubicon.books.portfolio.reserves}/retirement-calculations/1000`)
      .then((res) => {
        expect(res.status).toEqual(422);
        expect(res.body.message).toEqual(`Retirement cannot be made for Rubicon RCT Reserves`);
      });

    await request.get(`/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974/retirement-calculations/1000`).then((res) => {
      expect(res.status).toEqual(200);
      expect(res.body[0].projectVintage.project.country.name).toBeDefined();
      expect(res.body[0].projectVintage.project.registryProjectId).toBeDefined();
      expect(res.body[0].projectVintage.project.projectType.id).toBeDefined();
      expect(res.body[0].projectVintage.project.projectType.name).toBeDefined();
      const amounts: Decimal[] = res.body.map(
        (m: AdminBookEstimateRetirementResponseDTO) => new Decimal(m.amountTransacted),
      );
      expect(Decimal.sum(...amounts).toNumber()).toEqual(1000);
    });

    await request.get(`/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974/retirement-calculations/15`).then((res) => {
      expect(res.status).toEqual(200);
      const amounts: Decimal[] = res.body.map(
        (m: AdminBookEstimateRetirementResponseDTO) => new Decimal(m.amountTransacted),
      );
      expect(Decimal.sum(...amounts).toNumber()).toEqual(15);
    });
  });

  test('/admin/books/:id (PATCH)', async () => {
    let purchasePrice = 0;
    let purchasePriceWithBuffer = 0;
    await request.get('/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974').then((res) => {
      expect(res.status).toEqual(200);
      purchasePrice = res.body.purchasePrice;
      purchasePriceWithBuffer = res.body.purchasePriceWithBuffer;
    });

    await request
      .patch('/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974')
      .send({ purchasePrice: '15.01' })
      .then((res) => {
        expect(res.status).toEqual(403);
      });

    await request
      .patch('/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974')
      .send({ purchasePriceWithBuffer: '15.01' })
      .then((res) => {
        expect(res.status).toEqual(403);
      });

    await request
      .patch('/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974')
      .send({ description: 'new description' })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.name).toEqual('Nature-Based Emissions Reductions RCT');
        expect(res.body.description).toEqual('new description');
        expect(res.body.isEnabled).toEqual(true);
        expect(res.body.limit).toEqual({});
        expect(res.body.type).toEqual(BookType.RCT_PUBLIC);
        expect(res.body.purchasePrice).toEqual(purchasePrice);
        expect(res.body.purchasePriceWithBuffer).toEqual(purchasePriceWithBuffer);
      });

    // check setting null
    await request
      .patch('/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974')
      .send({ description: null })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.name).toEqual('Nature-Based Emissions Reductions RCT');
        expect(res.body.description == undefined).toBeTruthy();
      });

    await request
      .patch('/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974')
      .send({ isEnabled: false, type: BookType.RCT_CUSTOM, description: 'new description' })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.name).toEqual('Nature-Based Emissions Reductions RCT');
        expect(res.body.description).toEqual('new description');
        expect(res.body.isEnabled).toEqual(false);
        expect(res.body.limit).toEqual({});
        expect(res.body.type).toEqual(BookType.RCT_PUBLIC); // cannot change type of portfolio
        expect(res.body.purchasePrice).toEqual(purchasePrice);
        expect(res.body.purchasePriceWithBuffer).toEqual(purchasePriceWithBuffer);
      });

    await request
      .patch('/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974')
      .send({ isEnabled: true, limit: { holdingAmountMax: 14.21 } })
      .then((res) => {
        expect(res.status).toEqual(400);
        expect(res.body.message).toEqual(['limit.holdingAmountMax must be an int greater than or equal to 0']);
      });

    await request
      .patch('/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974')
      .send({ isEnabled: true, limit: { holdingAmountMax: 154 } })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.name).toEqual('Nature-Based Emissions Reductions RCT');
        expect(res.body.description).toEqual('new description');
        expect(res.body.isEnabled).toEqual(true);
        expect(res.body.limit).toEqual({ holdingAmountMax: 154 });
        expect(res.body.type).toEqual(BookType.RCT_PUBLIC);
        expect(res.body.purchasePrice).toEqual(purchasePrice);
        expect(res.body.purchasePriceWithBuffer).toEqual(purchasePriceWithBuffer);
      });

    await request
      .patch('/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974')
      .send({ name: 'Public Portfolio Book' })
      .then((res) => {
        expect(res.status).toEqual(409);
      });

    await request
      .patch('/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974')
      .send({ name: 'New Name', description: null })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.name).toEqual('New Name');
        expect(res.body.description).toBeNull();
        expect(res.body.isEnabled).toEqual(true);
        expect(res.body.limit).toEqual({ holdingAmountMax: 154 });
        expect(res.body.type).toEqual(BookType.RCT_PUBLIC);
        expect(res.body.purchasePrice).toEqual(purchasePrice);
        expect(res.body.purchasePriceWithBuffer).toEqual(purchasePriceWithBuffer);
      });

    await request
      .patch('/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974')
      .send({ projectTypeIds: null })
      .then((res) => {
        expect(res.status).toEqual(403);
      });

    auth.permissions = [...auth.permissions, PermissionEnum.BOOKS_UPDATE_PROJECT_TYPES];
    // todo : (TD-58) validate removing projectTypes for assets that exist after we figure out views
    // await request
    //   .patch(`/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974`)
    //   .send({ projectTypeIds: [] })
    //   .then((res) => {
    //     expect(res.status).toEqual(422);
    //     expect(res.body.name).toEqual('New Name');
    //     expect(res.body.description).toBeUndefined();
    //     expect(res.body.isEnabled).toEqual(true);
    //     expect(res.body.limit).toEqual({ holdingAmountMax: 154 });
    //     expect(res.body.type).toEqual(BookType.RCT_PUBLIC);
    //     expect(res.body.purchasePrice).toEqual(20);
    //     expect(res.body.purchasePriceWithBuffer).toEqual(25);
    //     expect(res.body.projectTypes).toEqual([]);
    //   });

    await request
      .patch(`/admin/books/${customPortfolioWithOrgId}`)
      .send({ projectTypeIds: [] })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.ownerAllocationsByProjectType).toEqual([]);
      });

    await request
      .patch(`/admin/books/${customPortfolioWithOrgId}`)
      .send({ projectTypeIds: [1, 2, 3] })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.ownerAllocationsByProjectType.length).toEqual(3);
        expect(
          res.body.ownerAllocationsByProjectType.find(
            (f: AdminProjectTypeGroupedAllocationResponseDTO) => f.projectType.id === 1,
          ),
        ).toBeDefined();
        expect(
          res.body.ownerAllocationsByProjectType.find(
            (f: AdminProjectTypeGroupedAllocationResponseDTO) => f.projectType.id === 2,
          ),
        ).toBeDefined();
        expect(
          res.body.ownerAllocationsByProjectType.find(
            (f: AdminProjectTypeGroupedAllocationResponseDTO) => f.projectType.id === 3,
          ),
        ).toBeDefined();
      });

    await request
      .patch(`/admin/books/${customPortfolioWithOrgId}`)
      .send({ projectTypeIds: [3, 4] })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.ownerAllocationsByProjectType.length).toEqual(2);
        expect(
          res.body.ownerAllocationsByProjectType.find(
            (f: AdminProjectTypeGroupedAllocationResponseDTO) => f.projectType.id === 3,
          ),
        ).toBeDefined();
        expect(
          res.body.ownerAllocationsByProjectType.find(
            (f: AdminProjectTypeGroupedAllocationResponseDTO) => f.projectType.id === 4,
          ),
        ).toBeDefined();
      });

    await request
      .patch(`/admin/books/${customPortfolioWithOrgId}`)
      .send({ description: 'test description', projectTypeIds: null })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.description).toEqual('test description');
        expect(res.body.ownerAllocationsByProjectType.length).toEqual(0);
      });

    await request
      .patch(`/admin/books/${customPortfolioWithOrgId}`)
      .send({ projectTypeIds: [20] })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.description).toEqual('test description');
        expect(res.body.ownerAllocationsByProjectType.length).toEqual(1);
        expect(res.body.ownerAllocationsByProjectType[0].projectType.id).toEqual(20);
      });

    auth.permissions = [...auth.permissions, PermissionEnum.BOOKS_UPDATE_PRICE];
    await request
      .patch('/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974')
      .send({ purchasePrice: -1 })
      .then((res) => {
        expect(res.status).toEqual(400);
        expect(res.body.message).toContain('purchasePrice must be a decimal greater than or equal to 0');
      });

    await request
      .patch('/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974')
      .send({ purchasePrice: 12.03 })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.purchasePrice).toEqual(12.03);
        expect(res.body.purchasePriceWithBuffer).toEqual(purchasePriceWithBuffer);
        purchasePrice = res.body.purchasePrice;
        purchasePriceWithBuffer = res.body.purchasePriceWithBuffer;
      });
    expect(notificationsService.handleEvent).toHaveBeenCalledTimes((notificationsCount += 1));

    await request
      .patch('/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974')
      .send({ purchasePriceWithBuffer: 17.03 })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.purchasePrice).toEqual(12.03);
        expect(res.body.purchasePriceWithBuffer).toEqual(17.03);
        purchasePrice = res.body.purchasePrice;
        purchasePriceWithBuffer = res.body.purchasePriceWithBuffer;
      });
    expect(notificationsService.handleEvent).toHaveBeenCalledTimes((notificationsCount += 1));

    await request
      .patch('/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974')
      .send({ purchasePrice: 15.01 })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.purchasePrice).toEqual(15.01);
        expect(res.body.purchasePriceWithBuffer).toEqual(purchasePriceWithBuffer);
        purchasePrice = res.body.purchasePrice;
        purchasePriceWithBuffer = res.body.purchasePriceWithBuffer;
      });
    expect(notificationsService.handleEvent).toHaveBeenCalledTimes((notificationsCount += 1));

    await request
      .patch('/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974')
      .send({ purchasePrice: 18 })
      .then((res) => {
        expect(res.status).toEqual(400);
        expect(res.body.message).toEqual([
          'purchasePriceWithBuffer 17.03 must be greater than or equal to purchasePrice 18',
        ]);
      });

    await request
      .patch('/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974')
      .send({ purchasePrice: 16 })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.purchasePrice).toEqual(16);
        expect(res.body.purchasePriceWithBuffer).toEqual(purchasePriceWithBuffer);
        purchasePrice = res.body.purchasePrice;
        purchasePriceWithBuffer = res.body.purchasePriceWithBuffer;
      });
    expect(notificationsService.handleEvent).toHaveBeenCalledTimes((notificationsCount += 1));
  });
});
