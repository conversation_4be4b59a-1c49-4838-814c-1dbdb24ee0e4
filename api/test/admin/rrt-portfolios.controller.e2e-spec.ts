/* third party */
import { TestingModule } from '@nestjs/testing';
import Decimal from 'decimal.js';
/* rubicon */
import {
  AdminBookResponse,
  BookRelations,
  BookType,
  NotificationCadence,
  NotificationEvent,
  PermissionEnum,
} from '@rubiconcarbon/shared-types';
/* env */
import { environment } from '@env/environment';
/* app */
import { AdminAllocationResponseDTO } from '@app/dtos/allocation.dto';
import { NotificationsService } from '@app/services';
/* test */
import { createUser } from '../helpers/users.helper';
import { ApplicationTest, AuthInfo, Request } from '../setup/e2e';
import { AdminNotificationSubscriptionRequestDTO } from '@app/dtos/notification.dto';
import { AdminRrtVintageAssetResponseDTO } from '@app/dtos/asset.dto';

describe('LOGIC : Admin RRT Portfolio (e2e)', () => {
  const appTest = new ApplicationTest();
  let request: Request;
  let auth: AuthInfo;
  let module: TestingModule;
  let notificationsService: NotificationsService;
  let notificationsCount = 0;
  let rrtPortfolio: AdminBookResponse;
  let defaultPortfolioAllocations: AdminAllocationResponseDTO[] = [];

  beforeAll(async () => {
    ({ request, auth, module } = await appTest.setup());
    // create admin user
    await createUser(request, auth, '<EMAIL>');

    // set auth permissions
    auth.permissions = [
      PermissionEnum.ADMIN,
      PermissionEnum.BOOKS_CREATE_RRT_PORTFOLIOS,
      PermissionEnum.BOOKS_READ,
      PermissionEnum.BOOKS_UPDATE,
      PermissionEnum.BOOKS_UPDATE_PRICE,
      PermissionEnum.BOOKS_UPDATE_PROJECT_TYPES,
      PermissionEnum.BOOKS_UPDATE_RRT_COMPOSITION,
      PermissionEnum.TRANSFERS_EXECUTE,
    ];

    notificationsService = module.get(NotificationsService);
    await request
      .put('/admin/notifications/subscriptions')
      .send({
        subscriptions: [
          { event: NotificationEvent.BASKET_COMPOSITION_UPDATED, cadence: NotificationCadence.REALTIME },
          { event: NotificationEvent.BOOK_CREATED, cadence: NotificationCadence.REALTIME },
          { event: NotificationEvent.BOOK_PRICE_UPDATED, cadence: NotificationCadence.REALTIME },
          { event: NotificationEvent.INVENTORY_UPDATED, cadence: NotificationCadence.REALTIME },
        ],
      } as AdminNotificationSubscriptionRequestDTO)
      .expect(200);
    jest.spyOn(notificationsService, 'handleEvent');

    await request
      .get(`/admin/books/${environment.rubicon.books.portfolio.default}`)
      .query({ includeRelations: [BookRelations.OWNER_ALLOCATIONS_NESTED] })
      .then((res) => {
        defaultPortfolioAllocations = (res.body.ownerAllocations.allocations as AdminAllocationResponseDTO[]).filter(
          (f) => f.asset.beZeroRating != undefined,
        );
      });
  });

  afterAll(async () => {
    await appTest.cleanup();
  });

  test('create and update RRT Portfolio', async () => {
    await request
      .post('/admin/portfolios/rrts')
      .send({
        name: 'RRT Portfolio',
        isEnabled: true,
        purchasePrice: '13.13',
      })
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body.name).toEqual('RRT Portfolio');
        expect(res.body.isEnabled).toBeTruthy();
        expect(res.body.limit).toBeDefined();
        expect(res.body.purchasePrice).toEqual(13.13);
        expect(res.body.type).toEqual(BookType.RRT_PUBLIC);
        rrtPortfolio = res.body;
      });
    expect(notificationsService.handleEvent).toHaveBeenCalledTimes((notificationsCount += 1));

    await request
      .get(`/admin/books/${rrtPortfolio.id}`)
      .query({ includeRelations: [BookRelations.OWNER_ALLOCATIONS_NESTED, BookRelations.ASSET_ALLOCATIONS_NESTED] })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.name).toEqual('RRT Portfolio');
        expect(res.body.isEnabled).toBeTruthy();
        expect(res.body.limit).toBeDefined();
        expect(res.body.purchasePrice).toEqual(13.13);
        expect(res.body.type).toEqual(BookType.RRT_PUBLIC);
        expect(res.body.ownerAllocations.totalAmountAllocated).toEqual(0);
        expect(res.body.ownerAllocations.allocations.length).toEqual(0);
        expect(res.body.assetAllocations.totalAmountAllocated).toEqual(0);
        expect(res.body.assetAllocations.allocations.length).toEqual(0);
      });

    await request
      .patch(`/admin/books/${rrtPortfolio.id}`)
      .send({ purchasePrice: '15.01', projectTypeIds: [1, 2, 3] })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.purchasePrice).toEqual(15.01);
        expect(res.body.projectTypes).toBeUndefined();
      });
    expect(notificationsService.handleEvent).toHaveBeenCalledTimes((notificationsCount += 1));
  });

  test('update RRT Portfolio composition', async () => {
    let rrtAllocations: AdminRrtVintageAssetResponseDTO[] = [];

    let totalGrossQuantity = new Decimal(defaultPortfolioAllocations[0].amountAvailable)
      .divToInt(10)
      .mul(1.43)
      .ceil()
      .toNumber();
    let totalNetQuantity = new Decimal(defaultPortfolioAllocations[0].amountAvailable).divToInt(10).toNumber();
    await request
      .patch(`/admin/portfolios/rrts/${rrtPortfolio.id}/assets`)
      .send({
        portfolioId: rrtPortfolio.id,
        memo: 'test transfer',
        assets: [
          {
            beZeroFactor: 1.43,
            grossQuantity: totalGrossQuantity,
            netQuantity: totalNetQuantity,
            projectVintageId: defaultPortfolioAllocations[0].asset.id,
          },
        ],
      })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.memo).toEqual('test transfer');
      });

    await request
      .get(`/admin/books/${rrtPortfolio.id}`)
      .query({ includeRelations: [BookRelations.OWNER_ALLOCATIONS_NESTED, BookRelations.ASSET_ALLOCATIONS_NESTED] })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.name).toEqual('RRT Portfolio');
        expect(res.body.type).toEqual(BookType.RRT_PUBLIC);
        expect(res.body.ownerAllocations.totalAmountAllocated).toEqual(totalGrossQuantity);
        expect(res.body.ownerAllocations.totalAmountAvailable).toEqual(totalGrossQuantity);
        expect(res.body.ownerAllocations.allocations.length).toEqual(1);
        expect(res.body.assetAllocations.totalAmountAllocated).toEqual(totalNetQuantity);
        expect(res.body.assetAllocations.totalAmountAvailable).toEqual(totalNetQuantity);
        expect(res.body.assetAllocations.allocations.length).toEqual(1);
      });

    await request.get(`/admin/portfolios/rrts/${rrtPortfolio.id}`).then((res) => {
      expect(res.status).toEqual(200);
      expect(res.body.assets.length).toEqual(1);
      expect(
        Decimal.sum(
          ...res.body.assets.map((m: AdminRrtVintageAssetResponseDTO) => m.portfolioNetPercentage),
        ).toNumber(),
      ).toEqual(1);
      expect(res.body.assets[0].portfolio.id).toEqual(rrtPortfolio.id);
      expect(res.body.assets[0].projectVintage.id).toEqual(defaultPortfolioAllocations[0].asset.id);
      expect(res.body.assets[0].beZeroFactor).toBeGreaterThanOrEqual(res.body.assets[0].calculatedFactor);
      rrtAllocations = res.body.assets;
    });

    totalGrossQuantity = new Decimal(rrtAllocations[0].grossQuantity).divToInt(4).toNumber();
    totalNetQuantity = new Decimal(rrtAllocations[0].netQuantity).divToInt(4).toNumber();
    await request
      .patch(`/admin/portfolios/rrts/${rrtPortfolio.id}/assets`)
      .send({
        portfolioId: rrtPortfolio.id,
        memo: 'test transfer',
        assets: [
          {
            beZeroFactor: 1.43,
            grossQuantity: new Decimal(rrtAllocations[0].grossQuantity).divToInt(4).toNumber(),
            netQuantity: new Decimal(rrtAllocations[0].netQuantity).divToInt(4).toNumber(),
            projectVintageId: rrtAllocations[0].projectVintage.id,
          },
        ],
      })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.memo).toEqual('test transfer');
      });

    await request
      .get(`/admin/books/${rrtPortfolio.id}`)
      .query({ includeRelations: [BookRelations.OWNER_ALLOCATIONS_NESTED, BookRelations.ASSET_ALLOCATIONS_NESTED] })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.name).toEqual('RRT Portfolio');
        expect(res.body.type).toEqual(BookType.RRT_PUBLIC);
        expect(res.body.ownerAllocations.totalAmountAllocated).toEqual(totalGrossQuantity);
        expect(res.body.ownerAllocations.totalAmountAvailable).toEqual(totalGrossQuantity);
        expect(res.body.ownerAllocations.allocations.length).toEqual(1);
        expect(res.body.assetAllocations.totalAmountAllocated).toEqual(totalNetQuantity);
        expect(res.body.assetAllocations.totalAmountAvailable).toEqual(totalNetQuantity);
        expect(res.body.assetAllocations.allocations.length).toEqual(1);
      });

    await request.get(`/admin/portfolios/rrts/${rrtPortfolio.id}`).then((res) => {
      expect(res.status).toEqual(200);
      expect(res.body.assets.length).toEqual(1);
      expect(
        Decimal.sum(
          ...res.body.assets.map((m: AdminRrtVintageAssetResponseDTO) => m.portfolioNetPercentage),
        ).toNumber(),
      ).toEqual(1);
      expect(res.body.assets[0].portfolio.id).toEqual(rrtPortfolio.id);
      expect(res.body.assets[0].projectVintage.id).toEqual(defaultPortfolioAllocations[0].asset.id);
      expect(res.body.assets[0].beZeroFactor).toBeGreaterThanOrEqual(res.body.assets[0].calculatedFactor);
      rrtAllocations = res.body.assets;
    });

    totalGrossQuantity =
      new Decimal(defaultPortfolioAllocations[1].amountAvailable).divToInt(45).toNumber() +
      new Decimal(defaultPortfolioAllocations[2].amountAvailable).divToInt(27).toNumber() +
      new Decimal(defaultPortfolioAllocations[3].amountAvailable).toNumber();
    totalNetQuantity =
      new Decimal(defaultPortfolioAllocations[1].amountAvailable).divToInt(50).toNumber() +
      new Decimal(defaultPortfolioAllocations[2].amountAvailable).divToInt(30).toNumber() +
      new Decimal(defaultPortfolioAllocations[3].amountAvailable).toNumber();
    await request
      .patch(`/admin/portfolios/rrts/${rrtPortfolio.id}/assets`)
      .send({
        portfolioId: rrtPortfolio.id,
        memo: 'test transfer',
        assets: [
          {
            beZeroFactor: 1.35,
            grossQuantity: 0,
            netQuantity: 0,
            projectVintageId: rrtAllocations[0].projectVintage.id,
          },
          {
            beZeroFactor: 1.12,
            grossQuantity: new Decimal(defaultPortfolioAllocations[1].amountAvailable).divToInt(45).toNumber(),
            netQuantity: new Decimal(defaultPortfolioAllocations[1].amountAvailable).divToInt(50).toNumber(),
            projectVintageId: defaultPortfolioAllocations[1].asset.id,
          },
          {
            beZeroFactor: 1.15,
            grossQuantity: new Decimal(defaultPortfolioAllocations[2].amountAvailable).divToInt(27).toNumber(),
            netQuantity: new Decimal(defaultPortfolioAllocations[2].amountAvailable).divToInt(30).toNumber(),
            projectVintageId: defaultPortfolioAllocations[2].asset.id,
          },
          {
            beZeroFactor: 1.22,
            grossQuantity: new Decimal(defaultPortfolioAllocations[3].amountAvailable).toNumber(),
            netQuantity: new Decimal(defaultPortfolioAllocations[3].amountAvailable).toNumber(),
            projectVintageId: defaultPortfolioAllocations[3].asset.id,
          },
        ],
      })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.memo).toEqual('test transfer');
      });

    await request
      .get(`/admin/books/${rrtPortfolio.id}`)
      .query({ includeRelations: [BookRelations.OWNER_ALLOCATIONS_NESTED, BookRelations.ASSET_ALLOCATIONS_NESTED] })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.name).toEqual('RRT Portfolio');
        expect(res.body.type).toEqual(BookType.RRT_PUBLIC);
        expect(res.body.ownerAllocations.totalAmountAllocated).toEqual(totalGrossQuantity);
        expect(res.body.ownerAllocations.totalAmountAvailable).toEqual(totalGrossQuantity);
        expect(res.body.ownerAllocations.allocations.length).toEqual(3);
        expect(res.body.assetAllocations.totalAmountAllocated).toEqual(totalNetQuantity);
        expect(res.body.assetAllocations.totalAmountAvailable).toEqual(totalNetQuantity);
        expect(res.body.assetAllocations.allocations.length).toEqual(1);
      });

    await request.get(`/admin/portfolios/rrts/${rrtPortfolio.id}`).then((res) => {
      expect(res.status).toEqual(200);
      expect(res.body.assets.length).toEqual(4);
      expect(
        Decimal.sum(
          ...res.body.assets.map((m: AdminRrtVintageAssetResponseDTO) => m.portfolioNetPercentage),
        ).toNumber(),
      ).toEqual(1);
      for (const asset of res.body.assets) {
        expect(asset.portfolio.id).toEqual(rrtPortfolio.id);
        expect(asset.beZeroFactor).toBeGreaterThanOrEqual(asset.calculatedFactor);
      }
    });

    totalGrossQuantity +=
      new Decimal(defaultPortfolioAllocations[0].amountAvailable).divToInt(10).mul(1.43).ceil().toNumber() -
      new Decimal(defaultPortfolioAllocations[3].amountAvailable).toNumber() +
      new Decimal(defaultPortfolioAllocations[3].amountAvailable).divToInt(98).toNumber();
    totalNetQuantity +=
      new Decimal(defaultPortfolioAllocations[0].amountAvailable).divToInt(10).toNumber() -
      new Decimal(defaultPortfolioAllocations[3].amountAvailable).toNumber() +
      new Decimal(defaultPortfolioAllocations[3].amountAvailable).divToInt(100).toNumber();

    await request
      .patch(`/admin/portfolios/rrts/${rrtPortfolio.id}/assets`)
      .send({
        portfolioId: rrtPortfolio.id,
        memo: 'test transfer',
        assets: [
          {
            beZeroFactor: 1.43,
            grossQuantity: new Decimal(defaultPortfolioAllocations[0].amountAvailable)
              .divToInt(10)
              .mul(1.43)
              .ceil()
              .toNumber(),
            netQuantity: new Decimal(defaultPortfolioAllocations[0].amountAvailable).divToInt(10).toNumber(),
            projectVintageId: defaultPortfolioAllocations[0].asset.id,
          },
          {
            beZeroFactor: 1.22,
            grossQuantity: new Decimal(defaultPortfolioAllocations[3].amountAvailable).divToInt(98).toNumber(),
            netQuantity: new Decimal(defaultPortfolioAllocations[3].amountAvailable).divToInt(100).toNumber(),
            projectVintageId: defaultPortfolioAllocations[3].asset.id,
          },
        ],
      })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.memo).toEqual('test transfer');
      });

    await request
      .get(`/admin/books/${rrtPortfolio.id}`)
      .query({ includeRelations: [BookRelations.OWNER_ALLOCATIONS_NESTED, BookRelations.ASSET_ALLOCATIONS_NESTED] })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.name).toEqual('RRT Portfolio');
        expect(res.body.type).toEqual(BookType.RRT_PUBLIC);
        expect(res.body.ownerAllocations.totalAmountAllocated).toEqual(totalGrossQuantity);
        expect(res.body.ownerAllocations.totalAmountAvailable).toEqual(totalGrossQuantity);
        expect(res.body.ownerAllocations.allocations.length).toEqual(4);
        expect(res.body.assetAllocations.totalAmountAllocated).toEqual(totalNetQuantity);
        expect(res.body.assetAllocations.totalAmountAvailable).toEqual(totalNetQuantity);
        expect(res.body.assetAllocations.allocations.length).toEqual(1);
      });

    await request.get(`/admin/portfolios/rrts/${rrtPortfolio.id}`).then((res) => {
      expect(res.status).toEqual(200);
      expect(res.body.assets.length).toEqual(4);
      expect(
        Decimal.sum(
          ...res.body.assets.map((m: AdminRrtVintageAssetResponseDTO) => m.portfolioNetPercentage),
        ).toNumber(),
      ).toEqual(1);
      for (const asset of res.body.assets) {
        expect(asset.portfolio.id).toEqual(rrtPortfolio.id);
        expect(asset.beZeroFactor).toBeGreaterThanOrEqual(asset.calculatedFactor);
      }
    });
  });
});
