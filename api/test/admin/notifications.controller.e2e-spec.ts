/* third party */
import { TestingModule } from '@nestjs/testing';
import Decimal from 'decimal.js';
/* rubicon */
import {
  AssetType,
  BookType,
  NotificationCadence,
  NotificationEvent,
  NotificationEventSummary,
  OrganizationUserRole,
  PermissionEnum,
  RetirementType,
  uuid,
} from '@rubiconcarbon/shared-types';
/* app */
import { AdminNotificationBatchRequestDTO, AdminNotificationSubscriptionRequestDTO } from '@app/dtos/notification.dto';
import { AdminOrganizationResponseDTO } from '@app/dtos/organization.dto';
import { PortalUserActionCreateDTO } from '@app/dtos/user-actions.dto';
import { NotificationsService, SendgridService } from '@app/services';
/* test */
import { withoutConsole } from '../helpers/console.helper';
import { createOrganizationWithCustomerPortfolio } from '../helpers/organizations.helper';
import { createAndCompletePurchase } from '../helpers/transactions.helper';
import { createRubiconManager, createUser } from '../helpers/users.helper';
import { ApplicationTest, AuthInfo, Request } from '../setup/e2e';

describe('AdminNotificationsController (e2e)', () => {
  const appTest = new ApplicationTest();
  let request: Request;
  let auth: AuthInfo;
  const now = new Date();
  let adminUserId: uuid;
  let user1Id: uuid;
  let user2Id: uuid;
  let organization1: AdminOrganizationResponseDTO;
  let organization2: AdminOrganizationResponseDTO;
  let module: TestingModule;
  let notificationsService: NotificationsService;
  let sendgridService: SendgridService;

  beforeAll(async () => {
    ({ request, auth, module } = await appTest.setup());

    // create test users
    const actionBody: PortalUserActionCreateDTO = { version: 1, type: 'login', data: { testField: 'testVal' } };
    auth.permissions = [PermissionEnum.VERIFIED];
    user1Id = (await createUser(request, auth, '<EMAIL>')).id;
    await request.post('/user-actions').send(actionBody).expect(201);
    user2Id = (await createUser(request, auth, '<EMAIL>')).id;
    await request.post('/user-actions').send(actionBody).expect(201);

    // create admin user as rubicon manager
    adminUserId = (await createRubiconManager(request, auth, '<EMAIL>')).id;

    // set permissions
    auth.permissions = [
      PermissionEnum.ADMIN,
      PermissionEnum.NOTIFICATIONS_TRIGGER_PERIODIC,
      PermissionEnum.RETIREMENTS_READ,
      PermissionEnum.USER_ACTIONS_VIEW_CUSTOMER,
      PermissionEnum.PROJECTS_WRITE,
      PermissionEnum.BOOKS_CREATE_RCT_PORTFOLIOS,
      PermissionEnum.RETIREMENTS_CREATE,
      PermissionEnum.RETIREMENTS_UPDATE,
      PermissionEnum.ORGANIZATIONS_MANAGE_USERS,
    ];

    // create organizations to admin user
    organization1 = await createOrganizationWithCustomerPortfolio(request, auth, adminUserId, `Test Organization 1`);
    organization2 = await createOrganizationWithCustomerPortfolio(request, auth, adminUserId, `Test Organization 2`);

    // link users to org
    await withoutConsole(async () => {
      await request
        .put(`/admin/organizations/${organization1.id}/users/${user1Id}`)
        .send({ organizationUserRoles: [OrganizationUserRole.VIEWER] })
        .expect(200);
      await request
        .put(`/admin/organizations/${organization2.id}/users/${user2Id}`)
        .send({ organizationUserRoles: [OrganizationUserRole.VIEWER] })
        .expect(200);
    });

    notificationsService = module.get(NotificationsService);
    sendgridService = module.get(SendgridService);
  });

  afterAll(async () => await appTest.cleanup());

  test('admin/notifications/subscriptions (GET) none', async () => {
    await request.get(`/admin/notifications/subscriptions`).then((res) => {
      expect(res.status).toEqual(200);
      expect(res.body).toMatchObject({
        subscriptions: [],
        page: {
          limit: 0,
          offset: 0,
          size: 0,
        },
      });
    });
  });

  test('admin/notifications/subscriptions (PUT)', async () => {
    await request
      .put('/admin/notifications/subscriptions')
      .send({
        subscriptions: [
          { event: NotificationEvent.USER_CREATED, cadence: NotificationCadence.DAILY },
          { event: NotificationEvent.ACTIVITY_EVENT, cadence: NotificationCadence.DAILY },
          { event: NotificationEvent.BASKET_COMPOSITION_UPDATED, cadence: NotificationCadence.DAILY },
        ],
      } as AdminNotificationSubscriptionRequestDTO)
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body).toMatchObject({
          subscriptions: [
            { event: NotificationEvent.USER_CREATED, cadence: NotificationCadence.DAILY },
            { event: NotificationEvent.ACTIVITY_EVENT, cadence: NotificationCadence.DAILY },
            { event: NotificationEvent.BASKET_COMPOSITION_UPDATED, cadence: NotificationCadence.DAILY },
          ],
          page: {
            limit: 3,
            offset: 0,
            size: 3,
          },
        });
      });
  });

  test('admin/notifications/subscriptions (GET) some', async () => {
    await request.get(`/admin/notifications/subscriptions/`).then((res) => {
      expect(res.status).toEqual(200);
      expect(res.body).toMatchObject({
        subscriptions: [
          { event: NotificationEvent.USER_CREATED, cadence: NotificationCadence.DAILY },
          { event: NotificationEvent.ACTIVITY_EVENT, cadence: NotificationCadence.DAILY },
          { event: NotificationEvent.BASKET_COMPOSITION_UPDATED, cadence: NotificationCadence.DAILY },
        ],
        page: {
          limit: 3,
          offset: 0,
          size: 3,
        },
      });
    });
  });

  test('admin/notifications/subscriptions (PUT) update', async () => {
    await request
      .put('/admin/notifications/subscriptions')
      .send({
        subscriptions: [{ event: NotificationEvent.BASKET_COMPOSITION_UPDATED, cadence: NotificationCadence.DAILY }],
      } as AdminNotificationSubscriptionRequestDTO)
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body).toMatchObject({
          subscriptions: [{ event: NotificationEvent.BASKET_COMPOSITION_UPDATED, cadence: NotificationCadence.DAILY }],
          page: {
            limit: 1,
            offset: 0,
            size: 1,
          },
        });
      });
  });

  test('admin/notifications/batch', async () => {
    await request
      .post('/admin/notifications/batch')
      .send({
        eventSummaries: [
          NotificationEventSummary.BOOKS,
          NotificationEventSummary.ORG_USER,
          NotificationEventSummary.PROJECTS,
          NotificationEventSummary.PURCHASES,
          NotificationEventSummary.RETIREMENTS,
        ],
        cadence: NotificationCadence.WEEKLY,
      } as AdminNotificationBatchRequestDTO)
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body).toMatchObject({
          eventSummaries: [
            NotificationEventSummary.BOOKS,
            NotificationEventSummary.ORG_USER,
            NotificationEventSummary.PROJECTS,
            NotificationEventSummary.PURCHASES,
            NotificationEventSummary.RETIREMENTS,
          ],
          cadence: NotificationCadence.WEEKLY,
        });
      });
  });

  test('activity notifications test', async () => {
    // activity performed in setup
    // verify activity shows up when queried directly
    jest.spyOn(sendgridService, 'createAndSendEmailTemplate');
    await request
      .get('/admin/user-actions')
      .query({ since: now })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body).toHaveLength(2);
      });
    // verify activity email is sent
    await request
      .put('/admin/notifications/subscriptions')
      .send({
        subscriptions: [{ event: NotificationEvent.ACTIVITY_EVENT, cadence: NotificationCadence.DAILY }],
      } as AdminNotificationSubscriptionRequestDTO)
      .then((res) => {
        expect(res.status).toEqual(200);
      });
    await request
      .post('/admin/notifications/batch')
      .send({
        eventSummaries: [NotificationEventSummary.ACTIVITY],
        cadence: NotificationCadence.DAILY,
      })
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body.emailCount).toEqual(1);
        expect(sendgridService.createAndSendEmailTemplate).toHaveBeenCalledWith(
          organization1.customerPortfolio!.rubiconManager.email,
          '',
          '',
          expect.objectContaining({
            activitySummary: [
              {
                activeUsersCount: 1,
                orgName: organization1.name,
              },
              {
                activeUsersCount: 1,
                orgName: organization2.name,
              },
            ],
          }),
        );
      });
  });

  test('big batch notifications test', async () => {
    jest.spyOn(notificationsService, 'handleEvent');
    let handleCalls = 0;
    jest.spyOn(notificationsService, 'createBatchSendgridPayload');
    await request
      .put('/admin/notifications/subscriptions')
      .send({
        subscriptions: [
          { event: NotificationEvent.RETIREMENT_CREATED, cadence: NotificationCadence.DAILY },
          { event: NotificationEvent.PURCHASE_CREATED, cadence: NotificationCadence.DAILY },
          { event: NotificationEvent.ORGANIZATION_CREATED, cadence: NotificationCadence.DAILY },
          { event: NotificationEvent.PROJECT_CREATED, cadence: NotificationCadence.DAILY },
          { event: NotificationEvent.BOOK_CREATED, cadence: NotificationCadence.DAILY },
        ],
      } as AdminNotificationSubscriptionRequestDTO)
      .then((res) => {
        expect(res.status).toEqual(200);
      });

    await createAndCompletePurchase(request, auth, organization1.id, AssetType.RCT);
    expect(notificationsService.handleEvent).toHaveBeenCalledTimes((handleCalls += 5));

    await request
      .post('/admin/retirements')
      .send({
        assets: [
          {
            assetId: '3b50b813-c390-4935-b8a6-2d812509bb07',
            amount: 60,
            rawPrice: new Decimal(0),
          },
        ],
        assetType: AssetType.RCT,
        beneficiary: 'beneficiary',
        memo: 'memo',
        organizationId: organization1.id,
        isPublic: false,
        type: RetirementType.RETIREMENT,
      })
      .then((res) => {
        expect(res.status).toEqual(201);
      });
    expect(notificationsService.handleEvent).toHaveBeenCalledTimes((handleCalls += 1));
    await request
      .post('/admin/retirements')
      .send({
        assets: [
          {
            assetId: '3b50b813-c390-4935-b8a6-2d812509bb07',
            amount: 30,
            rawPrice: new Decimal(0),
          },
        ],
        assetType: AssetType.RCT,
        beneficiary: 'beneficiary',
        memo: 'memo',
        organizationId: organization1.id,
        isPublic: false,
        type: RetirementType.RETIREMENT,
      })
      .then((res) => {
        expect(res.status).toEqual(201);
      });
    expect(notificationsService.handleEvent).toHaveBeenCalledTimes((handleCalls += 1));

    await request
      .post('/admin/projects')
      .send({ name: 'Dragon Test Project', projectTypeId: 1, registryProjectId: 'DG123' })
      .then((res) => {
        expect(res.status).toEqual(201);
      });

    expect(notificationsService.handleEvent).toHaveBeenCalledTimes((handleCalls += 1));
    await request
      .post('/admin/books/rct-custom')
      .send({
        name: 'Test Custom Portfolio Book No Org',
        isEnabled: true,
        purchasePrice: '13.13',
      })
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body.name).toEqual('Test Custom Portfolio Book No Org');
        expect(res.body.isEnabled).toBeTruthy();
        expect(res.body.limit).toBeDefined();
        expect(res.body.purchasePrice).toEqual(13.13);
        expect(res.body.purchasePriceWithBuffer).toBeNull();
        expect(res.body.type).toEqual(BookType.RCT_CUSTOM);
      });
    expect(notificationsService.handleEvent).toHaveBeenCalledTimes((handleCalls += 1));

    await request
      .post('/admin/notifications/batch')
      .send({
        eventSummaries: [
          NotificationEventSummary.BOOKS,
          NotificationEventSummary.ORG_USER,
          NotificationEventSummary.PROJECTS,
          NotificationEventSummary.PURCHASES,
          NotificationEventSummary.RETIREMENTS,
        ],
        cadence: NotificationCadence.DAILY,
      })
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body).toEqual(
          expect.objectContaining({
            cadence: 'daily',
          }),
        );
        expect(notificationsService.createBatchSendgridPayload).toHaveBeenCalledWith(
          NotificationEventSummary.PURCHASES,
          NotificationCadence.DAILY,
          expect.anything(),
          expect.anything(),
          expect.anything(),
          expect.objectContaining({
            link: expect.anything(),
          }),
        );
        expect(notificationsService.createBatchSendgridPayload).toHaveBeenCalledWith(
          NotificationEventSummary.RETIREMENTS,
          NotificationCadence.DAILY,
          expect.anything(),
          expect.anything(),
          expect.anything(),
          expect.objectContaining({
            link: expect.anything(),
          }),
        );
        expect(notificationsService.createBatchSendgridPayload).toHaveBeenCalledWith(
          NotificationEventSummary.ORG_USER,
          NotificationCadence.DAILY,
          expect.anything(),
          expect.anything(),
          expect.anything(),
          expect.objectContaining({
            link: expect.anything(),
          }),
        );
        expect(notificationsService.createBatchSendgridPayload).toHaveBeenCalledWith(
          NotificationEventSummary.PROJECTS,
          NotificationCadence.DAILY,
          expect.anything(),
          expect.anything(),
          expect.anything(),
          expect.objectContaining({
            link: expect.anything(),
          }),
        );
        expect(notificationsService.createBatchSendgridPayload).toHaveBeenCalledWith(
          NotificationEventSummary.BOOKS,
          NotificationCadence.DAILY,
          expect.anything(),
          expect.anything(),
          expect.anything(),
          expect.objectContaining({
            link: expect.anything(),
          }),
        );
        expect(notificationsService.createBatchSendgridPayload).toHaveBeenCalledTimes(5);
      });
  });
});
