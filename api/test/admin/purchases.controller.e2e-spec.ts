/* third party */
import Decimal from 'decimal.js';
/* rubicon */
import {
  AssetType,
  BookRelations,
  DocumentType,
  PermissionEnum,
  PurchaseFlowType,
  PurchaseStatus,
  PurchaseUpdateStatus,
  uuid,
  zeroUUID,
} from '@rubiconcarbon/shared-types';
/* env */
import { environment } from '@env/environment';
/* app */
import { AdminPurchaseResponseDTO } from '@app/dtos/purchase.dto';
/* test */
import { createAndUploadDoc } from '../helpers/documents.helper';
import { createOrganizationWithCustomerPortfolio } from '../helpers/organizations.helper';
import { createUser } from '../helpers/users.helper';
import { ApplicationTest, AuthInfo, Request } from '../setup/e2e';
import { AdminOrganizationResponseDTO } from '@app/dtos/organization.dto';

describe('AdminPurchasesController (e2e)', () => {
  const appTest = new ApplicationTest();
  let request: Request;
  let auth: AuthInfo;

  let organization: AdminOrganizationResponseDTO;
  let canceledId: uuid;

  beforeAll(async () => {
    ({ request, auth } = await appTest.setup());

    // create test user
    await createUser(request, auth, '<EMAIL>');

    // create admin user
    await createUser(request, auth, '<EMAIL>');

    // create organiation
    organization = await createOrganizationWithCustomerPortfolio(request, auth);

    // set auth permissions and email
    auth.email = '<EMAIL>';
    auth.permissions = [
      PermissionEnum.BOOKS_READ,
      PermissionEnum.BOOKS_UPDATE,
      PermissionEnum.CUSTOMER_SALES_CANCEL,
      PermissionEnum.CUSTOMER_SALES_CREATE,
      PermissionEnum.CUSTOMER_SALES_READ,
      PermissionEnum.CUSTOMER_SALES_SET_BINDING,
      PermissionEnum.CUSTOMER_SALES_SET_DELIVERED,
      PermissionEnum.CUSTOMER_SALES_SET_EXECUTED,
      PermissionEnum.CUSTOMER_SALES_UPDATE_PAYMENT,
      PermissionEnum.RETIREMENTS_CREATE,
    ];
  });

  afterAll(async () => await appTest.cleanup());

  test('/admin/purchases (POST)', async () => {
    let rctsPendingPurchase = 0;
    let rctsAmountAllocated = 0;
    await request
      .get(`/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974`)
      .query({ includeRelations: [BookRelations.ASSET_ALLOCATIONS] })
      .then((res) => {
        rctsPendingPurchase = res.body.assetAllocations.totalAmountPendingPurchase;
        rctsAmountAllocated = res.body.assetAllocations.totalAmountAllocated;
      });

    await request
      .post('/admin/purchases')
      .send({}) // empty
      .then((res) => {
        expect(res.status).toEqual(400);
        expect((res.body.message as string[]).sort()).toEqual([
          'assetType must be one of the following values: rct, registry_vintage, rrt',
          'assets must be an array',
          'assets must contain at least 1 elements',
          'flowType must be one of the following values: purchase_to_retire, purchase_and_hold, transfer_to_customer_account',
          'needsRiskAdjustment must be a boolean value',
          'organizationId must be a UUID',
        ]);
      });

    await request
      .post('/admin/purchases')
      .send({
        organizationId: zeroUUID,
        assets: [
          {
            assetId: 'f4cf07b2-04ba-4ee9-94b3-d1f860fe8974',
            sourceId: zeroUUID,
            amount: 1000000,
            rawPrice: new Decimal(0),
          },
        ],
        assetType: AssetType.RCT,
        needsRiskAdjustment: true,
        flowType: PurchaseFlowType.PURCHASE_TO_RETIRE,
      })
      .then((res) => {
        expect(res.status).toEqual(422);
        expect(res.body.message).toEqual(`customer organizationId ${zeroUUID} must be a valid uuid`);
      });

    await request
      .post('/admin/purchases')
      .send({
        assets: [
          {
            assetId: zeroUUID,
            sourceId: uuid(environment.rubicon.books.portfolio.default),
            amount: 1,
            rawPrice: new Decimal(0),
          },
        ],
        assetType: AssetType.RCT,
        organizationId: organization.id,
        needsRiskAdjustment: true,
        paymentDueDate: new Date(),
        flowType: PurchaseFlowType.PURCHASE_TO_RETIRE,
      })
      .then((res) => {
        expect(res.status).toEqual(422);
        expect(res.body.message).toEqual(`could not find assets [${zeroUUID}]`);
      });

    await request
      .post('/admin/purchases')
      .send({
        organizationId: organization.id,
        assets: [
          {
            assetId: 'f4cf07b2-04ba-4ee9-94b3-d1f860fe8974',
            sourceId: uuid(environment.rubicon.books.portfolio.default),
            amount: 1500000,
            rawPrice: new Decimal(0),
          },
        ],
        assetType: AssetType.RCT,
        needsRiskAdjustment: true,
        paymentDueDate: new Date(),
        flowType: PurchaseFlowType.PURCHASE_TO_RETIRE,
      })
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body).toMatchObject({
          organization: { id: organization.id },
          assets: [{ asset: { id: 'f4cf07b2-04ba-4ee9-94b3-d1f860fe8974' }, amount: 1500000, rawPrice: 0 }],
          status: PurchaseStatus.FIRM,
          amount: 1500000,
          flowType: PurchaseFlowType.PURCHASE_TO_RETIRE,
          isPaid: false,
          isDelivered: false,
        });
      });

    await request
      .get(`/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974`)
      .query({ includeRelations: [BookRelations.ASSET_ALLOCATIONS] })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body.assetAllocations).toMatchObject({
          totalAmountPendingPurchase: (rctsPendingPurchase += 1500000),
          totalAmountAllocated: rctsAmountAllocated,
        });
      });

    // todo : fix this when fixing organization holdings
    // await request.get(`/admin/books/${organization.customerPortfolio!.id}/assets/receivables`).then((res) => {
    //   expect(res.status).toEqual(200);
    // });

    // there's only 500000 available on the book -- this will go over and fail
    await request
      .post('/admin/purchases')
      .send({
        organizationId: organization.id,
        assets: [
          {
            assetId: 'f4cf07b2-04ba-4ee9-94b3-d1f860fe8974',
            sourceId: uuid(environment.rubicon.books.portfolio.default),
            amount: 1000000,
            rawPrice: new Decimal(0),
          },
        ],
        assetType: AssetType.RCT,
        flowType: PurchaseFlowType.PURCHASE_TO_RETIRE,
        needsRiskAdjustment: false,
        paymentDueDate: new Date(),
      })
      .then((res) => {
        expect(res.status).toEqual(422);
        expect(res.body.message).toEqual(
          `Book ${environment.rubicon.books.portfolio.default} must have amountAvaliable greater than or equal to 1000000 for asset f4cf07b2-04ba-4ee9-94b3-d1f860fe8974`,
        );
      });

    // idk if this is a real use case anymore
    // make inactive
    // await request.patch(`/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974`).send({ isEnabled: false }).expect(200);

    // await request
    //   .post('/admin/purchases')
    //   .send({
    //     organizationId: organizationId,
    //     assets: [
    //       {
    //         assetId: 'f4cf07b2-04ba-4ee9-94b3-d1f860fe8974',
    //         sourceId: uuid(environment.rubicon.books.portfolio.default),
    //         amount: 1,
    //         rawPrice: new Decimal(0),
    //       },
    //     ],
    //     assetType: AssetType.RCT,
    //     needsRiskAdjustment: true,
    //     paymentDueDate: new Date(),
    //     flowType: PurchaseFlowType.PURCHASE_TO_RETIRE,
    //   })
    //   .then((res) => {
    //     expect(res.status).toEqual(422);
    //     expect(res.body.message).toEqual(`Book Nature-Based Emissions Reductions RCT must be enabled`);
    //   });

    // await request.patch(`/admin/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974`).send({ isEnabled: true }).expect(200);
  });

  test('/admin/purchases/:id/cancel (PATCH)', async () => {
    let purchaseId: uuid;

    let rctsPendingPurchase = 0;
    let rctsAmountAllocated = 0;
    await request
      .get(`/admin/books/3b50b813-c390-4935-b8a6-2d812509bb07`)
      .query({ includeRelations: [BookRelations.ASSET_ALLOCATIONS] })
      .then((res) => {
        rctsPendingPurchase = res.body.assetAllocations.totalAmountPendingPurchase;
        rctsAmountAllocated = res.body.assetAllocations.totalAmountAllocated;
      });

    await request
      .post('/admin/purchases')
      .send({
        organizationId: organization.id,
        assets: [
          {
            assetId: '3b50b813-c390-4935-b8a6-2d812509bb07',
            sourceId: uuid(environment.rubicon.books.portfolio.default),
            amount: 100,
            rawPrice: new Decimal(0),
          },
        ],
        assetType: AssetType.RCT,
        needsRiskAdjustment: true,
        paymentDueDate: new Date(),
        flowType: PurchaseFlowType.PURCHASE_TO_RETIRE,
      })
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body).toMatchObject({
          organization: { id: organization.id },
          assets: [{ asset: { id: '3b50b813-c390-4935-b8a6-2d812509bb07' }, amount: 100, rawPrice: 0 }],
          amount: 100,
          isPaid: false,
          isDelivered: false,
        });
        purchaseId = (res.body as AdminPurchaseResponseDTO).id;
      });

    await request
      .get(`/admin/books/3b50b813-c390-4935-b8a6-2d812509bb07`)
      .query({ includeRelations: [BookRelations.ASSET_ALLOCATIONS] })
      .then((res) => {
        expect(res.body.assetAllocations).toMatchObject({
          totalAmountPendingPurchase: (rctsPendingPurchase += 100),
          totalAmountAllocated: rctsAmountAllocated,
        });
      });

    await request
      .patch(`/admin/purchases/${zeroUUID}/${PurchaseUpdateStatus.CANCEL}`)
      .send({})
      .then((res) => {
        expect(res.status).toEqual(404);
        expect(res.body.message).toEqual('Purchase not found');
      });

    await request
      .patch(`/admin/purchases/${purchaseId!}/${PurchaseUpdateStatus.CANCEL}`)
      .send({})
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body as AdminPurchaseResponseDTO).toMatchObject({
          id: purchaseId,
          organization: { id: organization.id },
          amount: 100,
          status: PurchaseStatus.CANCELED,
        });
      });

    await request
      .get(`/admin/books/3b50b813-c390-4935-b8a6-2d812509bb07`)
      .query({ includeRelations: [BookRelations.ASSET_ALLOCATIONS] })
      .then((res) => {
        expect(res.body.assetAllocations).toMatchObject({
          totalAmountPendingPurchase: (rctsPendingPurchase -= 100),
          totalAmountAllocated: rctsAmountAllocated,
        });
      });

    await request // the second time around fails
      .patch(`/admin/purchases/${purchaseId!}/${PurchaseUpdateStatus.CANCEL}`)
      .send({})
      .then((res) => {
        expect(res.status).toEqual(422);
        expect(res.body.message).toEqual('Purchase already canceled');
      });

    canceledId = purchaseId!;
  });

  test('/admin/purchases/:id/bind (PATCH)', async () => {
    let purchase: AdminPurchaseResponseDTO;
    let rctsPendingPurchase = 0;
    let rctsAmountAllocated = 0;
    await request
      .get(`/admin/books/3b50b813-c390-4935-b8a6-2d812509bb07`)
      .query({ includeRelations: [BookRelations.ASSET_ALLOCATIONS] })
      .then((res) => {
        rctsPendingPurchase = res.body.assetAllocations.totalAmountPendingPurchase;
        rctsAmountAllocated = res.body.assetAllocations.totalAmountAllocated;
      });

    await request
      .post('/admin/purchases')
      .send({
        organizationId: organization.id,
        assets: [
          {
            assetId: '3b50b813-c390-4935-b8a6-2d812509bb07',
            sourceId: uuid(environment.rubicon.books.portfolio.default),
            amount: 100,
            rawPrice: new Decimal(0),
          },
        ],
        assetType: AssetType.RCT,
        needsRiskAdjustment: true,
        flowType: PurchaseFlowType.PURCHASE_TO_RETIRE,
      })
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body).toMatchObject({
          organization: { id: organization.id },
          assets: [{ asset: { id: '3b50b813-c390-4935-b8a6-2d812509bb07' }, amount: 100, rawPrice: 0 }],
          amount: 100,
          isPaid: false,
          isDelivered: false,
        });
        purchase = res.body as AdminPurchaseResponseDTO;
      });

    await request
      .get(`/admin/books/3b50b813-c390-4935-b8a6-2d812509bb07`)
      .query({ includeRelations: [BookRelations.ASSET_ALLOCATIONS] })
      .then((res) => {
        expect(res.body.assetAllocations).toMatchObject({
          totalAmountPendingPurchase: (rctsPendingPurchase += 100),
          totalAmountAllocated: rctsAmountAllocated,
        });
      });

    await request.patch(`/admin/purchases/${purchase!.id}/${PurchaseUpdateStatus.BIND}`).then((res) => {
      expect(res.status).toEqual(422);
      expect(res.body.message).toEqual(
        `Purchase must have a document with type ${DocumentType.PROOF_OF_CONFIRMATION} uploaded before moving to status ${PurchaseStatus.BINDING}`,
      );
    });

    await createAndUploadDoc(request, auth, DocumentType.PROOF_OF_CONFIRMATION, organization.id, purchase!.uiKey);
    await request.patch(`/admin/purchases/${purchase!.id}/${PurchaseUpdateStatus.BIND}`).then((res) => {
      expect(res.status).toEqual(200);
      expect(res.body).toMatchObject({
        id: purchase!.id,
        status: PurchaseStatus.BINDING,
      });
    });

    await request
      .patch(`/admin/purchases/${zeroUUID}/${PurchaseUpdateStatus.BIND}`)
      .send({})
      .then((res) => {
        expect(res.status).toEqual(404);
        expect(res.body.message).toEqual('Purchase not found');
      });

    await request
      .patch(`/admin/purchases/${canceledId!}/${PurchaseUpdateStatus.BIND}`)
      .send({})
      .then((res) => {
        expect(res.status).toEqual(422);
        expect(res.body.message).toEqual(`Purchase must have status ${PurchaseStatus.FIRM}`);
      });
  });

  test('/admin/purchases/:id/execute (PATCH)', async () => {
    let purchase: AdminPurchaseResponseDTO;
    let rctsPendingPurchase = 0;
    let rctsAmountAllocated = 0;
    await request
      .get(`/admin/books/3b50b813-c390-4935-b8a6-2d812509bb07`)
      .query({ includeRelations: [BookRelations.ASSET_ALLOCATIONS] })
      .then((res) => {
        rctsPendingPurchase = res.body.assetAllocations.totalAmountPendingPurchase;
        rctsAmountAllocated = res.body.assetAllocations.totalAmountAllocated;
      });

    await request
      .post('/admin/purchases')
      .send({
        organizationId: organization.id,
        assets: [
          {
            assetId: '3b50b813-c390-4935-b8a6-2d812509bb07',
            sourceId: uuid(environment.rubicon.books.portfolio.default),
            amount: 100,
            rawPrice: new Decimal(0),
          },
        ],
        assetType: AssetType.RCT,
        needsRiskAdjustment: true,
        paymentDueDate: new Date(),
        flowType: PurchaseFlowType.PURCHASE_TO_RETIRE,
      })
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body).toMatchObject({
          organization: { id: organization.id },
          assets: [{ asset: { id: '3b50b813-c390-4935-b8a6-2d812509bb07' }, amount: 100, rawPrice: 0 }],
          amount: 100,
          isPaid: false,
          isDelivered: false,
        });
        purchase = res.body as AdminPurchaseResponseDTO;
      });

    await request
      .get(`/admin/books/3b50b813-c390-4935-b8a6-2d812509bb07`)
      .query({ includeRelations: [BookRelations.ASSET_ALLOCATIONS] })
      .then((res) => {
        expect(res.body.assetAllocations).toMatchObject({
          totalAmountPendingPurchase: (rctsPendingPurchase += 100),
          totalAmountAllocated: rctsAmountAllocated,
        });
      });
    await createAndUploadDoc(request, auth, DocumentType.PROOF_OF_CONFIRMATION, organization.id, purchase!.uiKey);
    await request.patch(`/admin/purchases/${purchase!.id}/${PurchaseUpdateStatus.BIND}`).then((res) => {
      expect(res.status).toEqual(200);
    });

    await request
      .patch(`/admin/purchases/${purchase!.id}/${PurchaseUpdateStatus.EXECUTE}`)
      .send({ updatableStatusOrder: ['pending_payment', 'pending_delivery'] })
      .then((res) => {
        expect(res.status).toEqual(422);
        expect(res.body.message).toEqual(
          `Purchase must have a document with type ${DocumentType.CONTRACT} or ${DocumentType.PURCHASE_AGREEMENT} uploaded before moving to status ${PurchaseStatus.EXECUTED}`,
        );
      });

    await createAndUploadDoc(request, auth, DocumentType.CONTRACT, organization.id, purchase!.uiKey);
    await request
      .patch(`/admin/purchases/${purchase!.id}/${PurchaseUpdateStatus.EXECUTE}`)
      .send({ updatableStatusOrder: ['pending_payment', 'pending_delivery'] })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body).toMatchObject({
          id: purchase!.id,
          status: PurchaseStatus.EXECUTED,
        });
      });

    await request
      .patch(`/admin/purchases/${zeroUUID}/${PurchaseUpdateStatus.EXECUTE}`)
      .send({ updatableStatusOrder: ['pending_payment', 'pending_delivery'] })
      .then((res) => {
        expect(res.status).toEqual(404);
        expect(res.body.message).toEqual('Purchase not found');
      });

    await request
      .patch(`/admin/purchases/${canceledId!}/${PurchaseUpdateStatus.EXECUTE}`)
      .send({ updatableStatusOrder: ['pending_payment', 'pending_delivery'] })
      .then((res) => {
        expect(res.status).toEqual(422);
        expect(res.body.message).toEqual(`Purchase ${canceledId} must have status ${PurchaseStatus.BINDING}`);
      });
  });

  test('/admin/purchases/:id/pay (PATCH)', async () => {
    let purchase: AdminPurchaseResponseDTO;
    let rctsPendingPurchase = 0;
    let rctsAmountAllocated = 0;
    await request
      .get(`/admin/books/3b50b813-c390-4935-b8a6-2d812509bb07`)
      .query({ includeRelations: [BookRelations.ASSET_ALLOCATIONS] })
      .then((res) => {
        rctsPendingPurchase = res.body.assetAllocations.totalAmountPendingPurchase;
        rctsAmountAllocated = res.body.assetAllocations.totalAmountAllocated;
      });

    await request
      .post('/admin/purchases')
      .send({
        organizationId: organization.id,
        assets: [
          {
            assetId: '3b50b813-c390-4935-b8a6-2d812509bb07',
            sourceId: uuid(environment.rubicon.books.portfolio.default),
            amount: 100,
            rawPrice: new Decimal(0),
          },
        ],
        assetType: AssetType.RCT,
        needsRiskAdjustment: true,
        paymentDueDate: new Date(),
        flowType: PurchaseFlowType.PURCHASE_TO_RETIRE,
      })
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body).toMatchObject({
          organization: { id: organization.id },
          assets: [
            { asset: { id: '3b50b813-c390-4935-b8a6-2d812509bb07', type: AssetType.RCT }, amount: 100, rawPrice: 0 },
          ],
          amount: 100,
          isPaid: false,
          isDelivered: false,
        });
        purchase = res.body as AdminPurchaseResponseDTO;
      });

    await request
      .get(`/admin/books/3b50b813-c390-4935-b8a6-2d812509bb07`)
      .query({ includeRelations: [BookRelations.ASSET_ALLOCATIONS] })
      .then((res) => {
        expect(res.body.assetAllocations).toMatchObject({
          totalAmountPendingPurchase: (rctsPendingPurchase += 100),
          totalAmountAllocated: rctsAmountAllocated,
        });
      });
    await createAndUploadDoc(request, auth, DocumentType.PROOF_OF_CONFIRMATION, organization.id, purchase!.uiKey);
    await request.patch(`/admin/purchases/${purchase!.id}/${PurchaseUpdateStatus.BIND}`).then((res) => {
      expect(res.status).toEqual(200);
    });
    await createAndUploadDoc(request, auth, DocumentType.CONTRACT, organization.id, purchase!.uiKey);
    await request
      .patch(`/admin/purchases/${purchase!.id}/${PurchaseUpdateStatus.EXECUTE}`)
      .send({ updatableStatusOrder: ['pending_payment', 'pending_delivery'] })
      .then((res) => {
        expect(res.status).toEqual(200);
      });

    await request.patch(`/admin/purchases/${purchase!.id}/${PurchaseUpdateStatus.PAY}`).then((res) => {
      expect(res.status).toEqual(200);
      expect(res.body).toMatchObject({
        id: purchase!.id,
        status: PurchaseStatus.EXECUTED,
        isPaid: true,
        isDelivered: false,
      });
    });

    await request
      .patch(`/admin/purchases/${zeroUUID}/${PurchaseUpdateStatus.PAY}`)
      .send({})
      .then((res) => {
        expect(res.status).toEqual(404);
        expect(res.body.message).toEqual('Purchase not found');
      });

    await request
      .patch(`/admin/purchases/${canceledId!}/${PurchaseUpdateStatus.PAY}`)
      .send({})
      .then((res) => {
        expect(res.status).toEqual(422);
        expect(res.body.message).toEqual(`Purchase ${canceledId} must have status ${PurchaseStatus.EXECUTED}`);
      });
  });

  test('/admin/purchases/:id/deliver (PATCH)', async () => {
    let purchase: AdminPurchaseResponseDTO;
    let rctsPendingPurchase = 0;
    let rctsAmountAllocated = 0;
    await request
      .get(`/admin/books/3b50b813-c390-4935-b8a6-2d812509bb07`)
      .query({ includeRelations: [BookRelations.ASSET_ALLOCATIONS] })
      .then((res) => {
        rctsPendingPurchase = res.body.assetAllocations.totalAmountPendingPurchase;
        rctsAmountAllocated = res.body.assetAllocations.totalAmountAllocated;
      });

    await request
      .post('/admin/purchases')
      .send({
        organizationId: organization.id,
        assets: [
          {
            assetId: '3b50b813-c390-4935-b8a6-2d812509bb07',
            sourceId: uuid(environment.rubicon.books.portfolio.default),
            amount: 100,
            rawPrice: new Decimal(0),
          },
        ],
        assetType: AssetType.RCT,
        needsRiskAdjustment: true,
        paymentDueDate: new Date(),
        flowType: PurchaseFlowType.PURCHASE_TO_RETIRE,
      })
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body).toMatchObject({
          organization: { id: organization.id },
          assets: [{ asset: { id: '3b50b813-c390-4935-b8a6-2d812509bb07' }, amount: 100, rawPrice: 0 }],
          amount: 100,
          isPaid: false,
          isDelivered: false,
        });
        purchase = res.body as AdminPurchaseResponseDTO;
      });

    await request
      .get(`/admin/books/3b50b813-c390-4935-b8a6-2d812509bb07`)
      .query({ includeRelations: [BookRelations.ASSET_ALLOCATIONS] })
      .then((res) => {
        expect(res.body.assetAllocations).toMatchObject({
          totalAmountPendingPurchase: (rctsPendingPurchase += 100),
          totalAmountAllocated: rctsAmountAllocated,
        });
      });
    await createAndUploadDoc(request, auth, DocumentType.PROOF_OF_CONFIRMATION, organization.id, purchase!.uiKey);
    await request.patch(`/admin/purchases/${purchase!.id}/${PurchaseUpdateStatus.BIND}`).then((res) => {
      expect(res.status).toEqual(200);
    });
    await createAndUploadDoc(request, auth, DocumentType.CONTRACT, organization.id, purchase!.uiKey);
    await request
      .patch(`/admin/purchases/${purchase!.id}/${PurchaseUpdateStatus.EXECUTE}`)
      .send({ updatableStatusOrder: ['pending_payment', 'pending_delivery'] })
      .then((res) => {
        expect(res.status).toEqual(200);
      });

    await request
      .patch(`/admin/purchases/${purchase!.id}/${PurchaseUpdateStatus.DELIVER}`)
      .send({ assetsDeliveredAt: new Date() })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body).toMatchObject({
          id: purchase!.id,
          status: PurchaseStatus.EXECUTED,
          isPaid: false,
          isDelivered: true,
        });
      });

    await request
      .patch(`/admin/purchases/${zeroUUID}/${PurchaseUpdateStatus.DELIVER}`)
      .send({ assetsDeliveredAt: new Date() })
      .then((res) => {
        expect(res.status).toEqual(404);
        expect(res.body.message).toEqual('Purchase not found');
      });

    await request
      .patch(`/admin/purchases/${canceledId!}/${PurchaseUpdateStatus.DELIVER}`)
      .send({ assetsDeliveredAt: new Date() })
      .then((res) => {
        expect(res.status).toEqual(422);
        expect(res.body.message).toEqual(`Purchase ${canceledId} must have status ${PurchaseStatus.EXECUTED}`);
      });
  });

  test('settling purchase', async () => {
    let purchase: AdminPurchaseResponseDTO;
    let rctsPendingPurchase = 0;
    let rctsAmountAllocated = 0;
    await request
      .get(`/admin/books/3b50b813-c390-4935-b8a6-2d812509bb07`)
      .query({ includeRelations: [BookRelations.ASSET_ALLOCATIONS] })
      .then((res) => {
        rctsPendingPurchase = res.body.assetAllocations.totalAmountPendingPurchase;
        rctsAmountAllocated = res.body.assetAllocations.totalAmountAllocated;
      });

    await request
      .post('/admin/purchases')
      .send({
        organizationId: organization.id,
        assets: [
          {
            assetId: '3b50b813-c390-4935-b8a6-2d812509bb07',
            sourceId: uuid(environment.rubicon.books.portfolio.default),
            amount: 100,
            rawPrice: new Decimal(0),
          },
        ],
        assetType: AssetType.RCT,
        needsRiskAdjustment: true,
        paymentDueDate: new Date(),
        flowType: PurchaseFlowType.PURCHASE_TO_RETIRE,
      })
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body).toMatchObject({
          organization: { id: organization.id },
          assets: [{ asset: { id: '3b50b813-c390-4935-b8a6-2d812509bb07' }, amount: 100, rawPrice: 0 }],
          amount: 100,
          isPaid: false,
          isDelivered: false,
        });
        purchase = res.body as AdminPurchaseResponseDTO;
      });

    await request
      .get(`/admin/books/3b50b813-c390-4935-b8a6-2d812509bb07`)
      .query({ includeRelations: [BookRelations.ASSET_ALLOCATIONS] })
      .then((res) => {
        expect(res.body.assetAllocations).toMatchObject({
          totalAmountPendingPurchase: (rctsPendingPurchase += 100),
          totalAmountAllocated: rctsAmountAllocated,
        });
      });
    await createAndUploadDoc(request, auth, DocumentType.PROOF_OF_CONFIRMATION, organization.id, purchase!.uiKey);
    await request.patch(`/admin/purchases/${purchase!.id}/${PurchaseUpdateStatus.BIND}`).then((res) => {
      expect(res.status).toEqual(200);
    });
    await createAndUploadDoc(request, auth, DocumentType.CONTRACT, organization.id, purchase!.uiKey);
    await request
      .patch(`/admin/purchases/${purchase!.id}/${PurchaseUpdateStatus.EXECUTE}`)
      .send({ updatableStatusOrder: ['pending_payment', 'pending_delivery'] })
      .then((res) => {
        expect(res.status).toEqual(200);
      });

    await request
      .patch(`/admin/purchases/${purchase!.id}/${PurchaseUpdateStatus.DELIVER}`)
      .send({ assetsDeliveredAt: new Date() })
      .then((res) => {
        expect(res.status).toEqual(200);
        expect(res.body).toMatchObject({
          id: purchase!.id,
          status: PurchaseStatus.EXECUTED,
          isPaid: false,
          isDelivered: true,
        });
      });

    await request.patch(`/admin/purchases/${purchase!.id}/${PurchaseUpdateStatus.CANCEL}`).then((res) => {
      expect(res.status).toEqual(422);
      expect(res.body.message).toEqual('Purchase already delivered and cannot be canceled');
    });

    await request.patch(`/admin/purchases/${purchase!.id}/${PurchaseUpdateStatus.PAY}`).then((res) => {
      expect(res.status).toEqual(200);
      expect(res.body).toMatchObject({
        id: purchase!.id,
        status: PurchaseStatus.SETTLED,
        isPaid: true,
        isDelivered: true,
      });
    });

    await request
      .patch(`/admin/purchases/${purchase!.id}/${PurchaseUpdateStatus.CANCEL}`)
      .send({})
      .then((res) => {
        expect(res.status).toEqual(422);
        expect(res.body.message).toEqual('Purchase already delivered and cannot be canceled');
      });
  });
});
