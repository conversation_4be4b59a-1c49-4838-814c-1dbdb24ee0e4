/* rubicon */
import { BookType, PermissionEnum, zeroUUID } from '@rubiconcarbon/shared-types';
/* app */
import { PortalBookResponseDTO } from '@app/dtos/book.dto';
/* test */
import { ApplicationTest, AuthInfo, Request } from './setup/e2e';
import { createUser } from './helpers/users.helper';

describe('BookController (e2e)', () => {
  const appTest = new ApplicationTest();
  let request: Request;
  let auth: AuthInfo;

  beforeAll(async () => {
    ({ request, auth } = await appTest.setup());
    await createUser(request, auth, `<EMAIL>`);
  });
  afterAll(async () => await appTest.cleanup());

  test('/books (GET) verified', async () => {
    auth.permissions = [PermissionEnum.VERIFIED];
    await request.get('/books').then((res) => {
      expect(res.status).toEqual(200);
      expect(res.body.data as PortalBookResponseDTO[]).toHaveLength(4);
      const books = res.body.data.sort() as PortalBookResponseDTO[];
      expect(books).toHaveLength(4);
      const book = books.find((x) => x.id == 'f4cf07b2-04ba-4ee9-94b3-d1f860fe8974');
      expect(book?.purchasePrice).toBeDefined();
      expect(book?.purchasePriceWithBuffer).toBeDefined();
    });
  });

  test('/books (GET) login', async () => {
    auth.permissions = [PermissionEnum.LOGIN];
    await request.get('/books').then((res) => {
      expect(res.status).toEqual(200);
      expect(res.body.data as PortalBookResponseDTO[]).toHaveLength(4);
      const books = res.body.data as PortalBookResponseDTO[];
      expect(books).toHaveLength(4);
      expect(books[0].purchasePrice).toBeNull();
    });
  });

  test('/books (GET) public', async () => {
    auth.permissions = [];
    await request.get('/books').then((res) => {
      expect(res.status).toEqual(200);
      expect(res.body.data as PortalBookResponseDTO[]).toHaveLength(4);
      const books = res.body.data as PortalBookResponseDTO[];
      expect(books).toHaveLength(4);
      expect(books[0].purchasePrice).toBeNull();
      expect(books[0].purchasePriceWithBuffer).toBeNull();
    });
  });

  test('/books/:id (GET) non-admin', async () => {
    auth.permissions = [PermissionEnum.LOGIN, PermissionEnum.VERIFIED];
    await request.get('/books/0a22034b-0ba2-4eec-a8d6-c9bbc6429ba1').then((res) => {
      expect(res.status).toEqual(200);
      const book = res.body as PortalBookResponseDTO;
      expect(book.id).toEqual('0a22034b-0ba2-4eec-a8d6-c9bbc6429ba1');
      expect(book.name).toEqual('Carbon Removal RCT');
    });

    await request.get('/books/f4cf07b2-04ba-4ee9-94b3-d1f860fe8974').then((res) => {
      expect(res.status).toEqual(200);
      const book = res.body as PortalBookResponseDTO;
      expect(book.id).toEqual('f4cf07b2-04ba-4ee9-94b3-d1f860fe8974');
      expect(book.purchasePrice).toBeDefined();
      expect(book.purchasePriceWithBuffer).toBeDefined();
    });
  });

  test('/books/:id (GET) non-existent', async () => {
    auth.permissions = [PermissionEnum.LOGIN, PermissionEnum.VERIFIED];
    await request.get(`/books/${zeroUUID}`).then((res) => {
      expect(res.status).toEqual(404);
    });
  });
});
