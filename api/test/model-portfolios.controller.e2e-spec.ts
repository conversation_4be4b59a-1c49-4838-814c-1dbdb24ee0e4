/* rubicon */
import { OrganizationUserRole, PermissionEnum, uuid } from '@rubiconcarbon/shared-types';
/* app */
import { AdminModelPortfolioCreateRequestDTO, AdminModelPortfolioResponseDTO } from '@app/dtos/model-portfolio.dto';
/* test */
import { createOrganizationWithCustomerPortfolio, setUserToOrganization } from './helpers/organizations.helper';
import { createUser } from './helpers/users.helper';
import { ApplicationTest, AuthInfo, Request } from './setup/e2e';

describe('ModelPortfoliosController (e2e)', () => {
  const appTest = new ApplicationTest();
  let request: Request;
  let auth: AuthInfo;

  const user1email = `<EMAIL>`;
  const user2email = `<EMAIL>`;
  let user2id = uuid();
  let user2ModelPortfolioId = uuid();
  let orgId: uuid;

  beforeAll(async () => {
    ({ request, auth } = await appTest.setup());

    // create users
    await createUser(request, auth, user1email);
    user2id = (await createUser(request, auth, user2email)).id;

    // create organization and set user2 to org
    orgId = (await createOrganizationWithCustomerPortfolio(request, auth)).id;
    await setUserToOrganization(request, auth, orgId, user2id, [OrganizationUserRole.TRANSACTOR]);

    // set auth permissions
    auth.permissions = [
      PermissionEnum.MODEL_PORTFOLIOS_COMPONENTS_WRITE,
      PermissionEnum.MODEL_PORTFOLIOS_READ,
      PermissionEnum.MODEL_PORTFOLIOS_CREATE,
      PermissionEnum.MODEL_PORTFOLIOS_DELETE,
      PermissionEnum.MODEL_PORTFOLIOS_UPDATE,
      PermissionEnum.MODEL_PORTFOLIOS_PRICE,
      PermissionEnum.VINTAGES_READ,
      PermissionEnum.VERIFIED,
    ];
  });
  afterAll(async () => await appTest.cleanup());

  test('/model-portfolios (GET)', async () => {
    const payload: AdminModelPortfolioCreateRequestDTO = {
      name: 'ModelPortfolio 2',
      showCustomer: true,
      organizationId: orgId,
      rfp: false,
      includeRiskAdjustment: false,
    };
    await request
      .post('/admin/model-portfolios')
      .send(payload)
      .then((res) => {
        expect(res.status).toEqual(201);
        expect(res.body as AdminModelPortfolioResponseDTO).toMatchObject({
          updatedBy: { email: '<EMAIL>' },
          organization: { id: orgId },
          showCustomer: true,
        });
        expect(res.body.uiKey).toBeDefined();
        user2ModelPortfolioId = res.body.id;
      });
    auth.email = user1email;
    const start = new Date();
    const end = new Date(start.getTime() + 1);
    await request
      .patch(`/admin/model-portfolios/${user2ModelPortfolioId}/components`)
      .send({
        create: [
          {
            amountAllocated: 10,
            bufferPercentage: '12.33',
            costBasis: '2.54',
            isBufferComponent: false,
            portfolioManagerEstimate: '13.13',
            registryProjectId: 'PNGN123',
            vintageInterval: `${start.toISOString()} - ${end.toISOString()}`,
            projectId: 'd4aa9b81-75dd-421c-a552-f43e356ccca7',
          },
        ],
      })
      .then((res) => {
        expect(res.status).toEqual(200);
      });
    auth.email = user2email;

    await request.get('/model-portfolios').then((res) => {
      expect(res.status).toEqual(200);
      expect(res.body.data.length).toEqual(1);
      expect(res.body.data[0].uiKey).toBeDefined();
    });

    // todo : (TD-76) fix this after amounts are fixed <-- amounts are fixed but need more context of model portfolios
    // await request.get(`/model-portfolios/${user2ModelPortfolioId}`).then((res) => {
    //   expect(res.status).toEqual(200);
    //   expect((res.body as ModelPortfolioResponseDTO).modelPortfolioComponents).toHaveLength(1);
    // });
  });

  test('/model-portfolios/generate-name (GET)', async () => {
    await request.get('/model-portfolios/generate-name').then((res) => {
      expect(res.status).toEqual(200);
      expect(res.text).toEqual('Test Organization #3');
    });
  });
});
