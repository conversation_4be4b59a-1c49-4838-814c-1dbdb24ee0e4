import { Migration } from '@mikro-orm/migrations';
import { environment } from '@env/environment';

export class Migration20250720202020 extends Migration {
  async up(): Promise<void> {
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".project_vintage_groupings_v2`);
    // project_vintage_groupings_v2
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".project_vintage_groupings_v2
        AS
        SELECT
                CASE
                    WHEN p.buffer_category_id IS NOT NULL THEN pbc.id
                    ELSE ptbc.id
                END AS buffer_category_id,
                CASE
                    WHEN p.buffer_category_id IS NOT NULL THEN pbc.name
                    ELSE ptbc.name
                END AS buffer_category_name,
            pt.id AS project_type_id,
            pt.type AS project_type_type,
            pt.category AS project_type_category,
            p.id AS project_id,
            c.alpha3 AS project_country_alpha3,
            c.name AS project_country_name,
            p.name AS project_name,
            p.registry_project_id,
            p.is_science_team_approved AS project_is_science_team_approved,
            p.is_byorct_approved AS project_is_byorct_approved,
            p.suspended AS project_is_suspended,
            p.rct_standard AS project_is_rct_standard,
            p.integrity_grade_score AS project_integrity_grade_score,
            p.integrity_grade_score_risk_adjusted AS project_integrity_grade_score_risk_adjusted,
            pv.id AS project_vintage_id,
            pv.label AS project_vintage_label,
            pv."interval" AS project_vintage_interval,
            pv.risk_buffer_percentage AS project_vintage_risk_buffer_percentage,
            pv.low_buffer_percentage AS project_vintage_low_buffer_percentage,
            pv.high_buffer_percentage AS project_vintage_high_buffer_percentage,
            case when p.is_science_team_approved = true and p.suspended = false and p.rct_standard = true and pv.risk_buffer_percentage is not null and pv.risk_buffer_percentage < 1 then true else false end AS is_rct_eligible
          FROM "${environment.db.schema.rubicon}".project_vintages pv
            JOIN "${environment.db.schema.rubicon}".projects p ON p.id = pv.project_id
            JOIN "${environment.db.schema.rubicon}".project_types pt ON pt.id = p.project_type_id
            LEFT JOIN "${environment.db.schema.rubicon}".countries c ON c.alpha3 = p.country_code
            LEFT JOIN "${environment.db.schema.rubicon}".buffer_categories pbc ON pbc.id = p.buffer_category_id
            LEFT JOIN "${environment.db.schema.rubicon}".buffer_categories ptbc ON ptbc.id = pt.buffer_category_id;`,
    );
  }
}
