import { Migration } from '@mikro-orm/migrations';
import { environment } from '@env/environment';

export class Migration20250814090549 extends Migration {
  async up(): Promise<void> {
    this.addSql(`ALTER TABLE "${environment.db.schema.rubicon}".model_portfolio_components ADD COLUMN book_id uuid;`);
    this.addSql(
      `ALTER TABLE "${environment.db.schema.rubicon}".model_portfolio_components
       ADD CONSTRAINT mpc_book_id_fkey FOREIGN KEY (book_id)
       REFERENCES "${environment.db.schema.rubicon}".books(id) ON UPDATE CASCADE;`,
    );
    this.addSql(
      `ALTER TABLE "${environment.db.schema.rubicon}".model_portfolio_components
       ADD CONSTRAINT model_portfolio_components_book_or_project_check
       CHECK (
       (book_id IS NULL AND project_id IS NOT NULL)
       OR
       (book_id IS NOT NULL AND project_id IS NULL)
      );`,
    );
    this.addSql(`
      ALTER TABLE "${environment.db.schema.rubicon}".model_portfolio_components ALTER COLUMN is_buffer_component DROP NOT NULL;
    `);
  }
}
