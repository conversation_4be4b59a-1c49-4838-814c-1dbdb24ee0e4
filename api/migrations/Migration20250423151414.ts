import { Migration } from '@mikro-orm/migrations';
import { environment } from '@env/environment';
import { AssetFlowStatus, AssetType, BookType, TradeStatus } from '@rubiconcarbon/shared-types';

export class Migration20250423151414 extends Migration {
  async up(): Promise<void> {
    // drop unnecessary constraints and indexes
    this.addSql(
      `alter table "${environment.db.schema.rubicon}".default_fee_structures drop constraint if exists dfs_counterparty_type_fee_type_unique;`,
    );
    this.addSql(
      `alter table "${environment.db.schema.rubicon}".trade_counterparties drop constraint if exists tc_pkey;`,
    );
    this.addSql(
      `alter table "${environment.db.schema.rubicon}".trade_counterparties drop constraint if exists tc_counterparty_id_fkey;`,
    );
    this.addSql(
      `alter table "${environment.db.schema.rubicon}".trade_counterparties drop constraint if exists tc_trade_id_fkey;`,
    );
    this.addSql(`drop index if exists "tc_counterparty_id";`);
    this.addSql(`drop index if exists "tc_trade_id";`);

    // add missing indexes and constraints
    this.addSql(
      `create index if not exists "assets_asset_type_index" on "${environment.db.schema.rubicon}"."assets" ("asset_type");`,
    );
    this.addSql(
      `alter table "${environment.db.schema.rubicon}".books drop constraint if exists books_organization_id_fkey;`,
    );
    this.addSql(
      `ALTER TABLE "${environment.db.schema.rubicon}".books add constraint books_organization_id_fkey foreign key (organization_id) references "${environment.db.schema.rubicon}".organizations(id) on update cascade;`,
    );
    this.addSql(
      `create index if not exists "counterparties_is_enabled_index" on "${environment.db.schema.rubicon}"."counterparties" ("is_enabled");`,
    );
    this.addSql(
      `create index if not exists "counterparties_is_onboarded_index" on "${environment.db.schema.rubicon}"."counterparties" ("is_onboarded");`,
    );
    this.addSql(
      `alter table "${environment.db.schema.rubicon}".default_fee_structures drop constraint if exists dfs_counterparty_id_type_unique;`,
    );
    this.addSql(
      `ALTER TABLE "${environment.db.schema.rubicon}".default_fee_structures add constraint dfs_counterparty_id_type_unique unique (counterparty_id, type);`,
    );
    this.addSql(
      `create index if not exists "documents_counterparty_id_index" on "${environment.db.schema.rubicon}"."documents" ("counterparty_id");`,
    );
    this.addSql(
      `create index if not exists "forwards_type_index" on "${environment.db.schema.rubicon}"."forwards" ("type");`,
    );
    this.addSql(
      `create index if not exists "organizations_is_onboarded_index" on "${environment.db.schema.rubicon}"."organizations" ("is_onboarded");`,
    );
    this.addSql(
      `create index if not exists "projects_is_byorct_approved_index" on "${environment.db.schema.rubicon}"."projects" ("is_byorct_approved");`,
    );
    this.addSql(
      `create index if not exists "projects_suspended_index" on "${environment.db.schema.rubicon}"."projects" ("suspended");`,
    );

    // renames for consistency
    this.addSql(
      `alter table "${environment.db.schema.rubicon}".trade_counterparties drop constraint if exists trade_counterparties_pkey;`,
    );
    this.addSql(
      `alter table "${environment.db.schema.rubicon}".trade_counterparties add constraint trade_counterparties_pkey primary key (id);`,
    );
    this.addSql(
      `alter table "${environment.db.schema.rubicon}".trade_counterparties drop constraint if exists trade_counterparties_counterparty_id_fkey;`,
    );
    this.addSql(
      `ALTER TABLE "${environment.db.schema.rubicon}".trade_counterparties add constraint trade_counterparties_counterparty_id_fkey foreign key (counterparty_id) references "${environment.db.schema.rubicon}".counterparties(id) on update cascade;`,
    );
    this.addSql(
      `alter table "${environment.db.schema.rubicon}".trade_counterparties drop constraint if exists trade_counterparties_trade_id_fkey;`,
    );
    this.addSql(
      `ALTER TABLE "${environment.db.schema.rubicon}".trade_counterparties add constraint trade_counterparties_trade_id_fkey foreign key (trade_id) references "${environment.db.schema.rubicon}".trades(id) on update cascade;`,
    );
    this.addSql(
      `create index if not exists "trade_counterparties_counterparty_id_index" on "${environment.db.schema.rubicon}"."trade_counterparties" ("counterparty_id");`,
    );
    this.addSql(
      `create index if not exists "trade_counterparties_trade_id_index" on "${environment.db.schema.rubicon}"."trade_counterparties" ("trade_id");`,
    );

    // fix views
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.reports}".holdings_with_sandbox_status
            AS
            WITH pending_trades AS (
                    SELECT virtual_table.registry_project_id,
                        virtual_table.label,
                        sum(virtual_table.amount) FILTER (WHERE virtual_table.action = 'buy'::text) AS pending_buy,
                        sum(virtual_table.amount) FILTER (WHERE virtual_table.action = ANY (ARRAY['sell'::text, 'purchase'::text])) AS pending_sell
                      FROM ( SELECT t.ui_key AS trade_key,
                                p_1.registry_project_id,
                                p_1.name,
                                pv_1.label,
                                af.amount,
                                af.transaction_subtype AS action,
                                af.status
                              FROM "${environment.db.schema.rubicon}".asset_flows af
                                JOIN "${environment.db.schema.rubicon}".trades t ON af.transaction_id = t.id
                                LEFT JOIN "${environment.db.schema.rubicon}".project_vintages pv_1 ON pv_1.id = af.asset_id
                                LEFT JOIN "${environment.db.schema.rubicon}".projects p_1 ON p_1.id = pv_1.project_id
                              WHERE t.status <> ALL (ARRAY['${TradeStatus.SETTLED}'::text, '${TradeStatus.CANCELED}'::text, '${TradeStatus.INDICATIVE}'::text])
                            UNION
                            SELECT purchases.ui_key,
                                p_1.registry_project_id,
                                p_1.name,
                                pv_1.label,
                                af.amount,
                                af.transaction_type AS action,
                                af.status
                              FROM "${environment.db.schema.rubicon}".purchases
                                JOIN "${environment.db.schema.rubicon}".asset_flows af ON af.transaction_id = purchases.id
                                LEFT JOIN "${environment.db.schema.rubicon}".project_vintages pv_1 ON pv_1.id = af.asset_id
                                LEFT JOIN "${environment.db.schema.rubicon}".projects p_1 ON p_1.id = pv_1.project_id
                              WHERE af.status = '${AssetFlowStatus.PENDING}'::text AND af.asset_type = '${AssetType.REGISTRY_VINTAGE}'::text) virtual_table
                      GROUP BY virtual_table.registry_project_id, virtual_table.label
                    ), pending_retirements AS (
                    WITH average_cost AS (
                            SELECT asset_flows_1.transaction_id,
                                asset_flows_1.asset_id AS project_vintage_id,
                                sum(asset_flows_1.amount) AS amount
                              FROM "${environment.db.schema.rubicon}".asset_flows asset_flows_1
                              GROUP BY asset_flows_1.transaction_id, asset_flows_1.asset_id
                            )
                    SELECT projects.registry_project_id,
                        project_vintages.label,
                        sum(average_cost.amount) AS pending_retirement
                      FROM "${environment.db.schema.rubicon}".asset_flows
                        JOIN "${environment.db.schema.rubicon}".project_vintages ON project_vintages.id = asset_flows.asset_id
                        JOIN "${environment.db.schema.rubicon}".projects ON projects.id = project_vintages.project_id
                        JOIN average_cost ON average_cost.transaction_id = asset_flows.transaction_id AND average_cost.project_vintage_id = asset_flows.asset_id
                      WHERE asset_flows.transaction_type = 'retirement'::text AND asset_flows.status = 'pending'::text
                      GROUP BY projects.registry_project_id, project_vintages.label
                    ), current_allocations AS (
                    SELECT ac.component_id AS project_vintage_id,
                        sum(ac.holdings) FILTER (WHERE b.type = '${BookType.RCT_PUBLIC}'::text) AS allocated_rct,
                        sum(ac.holdings) FILTER (WHERE b.type = '${BookType.RCT_CUSTOM}'::text) AS allocated_custom,
                        sum(ac.holdings) FILTER (WHERE b.type = '${BookType.PORTFOLIO_RESERVES}'::text) AS allocated_reserve,
                        sum(ac.holdings) FILTER (WHERE b.type = '${BookType.PORTFOLIO_CUSTOMER}'::text) AS allocated_customer,
                        sum(ac.holdings) FILTER (WHERE b.type <> ALL (ARRAY['${BookType.PORTFOLIO_CUSTOMER}'::text, '${BookType.PORTFOLIO_RESERVES}'::text, '${BookType.RCT_CUSTOM}'::text, '${BookType.RCT_PUBLIC}'::text])) AS allocated_other
                      FROM "${environment.db.schema.rubicon}".asset_composition_v2 ac
                        JOIN "${environment.db.schema.rubicon}".books b ON b.id = ac.parent_id
                      WHERE ac.asset_type = 'registry_vintage'::text AND ac.level = 1
                      GROUP BY ac.component_id
                    ), forward_deliveries AS (
                    SELECT fli.project_vintage_id,
                        sum(fli.expected_amount) FILTER (WHERE f.type = 'buy'::text) AS expected_amount_buy,
                        sum(fli.expected_amount) FILTER (WHERE f.type = 'sell'::text) AS expected_amount_sell,
                        array_agg(fli.last_updated_delivery_date::date::text) AS last_updated_delivery_date
                      FROM "${environment.db.schema.rubicon}".forwards f
                        JOIN "${environment.db.schema.rubicon}".forward_line_items fli ON fli.forward_id = f.id
                      WHERE fli.status = 'pending'::text
                      GROUP BY fli.project_vintage_id
                    ), marketing_agreements AS (
                    SELECT mli.project_vintage_id,
                        sum(mli.amount) AS marketing_amount,
                        array_agg(mli.last_updated_delivery_date::date::text) AS last_updated_delivery_date
                      FROM "${environment.db.schema.rubicon}".marketing_agreements m
                        JOIN "${environment.db.schema.rubicon}".marketing_agreement_line_items mli ON mli.marketing_agreement_id = m.id
                      WHERE mli.status = 'pending'::text AND m.status = 'contracted'::text
                      GROUP BY mli.project_vintage_id
                    ), sandbox_status AS (
                    SELECT (model_portfolio_components.project_vintage_id)::uuid AS vintage_id,
                        NULLIF(sum((model_portfolio_components.quantity)::bigint * (lower(model_portfolios.status) = 'client-reviewing'::text)::integer), 0::numeric) AS client_reviewing,
                        NULLIF(sum((model_portfolio_components.quantity)::bigint * (model_portfolios.status = 'encumber-lite'::text)::integer), 0::numeric) AS encumber_lite,
                        NULLIF(sum((model_portfolio_components.quantity)::bigint * (model_portfolios.status = 'firm-reserved'::text)::integer), 0::numeric) AS firm_reserved
                      FROM "${environment.db.schema.rubicon}".model_portfolio_components
                        JOIN "${environment.db.schema.rubicon}".model_portfolios ON model_portfolios.id = model_portfolio_components.model_portfolio_id
                      WHERE model_portfolios.status IS NOT NULL AND ((model_portfolio_components.project_vintage_id)::uuid) IS NOT NULL AND NOT model_portfolios.is_deleted
                      GROUP BY ((model_portfolio_components.project_vintage_id)::uuid)
                    )
            SELECT p.registry_project_id AS "Registry Project ID",
                p.name AS "Project",
                pv.label AS "Vintage",
                c.name AS "Country",
                c.region AS "Region",
                c.sub_region AS "Sub Region",
                    CASE
                        WHEN p.rct_standard THEN p.rct_standard
                        ELSE NULL::boolean
                    END AS "RCT Standard",
                    CASE
                        WHEN p.suspended THEN p.suspended
                        ELSE NULL::boolean
                    END AS "Suspended",
                    CASE
                        WHEN p.is_science_team_approved THEN p.is_science_team_approved
                        ELSE NULL::boolean
                    END AS "Website Ready",
                    CASE
                        WHEN p.rct_standard AND p.is_science_team_approved AND pv.risk_buffer_percentage < 1::numeric AND NOT p.suspended THEN true
                        ELSE NULL::boolean
                    END AS "RCT Portfolio Eligible",
                pva.holdings AS "Holdings",
                current_allocations.allocated_rct AS "Allocated to RCT (QTY)",
                current_allocations.allocated_custom AS "Allocated to Custom (QTY)",
                current_allocations.allocated_reserve AS "Allocated to Reserves (QTY)",
                current_allocations.allocated_customer AS "Allocated to Customers (QTY)",
                current_allocations.allocated_other AS "Allocated to Other (QTY)",
                pending_trades.pending_buy AS "Pending Buy",
                '-1'::integer * pending_trades.pending_sell AS "Pending Sell",
                '-1'::integer::numeric * pending_retirements.pending_retirement AS "Pending Retirement",
                forward_deliveries.expected_amount_buy AS "Buy Pending Forward Delivery QTY",
                '-1'::integer * forward_deliveries.expected_amount_sell AS "Sell Pending Forward Delivery QTY",
                forward_deliveries.last_updated_delivery_date AS "Forward Delivery Dates",
                marketing_agreements.marketing_amount AS "Marketing Amount",
                marketing_agreements.last_updated_delivery_date AS "Marketing Delivery Dates",
                p.registry_name AS "Registry",
                pt.category AS "Project Category",
                pt.type AS "Project Type",
                pva.average_cost_basis AS "Average Cost Basis",
                asset_prices_v5.unit_value AS "MTM",
                sum(ppm.price) FILTER (WHERE ppm.source = 'viridios'::text) AS "Viridios Price",
                pv.risk_buffer_percentage AS "Risk Buffer Percentage",
                buffer_categories.name AS "Buffer Category",
                sandbox_status.client_reviewing AS "Client Reviewing",
                sandbox_status.encumber_lite AS "Encumber-Lite",
                sandbox_status.firm_reserved AS "Firm Reserved",
                pv.id AS project_vintage_id,
                pv.project_id,
                p.integrity_grade_score,
                    CASE
                        WHEN p.integrity_grade_score >= 84.5 THEN 'A'::text
                        WHEN p.integrity_grade_score < 84.5 AND p.integrity_grade_score >= 74.5 THEN 'B'::text
                        WHEN p.integrity_grade_score < 74.5 AND p.integrity_grade_score >= 59.5 THEN 'C'::text
                        WHEN p.integrity_grade_score < 59.5 THEN 'D'::text
                        ELSE NULL::text
                    END AS integrity_grade_letter
              FROM "${environment.db.schema.rubicon}".project_vintages pv
                JOIN "${environment.db.schema.rubicon}".projects p ON p.id = pv.project_id
                LEFT JOIN "${environment.db.schema.rubicon}".countries c ON c.alpha3::text = p.country_code::text
                LEFT JOIN "${environment.db.schema.rubicon}".project_types pt ON pt.id = p.project_type_id
                LEFT JOIN "${environment.db.schema.rubicon}".buffer_categories ON COALESCE(p.buffer_category_id, pt.buffer_category_id) = buffer_categories.id
                LEFT JOIN "${environment.db.schema.marketData}".project_price_metrics ppm ON p.registry_project_id = ppm.project_id AND pv.label = ppm.vintage
                LEFT JOIN pending_trades USING (registry_project_id, label)
                LEFT JOIN pending_retirements USING (registry_project_id, label)
                LEFT JOIN current_allocations ON pv.id = current_allocations.project_vintage_id
                LEFT JOIN "${environment.db.schema.rubicon}".asset_prices_v5 ON asset_prices_v5.owner_id = pv.id
                LEFT JOIN "${environment.db.schema.rubicon}".project_vintage_amounts_v3 pva ON pv.id = pva.asset_id
                LEFT JOIN "${environment.db.schema.rubicon}".project_flags_v2 pf ON pf.id = p.id
                LEFT JOIN forward_deliveries ON forward_deliveries.project_vintage_id = pv.id
                LEFT JOIN marketing_agreements ON marketing_agreements.project_vintage_id = pv.id
                FULL JOIN sandbox_status ON sandbox_status.vintage_id = pv.id
              WHERE pva.holdings > 0 OR pending_trades.pending_buy > 0 OR forward_deliveries.expected_amount_buy > 0 OR forward_deliveries.expected_amount_sell > 0 OR marketing_agreements.marketing_amount > 0 OR sandbox_status.vintage_id IS NOT NULL
              GROUP BY p.registry_project_id, p.name, pv.label, c.name, c.region, c.sub_region, (
                    CASE
                        WHEN p.rct_standard THEN p.rct_standard
                        ELSE NULL::boolean
                    END), (
                    CASE
                        WHEN p.suspended THEN p.suspended
                        ELSE NULL::boolean
                    END), (
                    CASE
                        WHEN p.is_science_team_approved THEN p.is_science_team_approved
                        ELSE NULL::boolean
                    END), (
                    CASE
                        WHEN p.rct_standard AND p.is_science_team_approved AND pv.risk_buffer_percentage < 1::numeric AND NOT p.suspended THEN true
                        ELSE NULL::boolean
                    END), pva.holdings, current_allocations.allocated_rct, current_allocations.allocated_custom, current_allocations.allocated_reserve, current_allocations.allocated_customer, current_allocations.allocated_other, pending_trades.pending_buy, ('-1'::integer * pending_trades.pending_sell), ('-1'::integer::numeric * pending_retirements.pending_retirement), forward_deliveries.expected_amount_buy, ('-1'::integer * forward_deliveries.expected_amount_sell), forward_deliveries.last_updated_delivery_date, marketing_agreements.marketing_amount, marketing_agreements.last_updated_delivery_date, p.registry_name, pt.category, pt.type, pva.average_cost_basis, asset_prices_v5.unit_value, pv.risk_buffer_percentage, buffer_categories.name, sandbox_status.client_reviewing, sandbox_status.encumber_lite, sandbox_status.firm_reserved, pv.id, pv.project_id, p.integrity_grade_score
              ORDER BY pva.holdings DESC;`,
    );

    // drop tables and views
    this.addSql(`drop table if exists "${environment.db.schema.rubicon}"."deprecated_model_portfolio_components";`);
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}"."asset_holdings_v1";`);
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}"."line_item_amounts_v2";`);
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}"."transactions_v1";`);
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}"."transactions_v1_OLD";`);
    this.addSql(`drop view if exists "${environment.db.schema.recon}"."mismatch_transactions";`);
    this.addSql(`drop view if exists "${environment.db.schema.recon}"."registry_retirement_ids";`);
    this.addSql(`drop view if exists "${environment.db.schema.recon}"."mismatch_transaction_project_vintages";`);
    this.addSql(`drop view if exists "${environment.db.schema.reports}"."trades_buy";`);
    this.addSql(`drop view if exists "${environment.db.schema.reports}"."trades_sell";`);
  }
}
