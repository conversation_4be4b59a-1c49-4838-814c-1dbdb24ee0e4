import { Migration } from '@mikro-orm/migrations';
import { environment } from '@env/environment';
import { BookType, OwnerType } from '@rubiconcarbon/shared-types';

export class Migration20250728135747 extends Migration {
  async up(): Promise<void> {
    // rubicon.books
    this.addSql(
      `update "${environment.db.schema.rubicon}".books set type = '${BookType.RCT_CUSTOM}' where type = 'portfolio:custom'`,
    );
    this.addSql(
      `update "${environment.db.schema.rubicon}".books set type = '${BookType.RCT_PUBLIC}' where type = 'portfolio:public'`,
    );
    // ledger.balances
    this.addSql(
      `update "${environment.db.schema.ledger}".balances set owner_type = '${OwnerType.PORTFOLIO_CUSTOMER}' where owner_type = 'books:portfolio-customer'`,
    );
    this.addSql(
      `update "${environment.db.schema.ledger}".balances set owner_type = '${OwnerType.PORTFOLIO_RCT_CUSTOM}' where owner_type = 'books:portfolio-custom'`,
    );
    this.addSql(
      `update "${environment.db.schema.ledger}".balances set owner_type = '${OwnerType.PORTFOLIO_RCT_PUBLIC}' where owner_type = 'books:portfolio-public'`,
    );
    this.addSql(
      `update "${environment.db.schema.ledger}".balances set owner_type = '${OwnerType.PORTFOLIO_RESERVES}' where owner_type = 'books:portfolio-reserves'`,
    );
    // ledger.settled_entries
    this.addSql(
      `update "${environment.db.schema.ledger}".settled_entries set owner_type = '${OwnerType.PORTFOLIO_CUSTOMER}' where owner_type = 'books:portfolio-customer'`,
    );
    this.addSql(
      `update "${environment.db.schema.ledger}".settled_entries set owner_type = '${OwnerType.PORTFOLIO_RCT_CUSTOM}' where owner_type = 'books:portfolio-custom'`,
    );
    this.addSql(
      `update "${environment.db.schema.ledger}".settled_entries set owner_type = '${OwnerType.PORTFOLIO_RCT_PUBLIC}' where owner_type = 'books:portfolio-public'`,
    );
    this.addSql(
      `update "${environment.db.schema.ledger}".settled_entries set owner_type = '${OwnerType.PORTFOLIO_RESERVES}' where owner_type = 'books:portfolio-reserves'`,
    );
    // ledger.pending_entries
    this.addSql(
      `update "${environment.db.schema.ledger}".pending_entries set owner_type = '${OwnerType.PORTFOLIO_CUSTOMER}' where owner_type = 'books:portfolio-customer'`,
    );
    this.addSql(
      `update "${environment.db.schema.ledger}".pending_entries set owner_type = '${OwnerType.PORTFOLIO_RCT_CUSTOM}' where owner_type = 'books:portfolio-custom'`,
    );
    this.addSql(
      `update "${environment.db.schema.ledger}".pending_entries set owner_type = '${OwnerType.PORTFOLIO_RCT_PUBLIC}' where owner_type = 'books:portfolio-public'`,
    );
    this.addSql(
      `update "${environment.db.schema.ledger}".pending_entries set owner_type = '${OwnerType.PORTFOLIO_RESERVES}' where owner_type = 'books:portfolio-reserves'`,
    );
  }
}
