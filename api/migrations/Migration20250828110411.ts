import { Migration } from '@mikro-orm/migrations';
import { environment } from '@env/environment';
import { AssetType, BookType, TransactionType } from '@rubiconcarbon/shared-types';

const numeric = `numeric(${environment.app.decimal.precision}, ${environment.app.decimal.scale})`;

export class Migration20250828110411 extends Migration {
  async up(): Promise<void> {
    // rename bezeros for consistency
    this.addSql(
      `alter table "${environment.db.schema.rubicon}".projects RENAME COLUMN bezero_rating TO be_zero_rating;`,
    );
    this.addSql(
      `alter table "${environment.db.schema.rubicon}".projects RENAME COLUMN bezero_updated_date TO be_zero_updated_date;`,
    );

    // add rrt_assets table
    this.addSql(`
      CREATE TABLE IF NOT EXISTS "${environment.db.schema.rubicon}".rrt_assets (
        id uuid NOT NULL DEFAULT gen_random_uuid(),
        created_at timestamp(3) with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at timestamp(3) with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
        rrt_id uuid not null,
        project_vintage_id uuid not null,
        be_zero_factor ${numeric} NOT NULL,
        be_zero_rating_at_creation text not null,
        current_net_quantity integer not null,
        initial_net_quantity integer not null,
        initial_gross_quantity integer not null,
        total_net_quantity integer not null,
        total_gross_quantity integer not null,
        CONSTRAINT rrt_assets_pkey PRIMARY KEY (id),
        CONSTRAINT rrt_assets_rrt_id_fkey FOREIGN KEY (rrt_id)
            REFERENCES "${environment.db.schema.rubicon}".books (id) MATCH SIMPLE
            ON UPDATE NO ACTION
            ON DELETE NO ACTION,
        CONSTRAINT rrt_assets_project_vintage_id_fkey FOREIGN KEY (project_vintage_id)
            REFERENCES "${environment.db.schema.rubicon}".project_vintages (id) MATCH SIMPLE
            ON UPDATE NO ACTION
            ON DELETE NO ACTION, 
        CONSTRAINT rrt_assets_rrt_id_project_vintage_id_unique UNIQUE (rrt_id, project_vintage_id)
     );`);

    // asset_details_v2
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".asset_details_v2
        AS
          SELECT b.id,
            '${AssetType.RCT}' as asset_type,
            b.name as name,
            b.id AS portfolio_id,
            NULL::uuid AS project_vintage_id,
            CASE
                WHEN b.type = '${BookType.RCT_PUBLIC}'::text THEN true
                ELSE false
              END AS is_public
          FROM "${environment.db.schema.rubicon}".books b
          WHERE b.asset_type = '${AssetType.RCT}'::text
        UNION ALL
          SELECT b.id,
            '${AssetType.RRT}' as asset_type,
            b.name as name,
            b.id AS portfolio_id,
            NULL::uuid AS project_vintage_id,
            true AS is_public
          FROM "${environment.db.schema.rubicon}".books b
          WHERE b.asset_type = '${AssetType.RRT}'::text
        UNION ALL
          SELECT ra.id,
            '${AssetType.RRT_VINTAGE}' as asset_type,
            b.name || ' - ' || pvg.registry_project_id as name,
            b.id as portfolio_id,
            pvg.project_vintage_id AS project_vintage_id,
            true AS is_public
          FROM "${environment.db.schema.rubicon}".rrt_assets as ra
          inner join "${environment.db.schema.rubicon}".books as b on b.id = ra.rrt_id
          inner join "${environment.db.schema.rubicon}".project_vintage_groupings_v2 as pvg ON pvg.project_vintage_id = ra.project_vintage_id
        UNION ALL
          SELECT pv.id,
            '${AssetType.REGISTRY_VINTAGE}' as asset_type,
            p.name,
            NULL::uuid as portfolio_id,
            pv.id AS project_vintage_id,
            true AS is_public
          FROM "${environment.db.schema.rubicon}".project_vintages pv
          inner join "${environment.db.schema.rubicon}".projects p ON p.id = pv.project_id;`,
    );

    // transaction_assets_v2
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".transaction_assets_v2
            AS
            SELECT NULL::uuid AS parent_transaction_id,
                af.transaction_id,
                af.transaction_type,
                af.transaction_subtype,
                af.asset_id AS asset_id,
                af.asset_type,
                ad.name AS asset_name,
                b.name AS asset_portfolio_name,
                pvg.project_name AS asset_project_name,
                pvg.registry_project_id AS asset_registry_project_id,
                count(af.*) > 1 AS has_accounting_entries,
                sum(af.amount) AS asset_quantity,
                COALESCE(sum(af.raw_price), 0::numeric) AS asset_raw_price,
                COALESCE(sum(af.service_fee), 0::numeric) AS asset_service_fee,
                COALESCE(sum(af.other_fee), 0::numeric) AS asset_other_fee
              FROM "${environment.db.schema.rubicon}".asset_flows af
                JOIN "${environment.db.schema.rubicon}".asset_details_v2 as ad ON ad.id = af.asset_id
                JOIN "${environment.db.schema.rubicon}".books as b ON b.id = ad.portfolio_id
                JOIN "${environment.db.schema.rubicon}".project_vintage_groupings_v2 as pvg ON pvg.project_vintage_id = ad.project_vintage_id
              WHERE af.transaction_type <> ALL (ARRAY['forward_line_item'::text, 'marketing_agreement_line_item'::text])
              GROUP BY af.transaction_id, af.transaction_type, af.transaction_subtype, af.id, af.asset_type, ad.name, b.name, pvg.project_name, pvg.registry_project_id
            UNION ALL
            SELECT fli.forward_id AS parent_transaction_id,
                fli.id AS transaction_id,
                'forward_line_item'::text AS transaction_type,
                f.type AS transaction_subtype,
                ad.id AS asset_id,
                ad.asset_type,
                ad.name AS asset_name,
                NULL::text as asset_portfolio_name,
                pvg.project_name AS asset_project_name,
                pvg.registry_project_id AS asset_registry_project_id,
                count(af.*) > 1 AS has_accounting_entries,
                    CASE
                        WHEN fli.settled_amount IS NOT NULL THEN fli.settled_amount
                        ELSE fli.expected_amount
                    END AS asset_quantity,
                COALESCE(fli.raw_price, 0::numeric) AS asset_raw_price,
                COALESCE(fli.service_fee, 0::numeric) AS asset_service_fee,
                COALESCE(fli.other_fee, 0::numeric) AS asset_other_fee
              FROM "${environment.db.schema.rubicon}".forward_line_items fli
                JOIN "${environment.db.schema.rubicon}".forwards f ON f.id = fli.forward_id
                JOIN "${environment.db.schema.rubicon}".asset_details_v2 ad ON ad.id = fli.project_vintage_id
                JOIN "${environment.db.schema.rubicon}".project_vintage_groupings_v2 as pvg ON pvg.project_vintage_id = ad.project_vintage_id
                LEFT JOIN "${environment.db.schema.rubicon}".asset_flows af ON af.asset_id = fli.project_vintage_id
              GROUP BY f.id, fli.id, f.type, ad.id, ad.asset_type, ad.name, pvg.project_name, pvg.registry_project_id
            UNION ALL
            SELECT mali.marketing_agreement_id AS parent_transaction_id,
                mali.id AS transaction_id,
                'marketing_agreement_line_item'::text AS transaction_type,
                NULL::text AS transaction_subtype,
                ad.id AS asset_id,
                ad.asset_type,
                ad.name AS asset_name,
                NULL::text as asset_portfolio_name,
                pvg.project_name AS asset_project_name,
                pvg.registry_project_id AS asset_registry_project_id,
                count(af.*) > 1 AS has_accounting_entries,
                mali.amount AS asset_quantity,
                NULL::numeric AS asset_raw_price,
                NULL::numeric AS asset_service_fee,
                NULL::numeric AS asset_other_fee
              FROM "${environment.db.schema.rubicon}".marketing_agreement_line_items mali
                JOIN "${environment.db.schema.rubicon}".asset_details_v2 ad ON ad.id = mali.project_vintage_id
                JOIN "${environment.db.schema.rubicon}".project_vintage_groupings_v2 as pvg ON pvg.project_vintage_id = ad.project_vintage_id
                LEFT JOIN "${environment.db.schema.rubicon}".asset_flows af ON af.asset_id = mali.project_vintage_id
              GROUP BY mali.marketing_agreement_id, mali.id, ad.id, ad.asset_type, ad.name, pvg.project_name, pvg.registry_project_id;`,
    );

    // transactions_details_v2
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".transactions_details_v2
                AS
                WITH transaction_documents AS (
                        SELECT DISTINCT d.related_ui_key as ui_key,
                            count(d.*) AS docs_count
                          FROM "${environment.db.schema.rubicon}".documents d
                          WHERE is_deleted = false
                          GROUP BY d.related_ui_key
                          ORDER BY d.related_ui_key
                        ),
                organizations_details AS (
                        SELECT o.id as id, 
                            o.name as name,
                            c.id as counterparty_id,
                            cp.id as customer_portfolio_id
                          FROM "${environment.db.schema.rubicon}".organizations o
                          left join "${environment.db.schema.rubicon}".counterparties as c on c.organization_id = o.id
                          left join "${environment.db.schema.rubicon}".customer_portfolios as cp on cp.organization_id = o.id
                        )
                SELECT p1.id,
                    p1.created_at,
                    p1.updated_at,
                    p1.ui_key,
                    '${TransactionType.PURCHASE}'::text AS type,
                    NULL::text AS subtype,
                        CASE
                            WHEN p1.status = 'executed'::text AND p1.is_delivered THEN 'delivered'::text
                            WHEN p1.status = 'executed'::text AND p1.is_paid THEN 'paid'::text
                            ELSE p1.status
                        END AS status,
                    p1.date_finished AS settled_at,
                    o1.id AS counterparty_id,
                    o1.name AS counterparty_name,
                    count(ta1.*) AS unique_assets_count,
                    sum(ta1.asset_quantity) AS total_quantity,
                    COALESCE(sum(ta1.asset_raw_price), 0::numeric) AS total_raw_price,
                    COALESCE(sum(ta1.asset_service_fee), 0::numeric) AS total_service_fee,
                    COALESCE(sum(ta1.asset_other_fee), 0::numeric) AS total_other_fee,
                    COALESCE(d1.docs_count, 0::bigint) AS docs_count,
                    NULL::uuid AS parent_id
                  FROM "${environment.db.schema.rubicon}".purchases p1
                    JOIN organizations_details o1 ON o1.customer_portfolio_id = p1.customer_portfolio_id
                    LEFT JOIN "${environment.db.schema.rubicon}".transaction_assets_v2 ta1 ON ta1.transaction_id = p1.id
                    LEFT JOIN transaction_documents d1 ON d1.ui_key = p1.ui_key
                  GROUP BY p1.id, p1.created_at, p1.updated_at, p1.ui_key, o1.id, o1.name, ta1.transaction_id, d1.ui_key, d1.docs_count
                UNION ALL
                SELECT r2.id,
                    r2.created_at,
                    r2.updated_at,
                    r2.ui_key,
                    '${TransactionType.RETIREMENT}'::text AS type,
                    r2.type AS subtype,
                    r2.status,
                    r2.date_finished AS settled_at,
                    o2.id AS counterparty_id,
                    o2.name AS counterparty_name,
                    count(ta2.*) AS unique_assets_count,
                    sum(ta2.asset_quantity) AS total_quantity,
                    COALESCE(sum(ta2.asset_raw_price), 0::numeric) AS total_raw_price,
                    COALESCE(sum(ta2.asset_service_fee), 0::numeric) AS total_service_fee,
                    COALESCE(sum(ta2.asset_other_fee), 0::numeric) AS total_other_fee,
                    COALESCE(d2.docs_count, 0::bigint) AS docs_count,
                    NULL::uuid AS parent_id
                  FROM "${environment.db.schema.rubicon}".retirements r2
                    JOIN organizations_details o2 ON o2.customer_portfolio_id = r2.customer_portfolio_id
                    LEFT JOIN "${environment.db.schema.rubicon}".transaction_assets_v2 ta2 ON ta2.transaction_id = r2.id
                    LEFT JOIN transaction_documents d2 ON d2.ui_key = r2.ui_key
                  GROUP BY r2.id, r2.created_at, r2.updated_at, r2.ui_key, o2.id, o2.name, ta2.transaction_id, d2.ui_key, d2.docs_count
                UNION ALL
                SELECT t3.id,
                    t3.created_at,
                    t3.updated_at,
                    t3.ui_key,
                    '${TransactionType.TRADE}'::text AS type,
                    t3.type AS subtype,
                        CASE
                            WHEN t3.status = 'executed'::text AND t3.is_delivered THEN 'delivered'::text
                            WHEN t3.status = 'executed'::text AND t3.is_paid THEN 'paid'::text
                            ELSE t3.status
                        END AS status,
                    t3.settled_at,
                    o3.id AS counterparty_id,
                    o3.name AS counterparty_name,
                    count(ta3.*) AS unique_assets_count,
                    sum(ta3.asset_quantity) AS total_quantity,
                    COALESCE(sum(ta3.asset_raw_price), 0::numeric) AS total_raw_price,
                    COALESCE(sum(ta3.asset_service_fee), 0::numeric) AS total_service_fee,
                    COALESCE(sum(ta3.asset_other_fee), 0::numeric) AS total_other_fee,
                    COALESCE(d3.docs_count, 0::bigint) AS docs_count,
                    NULL::uuid AS parent_id
                  FROM "${environment.db.schema.rubicon}".trades t3
                    LEFT JOIN "${environment.db.schema.rubicon}".transaction_assets_v2 ta3 ON ta3.transaction_id = t3.id
                    LEFT JOIN "${environment.db.schema.rubicon}".trade_counterparties tc3 ON tc3.trade_id = t3.id AND tc3.is_primary = true
                    LEFT JOIN organizations_details as o3 on o3.counterparty_id = tc3.counterparty_id
                    LEFT JOIN transaction_documents d3 ON d3.ui_key = t3.ui_key
                  GROUP BY t3.id, t3.created_at, t3.updated_at, t3.ui_key, o3.id, o3.name, ta3.transaction_id, d3.ui_key, d3.docs_count
                UNION ALL
                SELECT t4.id,
                    t4.created_at,
                    t4.updated_at,
                    NULL::text AS ui_key,
                    '${TransactionType.INTERNAL_TRANSFER}'::text AS type,
                    NULL::text AS subtype,
                    'executed'::text AS status,
                    t4.updated_at AS settled_at,
                    NULL::uuid AS counterparty_id,
                    'internal'::text AS counterparty_name,
                    count(ta4.*) AS unique_assets_count,
                    sum(ta4.asset_quantity) AS total_quantity,
                    COALESCE(sum(ta4.asset_raw_price), 0::numeric) AS total_raw_price,
                    COALESCE(sum(ta4.asset_service_fee), 0::numeric) AS total_service_fee,
                    COALESCE(sum(ta4.asset_other_fee), 0::numeric) AS total_other_fee,
                    0 AS docs_count,
                    NULL::uuid AS parent_id
                  FROM "${environment.db.schema.rubicon}".transfers t4
                    LEFT JOIN "${environment.db.schema.rubicon}".transaction_assets_v2 ta4 ON ta4.transaction_id = t4.id
                  GROUP BY t4.id, t4.created_at, t4.updated_at, ta4.transaction_id
                UNION ALL
                SELECT r5.id,
                    r5.created_at,
                    r5.updated_at,
                    NULL::text AS ui_key,
                    '${TransactionType.RESERVE}'::text AS type,
                    NULL::text AS subtype,
                        CASE
                            WHEN r5.is_deleted THEN 'deleted'::text
                            ELSE 'reserved'::text
                        END AS status,
                    r5.updated_at AS settled_at,
                    o5.id AS counterparty_id,
                    o5.name AS counterparty_name,
                    count(ta5.*) AS unique_assets_count,
                    r5.amount::numeric AS total_quantity,
                    COALESCE(sum(ta5.asset_raw_price), 0::numeric) AS total_raw_price,
                    COALESCE(sum(ta5.asset_service_fee), 0::numeric) AS total_service_fee,
                    COALESCE(sum(ta5.asset_other_fee), 0::numeric) AS total_other_fee,
                    0 AS docs_count,
                    NULL::uuid AS parent_id
                  FROM "${environment.db.schema.rubicon}".reserves r5
                    LEFT JOIN "${environment.db.schema.rubicon}".organizations o5 ON o5.id = r5.organization_id
                    LEFT JOIN "${environment.db.schema.rubicon}".transaction_assets_v2 ta5 ON ta5.transaction_id = r5.id
                  GROUP BY r5.id, r5.created_at, r5.updated_at, o5.id, o5.name, ta5.transaction_id
                UNION ALL
                SELECT fli6.id,
                    fli6.created_at,
                    fli6.updated_at,
                    fli6.ui_key,
                    '${TransactionType.FORWARD_LINE_ITEM}'::text AS type,
                    f6.type AS subtype,
                    fli6.status,
                    fli6.settled_at,
                    o6.id as counterparty_id,
                    o6.name as counterparty_name,
                    1 AS unique_assets_count,
                    sum(ta6.asset_quantity) AS total_quantity,
                    COALESCE(sum(ta6.asset_raw_price), 0::numeric) AS total_raw_price,
                    COALESCE(sum(ta6.asset_service_fee), 0::numeric) AS total_service_fee,
                    COALESCE(sum(ta6.asset_other_fee), 0::numeric) AS total_other_fee,
                    COALESCE(d6.docs_count, 0::bigint) AS docs_count,
                    f6.id AS parent_id
                  FROM "${environment.db.schema.rubicon}".forward_line_items fli6
                    JOIN "${environment.db.schema.rubicon}".forwards f6 ON f6.id = fli6.forward_id
                    LEFT JOIN organizations_details o6 ON (o6.counterparty_id = f6.counterparty_id or o6.customer_portfolio_id = f6.customer_portfolio_id)
                    LEFT JOIN "${environment.db.schema.rubicon}".transaction_assets_v2 ta6 ON ta6.transaction_id = fli6.id
                    LEFT JOIN transaction_documents d6 ON d6.ui_key = fli6.ui_key
                  GROUP BY fli6.id, fli6.created_at, fli6.updated_at, fli6.ui_key, f6.type, o6.id, o6.name, ta6.transaction_id, d6.ui_key, d6.docs_count, f6.id
                UNION ALL
                SELECT mali7.id,
                    mali7.created_at,
                    mali7.updated_at,
                    mali7.ui_key,
                    '${TransactionType.MARKETING_AGREEMENT_LINE_ITEM}'::text AS type,
                    NULL::text AS subtype,
                    mali7.status,
                    mali7.settled_at,
                    NULL::uuid AS counterparty_id,
                    o7.name AS counterparty_name,
                    1 AS unique_assets_count,
                    sum(ta7.asset_quantity) AS total_quantity,
                    COALESCE(sum(ta7.asset_raw_price), 0::numeric) AS total_raw_price,
                    COALESCE(sum(ta7.asset_service_fee), 0::numeric) AS total_service_fee,
                    COALESCE(sum(ta7.asset_other_fee), 0::numeric) AS total_other_fee,
                    COALESCE(d7.docs_count, 0::bigint) AS docs_count,
                    m7.id AS parent_id
                  FROM "${environment.db.schema.rubicon}".marketing_agreement_line_items mali7
                    JOIN "${environment.db.schema.rubicon}".marketing_agreements m7 ON m7.id = mali7.marketing_agreement_id
                    LEFT JOIN organizations_details o7 ON (o7.counterparty_id = m7.counterparty_id)
                    LEFT JOIN "${environment.db.schema.rubicon}".transaction_assets_v2 ta7 ON ta7.transaction_id = mali7.id
                    LEFT JOIN transaction_documents d7 ON d7.ui_key = mali7.ui_key
                  GROUP BY mali7.id, mali7.created_at, mali7.updated_at, mali7.ui_key, o7.name, ta7.transaction_id, d7.ui_key, d7.docs_count, m7.id;`,
    );

    // project_vintage_groupings_v2
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".project_vintage_groupings_v2
        AS
        SELECT
                CASE
                    WHEN p.buffer_category_id IS NOT NULL THEN pbc.id
                    ELSE ptbc.id
                END AS buffer_category_id,
                CASE
                    WHEN p.buffer_category_id IS NOT NULL THEN pbc.name
                    ELSE ptbc.name
                END AS buffer_category_name,
            pt.id AS project_type_id,
            pt.type AS project_type_type,
            pt.category AS project_type_category,
            p.id AS project_id,
            c.alpha3 AS project_country_alpha3,
            c.name AS project_country_name,
            p.name AS project_name,
            p.registry_project_id,
            p.is_science_team_approved AS project_is_science_team_approved,
            p.is_byorct_approved AS project_is_byorct_approved,
            p.suspended AS project_is_suspended,
            p.rct_standard AS project_is_rct_standard,
            p.integrity_grade_score AS project_integrity_grade_score,
            p.integrity_grade_score_risk_adjusted AS project_integrity_grade_score_risk_adjusted,
            pv.id AS project_vintage_id,
            pv.label AS project_vintage_label,
            pv."interval" AS project_vintage_interval,
            pv.risk_buffer_percentage AS project_vintage_risk_buffer_percentage,
            pv.low_buffer_percentage AS project_vintage_low_buffer_percentage,
            pv.high_buffer_percentage AS project_vintage_high_buffer_percentage,
            case when p.is_science_team_approved = true and p.suspended = false and p.rct_standard = true and pv.risk_buffer_percentage is not null and pv.risk_buffer_percentage < 1 then true else false end AS is_rct_eligible,
            p.registry_name as project_registry_name,
            p.be_zero_rating as project_be_zero_rating,
            p.be_zero_updated_date as project_be_zero_updated_date
          FROM "${environment.db.schema.rubicon}".project_vintages pv
            JOIN "${environment.db.schema.rubicon}".projects p ON p.id = pv.project_id
            JOIN "${environment.db.schema.rubicon}".project_types pt ON pt.id = p.project_type_id
            LEFT JOIN "${environment.db.schema.rubicon}".countries c ON c.alpha3 = p.country_code
            LEFT JOIN "${environment.db.schema.rubicon}".buffer_categories pbc ON pbc.id = p.buffer_category_id
            LEFT JOIN "${environment.db.schema.rubicon}".buffer_categories ptbc ON ptbc.id = pt.buffer_category_id;`,
    );

    // drop views
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".transaction_assets_v1;`);
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".asset_details_v1;`);
  }
}
