import { Migration } from '@mikro-orm/migrations';
import { environment } from '@env/environment';
import { AssetFlowStatus, AssetType, BookType, TradeStatus } from '@rubiconcarbon/shared-types';

export class Migration20250627093729 extends Migration {
  async up(): Promise<void> {
    // fix view holdings_with_sandbox_status
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.reports}".holdings_with_sandbox_status
                AS
                WITH pending_trades AS (
                        SELECT virtual_table.registry_project_id,
                            virtual_table.label,
                            sum(virtual_table.amount) FILTER (WHERE virtual_table.action = 'buy'::text) AS pending_buy,
                            sum(virtual_table.amount) FILTER (WHERE virtual_table.action = ANY (ARRAY['sell'::text, 'purchase'::text])) AS pending_sell
                          FROM ( SELECT t.ui_key AS trade_key,
                                    p_1.registry_project_id,
                                    p_1.name,
                                    pv_1.label,
                                    af.amount,
                                    af.transaction_subtype AS action,
                                    af.status
                                  FROM "${environment.db.schema.rubicon}".asset_flows af
                                    JOIN "${environment.db.schema.rubicon}".trades t ON af.transaction_id = t.id
                                    LEFT JOIN "${environment.db.schema.rubicon}".project_vintages pv_1 ON pv_1.id = af.asset_id
                                    LEFT JOIN "${environment.db.schema.rubicon}".projects p_1 ON p_1.id = pv_1.project_id
                                  WHERE t.status <> ALL (ARRAY['${TradeStatus.SETTLED}'::text, '${TradeStatus.CANCELED}'::text, '${TradeStatus.INDICATIVE}'::text])
                                UNION
                                SELECT purchases.ui_key,
                                    p_1.registry_project_id,
                                    p_1.name,
                                    pv_1.label,
                                    af.amount,
                                    af.transaction_type AS action,
                                    af.status
                                  FROM "${environment.db.schema.rubicon}".purchases
                                    JOIN "${environment.db.schema.rubicon}".asset_flows af ON af.transaction_id = purchases.id
                                    LEFT JOIN "${environment.db.schema.rubicon}".project_vintages pv_1 ON pv_1.id = af.asset_id
                                    LEFT JOIN "${environment.db.schema.rubicon}".projects p_1 ON p_1.id = pv_1.project_id
                                  WHERE af.status = '${AssetFlowStatus.PENDING}'::text AND af.asset_type = '${AssetType.REGISTRY_VINTAGE}'::text) virtual_table
                          GROUP BY virtual_table.registry_project_id, virtual_table.label
                        ), pending_retirements AS (
                        WITH average_cost AS (
                                SELECT asset_flows_1.transaction_id,
                                    asset_flows_1.asset_id AS project_vintage_id,
                                    sum(asset_flows_1.amount) AS amount
                                  FROM "${environment.db.schema.rubicon}".asset_flows asset_flows_1
                                  GROUP BY asset_flows_1.transaction_id, asset_flows_1.asset_id
                                )
                        SELECT projects.registry_project_id,
                            project_vintages.label,
                            sum(average_cost.amount) AS pending_retirement
                          FROM "${environment.db.schema.rubicon}".asset_flows
                            JOIN "${environment.db.schema.rubicon}".project_vintages ON project_vintages.id = asset_flows.asset_id
                            JOIN "${environment.db.schema.rubicon}".projects ON projects.id = project_vintages.project_id
                            JOIN average_cost ON average_cost.transaction_id = asset_flows.transaction_id AND average_cost.project_vintage_id = asset_flows.asset_id
                          WHERE asset_flows.transaction_type = 'retirement'::text AND asset_flows.status = 'pending'::text
                          GROUP BY projects.registry_project_id, project_vintages.label
                        ), current_allocations AS (
                        SELECT ac.component_id AS project_vintage_id,
                            sum(ac.holdings) FILTER (WHERE b.type = '${BookType.RCT_PUBLIC}'::text) AS allocated_rct,
                            sum(ac.holdings) FILTER (WHERE b.type = '${BookType.RCT_CUSTOM}'::text) AS allocated_custom,
                            sum(ac.holdings) FILTER (WHERE b.type = '${BookType.PORTFOLIO_RESERVES}'::text) AS allocated_reserve,
                            sum(ac.holdings) FILTER (WHERE b.type = '${BookType.PORTFOLIO_CUSTOMER}'::text) AS allocated_customer,
                            sum(ac.holdings) FILTER (WHERE b.type <> ALL (ARRAY['${BookType.PORTFOLIO_CUSTOMER}'::text, '${BookType.PORTFOLIO_RESERVES}'::text, '${BookType.RCT_CUSTOM}'::text, '${BookType.RCT_PUBLIC}'::text])) AS allocated_other
                          FROM "${environment.db.schema.rubicon}".asset_composition_v2 ac
                            JOIN "${environment.db.schema.rubicon}".books b ON b.id = ac.parent_id
                          WHERE ac.asset_type = 'registry_vintage'::text AND ac.level = 1
                          GROUP BY ac.component_id
                        ), forward_deliveries AS (
                        SELECT fli.project_vintage_id,
                            sum(fli.expected_amount) FILTER (WHERE f.type = 'buy'::text) AS expected_amount_buy,
                            sum(fli.expected_amount) FILTER (WHERE f.type = 'sell'::text) AS expected_amount_sell,
                            array_agg(fli.last_updated_delivery_date::date::text) AS last_updated_delivery_date
                          FROM "${environment.db.schema.rubicon}".forwards f
                            JOIN "${environment.db.schema.rubicon}".forward_line_items fli ON fli.forward_id = f.id
                          WHERE fli.status = 'pending'::text
                          GROUP BY fli.project_vintage_id
                        ), marketing_agreements AS (
                        SELECT mli.project_vintage_id,
                            sum(mli.amount) AS marketing_amount,
                            array_agg(mli.last_updated_delivery_date::date::text) AS last_updated_delivery_date
                          FROM "${environment.db.schema.rubicon}".marketing_agreements m
                            JOIN "${environment.db.schema.rubicon}".marketing_agreement_line_items mli ON mli.marketing_agreement_id = m.id
                          WHERE mli.status = 'pending'::text AND m.status = 'contracted'::text
                          GROUP BY mli.project_vintage_id
                        ), sandbox_status AS (
                        SELECT (model_portfolio_components.project_vintage_id)::uuid AS vintage_id,
                            NULLIF(sum((model_portfolio_components.quantity)::bigint * (lower(model_portfolios.status) = 'client-reviewing'::text)::integer), 0::numeric) AS client_reviewing,
                            NULLIF(sum((model_portfolio_components.quantity)::bigint * (model_portfolios.status = 'encumber-lite'::text)::integer), 0::numeric) AS encumber_lite,
                            NULLIF(sum((model_portfolio_components.quantity)::bigint * (model_portfolios.status = 'firm-reserved'::text)::integer), 0::numeric) AS firm_reserved
                          FROM "${environment.db.schema.rubicon}".model_portfolio_components
                            JOIN "${environment.db.schema.rubicon}".model_portfolios ON model_portfolios.id = model_portfolio_components.model_portfolio_id
                          WHERE model_portfolios.status IS NOT NULL AND ((model_portfolio_components.project_vintage_id)::uuid) IS NOT NULL AND NOT model_portfolios.is_deleted
                          GROUP BY ((model_portfolio_components.project_vintage_id)::uuid)
                        )
                SELECT p.registry_project_id AS "Registry Project ID",
                    p.name AS "Project",
                    pv.label AS "Vintage",
                    c.name AS "Country",
                    c.region AS "Region",
                    c.sub_region AS "Sub Region",
                        CASE
                            WHEN p.rct_standard THEN p.rct_standard
                            ELSE NULL::boolean
                        END AS "RCT Standard",
                        CASE
                            WHEN p.suspended THEN p.suspended
                            ELSE NULL::boolean
                        END AS "Suspended",
                        CASE
                            WHEN p.is_science_team_approved THEN p.is_science_team_approved
                            ELSE NULL::boolean
                        END AS "Website Ready",
                        CASE
                            WHEN p.rct_standard AND p.is_science_team_approved AND pv.risk_buffer_percentage < 1::numeric AND NOT p.suspended THEN true
                            ELSE NULL::boolean
                        END AS "RCT Portfolio Eligible",
                    pva.holdings AS "Holdings",
                    current_allocations.allocated_rct AS "Allocated to RCT (QTY)",
                    current_allocations.allocated_custom AS "Allocated to Custom (QTY)",
                    current_allocations.allocated_reserve AS "Allocated to Reserves (QTY)",
                    current_allocations.allocated_customer AS "Allocated to Customers (QTY)",
                    current_allocations.allocated_other AS "Allocated to Other (QTY)",
                    pending_trades.pending_buy AS "Pending Buy",
                    '-1'::integer * pending_trades.pending_sell AS "Pending Sell",
                    '-1'::integer::numeric * pending_retirements.pending_retirement AS "Pending Retirement",
                    forward_deliveries.expected_amount_buy AS "Buy Pending Forward Delivery QTY",
                    '-1'::integer * forward_deliveries.expected_amount_sell AS "Sell Pending Forward Delivery QTY",
                    forward_deliveries.last_updated_delivery_date AS "Forward Delivery Dates",
                    marketing_agreements.marketing_amount AS "Marketing Amount",
                    marketing_agreements.last_updated_delivery_date AS "Marketing Delivery Dates",
                    p.registry_name AS "Registry",
                    pt.category AS "Project Category",
                    pt.type AS "Project Type",
                    pva.average_cost_basis AS "Average Cost Basis",
                    asset_prices_v5.unit_value AS "MTM",
                    sum(ppm.price) FILTER (WHERE ppm.source = 'viridios'::text) AS "Viridios Price",
                    pv.risk_buffer_percentage AS "Risk Buffer Percentage",
                    buffer_categories.name AS "Buffer Category",
                    sandbox_status.client_reviewing AS "Client Reviewing",
                    sandbox_status.encumber_lite AS "Encumber-Lite",
                    sandbox_status.firm_reserved AS "Firm Reserved",
                    pv.id AS project_vintage_id,
                    pv.project_id,
                    p.integrity_grade_score,
                        CASE
                            WHEN p.integrity_grade_score >= 84.5 THEN 'A'::text
                            WHEN p.integrity_grade_score < 84.5 AND p.integrity_grade_score >= 74.5 THEN 'B'::text
                            WHEN p.integrity_grade_score < 74.5 AND p.integrity_grade_score >= 59.5 THEN 'C'::text
                            WHEN p.integrity_grade_score < 59.5 THEN 'D'::text
                            ELSE NULL::text
                        END AS integrity_grade_letter
                  FROM "${environment.db.schema.rubicon}".project_vintages pv
                    JOIN "${environment.db.schema.rubicon}".projects p ON p.id = pv.project_id
                    LEFT JOIN "${environment.db.schema.rubicon}".countries c ON c.alpha3::text = p.country_code::text
                    LEFT JOIN "${environment.db.schema.rubicon}".project_types pt ON pt.id = p.project_type_id
                    LEFT JOIN "${environment.db.schema.rubicon}".buffer_categories ON COALESCE(p.buffer_category_id, pt.buffer_category_id) = buffer_categories.id
                    LEFT JOIN "${environment.db.schema.marketData}".project_price_metrics ppm ON p.registry_project_id = ppm.project_id AND pv.label = ppm.vintage
                    LEFT JOIN pending_trades USING (registry_project_id, label)
                    LEFT JOIN pending_retirements USING (registry_project_id, label)
                    LEFT JOIN current_allocations ON pv.id = current_allocations.project_vintage_id
                    LEFT JOIN "${environment.db.schema.rubicon}".asset_prices_v5 ON asset_prices_v5.owner_id = pv.id
                    LEFT JOIN "${environment.db.schema.rubicon}".project_vintage_amounts_v3 pva ON pv.id = pva.asset_id
                    LEFT JOIN "${environment.db.schema.rubicon}".project_flags_v2 pf ON pf.id = p.id
                    LEFT JOIN forward_deliveries ON forward_deliveries.project_vintage_id = pv.id
                    LEFT JOIN marketing_agreements ON marketing_agreements.project_vintage_id = pv.id
                    FULL JOIN sandbox_status ON sandbox_status.vintage_id = pv.id
                  WHERE pva.holdings > 0 OR pending_trades.pending_buy > 0 OR forward_deliveries.expected_amount_buy > 0 OR forward_deliveries.expected_amount_sell > 0 OR marketing_agreements.marketing_amount > 0 OR sandbox_status.vintage_id IS NOT NULL
                  GROUP BY p.registry_project_id, p.name, pv.label, c.name, c.region, c.sub_region, (
                        CASE
                            WHEN p.rct_standard THEN p.rct_standard
                            ELSE NULL::boolean
                        END), (
                        CASE
                            WHEN p.suspended THEN p.suspended
                            ELSE NULL::boolean
                        END), (
                        CASE
                            WHEN p.is_science_team_approved THEN p.is_science_team_approved
                            ELSE NULL::boolean
                        END), (
                        CASE
                            WHEN p.rct_standard AND p.is_science_team_approved AND pv.risk_buffer_percentage < 1::numeric AND NOT p.suspended THEN true
                            ELSE NULL::boolean
                        END), pva.holdings, current_allocations.allocated_rct, current_allocations.allocated_custom, current_allocations.allocated_reserve, current_allocations.allocated_customer, current_allocations.allocated_other, pending_trades.pending_buy, ('-1'::integer * pending_trades.pending_sell), ('-1'::integer::numeric * pending_retirements.pending_retirement), forward_deliveries.expected_amount_buy, ('-1'::integer * forward_deliveries.expected_amount_sell), forward_deliveries.last_updated_delivery_date, marketing_agreements.marketing_amount, marketing_agreements.last_updated_delivery_date, p.registry_name, pt.category, pt.type, pva.average_cost_basis, asset_prices_v5.unit_value, pv.risk_buffer_percentage, buffer_categories.name, sandbox_status.client_reviewing, sandbox_status.encumber_lite, sandbox_status.firm_reserved, pv.id, pv.project_id, p.integrity_grade_score
                  ORDER BY pva.holdings DESC;`,
    );

    // update recon views
    this.addSql(`
    CREATE OR REPLACE VIEW "${environment.db.schema.recon}".mismatch_purchases_acr AS
     SELECT DISTINCT ON (txs.credit_serial_numbers) txs.project_id,
      txs.vintage,
      txs.project_name,
      txs."user",
      txs.credit_serial_numbers,
      txs.quantity,
      txs.date_issued,
      txs.transferor,
      txs.transferee,
      txs.action,
      txs.account,
      txs.transaction_id,
      txs.id,
      txs.as_of,
      ARRAY[((txs.credit_serial_numbers ->> 'start'::text))::bigint, ((txs.credit_serial_numbers ->> 'end'::text))::bigint] AS block
    FROM "${environment.db.schema.recon}".acr_transactions txs
    WHERE ((txs.transferee = 'Rubicon Carbon Services LLC'::text) AND (txs.action = 'Confirm'::text) AND (NOT (txs.id IN ( SELECT ar2.id
            FROM (("${environment.db.schema.recon}".acr_transactions ar
              JOIN "${environment.db.schema.recon}".purchases_v2 r ON (((ar.id = r.registry_item_id) AND (r.registry = ANY (ARRAY['American Carbon Registry'::text, 'ACR'::text])))))
              JOIN "${environment.db.schema.recon}".acr_transactions ar2 ON ((((ar2.credit_serial_numbers -> 'end'::text) = (ar.credit_serial_numbers -> 'end'::text)) AND ((ar2.credit_serial_numbers -> 'start'::text) = (ar.credit_serial_numbers -> 'start'::text)) AND (replace((ar2.credit_serial_numbers ->> 'identifier'::text), ' to '::text, '-'::text) = replace((ar.credit_serial_numbers ->> 'identifier'::text), ' to '::text, '-'::text)))))))));`);

    this.addSql(`
    CREATE OR REPLACE VIEW "${environment.db.schema.recon}".mismatch_retirements_acr AS
     SELECT DISTINCT ON (acr_retirements.credit_serial_numbers) acr_retirements.status_effective,
      acr_retirements.retirement_account,
      acr_retirements.retirement_reason,
      acr_retirements.retirement_reason_details,
      acr_retirements.email_notification,
      acr_retirements.credit_serial_numbers,
      acr_retirements.vintage,
      acr_retirements.date_issued,
      acr_retirements.quantity,
      acr_retirements.verified_removal,
      acr_retirements.arb_eligible,
      acr_retirements.sdgs,
      acr_retirements.project_id,
      acr_retirements.project_name,
      acr_retirements.project_type,
      acr_retirements.project_methodology,
      acr_retirements.project_methodology_version,
      acr_retirements.corsia_eligible,
      acr_retirements.id,
      acr_retirements.as_of
    FROM "${environment.db.schema.recon}".acr_retirements
    WHERE (NOT (acr_retirements.id IN ( SELECT ar2.id
            FROM (("${environment.db.schema.recon}".acr_retirements ar
              JOIN "${environment.db.schema.recon}".retirements_v2 r ON (((ar.id = r.registry_item_id) AND (r.registry = ANY (ARRAY['American Carbon Registry'::text, 'ACR'::text])))))
              JOIN "${environment.db.schema.recon}".acr_retirements ar2 ON ((ar2.credit_serial_numbers = ar.credit_serial_numbers))))));`);

    this.addSql(`
      CREATE OR REPLACE VIEW "${environment.db.schema.recon}".mismatch_sales_acr AS
       SELECT DISTINCT ON (txs.credit_serial_numbers) txs.project_id,
        txs.vintage,
        txs.project_name,
        txs."user",
        txs.credit_serial_numbers,
        txs.quantity,
        txs.date_issued,
        txs.transferor,
        txs.transferee,
        txs.action,
        txs.account,
        txs.transaction_id,
        txs.id,
        txs.as_of,
        ARRAY[((txs.credit_serial_numbers ->> 'start'::text))::bigint, ((txs.credit_serial_numbers ->> 'end'::text))::bigint] AS block
      FROM "${environment.db.schema.recon}".acr_transactions txs
      WHERE ((txs.transferor = 'Rubicon Carbon Services LLC'::text) AND (txs.action = 'Confirm'::text) AND (NOT (txs.id IN ( SELECT ar2.id
              FROM (("${environment.db.schema.recon}".acr_transactions ar
                JOIN "${environment.db.schema.recon}".purchases_v2 r ON (((ar.id = r.registry_item_id) AND (r.registry = ANY (ARRAY['American Carbon Registry'::text, 'ACR'::text])))))
                JOIN "${environment.db.schema.recon}".acr_transactions ar2 ON ((((ar2.credit_serial_numbers -> 'end'::text) = (ar.credit_serial_numbers -> 'end'::text)) AND ((ar2.credit_serial_numbers -> 'start'::text) = (ar.credit_serial_numbers -> 'start'::text)) AND (replace((ar2.credit_serial_numbers ->> 'identifier'::text), ' to '::text, '-'::text) = replace((ar.credit_serial_numbers ->> 'identifier'::text), ' to '::text, '-'::text)))))))));`);

    this.addSql(`
      CREATE OR REPLACE VIEW "${environment.db.schema.recon}".mismatch_sales_verra AS
       SELECT txs.project_id,
    txs.vintage,
    txs.project_name,
    txs."user",
    txs.serial_number,
    txs.unit_type,
    txs.quantity,
    txs.date_issued,
    txs.transferor,
    txs.transferee,
    txs.action,
    txs.account,
    txs.transaction_id,
    txs.id,
    txs.as_of,
    ARRAY[((txs.serial_number ->> 'start'::text))::bigint, ((txs.serial_number ->> 'end'::text))::bigint] AS block,
    daterange(to_date(split_part(txs.vintage, '-'::text, 1), 'DD/MM/YYYY'::text), to_date(split_part(txs.vintage, '-'::text, 2), 'DD/MM/YYYY'::text), '[]'::text) AS "interval"
   FROM "${environment.db.schema.recon}".verra_transactions txs
  WHERE ((txs.transferor = 'Rubicon Carbon Services LLC'::text) AND (txs.action = 'Confirm'::text) AND (NOT (txs.id IN ( SELECT ar2.id
           FROM (("${environment.db.schema.recon}".verra_transactions ar
             JOIN "${environment.db.schema.recon}".purchases_v2 r ON (((ar.id = r.registry_item_id) AND (r.registry = 'Verra'::text))))
             JOIN "${environment.db.schema.recon}".verra_transactions ar2 ON ((((ar2.serial_number -> 'end'::text) = (ar.serial_number -> 'end'::text)) AND ((ar2.serial_number -> 'start'::text) = (ar.serial_number -> 'start'::text)) AND ((ar2.serial_number ->> 'identifier'::text) = (ar.serial_number ->> 'identifier'::text)))))))));
  `);

    this.addSql(`
      CREATE OR REPLACE VIEW "${environment.db.schema.recon}".mismatch_transfers_internal AS 
       SELECT transfers_internal.id,
        transfers_internal.retired,
        transfers_internal.registry,
        transfers_internal.date_started,
        transfers_internal.date_finished,
        transfers_internal.beneficiary,
        transfers_internal.memo,
        transfers_internal.transaction_key,
        transfers_internal.registry_project_id,
        transfers_internal.vintage,
        transfers_internal.name,
        transfers_internal.transaction_id,
        transfers_internal."interval",
        transfers_internal.transaction_type,
        transfers_internal.transaction_subtype
      FROM ("${environment.db.schema.recon}".transfers_internal
        LEFT JOIN "${environment.db.schema.recon}".retirements_v2 ON ((transfers_internal.id = retirements_v2.line_item_transaction_id)))
      WHERE (retirements_v2.line_item_transaction_id IS NULL);
  `);

    this.addSql(`
      CREATE OR REPLACE VIEW "${environment.db.schema.recon}".retirements_purchases AS
        SELECT p.created_at,
          p.registry,
          pi.registry_project_id,
          pi.name,
          pi.vintage,
          p.line_item_amount AS amount,
          p.registry_item_amount AS registry_amount,
          p.match,
          p.reconciled,
          p.notes,
          (p.line_item_id)::text AS line_item_id,
          t.settled_at AS settlement_date
        FROM (((("${environment.db.schema.recon}".purchases_v2 p
          JOIN "${environment.db.schema.recon}".purchases_internal pi ON ((pi.id = p.line_item_id)))
          JOIN "${environment.db.schema.rubicon}".line_items_view li ON ((pi.id = li.id)))
          JOIN "${environment.db.schema.rubicon}".asset_flows li_vf ON ((li_vf.id = li.asset_flow_id)))
          JOIN "${environment.db.schema.rubicon}".trades t ON ((t.id = li_vf.transaction_id)))
        ORDER BY t.settled_at, p.created_at;
      `);

    this.addSql(`
      CREATE OR REPLACE VIEW "${environment.db.schema.recon}".retirements_reconciliation AS
         SELECT r.created_at,
          r.registry,
          ri.registry_project_id,
          ri.name,
          ri.vintage,
          r.line_item_transaction_amount AS amount,
          r.registry_item_amount AS registry_amount,
          r.match,
          r.reconciled,
          r.notes,
          ri.transaction_key,
          ri.date_started,
          ri.date_finished,
          ri.beneficiary
        FROM ("${environment.db.schema.recon}".retirements_v2 r
          JOIN "${environment.db.schema.recon}".retirements_internal ri ON ((ri.id = r.line_item_transaction_id)))
        ORDER BY ri.date_started, ri.date_finished, r.created_at;
       `);

    // update report views
    this.addSql(`
      CREATE OR REPLACE VIEW "${environment.db.schema.reports}".superset_holdings AS
       WITH internal_prices AS (
            ( SELECT DISTINCT ON (internal_prices.project_id, internal_prices.vintage, (date_trunc('day'::text, (internal_prices."timestamp" AT TIME ZONE 'AMERICA/LOS_ANGELES'::text))))
                    CASE
                        WHEN (internal_prices.source = ANY (ARRAY['trader_override'::text, 'index_calculated'::text])) THEN 'rubicon_trader'::text
                        ELSE internal_prices.source
                    END AS source,
                internal_prices.project_id,
                internal_prices.vintage,
                date_trunc('day'::text, (internal_prices."timestamp" AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) AS "timestamp",
                internal_prices.price
              FROM "${environment.db.schema.marketData}".internal_prices
              ORDER BY internal_prices.project_id, internal_prices.vintage, (date_trunc('day'::text, (internal_prices."timestamp" AT TIME ZONE 'AMERICA/LOS_ANGELES'::text))))
            UNION
            SELECT 'rubicon_trader'::text AS source,
                p_1.registry_project_id AS project_id,
                pv_1.label AS vintage,
                '2024-04-25 00:00:00'::timestamp without time zone AS "timestamp",
                pvc.average_cost_basis AS price
              FROM (("${environment.db.schema.rubicon}".project_vintage_cost_basis_v1 pvc
                JOIN "${environment.db.schema.rubicon}".project_vintages pv_1 ON ((pv_1.id = pvc.project_vintage_id)))
                JOIN "${environment.db.schema.rubicon}".projects p_1 ON ((p_1.id = pv_1.project_id)))
            ), prices_no_gaps AS (
            SELECT dates."timestamp",
                vintages.source,
                vintages.project_id,
                vintages.vintage,
                p_1.price,
                ((jsonb_agg(p_1.price) FILTER (WHERE (p_1.price IS NOT NULL)) OVER (PARTITION BY vintages.source, vintages.project_id, vintages.vintage ORDER BY dates."timestamp" ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) ->> '-1'::integer))::numeric AS last_price
              FROM ((generate_series('2024-04-25 00:00:00+00'::timestamp with time zone, ((now())::date)::timestamp with time zone, '1 day'::interval) dates("timestamp")
                CROSS JOIN ( SELECT DISTINCT internal_prices.source,
                        internal_prices.project_id,
                        internal_prices.vintage
                      FROM internal_prices) vintages)
                LEFT JOIN internal_prices p_1 ON (((p_1."timestamp" = dates."timestamp") AND (p_1.source = vintages.source) AND (p_1.project_id = vintages.project_id) AND (p_1.vintage = vintages.vintage))))
            ), price_summary AS (
            SELECT prices_no_gaps.project_id,
                prices_no_gaps.vintage,
                sum(prices_no_gaps.last_price) FILTER (WHERE (prices_no_gaps."timestamp" = (current_setting('reports.start_date'::text))::date)) AS lagging_price,
                sum(prices_no_gaps.last_price) FILTER (WHERE (prices_no_gaps."timestamp" = (current_setting('reports.end_date'::text))::date)) AS leading_price
              FROM prices_no_gaps
              GROUP BY prices_no_gaps.project_id, prices_no_gaps.vintage
            )
    SELECT dh.holding_date,
        dh.owner_type,
        dh.asset_id,
        p.name,
        p.registry_project_id,
        pv.label,
        pt.category,
        pt.type,
        dh.asset_type,
        ps.lagging_price,
        ps.leading_price,
        sum(dh.balance) AS holdings,
        (sum(dh.balance) * (ps.lagging_price)::double precision) AS lagging_value,
        (sum(dh.balance) * (ps.leading_price)::double precision) AS leading_value
      FROM (((("${environment.db.schema.marketData}".daily_holdings dh
        LEFT JOIN "${environment.db.schema.rubicon}".project_vintages pv ON ((pv.id = (dh.asset_id)::uuid)))
        LEFT JOIN "${environment.db.schema.rubicon}".projects p ON ((p.id = pv.project_id)))
        LEFT JOIN "${environment.db.schema.rubicon}".project_types pt ON ((pt.id = p.project_type_id)))
        LEFT JOIN price_summary ps ON (((ps.vintage = pv.label) AND (ps.project_id = p.registry_project_id))))
      WHERE ((dh.owner_type <> ALL (ARRAY['registry'::text, 'external'::text, 'offsets'::text])) AND (dh.asset_type = 'registry_vintage'::text) AND (dh.holding_date = ANY (ARRAY[((current_setting('reports.start_date'::text))::date)::timestamp without time zone, ((current_setting('reports.end_date'::text))::date)::timestamp without time zone])))
      GROUP BY dh.holding_date, dh.owner_type, dh.asset_id, p.name, p.registry_project_id, pv.label, pt.category, pt.type, dh.asset_type, ps.lagging_price, ps.leading_price
      ORDER BY dh.holding_date DESC, (sum(dh.balance)) DESC;`);

    this.addSql(`
      CREATE OR REPLACE VIEW "${environment.db.schema.reports}".superset_holdings_settled AS
       WITH internal_prices AS (
            ( SELECT DISTINCT ON (internal_prices.project_id, internal_prices.vintage, (date_trunc('day'::text, (internal_prices."timestamp" AT TIME ZONE 'AMERICA/LOS_ANGELES'::text))))
                    CASE
                        WHEN (internal_prices.source = ANY (ARRAY['trader_override'::text, 'index_calculated'::text])) THEN 'rubicon_trader'::text
                        ELSE internal_prices.source
                    END AS source,
                internal_prices.project_id,
                internal_prices.vintage,
                date_trunc('day'::text, (internal_prices."timestamp" AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) AS "timestamp",
                internal_prices.price
              FROM "${environment.db.schema.marketData}".internal_prices
              ORDER BY internal_prices.project_id, internal_prices.vintage, (date_trunc('day'::text, (internal_prices."timestamp" AT TIME ZONE 'AMERICA/LOS_ANGELES'::text))))
            UNION
            SELECT 'rubicon_trader'::text AS source,
                p_1.registry_project_id AS project_id,
                pv_1.label AS vintage,
                '2024-04-25 00:00:00'::timestamp without time zone AS "timestamp",
                pvc.average_cost_basis AS price
              FROM (("${environment.db.schema.rubicon}".project_vintage_cost_basis_v1 pvc
                JOIN "${environment.db.schema.rubicon}".project_vintages pv_1 ON ((pv_1.id = pvc.project_vintage_id)))
                JOIN "${environment.db.schema.rubicon}".projects p_1 ON ((p_1.id = pv_1.project_id)))
            ), prices_no_gaps AS (
            SELECT dates."timestamp",
                vintages.source,
                vintages.project_id,
                vintages.vintage,
                p_1.price,
                ((jsonb_agg(p_1.price) FILTER (WHERE (p_1.price IS NOT NULL)) OVER (PARTITION BY vintages.source, vintages.project_id, vintages.vintage ORDER BY dates."timestamp" ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) ->> '-1'::integer))::numeric AS last_price
              FROM ((generate_series('2024-04-25 00:00:00+00'::timestamp with time zone, ((now())::date)::timestamp with time zone, '1 day'::interval) dates("timestamp")
                CROSS JOIN ( SELECT DISTINCT internal_prices.source,
                        internal_prices.project_id,
                        internal_prices.vintage
                      FROM internal_prices) vintages)
                LEFT JOIN internal_prices p_1 ON (((p_1."timestamp" = dates."timestamp") AND (p_1.source = vintages.source) AND (p_1.project_id = vintages.project_id) AND (p_1.vintage = vintages.vintage))))
            ), settled_trades AS (
         SELECT p_1.registry_project_id,
            pv_1.label,
            date_trunc('day'::text, (t.settled_at AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) AS settled_at,
            pn.last_price,
            sum(t.amount) FILTER (WHERE (t.type = 'buy'::text)) AS amount_bought,
            sum(t.amount) FILTER (WHERE (t.type = 'sell'::text)) AS amount_sold
           FROM "${environment.db.schema.rubicon}".trades t
             JOIN "${environment.db.schema.rubicon}".asset_flows vf ON (vf.transaction_id = t.id)
             JOIN "${environment.db.schema.rubicon}".project_vintages pv_1 ON (pv_1.id = vf.asset_id)
             JOIN "${environment.db.schema.rubicon}".projects p_1 ON (p_1.id = pv_1.project_id)
             LEFT JOIN prices_no_gaps pn ON (pn.project_id = p_1.registry_project_id AND pn.vintage = pv_1.label AND pn."timestamp" = date_trunc('day'::text, (t.settled_at AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)))
          WHERE t.status = 'settled'
            AND date_trunc('day'::text, (t.settled_at AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) >= (current_setting('reports.start_date'::text))::date
            AND date_trunc('day'::text, (t.settled_at AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) <= (current_setting('reports.end_date'::text))::date
          GROUP BY p_1.registry_project_id, pv_1.label, date_trunc('day'::text, (t.settled_at AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)), pn.last_price
        ), settled_retirements AS (
         SELECT p_1.registry_project_id,
            pv_1.label,
            date_trunc('day'::text, t.date_finished) AS settled_at,
            pn.last_price,
            sum(vf.amount) AS amount_retired
           FROM "${environment.db.schema.rubicon}".retirements t
             JOIN "${environment.db.schema.rubicon}".asset_flows vf ON (vf.transaction_id = t.id)
             JOIN "${environment.db.schema.rubicon}".project_vintages pv_1 ON (pv_1.id = vf.asset_id)
             JOIN "${environment.db.schema.rubicon}".projects p_1 ON (p_1.id = pv_1.project_id)
             LEFT JOIN prices_no_gaps pn ON (pn.project_id = p_1.registry_project_id AND pn.vintage = pv_1.label AND pn."timestamp" = date_trunc('day'::text, t.date_finished))
          WHERE t.type = 'retirement'
            AND t.status = 'completed'
            AND date_trunc('day'::text, (t.date_finished AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) >= (current_setting('reports.start_date'::text))::date
            AND date_trunc('day'::text, (t.date_finished AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) <= (current_setting('reports.end_date'::text))::date
          GROUP BY p_1.registry_project_id, pv_1.label, date_trunc('day'::text, t.date_finished), pn.last_price
        ), settled_transfers AS (
         SELECT p_1.registry_project_id,
            pv_1.label,
            date_trunc('day'::text, t.date_finished) AS settled_at,
            pn.last_price,
            sum(vf.amount) AS amount_transfered
           FROM "${environment.db.schema.rubicon}".retirements t
             JOIN "${environment.db.schema.rubicon}".asset_flows vf ON (vf.transaction_id = t.id)
             JOIN "${environment.db.schema.rubicon}".project_vintages pv_1 ON (pv_1.id = vf.asset_id)
             JOIN "${environment.db.schema.rubicon}".projects p_1 ON (p_1.id = pv_1.project_id)
             LEFT JOIN prices_no_gaps pn ON (pn.project_id = p_1.registry_project_id AND pn.vintage = pv_1.label AND pn."timestamp" = date_trunc('day'::text, t.date_finished))
          WHERE t.type = 'transfer_outflow'
            AND t.status = 'completed'
            AND date_trunc('day'::text, (t.date_finished AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) >= (current_setting('reports.start_date'::text))::date
            AND date_trunc('day'::text, (t.date_finished AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) <= (current_setting('reports.end_date'::text))::date
          GROUP BY p_1.registry_project_id, pv_1.label, date_trunc('day'::text, t.date_finished), pn.last_price
        )
 SELECT pt.category,
    pt.type,
    sum(st.amount_bought) AS amount_bought,
    sum((st.amount_bought)::numeric * st.last_price) AS value_bought,
    sum(st.amount_sold) AS amount_sold,
    sum((st.amount_sold)::numeric * st.last_price) AS value_sold,
    sum(sr.amount_retired) AS amount_retired,
    sum((sr.amount_retired)::numeric * sr.last_price) AS value_retired,
    sum(str.amount_transfered) AS amount_transfered,
    sum((str.amount_transfered)::numeric * str.last_price) AS value_transfered
   FROM "${environment.db.schema.rubicon}".projects p
     JOIN "${environment.db.schema.rubicon}".project_vintages pv ON (pv.project_id = p.id)
     JOIN "${environment.db.schema.rubicon}".project_types pt ON (pt.id = p.project_type_id)
     LEFT JOIN settled_trades st ON (st.registry_project_id = p.registry_project_id AND st.label = pv.label)
     LEFT JOIN settled_retirements sr ON (sr.registry_project_id = p.registry_project_id AND sr.label = pv.label)
     LEFT JOIN settled_transfers str ON (str.registry_project_id = p.registry_project_id AND str.label = pv.label)
  GROUP BY pt.category, pt.type;
      `);
    this.addSql(`DROP VIEW IF EXISTS "${environment.db.schema.reports}".finance_trades;`);
    this.addSql(`
      CREATE OR REPLACE VIEW "${environment.db.schema.reports}".finance_trades AS
      with line_items AS (
            SELECT asset_flows_1.transaction_id,
                json_object_agg(t.ui_key, abs(cf_o.amount)) AS line_item_amounts,
                json_object_agg(t.ui_key, t.settled_at::date) AS line_item_dates,
                cf_i.project_vintage_id
              FROM "${environment.db.schema.rubicon}".asset_flows asset_flows_1
                JOIN "${environment.db.schema.rubicon}".credit_flows cf_o ON cf_o.asset_flow_id = asset_flows_1.id
                JOIN "${environment.db.schema.rubicon}".credit_flows cf_i ON cf_i.id = cf_o.linked_credit_flow_id
                JOIN "${environment.db.schema.rubicon}".asset_flows af_i ON af_i.id = cf_i.asset_flow_id
                JOIN "${environment.db.schema.rubicon}".trades t ON t.id = af_i.transaction_id
              GROUP BY asset_flows_1.id, cf_i.project_vintage_id
            ),
            internal_prices AS (
            ( SELECT DISTINCT ON (internal_prices.project_id, internal_prices.vintage, (date_trunc('day'::text, (internal_prices."timestamp" AT TIME ZONE 'AMERICA/LOS_ANGELES'::text))))
                    CASE
                        WHEN internal_prices.source = ANY (ARRAY['trader_override'::text, 'index_calculated'::text]) THEN 'rubicon_trader'::text
                        ELSE internal_prices.source
                    END AS source,
                internal_prices.project_id,
                internal_prices.vintage,
                date_trunc('day'::text, (internal_prices."timestamp" AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) AS "timestamp",
                internal_prices.price
              FROM "${environment.db.schema.marketData}".internal_prices
              ORDER BY internal_prices.project_id, internal_prices.vintage, (date_trunc('day'::text, (internal_prices."timestamp" AT TIME ZONE 'AMERICA/LOS_ANGELES'::text))))
            UNION
            SELECT 'rubicon_trader'::text AS source,
                project_vintage_prices_v2.registry_project_id AS project_id,
                project_vintage_prices_v2.vintage,
                '2024-04-25 00:00:00'::timestamp without time zone AS "timestamp",
                project_vintage_prices_v2.price
              FROM "${environment.db.schema.rubicon}".project_vintage_prices_v2
              WHERE project_vintage_prices_v2.source = 'cost_basis'::text
            ), prices_no_gaps AS (
            SELECT dates."timestamp",
                vintages.source,
                vintages.project_id,
                vintages.vintage,
                p_1.price,
                (jsonb_agg(p_1.price) FILTER (WHERE p_1.price IS NOT NULL) OVER (PARTITION BY vintages.source, vintages.project_id, vintages.vintage ORDER BY dates."timestamp" ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) ->> '-1'::integer)::numeric AS last_price
              FROM generate_series('2024-04-25 00:00:00+00'::timestamp with time zone, now()::date::timestamp with time zone, '1 day'::interval) dates("timestamp")
                CROSS JOIN ( SELECT DISTINCT internal_prices.source,
                        internal_prices.project_id,
                        internal_prices.vintage
                      FROM internal_prices) vintages
                LEFT JOIN internal_prices p_1 ON p_1."timestamp" = dates."timestamp" AND p_1.source = vintages.source AND p_1.project_id = vintages.project_id AND p_1.vintage = vintages.vintage
            )
    SELECT t.ui_key AS trade_key,
        p.registry_project_id,
        p.name,
        pv.label,
        t.amount,
        sum(COALESCE(asset_flows.raw_price, 0::numeric) + COALESCE(asset_flows.service_fee, 0::numeric) + COALESCE(asset_flows.other_fee, 0::numeric)) FILTER (WHERE cf_o.type <> 'accounting'::text) / count(*) FILTER (WHERE cf_o.type <> 'accounting'::text)::numeric AS total_cost_buy,
        sum(COALESCE(asset_flows.raw_price, 0::numeric) - COALESCE(asset_flows.service_fee, 0::numeric) - COALESCE(asset_flows.other_fee, 0::numeric)) FILTER (WHERE cf_o.type <> 'accounting'::text) / count(*) FILTER (WHERE cf_o.type <> 'accounting'::text)::numeric AS total_cost_sell,
        sum(COALESCE(asset_flows.raw_price, 0::numeric) + COALESCE(asset_flows.service_fee, 0::numeric) + COALESCE(asset_flows.other_fee, 0::numeric)) FILTER (WHERE cf_o.type <> 'accounting'::text) / t.amount::numeric / count(*) FILTER (WHERE cf_o.type <> 'accounting'::text)::numeric AS unit_cost_buy,
        sum(COALESCE(asset_flows.raw_price, 0::numeric) - COALESCE(asset_flows.service_fee, 0::numeric) - COALESCE(asset_flows.other_fee, 0::numeric)) FILTER (WHERE cf_o.type <> 'accounting'::text) / t.amount::numeric / count(*) FILTER (WHERE cf_o.type <> 'accounting'::text)::numeric AS unit_cost_sell,
        sum(asset_flows.raw_price) FILTER (WHERE cf_o.type <> 'accounting'::text) / count(*) FILTER (WHERE cf_o.type <> 'accounting'::text)::numeric AS raw_price,
        sum(asset_flows.service_fee) FILTER (WHERE cf_o.type <> 'accounting'::text) / count(*) FILTER (WHERE cf_o.type <> 'accounting'::text)::numeric AS service_fee,
        sum(asset_flows.other_fee) FILTER (WHERE cf_o.type <> 'accounting'::text) / count(*) FILTER (WHERE cf_o.type <> 'accounting'::text)::numeric AS other_fee,
        t.settled_at,
        t.created_at,
        t.type,
        t.status,
        t.memo,
        o.name AS counterparty_name,
        books.name AS book_name,
        pt.category AS project_category,
        pt.type AS project_type,
        line_items.line_item_amounts::text,
        line_items.line_item_dates::text,
        sum(cf_o.amount::numeric * cb.cost_basis * '-1'::integer::numeric) AS cost_basis,
        sum(cf_o.amount::numeric * orig_cb.cost_basis * '-1'::integer::numeric) AS orig_cost_basis,
        png.last_price,
        tsl.timestamp as executed_at
      FROM "${environment.db.schema.rubicon}".trades t
        LEFT JOIN "${environment.db.schema.rubicon}".books ON books.id = t.book_id
        LEFT JOIN "${environment.db.schema.rubicon}".asset_flows ON asset_flows.transaction_id = t.id
        LEFT JOIN "${environment.db.schema.rubicon}".credit_flows cf_o ON cf_o.asset_flow_id = asset_flows.id
        LEFT JOIN "${environment.db.schema.rubicon}".credit_flows cf_i ON cf_i.id = cf_o.linked_credit_flow_id
        LEFT JOIN "${environment.db.schema.rubicon}".asset_flows li_vf ON li_vf.id = cf_i.asset_flow_id
        LEFT JOIN "${environment.db.schema.rubicon}".trades li_t ON li_t.id = li_vf.transaction_id
        LEFT JOIN "${environment.db.schema.rubicon}".trade_counterparties tc ON tc.trade_id = t.id AND tc.is_primary = true
        LEFT JOIN "${environment.db.schema.rubicon}".counterparties c ON tc.counterparty_id = c.id
        LEFT JOIN "${environment.db.schema.rubicon}".organizations o ON o.id = c.organization_id
        left join "${environment.db.schema.rubicon}".transaction_status_logs tsl on tsl.transaction_id = t.id
        LEFT JOIN LATERAL ( SELECT cf_i.id,
                credit_historical_cost_basis_v1.cost_basis,
                credit_historical_cost_basis_v1.cost_basis_raw_price,
                credit_historical_cost_basis_v1.cost_basis_service_fee,
                credit_historical_cost_basis_v1.cost_basis_other_fee
              FROM "${environment.db.schema.rubicon}".credit_historical_cost_basis_v1
              WHERE credit_historical_cost_basis_v1.credit_inflow_id = cf_i.id AND credit_historical_cost_basis_v1.settled_at <= tsl.timestamp
              ORDER BY credit_historical_cost_basis_v1.settled_at DESC
            LIMIT 1) cb ON cb.id = cf_i.id
        left join "${environment.db.schema.rubicon}".credit_historical_cost_basis_v1 orig_cb on orig_cb.credit_flow_id = cf_i.id
        LEFT JOIN "${environment.db.schema.rubicon}".project_vintages pv ON pv.id = asset_flows.asset_id
        left join line_items on line_items.project_vintage_id = pv.id AND line_items.transaction_id = asset_flows.transaction_id
        LEFT JOIN "${environment.db.schema.rubicon}".projects p ON p.id = pv.project_id
        LEFT JOIN "${environment.db.schema.rubicon}".project_types pt ON pt.id = p.project_type_id
        left join prices_no_gaps png on png.project_id = p.registry_project_id and png.vintage = pv.label and png.timestamp::date = tsl.timestamp::date
      GROUP BY t.ui_key, p.registry_project_id, p.name, pv.label, t.amount, t.settled_at, t.created_at, t.type, t.status, t.memo, o.name, books.name, pt.category, pt.type, line_items.line_item_amounts::text, line_items.line_item_dates::text, last_price, tsl.timestamp
    UNION
    SELECT forwards.ui_key AS trade_key,
        p.registry_project_id,
        p.name,
        pv.label,
        fli.expected_amount AS amount,
        COALESCE(fli.raw_price, 0::numeric) + COALESCE(fli.service_fee, 0::numeric) + COALESCE(fli.other_fee, 0::numeric) AS total_cost_buy,
        NULL::double precision AS total_cost_sell,
        (COALESCE(fli.raw_price, 0::numeric) + COALESCE(fli.service_fee, 0::numeric) + COALESCE(fli.other_fee, 0::numeric)) / fli.expected_amount::numeric AS unit_cost_buy,
        NULL::double precision AS unit_cost_sell,
        fli.raw_price,
        fli.service_fee,
        fli.other_fee,
        fli.last_updated_delivery_date AS settled_at,
        fli.created_at,
        concat('forward_', forwards.type) AS type,
        fli.status,
        o.name AS memo,
        o.name AS counterparty_name,
        NULL::text AS book_name,
        pt.category AS project_category,
        pt.type AS project_type,
        NULL::text AS line_item_amounts,
        NULL::text AS line_item_dates,
        NULL::numeric AS cost_basis,
        NULL::numeric AS orig_cost_basis,
        NULL::numeric AS last_price,
        NULL::timestamp AS executed_at
      FROM "${environment.db.schema.rubicon}".forwards
        JOIN "${environment.db.schema.rubicon}".forward_line_items fli ON fli.forward_id = forwards.id
        LEFT JOIN "${environment.db.schema.rubicon}".counterparties c ON c.id = forwards.counterparty_id
        LEFT JOIN "${environment.db.schema.rubicon}".organizations o ON o.id = c.organization_id
        LEFT JOIN "${environment.db.schema.rubicon}".asset_flows af ON af.id = fli.asset_flow_id
        LEFT JOIN "${environment.db.schema.rubicon}".project_vintages pv ON pv.id = fli.project_vintage_id
        LEFT JOIN "${environment.db.schema.rubicon}".projects p ON p.id = forwards.project_id
        LEFT JOIN "${environment.db.schema.rubicon}".project_types pt ON pt.id = p.project_type_id
      WHERE fli.status = 'pending'::text;
      `);

    this.addSql(`DROP VIEW IF EXISTS "${environment.db.schema.reports}".finance_confirmed_sales;`);
    this.addSql(`
      CREATE OR REPLACE VIEW "${environment.db.schema.reports}".finance_confirmed_sales AS
      WITH internal_prices AS (
          (
              SELECT DISTINCT ON (
                  internal_prices.project_id, 
                  internal_prices.vintage, 
                  (date_trunc('day'::text, (internal_prices."timestamp" AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)))
              )
                  CASE
                      WHEN internal_prices.source = ANY (ARRAY['trader_override'::text, 'index_calculated'::text]) 
                      THEN 'rubicon_trader'::text
                      ELSE internal_prices.source
                  END AS source,
                  internal_prices.project_id,
                  internal_prices.vintage,
                  date_trunc('day'::text, (internal_prices."timestamp" AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) AS "timestamp",
                  internal_prices.price
              FROM "${environment.db.schema.marketData}".internal_prices
              ORDER BY 
                  internal_prices.project_id, 
                  internal_prices.vintage, 
                  (date_trunc('day'::text, (internal_prices."timestamp" AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)))
          )
          UNION
          SELECT 
              'rubicon_trader'::text AS source,
              project_vintage_prices_v2.registry_project_id AS project_id,
              project_vintage_prices_v2.vintage,
              '2024-04-25 00:00:00'::timestamp without time zone AS "timestamp",
              project_vintage_prices_v2.price
          FROM "${environment.db.schema.rubicon}".project_vintage_prices_v2
          WHERE project_vintage_prices_v2.source = 'cost_basis'::text
      ),
      prices_no_gaps AS (
          SELECT 
              dates."timestamp",
              vintages.source,
              vintages.project_id,
              vintages.vintage,
              p_1.price,
              (
                  jsonb_agg(p_1.price) FILTER (WHERE p_1.price IS NOT NULL) 
                  OVER (
                      PARTITION BY vintages.source, vintages.project_id, vintages.vintage 
                      ORDER BY dates."timestamp" 
                      ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
                  ) ->> '-1'::integer
              )::numeric AS last_price
          FROM generate_series(
              '2024-04-25 00:00:00+00'::timestamp with time zone, 
              now()::date::timestamp with time zone, 
              '1 day'::interval
          ) dates("timestamp")
          CROSS JOIN (
              SELECT DISTINCT 
                  internal_prices.source,
                  internal_prices.project_id,
                  internal_prices.vintage
              FROM internal_prices
          ) vintages
          LEFT JOIN internal_prices p_1 ON 
              p_1."timestamp" = dates."timestamp" 
              AND p_1.source = vintages.source 
              AND p_1.project_id = vintages.project_id 
              AND p_1.vintage = vintages.vintage
      )
      SELECT 
          customer_portfolios.salesforce_identifier AS salesforce_id,
          organizations.id::text AS internal_id,
          organizations.name AS customer_name,
          purchases.ui_key AS transaction_id,
          purchases.flow_type AS sale_type,
          source_books.name AS basket,
          purchases.amount AS rcts,
          tsl.timestamp AS executed_at,
          purchases.date_started AS create_date,
          purchases.date_finished AS complete_date,
          sum(af.raw_price) AS purchase_price,
          purchases.created_at,
          purchases.needs_risk_adjustment,
          coalesce(
              sum(combined_cb.executed_avg_cost_basis * rct_comp.percentage_of_total), 
              sum(combined_cb.executed_avg_cost_basis)
          ) AS executed_cb_weighted,
          coalesce(
              sum(combined_cb.original_avg_cost_basis * rct_comp.percentage_of_total), 
              sum(combined_cb.original_avg_cost_basis)
          ) AS original_cb_weighted,
          coalesce(
              sum(png.last_price * rct_comp.percentage_of_total), 
              sum(png.last_price)
          ) AS weighted_mtm
      FROM "${environment.db.schema.rubicon}".purchases
      LEFT JOIN "${environment.db.schema.rubicon}".books ON books.id = purchases.customer_portfolio_id
      LEFT JOIN "${environment.db.schema.rubicon}".customer_portfolios ON customer_portfolios.id = books.id
      LEFT JOIN "${environment.db.schema.rubicon}".organizations ON organizations.id = books.organization_id
      LEFT JOIN "${environment.db.schema.rubicon}".asset_flows af ON af.transaction_id = purchases.id
      LEFT JOIN "${environment.db.schema.rubicon}".transaction_status_logs tsl ON tsl.transaction_id = purchases.id
      LEFT JOIN LATERAL (
          SELECT 
              owner_id, 
              asset_type, 
              asset_id, 
              SUM(amount) AS total_amount,
              COALESCE(
                  ROUND(
                      (SUM(amount)) / NULLIF(SUM(SUM(amount)) OVER (), 0), 
                      4
                  ), 
                  0
              ) AS percentage_of_total
          FROM "${environment.db.schema.ledger}".settled_entries 
          WHERE owner_id = af.asset_id
              AND executed_at < tsl.timestamp
          GROUP BY owner_id, asset_type, asset_id 
          HAVING SUM(amount) > 0
      ) rct_comp ON owner_id = af.asset_id
      LEFT JOIN LATERAL (
          WITH all_cb AS (
              SELECT DISTINCT ON (project_vintage_id, credit_inflow_id) 
                  project_vintage_id, 
                  credit_inflow_id, 
                  credits, 
                  cost_basis
              FROM "${environment.db.schema.rubicon}".credit_historical_cost_basis_v1
              WHERE settled_at < tsl.timestamp 
                  AND project_vintage_id = coalesce(rct_comp.asset_id, af.asset_id)
              ORDER BY project_vintage_id, credit_inflow_id, settled_at DESC
          ),
          positive_credits AS (
              SELECT credit_inflow_id 
              FROM all_cb 
              WHERE credits > 0
          )
          SELECT 
              project_vintage_id,
              SUM(CASE WHEN credits > 0 THEN credits * cost_basis ELSE 0 END) / 
              NULLIF(SUM(CASE WHEN credits > 0 THEN credits ELSE 0 END), 0) AS executed_avg_cost_basis, 
              (SELECT 
                  SUM(credits * cost_basis) / NULLIF(SUM(credits), 0)
              FROM "${environment.db.schema.rubicon}".credit_historical_cost_basis_v1 cb2
              WHERE cb2.project_vintage_id = all_cb.project_vintage_id
                AND cb2.credit_flow_id IN (SELECT credit_inflow_id FROM positive_credits)
              ) AS original_avg_cost_basis
              
          FROM all_cb
          GROUP BY project_vintage_id
      ) combined_cb ON combined_cb.project_vintage_id = coalesce(rct_comp.asset_id, af.asset_id)
      LEFT JOIN "${environment.db.schema.rubicon}".books source_books ON source_books.id = af.source_id
      LEFT JOIN "${environment.db.schema.rubicon}".project_vintages pv ON pv.id = coalesce(rct_comp.asset_id, af.asset_id)
      LEFT JOIN "${environment.db.schema.rubicon}".projects p ON p.id = pv.project_id
      LEFT JOIN prices_no_gaps png ON 
          png.project_id = p.registry_project_id 
          AND png.vintage = pv.label 
          AND png.timestamp::date = tsl.timestamp::date
      WHERE purchases.status = 'settled'::text 
          AND (organizations.id <> ALL (ARRAY[
              'ce9895ff-41bc-4a66-8d4d-6a1102553836'::uuid, 
              'c9d79655-209e-4c6b-ac04-91dffac23a8a'::uuid
          ]))
      GROUP BY 
          customer_portfolios.salesforce_identifier, 
          (organizations.id::text), 
          organizations.name, 
          purchases.ui_key, 
          purchases.flow_type, 
          source_books.name, 
          purchases.amount, 
          tsl.timestamp, 
          purchases.date_started, 
          purchases.date_finished, 
          purchases.created_at, 
          purchases.needs_risk_adjustment
      ORDER BY create_date DESC
      `);
  }
}
