import { Migration } from '@mikro-orm/migrations';
import { environment } from '@env/environment';
import {
  AssetFlowStatus,
  AssetType,
  BookType,
  CreditFlowType,
  LedgerBalanceType,
  OwnerType,
  RetirementType,
  TradeType,
  TransactionType,
} from '@rubiconcarbon/shared-types';

// creates rubicon views, must keep even during migration cleanup
// condensed rubicon schema views 2024.07.25 by irene
// condensed rubicon schema views 2025.01.08 by irene
// condensed rubicon schema views 2025.04.28 by irene
// notes : replace "rubicon" with "${environment.db.schema.rubicon}" as well as other schemas
export class Migration20240404152831 extends Migration {
  async up(): Promise<void> {
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".asset_composition_v2;`);
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".asset_details_v1;`);
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".asset_holdings_v1;`); // not in prod 2025.04.28
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".asset_holdings_v2;`);
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".asset_prices_v5;`);
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".credit_amounts_v1;`);
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".credit_amounts_v2;`);
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".credit_amounts_v2_check;`);
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".credit_historical_cost_basis_v1;`);
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".line_item_amounts_v2;`); // not in prod 2025.04.28
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".line_item_transactions_views;`);
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".line_item_views;`);
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".portfolio_allocations_v2;`);
    this.addSql(
      `drop view if exists "${environment.db.schema.rubicon}".portfolio_completed_transactions_by_quarter_v2;`,
    );
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".project_flags_v2;`);
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".project_vintage_amounts_v3;`);
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".project_vintage_cost_basis_v1;`);
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".project_vintage_groupings_v1;`);
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".project_vintage_historical_cost_basis_v1;`);
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".project_vintage_prices_v2;`);
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".transaction_assets_v1;`);
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".transactions_details_v2;`);
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".transactions_v1;`); // deprecated
    this.addSql(`drop view if exists "${environment.db.schema.rubicon}".transactions_v1_OLD;`); // deprecated

    // asset_details_v1
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".asset_details_v1
        AS
        SELECT b.id,
            a.asset_type,
            b.name,
            NULL::uuid AS project_id,
            NULL::text AS registry_project_id,
            NULL::text AS label,
                CASE
                    WHEN b.type = '${BookType.RCT_PUBLIC}'::text THEN true
                    ELSE false
                END AS is_public,
            b.organization_id AS limited_organization_id
          FROM "${environment.db.schema.rubicon}".books b
            JOIN "${environment.db.schema.rubicon}".assets a ON a.id = b.id
          WHERE b.asset_type = '${AssetType.RCT}'::text
        UNION ALL
        SELECT pv.id,
            a.asset_type,
            p.name,
            p.id AS project_id,
            p.registry_project_id,
            pv.label,
            true AS is_public,
            NULL::uuid AS limited_organization_id
          FROM "${environment.db.schema.rubicon}".project_vintages pv
            JOIN "${environment.db.schema.rubicon}".projects p ON p.id = pv.project_id
            JOIN "${environment.db.schema.rubicon}".assets a ON a.id = pv.id;`,
    );

    // credit_amounts_v2
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".credit_amounts_v2
        AS
        WITH max_settlements AS (
                SELECT COALESCE(cf_1.linked_credit_flow_id, cf_1.id) AS id,
                    max(asset_flows.settled_at) AS max_settled_at
                  FROM "${environment.db.schema.rubicon}".credit_flows cf_1
                    JOIN "${environment.db.schema.rubicon}".asset_flows ON asset_flows.id = cf_1.asset_flow_id
                  WHERE (cf_1.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text])) AND asset_flows.status = '${AssetFlowStatus.SETTLED}'::text
                  GROUP BY (COALESCE(cf_1.linked_credit_flow_id, cf_1.id))
                )
        SELECT COALESCE(cf.linked_credit_flow_id, cf.id) AS id,
            - COALESCE(sum(
                CASE
                    WHEN af.transaction_type = '${TransactionType.RETIREMENT}'::text AND af.transaction_subtype = '${RetirementType.RETIREMENT}'::text AND af.status = '${AssetFlowStatus.SETTLED}'::text THEN cf.amount
                    ELSE 0
                END), 0::bigint)::integer AS amount_retired,
            - COALESCE(sum(
                CASE
                    WHEN af.transaction_type = '${TransactionType.RETIREMENT}'::text AND af.transaction_subtype = '${RetirementType.RETIREMENT}'::text AND af.status = '${AssetFlowStatus.PENDING}'::text THEN cf.amount
                    ELSE 0
                END), 0::bigint)::integer AS amount_pending_retire,
            - COALESCE(sum(
                CASE
                    WHEN af.transaction_type = '${TransactionType.TRADE}'::text AND af.transaction_subtype = '${TradeType.SELL}'::text AND af.status = '${AssetFlowStatus.SETTLED}'::text THEN cf.amount
                    ELSE 0
                END), 0::bigint)::integer AS amount_sold,
            - COALESCE(sum(
                CASE
                    WHEN af.transaction_type = '${TransactionType.TRADE}'::text AND af.transaction_subtype = '${TradeType.SELL}'::text AND af.status = '${AssetFlowStatus.PENDING}'::text THEN cf.amount
                    ELSE 0
                END), 0::bigint)::integer AS amount_pending_sell,
            - COALESCE(sum(
                CASE
                    WHEN af.transaction_type = '${TransactionType.RETIREMENT}'::text AND af.transaction_subtype = '${RetirementType.TRANSFER_OUTFLOW}'::text AND af.status = '${AssetFlowStatus.SETTLED}'::text THEN cf.amount
                    ELSE 0
                END), 0::bigint)::integer AS amount_customer_transferred_outflow,
            - COALESCE(sum(
                CASE
                    WHEN af.transaction_type = '${TransactionType.RETIREMENT}'::text AND af.transaction_subtype = '${RetirementType.TRANSFER_OUTFLOW}'::text AND af.status = '${AssetFlowStatus.PENDING}'::text THEN cf.amount
                    ELSE 0
                END), 0::bigint)::integer AS amount_pending_customer_transfer_outflow,
            COALESCE(sum(
                CASE
                    WHEN cf.type = '${CreditFlowType.INFLOW}'::text THEN cf.amount
                    ELSE 0
                END), 0::bigint) AS total_inflows,
            - COALESCE(sum(
                CASE
                    WHEN cf.type <> '${CreditFlowType.INFLOW}'::text THEN cf.amount
                    ELSE 0
                END), 0::bigint) AS total_outflows,
            COALESCE(sum(cf.amount * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer), 0::bigint) AS inventory,
            sum(cf.amount * (af.settled_at <= max_settlements.max_settled_at)::integer * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer) AS cost_basis_credits,
            sum((COALESCE(af.raw_price, 0::numeric) + COALESCE(af.service_fee, 0::numeric) + COALESCE(af.other_fee, 0::numeric)) * (af.settled_at <= max_settlements.max_settled_at)::integer::numeric * (cf.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer::numeric) AS cost_basis_total_amount,
            sum((COALESCE(af.raw_price, 0::numeric) + COALESCE(af.service_fee, 0::numeric) + COALESCE(af.other_fee, 0::numeric)) * (af.settled_at <= max_settlements.max_settled_at)::integer::numeric * (cf.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer::numeric) / NULLIF(sum(cf.amount * (af.settled_at <= max_settlements.max_settled_at)::integer * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer), 0)::numeric AS cost_basis,
            sum(COALESCE(af.raw_price, 0::numeric) * (af.settled_at <= max_settlements.max_settled_at)::integer::numeric * (cf.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer::numeric) AS cost_raw_price,
            sum(COALESCE(af.raw_price, 0::numeric) * (af.settled_at <= max_settlements.max_settled_at)::integer::numeric * (cf.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer::numeric) / NULLIF(sum(cf.amount * (af.settled_at <= max_settlements.max_settled_at)::integer * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer), 0)::numeric AS cost_basis_raw_price,
            sum(COALESCE(af.service_fee, 0::numeric) * (af.settled_at <= max_settlements.max_settled_at)::integer::numeric * (cf.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer::numeric) AS cost_service_fee,
            sum(COALESCE(af.service_fee, 0::numeric) * (af.settled_at <= max_settlements.max_settled_at)::integer::numeric * (cf.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer::numeric) / NULLIF(sum(cf.amount * (af.settled_at <= max_settlements.max_settled_at)::integer * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer), 0)::numeric AS cost_basis_service_fee,
            sum(COALESCE(af.other_fee, 0::numeric) * (af.settled_at <= max_settlements.max_settled_at)::integer::numeric * (cf.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer::numeric) AS cost_other_fee,
            sum(COALESCE(af.other_fee, 0::numeric) * (af.settled_at <= max_settlements.max_settled_at)::integer::numeric * (cf.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer::numeric) / NULLIF(sum(cf.amount * (af.settled_at <= max_settlements.max_settled_at)::integer * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer), 0)::numeric AS cost_basis_other_fee
          FROM "${environment.db.schema.rubicon}".credit_flows cf
            JOIN "${environment.db.schema.rubicon}".asset_flows af ON af.id = cf.asset_flow_id
            JOIN max_settlements ON max_settlements.id = COALESCE(cf.linked_credit_flow_id, cf.id)
          GROUP BY (COALESCE(cf.linked_credit_flow_id, cf.id));`,
    );

    // credit_amounts_v2_check
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".credit_amounts_v2_check
        AS
        WITH max_settlements AS (
                SELECT COALESCE(cf_1.linked_credit_flow_id, cf_1.id) AS id,
                    max(asset_flows.settled_at) AS max_settled_at
                  FROM "${environment.db.schema.rubicon}".credit_flows cf_1
                    JOIN "${environment.db.schema.rubicon}".asset_flows ON asset_flows.id = cf_1.asset_flow_id
                  WHERE (cf_1.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text])) AND asset_flows.status = '${AssetFlowStatus.SETTLED}'::text
                  GROUP BY (COALESCE(cf_1.linked_credit_flow_id, cf_1.id))
                )
        SELECT COALESCE(cf.linked_credit_flow_id, cf.id) AS id,
            - COALESCE(sum(
                CASE
                    WHEN af.transaction_type = '${TransactionType.RETIREMENT}'::text AND af.transaction_subtype = '${RetirementType.RETIREMENT}'::text AND af.status = '${AssetFlowStatus.SETTLED}'::text THEN cf.amount
                    ELSE 0
                END), 0::bigint)::integer AS amount_retired,
            - COALESCE(sum(
                CASE
                    WHEN af.transaction_type = '${TransactionType.RETIREMENT}'::text AND af.transaction_subtype = '${RetirementType.RETIREMENT}'::text AND af.status = '${AssetFlowStatus.PENDING}'::text THEN cf.amount
                    ELSE 0
                END), 0::bigint)::integer AS amount_pending_retire,
            - COALESCE(sum(
                CASE
                    WHEN af.transaction_type = '${TransactionType.TRADE}'::text AND af.transaction_subtype = '${TradeType.SELL}'::text AND af.status = '${AssetFlowStatus.SETTLED}'::text THEN cf.amount
                    ELSE 0
                END), 0::bigint)::integer AS amount_sold,
            - COALESCE(sum(
                CASE
                    WHEN af.transaction_type = '${TransactionType.TRADE}'::text AND af.transaction_subtype = '${TradeType.SELL}'::text AND af.status = '${AssetFlowStatus.PENDING}'::text THEN cf.amount
                    ELSE 0
                END), 0::bigint)::integer AS amount_pending_sell,
            - COALESCE(sum(
                CASE
                    WHEN af.transaction_type = '${TransactionType.RETIREMENT}'::text AND af.transaction_subtype = '${RetirementType.TRANSFER_OUTFLOW}'::text AND af.status = '${AssetFlowStatus.SETTLED}'::text THEN cf.amount
                    ELSE 0
                END), 0::bigint)::integer AS amount_customer_transferred_outflow,
            - COALESCE(sum(
                CASE
                    WHEN af.transaction_type = '${TransactionType.RETIREMENT}'::text AND af.transaction_subtype = '${RetirementType.TRANSFER_OUTFLOW}'::text AND af.status = '${AssetFlowStatus.PENDING}'::text THEN cf.amount
                    ELSE 0
                END), 0::bigint)::integer AS amount_pending_customer_transfer_outflow,
            COALESCE(sum(
                CASE
                    WHEN cf.type = '${CreditFlowType.INFLOW}'::text THEN cf.amount
                    ELSE 0
                END), 0::bigint) AS total_inflows,
            - COALESCE(sum(
                CASE
                    WHEN cf.type <> '${CreditFlowType.INFLOW}'::text THEN cf.amount
                    ELSE 0
                END), 0::bigint) AS total_outflows,
            COALESCE(sum(cf.amount * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer), 0::bigint) AS inventory,
            sum(cf.amount * (af.settled_at <= max_settlements.max_settled_at)::integer * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer) AS cost_basis_credits,
            sum((COALESCE(af.raw_price, 0::numeric) + COALESCE(af.service_fee, 0::numeric) + COALESCE(af.other_fee, 0::numeric)) * (af.settled_at <= max_settlements.max_settled_at)::integer::numeric * (cf.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer::numeric) AS cost_basis_total_amount,
            sum((COALESCE(af.raw_price, 0::numeric) + COALESCE(af.service_fee, 0::numeric) + COALESCE(af.other_fee, 0::numeric)) * (af.settled_at <= max_settlements.max_settled_at)::integer::numeric * (cf.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer::numeric) / NULLIF(sum(cf.amount * (af.settled_at <= max_settlements.max_settled_at)::integer * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer), 0)::numeric AS cost_basis,
            sum(COALESCE(af.raw_price, 0::numeric) * (af.settled_at <= max_settlements.max_settled_at)::integer::numeric * (cf.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer::numeric) AS cost_raw_price,
            sum(COALESCE(af.raw_price, 0::numeric) * (af.settled_at <= max_settlements.max_settled_at)::integer::numeric * (cf.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer::numeric) / NULLIF(sum(cf.amount * (af.settled_at <= max_settlements.max_settled_at)::integer * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer), 0)::numeric AS cost_basis_raw_price,
            sum(COALESCE(af.service_fee, 0::numeric) * (af.settled_at <= max_settlements.max_settled_at)::integer::numeric * (cf.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer::numeric) AS cost_service_fee,
            sum(COALESCE(af.service_fee, 0::numeric) * (af.settled_at <= max_settlements.max_settled_at)::integer::numeric * (cf.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer::numeric) / NULLIF(sum(cf.amount * (af.settled_at <= max_settlements.max_settled_at)::integer * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer), 0)::numeric AS cost_basis_service_fee,
            sum(COALESCE(af.other_fee, 0::numeric) * (af.settled_at <= max_settlements.max_settled_at)::integer::numeric * (cf.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer::numeric) AS cost_other_fee,
            sum(COALESCE(af.other_fee, 0::numeric) * (af.settled_at <= max_settlements.max_settled_at)::integer::numeric * (cf.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer::numeric) / NULLIF(sum(cf.amount * (af.settled_at <= max_settlements.max_settled_at)::integer * (af.status = '${AssetFlowStatus.SETTLED}'::text)::integer), 0)::numeric AS cost_basis_other_fee
          FROM "${environment.db.schema.rubicon}".credit_flows cf
            JOIN "${environment.db.schema.rubicon}".asset_flows af ON af.id = cf.asset_flow_id
            JOIN max_settlements ON max_settlements.id = COALESCE(cf.linked_credit_flow_id, cf.id)
          GROUP BY (COALESCE(cf.linked_credit_flow_id, cf.id));`,
    );

    // project_vintage_cost_basis_v1
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".project_vintage_cost_basis_v1
        AS
        SELECT cf.project_vintage_id,
            sum(credit_amounts_v2.cost_basis * credit_amounts_v2.inventory::numeric) / NULLIF(sum(credit_amounts_v2.inventory), 0::numeric) AS average_cost_basis
          FROM "${environment.db.schema.rubicon}".credit_flows cf
            JOIN "${environment.db.schema.rubicon}".credit_amounts_v2 USING (id)
          GROUP BY cf.project_vintage_id;`,
    );

    // project_vintage_amounts_v3
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".project_vintage_amounts_v3
        AS
        WITH amounts AS (
                SELECT asset_flows.asset_id,
                    COALESCE(sum(asset_flows.amount) FILTER (WHERE asset_flows.status = '${AssetFlowStatus.SETTLED}'::text AND asset_flows.transaction_subtype = '${TradeType.BUY}'::text), 0::bigint) AS bought,
                    COALESCE(sum(asset_flows.amount) FILTER (WHERE asset_flows.status = '${AssetFlowStatus.SETTLED}'::text AND asset_flows.transaction_subtype = '${TradeType.SELL}'::text), 0::bigint) AS sold,
                    COALESCE(sum(asset_flows.amount) FILTER (WHERE asset_flows.status = '${AssetFlowStatus.SETTLED}'::text AND asset_flows.transaction_type = '${TransactionType.RETIREMENT}'::text AND asset_flows.transaction_subtype = '${RetirementType.RETIREMENT}'::text), 0::bigint) AS retired,
                    COALESCE(sum(asset_flows.amount) FILTER (WHERE asset_flows.status = '${AssetFlowStatus.SETTLED}'::text AND asset_flows.transaction_type = '${TransactionType.RETIREMENT}'::text AND asset_flows.transaction_subtype = '${RetirementType.TRANSFER_OUTFLOW}'::text), 0::bigint) AS transfer_outflow,
                    COALESCE(sum(asset_flows.amount) FILTER (WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_type = 'trade'::text AND asset_flows.transaction_subtype = '${TradeType.BUY}'::text), 0::bigint) AS pending_buy,
                    COALESCE(sum(asset_flows.amount) FILTER (WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_type = 'trade'::text AND asset_flows.transaction_subtype = '${TradeType.SELL}'::text), 0::bigint) AS pending_sell,
                    COALESCE(sum(asset_flows.amount) FILTER (WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_type = '${TransactionType.RETIREMENT}'::text AND asset_flows.transaction_subtype = '${RetirementType.RETIREMENT}'::text), 0::bigint) AS pending_retire,
                    COALESCE(sum(asset_flows.amount) FILTER (WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_type = '${TransactionType.RETIREMENT}'::text AND asset_flows.transaction_subtype = '${RetirementType.TRANSFER_OUTFLOW}'::text), 0::bigint) AS pending_transfer_outflow,
                    COALESCE(sum(asset_flows.amount) FILTER (WHERE asset_flows.status = '${AssetFlowStatus.SETTLED}'::text AND asset_flows.transaction_subtype = '${TradeType.BUY}'::text), 0::bigint) - COALESCE(sum(asset_flows.amount) FILTER (WHERE asset_flows.status = '${AssetFlowStatus.SETTLED}'::text AND (asset_flows.transaction_subtype = ANY (ARRAY['${TradeType.SELL}'::text, '${RetirementType.RETIREMENT}'::text, '${RetirementType.TRANSFER_OUTFLOW}'::text]))), 0::bigint) AS holdings,
                    COALESCE(sum(asset_flows.amount) FILTER (WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_subtype = '${TradeType.BUY}'::text), 0::bigint) - COALESCE(sum(asset_flows.amount) FILTER (WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND (asset_flows.transaction_subtype = ANY (ARRAY['${TradeType.SELL}'::text, '${RetirementType.RETIREMENT}'::text, '${RetirementType.TRANSFER_OUTFLOW}'::text]))), 0::bigint) AS pending
                  FROM "${environment.db.schema.rubicon}".asset_flows
                  GROUP BY asset_flows.asset_id
                )
        SELECT amounts.asset_id,
            amounts.bought,
            amounts.sold,
            amounts.retired,
            amounts.pending_buy,
            amounts.pending_sell,
            amounts.pending_retire,
            amounts.holdings,
            amounts.pending,
            cost_basis.average_cost_basis,
            amounts.transfer_outflow,
            amounts.pending_transfer_outflow
          FROM amounts
            FULL JOIN "${environment.db.schema.rubicon}".project_vintage_cost_basis_v1 cost_basis ON cost_basis.project_vintage_id = amounts.asset_id;`,
    );

    // project_vintage_prices_v2
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".project_vintage_prices_v2
        AS
        WITH rubicon_prices AS (
                SELECT i.project_id,
                    i.vintage,
                    i.price,
                    i."timestamp",
                    i.source
                  FROM "${environment.db.schema.marketData}".internal_prices i
                    JOIN ( SELECT internal_prices.project_id,
                            internal_prices.vintage,
                            max(internal_prices."timestamp") AS max
                          FROM "${environment.db.schema.marketData}".internal_prices
                          GROUP BY internal_prices.project_id, internal_prices.vintage) q ON q.project_id = i.project_id AND q.vintage = i.vintage AND i."timestamp" = q.max
                ), last_trade AS (
                SELECT asset_flows.asset_id AS vintage_id,
                    max((COALESCE(asset_flows.raw_price, 0::numeric) + COALESCE(asset_flows.service_fee, 0::numeric) + COALESCE(asset_flows.other_fee, 0::numeric)) / NULLIF(asset_flows.amount::numeric, 0::numeric)) AS cost
                  FROM "${environment.db.schema.rubicon}".trades
                    JOIN "${environment.db.schema.rubicon}".asset_flows ON asset_flows.transaction_id = trades.id
                  WHERE ((asset_flows.asset_id, trades.updated_at) IN ( SELECT asset_flows_1.asset_id,
                            max(trades_1.updated_at) AS max
                          FROM "${environment.db.schema.rubicon}".trades trades_1
                            JOIN "${environment.db.schema.rubicon}".asset_flows asset_flows_1 ON asset_flows_1.transaction_id = trades_1.id
                          GROUP BY asset_flows_1.asset_id))
                  GROUP BY asset_flows.asset_id
                ), last_forward AS (
                SELECT forward_line_items.project_vintage_id AS vintage_id,
                    max((COALESCE(forward_line_items.raw_price, 0::numeric) + COALESCE(forward_line_items.service_fee, 0::numeric) + COALESCE(forward_line_items.other_fee, 0::numeric)) / NULLIF(forward_line_items.expected_amount::numeric, 0::numeric)) AS cost
                  FROM "${environment.db.schema.rubicon}".forward_line_items
                  WHERE ((forward_line_items.project_vintage_id, forward_line_items.updated_at) IN ( SELECT forward_line_items_1.project_vintage_id AS vintage_id,
                            max(forward_line_items_1.updated_at) AS max
                          FROM "${environment.db.schema.rubicon}".forward_line_items forward_line_items_1
                          GROUP BY forward_line_items_1.project_vintage_id))
                  GROUP BY forward_line_items.project_vintage_id
                ), last_marketing_agreement AS (
                SELECT marketing_agreement_line_items.project_vintage_id AS vintage_id,
                    max(COALESCE(marketing_agreements.floor_price, 0::numeric)) AS cost
                  FROM "${environment.db.schema.rubicon}".marketing_agreement_line_items
                    JOIN "${environment.db.schema.rubicon}".marketing_agreements ON marketing_agreements.id = marketing_agreement_line_items.marketing_agreement_id
                  WHERE ((marketing_agreement_line_items.project_vintage_id, marketing_agreement_line_items.updated_at) IN ( SELECT marketing_agreement_line_items_1.project_vintage_id AS vintage_id,
                            max(marketing_agreement_line_items_1.updated_at) AS max
                          FROM "${environment.db.schema.rubicon}".marketing_agreement_line_items marketing_agreement_line_items_1
                          GROUP BY marketing_agreement_line_items_1.project_vintage_id))
                  GROUP BY marketing_agreement_line_items.project_vintage_id
                )
        SELECT p.id AS project_id,
            pv.id AS vintage_id,
            p.registry_project_id,
            pv.label AS vintage,
            p.name,
            pva.average_cost_basis,
            rp.price AS rubicon_price,
            rp."timestamp"::date AS rubicon_date,
            pva.holdings,
            COALESCE(rp.price, pva.average_cost_basis, last_trade.cost, last_forward.cost, last_marketing_agreement.cost) AS price,
            COALESCE(rp.source,
                CASE
                    WHEN pva.average_cost_basis IS NOT NULL THEN 'cost_basis'::text
                    WHEN last_trade.cost IS NOT NULL THEN 'pending_trade'::text
                    WHEN last_forward.cost IS NOT NULL THEN 'pending_forward'::text
                    WHEN last_marketing_agreement.cost IS NOT NULL THEN 'pending_marketing_agreement'::text
                    ELSE NULL::text
                END) AS source
          FROM "${environment.db.schema.rubicon}".projects p
            JOIN "${environment.db.schema.rubicon}".project_vintages pv ON pv.project_id = p.id
            LEFT JOIN "${environment.db.schema.rubicon}".project_vintage_amounts_v3 pva ON pva.asset_id = pv.id
            LEFT JOIN rubicon_prices rp ON rp.project_id = p.registry_project_id AND rp.vintage = pv.label
            LEFT JOIN last_trade ON pv.id = last_trade.vintage_id
            LEFT JOIN last_forward ON pv.id = last_forward.vintage_id
            LEFT JOIN last_marketing_agreement ON pv.id = last_marketing_agreement.vintage_id;`,
    );

    // asset_composition_v2
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".asset_composition_v2
        AS
        WITH RECURSIVE allocations AS (
                SELECT books.id AS owner_id,
                    1 AS level,
                    books.id AS parent_id,
                    balances.asset_id AS component_id,
                    balances.balance::numeric AS holdings,
                    balances.asset_type
                  FROM "${environment.db.schema.rubicon}".books
                    JOIN "${environment.db.schema.ledger}".balances ON balances.owner_id = books.id
                  WHERE balances.balance <> 0::numeric AND balances.type = '${LedgerBalanceType.SETTLED}'::text
                UNION ALL
                SELECT allocations_1.owner_id,
                    allocations_1.level + 1 AS level,
                    allocations_1.component_id AS parent_id,
                    balances.asset_id AS component_id,
                    allocations_1.holdings * (balances.balance / NULLIF(owner_balances.balance, 0::numeric)) AS holdings,
                    balances.asset_type
                  FROM allocations allocations_1
                    JOIN "${environment.db.schema.ledger}".balances ON balances.owner_id = allocations_1.component_id
                    JOIN ( SELECT balances_1.owner_id,
                            sum(balances_1.balance) AS balance
                          FROM "${environment.db.schema.ledger}".balances balances_1
                          WHERE balances_1.type = '${LedgerBalanceType.SETTLED}'::text AND balances_1.owner_id <> balances_1.asset_id
                          GROUP BY balances_1.owner_id) owner_balances ON owner_balances.owner_id = balances.owner_id
                  WHERE allocations_1.asset_type <> '${AssetType.REGISTRY_VINTAGE}'::text AND NOT (allocations_1.component_id = balances.asset_id AND balances.asset_type = '${AssetType.RCT}'::text) AND balances.type = '${LedgerBalanceType.SETTLED}'::text
                ), total_allocations AS (
                SELECT allocations.owner_id,
                    sum(COALESCE(allocations.holdings, 0::numeric)) AS holdings
                  FROM allocations
                  WHERE allocations.level = 1
                  GROUP BY allocations.owner_id
                )
        SELECT groupings.parent_id AS owner_id,
            1 AS level,
            groupings.parent_id,
            groupings.book_id AS component_id,
            total_allocations.holdings,
            'child'::text AS asset_type
          FROM "${environment.db.schema.rubicon}".groupings
            JOIN total_allocations ON groupings.book_id = total_allocations.owner_id
        UNION ALL
        SELECT groupings.parent_id AS owner_id,
            allocations.level + 1 AS level,
            allocations.parent_id,
            allocations.component_id,
            allocations.holdings,
            allocations.asset_type
          FROM "${environment.db.schema.rubicon}".groupings
            JOIN allocations ON groupings.book_id = allocations.owner_id
        UNION ALL
        SELECT allocations.owner_id,
            allocations.level,
            allocations.parent_id,
            allocations.component_id,
            allocations.holdings,
            allocations.asset_type
          FROM allocations
          WHERE allocations.holdings <> 0::numeric;`,
    );

    // asset_holdings_v2
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".asset_holdings_v2
        AS
        SELECT owner_id,
            asset_id,
            h.asset_type,
            COALESCE(h.holdings, 0::numeric) AS holdings,
            COALESCE(scto.amount, 0::bigint) AS holdings_customer_transferred_outflow,
            COALESCE(sro.amount, 0::bigint) AS holdings_retired_outflow,
            COALESCE(sso.amount, 0::bigint) AS holdings_sold_outflow,
            COALESCE(ppi.amount, 0::bigint) AS pending_purchase_inflow,
            COALESCE(ppo.amount, 0::bigint) AS pending_purchase_outflow,
            COALESCE(pri.amount, 0::bigint) AS pending_retirement_inflow,
            COALESCE(pro.amount, 0::bigint) AS pending_retirement_outflow,
            COALESCE(pbi.amount, 0::bigint) AS pending_buy_inflow,
            COALESCE(pbo.amount, 0::bigint) AS pending_buy_outflow,
            COALESCE(psi.amount, 0::bigint) AS pending_sell_inflow,
            COALESCE(pso.amount, 0::bigint) AS pending_sell_outflow,
            COALESCE(prsi.amount, 0::bigint) AS pending_reserves_inflow,
            COALESCE(prso.amount, 0::bigint) AS pending_reserves_outflow,
            COALESCE(piti.amount, 0::bigint) AS pending_transfer_inflow,
            COALESCE(pito.amount, 0::bigint) AS pending_transfer_outflow,
            COALESCE(pcti.amount, 0::bigint) AS pending_customer_transfer_inflow,
            COALESCE(pcto.amount, 0::bigint) AS pending_customer_transfer_outflow,
            COALESCE(ppi.amount, 0::bigint) + COALESCE(pri.amount, 0::bigint) + COALESCE(pbi.amount, 0::bigint) + COALESCE(psi.amount, 0::bigint) + COALESCE(prsi.amount, 0::bigint) + COALESCE(piti.amount, 0::bigint) + COALESCE(pcti.amount, 0::bigint) AS pending_inflow,
            COALESCE(ppo.amount, 0::bigint) + COALESCE(pro.amount, 0::bigint) + COALESCE(pbo.amount, 0::bigint) + COALESCE(pso.amount, 0::bigint) + COALESCE(prso.amount, 0::bigint) + COALESCE(pito.amount, 0::bigint) + COALESCE(pcto.amount, 0::bigint) AS pending_outflow
          FROM ( SELECT ac.owner_id,
                    ac.component_id AS asset_id,
                    ac.asset_type,
                    sum(ac.holdings) AS holdings
                  FROM "${environment.db.schema.rubicon}".asset_composition_v2 ac
                  WHERE ac.level = 1
                  GROUP BY ac.owner_id, ac.component_id, ac.asset_type) h
            FULL JOIN ( SELECT asset_flows.source_id AS owner_id,
                    asset_flows.asset_id,
                    sum(asset_flows.amount) AS amount
                  FROM "${environment.db.schema.rubicon}".asset_flows
                  WHERE asset_flows.status = '${AssetFlowStatus.SETTLED}'::text AND asset_flows.transaction_subtype = '${RetirementType.TRANSFER_OUTFLOW}'::text
                  GROUP BY asset_flows.source_id, asset_flows.asset_id) scto USING (owner_id, asset_id)
            FULL JOIN ( SELECT asset_flows.source_id AS owner_id,
                    asset_flows.asset_id,
                    sum(asset_flows.amount) AS amount
                  FROM "${environment.db.schema.rubicon}".asset_flows
                  WHERE asset_flows.status = '${AssetFlowStatus.SETTLED}'::text AND asset_flows.transaction_subtype = '${RetirementType.RETIREMENT}'::text
                  GROUP BY asset_flows.source_id, asset_flows.asset_id) sro USING (owner_id, asset_id)
            FULL JOIN ( SELECT asset_flows.source_id AS owner_id,
                    asset_flows.asset_id,
                    sum(asset_flows.amount) AS amount
                  FROM "${environment.db.schema.rubicon}".asset_flows
                  WHERE asset_flows.status = '${AssetFlowStatus.SETTLED}'::text AND asset_flows.transaction_subtype = '${TradeType.SELL}'::text
                  GROUP BY asset_flows.source_id, asset_flows.asset_id) sso USING (owner_id, asset_id)
            FULL JOIN ( SELECT asset_flows.destination_id AS owner_id,
                    asset_flows.asset_id,
                    sum(asset_flows.amount) AS amount
                  FROM "${environment.db.schema.rubicon}".asset_flows
                  WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_type = 'purchase'::text
                  GROUP BY asset_flows.destination_id, asset_flows.asset_id) ppi USING (owner_id, asset_id)
            FULL JOIN ( SELECT asset_flows.source_id AS owner_id,
                    asset_flows.asset_id,
                    sum(asset_flows.amount) AS amount
                  FROM "${environment.db.schema.rubicon}".asset_flows
                  WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_type = 'purchase'::text
                  GROUP BY asset_flows.source_id, asset_flows.asset_id) ppo USING (owner_id, asset_id)
            FULL JOIN ( SELECT asset_flows.destination_id AS owner_id,
                    asset_flows.asset_id,
                    sum(asset_flows.amount) AS amount
                  FROM "${environment.db.schema.rubicon}".asset_flows
                  WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_subtype = '${RetirementType.RETIREMENT}'::text
                  GROUP BY asset_flows.destination_id, asset_flows.asset_id) pri USING (owner_id, asset_id)
            FULL JOIN ( SELECT asset_flows.source_id AS owner_id,
                    asset_flows.asset_id,
                    sum(asset_flows.amount) AS amount
                  FROM "${environment.db.schema.rubicon}".asset_flows
                  WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_subtype = '${RetirementType.RETIREMENT}'::text
                  GROUP BY asset_flows.source_id, asset_flows.asset_id) pro USING (owner_id, asset_id)
            FULL JOIN ( SELECT asset_flows.destination_id AS owner_id,
                    asset_flows.asset_id,
                    sum(asset_flows.amount) AS amount
                  FROM "${environment.db.schema.rubicon}".asset_flows
                  WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_subtype = '${TradeType.BUY}'::text
                  GROUP BY asset_flows.destination_id, asset_flows.asset_id) pbi USING (owner_id, asset_id)
            FULL JOIN ( SELECT asset_flows.source_id AS owner_id,
                    asset_flows.asset_id,
                    sum(asset_flows.amount) AS amount
                  FROM "${environment.db.schema.rubicon}".asset_flows
                  WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_subtype = '${TradeType.BUY}'::text
                  GROUP BY asset_flows.source_id, asset_flows.asset_id) pbo USING (owner_id, asset_id)
            FULL JOIN ( SELECT asset_flows.destination_id AS owner_id,
                    asset_flows.asset_id,
                    sum(asset_flows.amount) AS amount
                  FROM "${environment.db.schema.rubicon}".asset_flows
                  WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_subtype = '${TradeType.SELL}'::text
                  GROUP BY asset_flows.destination_id, asset_flows.asset_id) psi USING (owner_id, asset_id)
            FULL JOIN ( SELECT asset_flows.source_id AS owner_id,
                    asset_flows.asset_id,
                    sum(asset_flows.amount) AS amount
                  FROM "${environment.db.schema.rubicon}".asset_flows
                  WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_subtype = '${TradeType.SELL}'::text
                  GROUP BY asset_flows.source_id, asset_flows.asset_id) pso USING (owner_id, asset_id)
            FULL JOIN ( SELECT asset_flows.destination_id AS owner_id,
                    asset_flows.asset_id,
                    sum(asset_flows.amount) AS amount
                  FROM "${environment.db.schema.rubicon}".asset_flows
                  WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_type = '${TransactionType.RESERVE}'::text
                  GROUP BY asset_flows.destination_id, asset_flows.asset_id) prsi USING (owner_id, asset_id)
            FULL JOIN ( SELECT asset_flows.source_id AS owner_id,
                    asset_flows.asset_id,
                    sum(asset_flows.amount) AS amount
                  FROM "${environment.db.schema.rubicon}".asset_flows
                  WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_type = '${TransactionType.RESERVE}'::text
                  GROUP BY asset_flows.source_id, asset_flows.asset_id) prso USING (owner_id, asset_id)
            FULL JOIN ( SELECT asset_flows.destination_id AS owner_id,
                    asset_flows.asset_id,
                    sum(asset_flows.amount) AS amount
                  FROM "${environment.db.schema.rubicon}".asset_flows
                  WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_type = '${TransactionType.INTERNAL_TRANSFER}'::text
                  GROUP BY asset_flows.destination_id, asset_flows.asset_id) piti USING (owner_id, asset_id)
            FULL JOIN ( SELECT asset_flows.source_id AS owner_id,
                    asset_flows.asset_id,
                    sum(asset_flows.amount) AS amount
                  FROM "${environment.db.schema.rubicon}".asset_flows
                  WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_type = '${TransactionType.INTERNAL_TRANSFER}'::text
                  GROUP BY asset_flows.source_id, asset_flows.asset_id) pito USING (owner_id, asset_id)
            FULL JOIN ( SELECT asset_flows.destination_id AS owner_id,
                    asset_flows.asset_id,
                    sum(asset_flows.amount) AS amount
                  FROM "${environment.db.schema.rubicon}".asset_flows
                  WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_subtype = '${RetirementType.TRANSFER_OUTFLOW}'::text
                  GROUP BY asset_flows.destination_id, asset_flows.asset_id) pcti USING (owner_id, asset_id)
            FULL JOIN ( SELECT asset_flows.source_id AS owner_id,
                    asset_flows.asset_id,
                    sum(asset_flows.amount) AS amount
                  FROM "${environment.db.schema.rubicon}".asset_flows
                  WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_subtype = '${RetirementType.TRANSFER_OUTFLOW}'::text
                  GROUP BY asset_flows.source_id, asset_flows.asset_id) pcto USING (owner_id, asset_id);`,
    );

    // asset_prices_v5
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".asset_prices_v5
        AS
        WITH settled_books AS (
                SELECT ac.owner_id,
                    sum(ac.holdings) AS holdings,
                    NULLIF(sum(ac.holdings * COALESCE(pvp.price, 'NaN'::numeric)), 'NaN'::numeric) AS value,
                    NULLIF(sum(ac.holdings * COALESCE(pvp.price, 'NaN'::numeric)) / NULLIF(sum(ac.holdings), 0::numeric), 'NaN'::numeric) AS unit_value,
                    NULLIF(sum(ac.holdings * COALESCE(pvp.average_cost_basis, 'NaN'::numeric)) / NULLIF(sum(ac.holdings), 0::numeric), 'NaN'::numeric) AS average_cost_basis
                  FROM "${environment.db.schema.rubicon}".asset_composition_v2 ac
                    LEFT JOIN "${environment.db.schema.rubicon}".project_vintage_prices_v2 pvp ON pvp.vintage_id = ac.component_id
                  WHERE ac.asset_type = '${AssetType.REGISTRY_VINTAGE}'::text AND ac.level = 1
                  GROUP BY ac.owner_id
                ), settled AS (
                SELECT groupings.parent_id AS owner_id,
                    sum(COALESCE(settled_books.holdings, 0::numeric)) AS holdings,
                    sum(COALESCE(settled_books.value, 0::numeric)) AS value,
                    sum(COALESCE(settled_books.holdings * settled_books.unit_value, 0::numeric)) / NULLIF(sum(settled_books.holdings), 0::numeric) AS unit_value,
                    sum(COALESCE(settled_books.holdings * settled_books.average_cost_basis, 0::numeric)) / NULLIF(sum(settled_books.holdings), 0::numeric) AS average_cost_basis
                  FROM "${environment.db.schema.rubicon}".groupings
                    JOIN settled_books ON settled_books.owner_id = groupings.book_id
                  GROUP BY groupings.parent_id
                UNION ALL
                SELECT settled_books.owner_id,
                    settled_books.holdings,
                    settled_books.value,
                    settled_books.unit_value,
                    settled_books.average_cost_basis
                  FROM settled_books
                ), pending_rct AS (
                SELECT owner_id,
                    asset_id,
                    b.pending_transfer_inflow_holdings,
                    s.pending_transfer_ouflow_holdings,
                    r.pending_retirement_holdings
                  FROM ( SELECT asset_flows.source_id AS owner_id,
                            asset_flows.asset_id,
                            sum(asset_flows.amount) AS pending_transfer_ouflow_holdings
                          FROM "${environment.db.schema.rubicon}".asset_flows
                          WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_type = '${TransactionType.INTERNAL_TRANSFER}'::text AND asset_flows.source_id <> asset_flows.asset_id
                          GROUP BY asset_flows.source_id, asset_flows.asset_id) s
                    FULL JOIN ( SELECT asset_flows.destination_id AS owner_id,
                            asset_flows.asset_id,
                            sum(asset_flows.amount) AS pending_transfer_inflow_holdings
                          FROM "${environment.db.schema.rubicon}".asset_flows
                          WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_type = '${TransactionType.INTERNAL_TRANSFER}'::text AND asset_flows.destination_id <> asset_flows.asset_id
                          GROUP BY asset_flows.destination_id, asset_flows.asset_id) b USING (owner_id, asset_id)
                    FULL JOIN ( SELECT asset_flows.source_id AS owner_id,
                            asset_flows.asset_id,
                            sum(asset_flows.amount) AS pending_retirement_holdings
                          FROM "${environment.db.schema.rubicon}".asset_flows
                          WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_subtype = '${RetirementType.RETIREMENT}'::text
                          GROUP BY asset_flows.source_id, asset_flows.asset_id) r USING (owner_id, asset_id)
                ), pending_vintages AS (
                SELECT owner_id,
                    asset_id,
                    b.amount_buy_1,
                    s.amount_buy_2,
                    b2.amount_sell_1,
                    s2.amount_sell_2,
                    r1.amount_retire_1,
                    r2.amount_retire_2,
                    COALESCE(b.amount_buy_1, 0) + COALESCE(b2.amount_sell_1, 0) AS inflow,
                    COALESCE(s2.amount_sell_2, 0) + COALESCE(s.amount_buy_2, 0) AS outflow,
                    COALESCE(r1.amount_retire_1, 0) - COALESCE(r2.amount_retire_2, 0) AS retirement
                  FROM ( SELECT asset_flows.destination_id AS owner_id,
                            asset_flows.asset_id,
                            asset_flows.amount AS amount_buy_1
                          FROM "${environment.db.schema.rubicon}".asset_flows
                          WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_subtype = '${TradeType.BUY}'::text) b
                    FULL JOIN ( SELECT asset_flows.source_id AS owner_id,
                            asset_flows.asset_id,
                            asset_flows.amount AS amount_buy_2
                          FROM "${environment.db.schema.rubicon}".asset_flows
                          WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_subtype = '${TradeType.BUY}'::text) s USING (owner_id, asset_id)
                    FULL JOIN ( SELECT asset_flows.destination_id AS owner_id,
                            asset_flows.asset_id,
                            asset_flows.amount AS amount_sell_1
                          FROM "${environment.db.schema.rubicon}".asset_flows
                          WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_subtype = '${TradeType.SELL}'::text) b2 USING (owner_id, asset_id)
                    FULL JOIN ( SELECT asset_flows.source_id AS owner_id,
                            asset_flows.asset_id,
                            asset_flows.amount AS amount_sell_2
                          FROM "${environment.db.schema.rubicon}".asset_flows
                          WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_subtype = '${TradeType.SELL}'::text) s2 USING (owner_id, asset_id)
                    FULL JOIN ( SELECT asset_flows.source_id AS owner_id,
                            asset_flows.asset_id,
                            asset_flows.amount AS amount_retire_1
                          FROM "${environment.db.schema.rubicon}".asset_flows
                          WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_type = '${TransactionType.RETIREMENT}'::text) r1 USING (owner_id, asset_id)
                    FULL JOIN ( SELECT asset_flows.destination_id AS owner_id,
                            asset_flows.asset_id,
                            asset_flows.amount AS amount_retire_2
                          FROM "${environment.db.schema.rubicon}".asset_flows
                          WHERE asset_flows.status = '${AssetFlowStatus.PENDING}'::text AND asset_flows.transaction_type = '${TransactionType.RETIREMENT}'::text) r2 USING (owner_id, asset_id)
                ), ledger_books_pending AS (
                SELECT books.id AS owner_id,
                    sum(balances.balance::numeric) AS pending_net_holdings,
                    NULLIF(sum(COALESCE(balances.balance::numeric * ss.unit_value, balances.balance::numeric * project_vintage_prices_v2.price, 'NaN'::numeric)), 'NaN'::numeric) AS pending_net_value
                  FROM "${environment.db.schema.rubicon}".books
                    JOIN "${environment.db.schema.ledger}".balances ON balances.owner_id = books.id
                    LEFT JOIN settled ss ON ss.owner_id = balances.asset_id AND balances.asset_type = '${AssetType.RCT}'::text
                    LEFT JOIN "${environment.db.schema.rubicon}".project_vintage_prices_v2 ON balances.asset_id = project_vintage_prices_v2.vintage_id AND balances.asset_type = '${AssetType.REGISTRY_VINTAGE}'::text
                  WHERE balances.balance <> 0::numeric AND balances.asset_id <> books.id AND balances.type = 'pending'::text
                  GROUP BY books.id
                ), ledger_pending AS (
                SELECT groupings.parent_id AS owner_id,
                    sum(ledger_books_pending.pending_net_holdings) AS pending_net_holdings,
                    sum(ledger_books_pending.pending_net_value) AS pending_net_value
                  FROM "${environment.db.schema.rubicon}".groupings
                    JOIN ledger_books_pending ON ledger_books_pending.owner_id = groupings.book_id
                  GROUP BY groupings.parent_id
                UNION ALL
                SELECT ledger_books_pending.owner_id,
                    ledger_books_pending.pending_net_holdings,
                    ledger_books_pending.pending_net_value
                  FROM ledger_books_pending
                ), p_books_rct AS (
                SELECT pending_rct.owner_id,
                    sum(pending_rct.pending_transfer_inflow_holdings) AS pending_transfer_inflow_holdings,
                    sum(pending_rct.pending_transfer_ouflow_holdings) AS pending_transfer_ouflow_holdings,
                    sum(pending_rct.pending_retirement_holdings) AS pending_retirement_holdings,
                    sum(pending_rct.pending_transfer_inflow_holdings::numeric * COALESCE(settled_1.unit_value, project_vintage_prices_v2.price)) AS pending_transfer_inflow_value,
                    sum(pending_rct.pending_transfer_ouflow_holdings::numeric * COALESCE(settled_1.unit_value, project_vintage_prices_v2.price)) AS pending_transfer_ouflow_value,
                    sum(pending_rct.pending_retirement_holdings::numeric * COALESCE(settled_1.unit_value, project_vintage_prices_v2.price)) AS pending_retirement_value
                  FROM pending_rct
                    LEFT JOIN settled settled_1 ON pending_rct.asset_id = settled_1.owner_id
                    LEFT JOIN "${environment.db.schema.rubicon}".project_vintage_prices_v2 ON pending_rct.asset_id = project_vintage_prices_v2.vintage_id
                  GROUP BY pending_rct.owner_id
                ), p_rct AS (
                SELECT groupings.parent_id AS owner_id,
                    sum(p_books_rct.pending_transfer_inflow_holdings) AS pending_transfer_inflow_holdings,
                    sum(p_books_rct.pending_transfer_ouflow_holdings) AS pending_transfer_ouflow_holdings,
                    sum(p_books_rct.pending_retirement_holdings) AS pending_retirement_holdings,
                    sum(p_books_rct.pending_transfer_inflow_value) AS pending_transfer_inflow_value,
                    sum(p_books_rct.pending_transfer_ouflow_value) AS pending_transfer_ouflow_value,
                    sum(p_books_rct.pending_retirement_value) AS pending_retirement_value
                  FROM "${environment.db.schema.rubicon}".groupings
                    JOIN p_books_rct ON p_books_rct.owner_id = groupings.book_id
                  GROUP BY groupings.parent_id
                UNION ALL
                SELECT p_books_rct.owner_id,
                    p_books_rct.pending_transfer_inflow_holdings,
                    p_books_rct.pending_transfer_ouflow_holdings,
                    p_books_rct.pending_retirement_holdings,
                    p_books_rct.pending_transfer_inflow_value,
                    p_books_rct.pending_transfer_ouflow_value,
                    p_books_rct.pending_retirement_value
                  FROM p_books_rct
                ), p_books_vintages AS (
                SELECT pending_vintages.owner_id,
                    sum(pending_vintages.inflow) AS pending_transfer_inflow_holdings,
                    sum(pending_vintages.outflow) AS pending_transfer_ouflow_holdings,
                    sum(pending_vintages.retirement) AS pending_retirement_holdings,
                    sum(pending_vintages.inflow::numeric * COALESCE(settled_1.unit_value, project_vintage_prices_v2.price)) AS pending_transfer_inflow_value,
                    sum(pending_vintages.outflow::numeric * COALESCE(settled_1.unit_value, project_vintage_prices_v2.price)) AS pending_transfer_ouflow_value,
                    sum(pending_vintages.retirement::numeric * COALESCE(settled_1.unit_value, project_vintage_prices_v2.price)) AS pending_retirement_value
                  FROM pending_vintages
                    LEFT JOIN settled settled_1 ON pending_vintages.asset_id = settled_1.owner_id
                    LEFT JOIN "${environment.db.schema.rubicon}".project_vintage_prices_v2 ON pending_vintages.asset_id = project_vintage_prices_v2.vintage_id
                  GROUP BY pending_vintages.owner_id
                ), p_vintages AS (
                SELECT groupings.parent_id AS owner_id,
                    sum(p_books_vintages.pending_transfer_inflow_holdings) AS pending_transfer_inflow_holdings,
                    sum(p_books_vintages.pending_transfer_ouflow_holdings) AS pending_transfer_ouflow_holdings,
                    sum(p_books_vintages.pending_retirement_holdings) AS pending_retirement_holdings,
                    sum(p_books_vintages.pending_transfer_inflow_value) AS pending_transfer_inflow_value,
                    sum(p_books_vintages.pending_transfer_ouflow_value) AS pending_transfer_ouflow_value,
                    sum(p_books_vintages.pending_retirement_value) AS pending_retirement_value
                  FROM "${environment.db.schema.rubicon}".groupings
                    JOIN p_books_vintages ON p_books_vintages.owner_id = groupings.book_id
                  GROUP BY groupings.parent_id
                UNION ALL
                SELECT p_books_vintages.owner_id,
                    p_books_vintages.pending_transfer_inflow_holdings,
                    p_books_vintages.pending_transfer_ouflow_holdings,
                    p_books_vintages.pending_retirement_holdings,
                    p_books_vintages.pending_transfer_inflow_value,
                    p_books_vintages.pending_transfer_ouflow_value,
                    p_books_vintages.pending_retirement_value
                  FROM p_books_vintages
                )
        SELECT settled.owner_id,
                CASE
                    WHEN groupings.id IS NULL THEN 'book'::text
                    ELSE 'grouping'::text
                END AS type,
            round(settled.holdings, 0)::integer AS holdings,
            settled.value,
            settled.unit_value,
            settled.average_cost_basis,
            ledger_pending.pending_net_holdings::integer AS pending_net_holdings,
            ledger_pending.pending_net_value,
            COALESCE(p_rct.pending_transfer_inflow_holdings, 0::numeric) + COALESCE(p_vintages.pending_transfer_inflow_holdings, 0::bigint::numeric)::integer::numeric AS pending_transfer_inflow_holdings,
            COALESCE(p_rct.pending_transfer_ouflow_holdings, 0::numeric) + COALESCE(p_vintages.pending_transfer_ouflow_holdings, 0::bigint::numeric)::integer::numeric AS pending_transfer_ouflow_holdings,
            COALESCE(p_rct.pending_retirement_holdings, 0::numeric) + COALESCE(p_vintages.pending_retirement_holdings, 0::bigint::numeric)::integer::numeric AS pending_retirement_holdings,
            COALESCE(p_rct.pending_transfer_inflow_value, 0::numeric) + COALESCE(p_vintages.pending_transfer_inflow_value, 0::numeric) AS pending_transfer_inflow_value,
            COALESCE(p_rct.pending_transfer_ouflow_value, 0::numeric) + COALESCE(p_vintages.pending_transfer_ouflow_value, 0::numeric) AS pending_transfer_ouflow_value,
            COALESCE(p_rct.pending_retirement_value, 0::numeric) + COALESCE(p_vintages.pending_retirement_value, 0::numeric) AS pending_retirement_value
          FROM settled
            LEFT JOIN ( SELECT grouping_parents.id,
                    grouping_parents.created_at,
                    grouping_parents.updated_at,
                    grouping_parents.name,
                    grouping_parents.description
                  FROM "${environment.db.schema.rubicon}".grouping_parents) groupings ON settled.owner_id = groupings.id
            LEFT JOIN ledger_pending USING (owner_id)
            LEFT JOIN p_rct USING (owner_id)
            LEFT JOIN p_vintages USING (owner_id)
        UNION ALL
        SELECT "${environment.db.schema.rubicon}".project_vintage_prices_v2.vintage_id AS owner_id,
            'vintage'::text AS type,
            project_vintage_prices_v2.holdings,
            project_vintage_prices_v2.holdings::numeric * project_vintage_prices_v2.price AS value,
            project_vintage_prices_v2.price AS unit_value,
            project_vintage_prices_v2.average_cost_basis,
            NULL::integer AS pending_net_holdings,
            NULL::numeric AS pending_net_value,
            NULL::integer AS pending_transfer_inflow_holdings,
            NULL::integer AS pending_transfer_ouflow_holdings,
            NULL::integer AS pending_retirement_holdings,
            NULL::numeric AS pending_transfer_inflow_value,
            NULL::numeric AS pending_transfer_ouflow_value,
            NULL::numeric AS pending_retirement_value
          FROM "${environment.db.schema.rubicon}".project_vintage_prices_v2;`,
    );

    // credit_historical_cost_basis_v1
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".credit_historical_cost_basis_v1
        AS
        SELECT cfo.id AS credit_flow_id,
            COALESCE(cfo.linked_credit_flow_id, cfo.id) AS credit_inflow_id,
            cfo.project_vintage_id,
            cfo.type,
            afo.settled_at,
            qc.credits,
            qo.cost_basis,
            qo.cost_basis_raw_price,
            qo.cost_basis_service_fee,
            qo.cost_basis_other_fee
          FROM "${environment.db.schema.rubicon}".credit_flows cfo
            JOIN "${environment.db.schema.rubicon}".asset_flows afo ON afo.id = cfo.asset_flow_id
            JOIN LATERAL ( SELECT COALESCE(cf.linked_credit_flow_id, cf.id) AS id,
                    af.settled_at,
                    q_1.cost_basis,
                    q_1.cost_basis_raw_price,
                    q_1.cost_basis_service_fee,
                    q_1.cost_basis_other_fee
                  FROM "${environment.db.schema.rubicon}".credit_flows cf
                    JOIN "${environment.db.schema.rubicon}".asset_flows af ON af.id = cf.asset_flow_id
                    LEFT JOIN LATERAL ( SELECT COALESCE(cfl.linked_credit_flow_id, cfl.id) AS id,
                            sum(cfl.amount) AS credits,
                            sum((COALESCE(afl.raw_price, 0::numeric) + COALESCE(afl.service_fee, 0::numeric) + COALESCE(afl.other_fee, 0::numeric)) * (cfl.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric) AS cost,
                            sum((COALESCE(afl.raw_price, 0::numeric) + COALESCE(afl.service_fee, 0::numeric) + COALESCE(afl.other_fee, 0::numeric)) * (cfl.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric) / NULLIF(sum(cfl.amount)::numeric, 0::numeric) AS cost_basis,
                            sum(COALESCE(afl.raw_price, 0::numeric) * (cfl.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric) AS cost_raw_price,
                            sum(COALESCE(afl.raw_price, 0::numeric) * (cfl.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric) / NULLIF(sum(cfl.amount)::numeric, 0::numeric) AS cost_basis_raw_price,
                            sum(COALESCE(afl.service_fee, 0::numeric) * (cfl.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric) AS cost_service_fee,
                            sum(COALESCE(afl.service_fee, 0::numeric) * (cfl.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric) / NULLIF(sum(cfl.amount)::numeric, 0::numeric) AS cost_basis_service_fee,
                            sum(COALESCE(afl.other_fee, 0::numeric) * (cfl.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric) AS cost_other_fee,
                            sum(COALESCE(afl.other_fee, 0::numeric) * (cfl.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text]))::integer::numeric) / NULLIF(sum(cfl.amount)::numeric, 0::numeric) AS cost_basis_other_fee
                          FROM "${environment.db.schema.rubicon}".credit_flows cfl
                            JOIN "${environment.db.schema.rubicon}".asset_flows afl ON afl.id = cfl.asset_flow_id
                          WHERE COALESCE(cfl.linked_credit_flow_id, cfl.id) = COALESCE(cf.linked_credit_flow_id, cf.id) AND afl.settled_at <= af.settled_at AND afl.status = 'settled'::text
                          GROUP BY (COALESCE(cfl.linked_credit_flow_id, cfl.id))) q_1 ON COALESCE(cf.linked_credit_flow_id, cf.id) = q_1.id
                  WHERE (cf.type = ANY (ARRAY['${CreditFlowType.INFLOW}'::text, '${CreditFlowType.ACCOUNTING}'::text])) AND af.status = '${AssetFlowStatus.SETTLED}'::text AND af.settled_at <= afo.settled_at AND COALESCE(cf.linked_credit_flow_id, cf.id) = COALESCE(cfo.linked_credit_flow_id, cfo.id)
                  ORDER BY (COALESCE(cf.linked_credit_flow_id, cf.id)), af.settled_at DESC
                LIMIT 1) qo ON qo.id = COALESCE(cfo.linked_credit_flow_id, cfo.id)
            JOIN LATERAL ( SELECT COALESCE(cf.linked_credit_flow_id, cf.id) AS id,
                    sum(cf.amount) AS credits
                  FROM "${environment.db.schema.rubicon}".credit_flows cf
                    JOIN "${environment.db.schema.rubicon}".asset_flows af ON af.id = cf.asset_flow_id
                  WHERE af.settled_at <= afo.settled_at AND COALESCE(cf.linked_credit_flow_id, cf.id) = COALESCE(cfo.linked_credit_flow_id, cfo.id) AND af.status = '${AssetFlowStatus.SETTLED}'::text
                  GROUP BY (COALESCE(cf.linked_credit_flow_id, cf.id))) qc ON qc.id = COALESCE(cfo.linked_credit_flow_id, cfo.id)
          ORDER BY cfo.project_vintage_id, (COALESCE(cfo.linked_credit_flow_id, cfo.id)), afo.settled_at DESC;`,
    );

    // line_item_transactions_view
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".line_item_transactions_view
        AS
        SELECT credit_flows.id,
            credit_flows.created_at,
            credit_flows.updated_at,
            credit_flows.linked_credit_flow_id AS line_item_id,
            - credit_flows.amount AS amount_attributed,
            credit_flows.asset_flow_id
          FROM "${environment.db.schema.rubicon}".credit_flows
          WHERE credit_flows.type = 'outflow'::text;`,
    );

    // line_items_view // todo : @aren can this just use credit_flows?
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".line_items_view
        AS
        SELECT credit_flows.id,
            credit_flows.created_at,
            credit_flows.updated_at,
            credit_flows.project_vintage_id,
            credit_flows.acquired_at,
            credit_flows.amount AS amount_acquired,
            credit_flows.registry_serial_block,
            credit_flows.registry_serial,
            credit_flows.registry_block,
            credit_flows.linked_credit_flow_id AS linked_line_item_id,
            credit_flows.asset_flow_id
          FROM "${environment.db.schema.rubicon}".credit_flows
          WHERE credit_flows.type = '${CreditFlowType.INFLOW}'::text;`,
    );

    // portfolio_completed_transactions_by_quarter_v2
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".portfolio_completed_transactions_by_quarter_v2
        AS
        SELECT b.id AS portfolio_id,
            af.asset_id,
            af.asset_type,
            date_trunc('quarter'::text, af.updated_at) AS quarter,
            sum(
                CASE
                    WHEN af.transaction_type = 'purchase'::text AND af.status = '${AssetFlowStatus.SETTLED}'::text AND af.destination_id = b.id THEN af.amount
                    ELSE 0
                END)::integer AS purchased,
            sum(
                CASE
                    WHEN af.transaction_type = '${TransactionType.RETIREMENT}'::text AND af.transaction_subtype = '${RetirementType.RETIREMENT}'::text AND af.status = '${AssetFlowStatus.SETTLED}'::text AND af.source_id = b.id THEN af.amount
                    ELSE 0
                END)::integer AS retired,
            sum(
                CASE
                    WHEN af.transaction_type = '${TransactionType.RETIREMENT}'::text AND af.transaction_subtype = '${RetirementType.TRANSFER_OUTFLOW}'::text AND af.status = '${AssetFlowStatus.SETTLED}'::text AND af.source_id = b.id THEN af.amount
                    ELSE 0
                END)::integer AS customer_transferred_outflow
          FROM "${environment.db.schema.rubicon}".books b
            LEFT JOIN "${environment.db.schema.rubicon}".asset_flows af ON af.source_id = b.id OR af.destination_id = b.id
          GROUP BY b.id, af.asset_id, af.asset_type, (date_trunc('quarter'::text, af.updated_at));`,
    );

    // portfolio_allocations_v2
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".portfolio_allocations_v2
        AS
        SELECT portfolio_completed_transactions_by_quarter_v2.portfolio_id,
            portfolio_completed_transactions_by_quarter_v2.asset_id,
            portfolio_completed_transactions_by_quarter_v2.asset_type,
            sum(portfolio_completed_transactions_by_quarter_v2.purchased - portfolio_completed_transactions_by_quarter_v2.retired - portfolio_completed_transactions_by_quarter_v2.customer_transferred_outflow) AS allocation
          FROM "${environment.db.schema.rubicon}".portfolio_completed_transactions_by_quarter_v2
          GROUP BY portfolio_completed_transactions_by_quarter_v2.portfolio_id, portfolio_completed_transactions_by_quarter_v2.asset_id, portfolio_completed_transactions_by_quarter_v2.asset_type;`,
    );

    // project_flags_v2
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".project_flags_v2
        AS
        SELECT projects.id,
            projects.name,
            projects.registry_project_id,
            projects.rct_standard,
            projects.suspended,
            projects.is_byorct_approved,
            projects.is_science_team_approved,
            NOT v.project_id IS NULL AS has_vintages,
            NOT t.id IS NULL AS has_trades,
            COALESCE(b.balance, 0::numeric) > 0::numeric AS has_balance
          FROM "${environment.db.schema.rubicon}".projects
            LEFT JOIN ( SELECT DISTINCT project_vintages.project_id
                  FROM "${environment.db.schema.rubicon}".project_vintages) v ON v.project_id = projects.id
            LEFT JOIN ( SELECT DISTINCT p.id
                  FROM "${environment.db.schema.rubicon}".asset_flows
                    JOIN "${environment.db.schema.rubicon}".project_vintages v_1 ON v_1.id = asset_flows.asset_id
                    JOIN "${environment.db.schema.rubicon}".projects p ON p.id = v_1.project_id) t ON t.id = projects.id
            LEFT JOIN ( SELECT project_vintages.project_id AS id,
                    - sum(balances.balance) AS balance
                  FROM "${environment.db.schema.ledger}".balances
                    JOIN "${environment.db.schema.rubicon}".project_vintages ON project_vintages.id = balances.asset_id
                  WHERE balances.asset_type = '${AssetType.REGISTRY_VINTAGE}'::text AND balances.type = '${LedgerBalanceType.SETTLED}'::text AND balances.owner_type = '${OwnerType.OFFSETS}'::text
                  GROUP BY project_vintages.project_id) b ON b.id = projects.id;`,
    );

    // project_vintage_historical_cost_basis_v1
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".project_vintage_historical_cost_basis_v1
        AS
        WITH dates AS (
                SELECT DISTINCT credit_historical_cost_basis_v1.project_vintage_id,
                    credit_historical_cost_basis_v1.settled_at
                  FROM "${environment.db.schema.rubicon}".credit_historical_cost_basis_v1
                )
        SELECT d.project_vintage_id,
            d.settled_at,
            sum(q.credits::numeric * q.cost_basis) / NULLIF(sum(q.credits), 0::numeric) AS cost_basis,
            sum(q.credits::numeric * q.cost_basis_raw_price) / NULLIF(sum(q.credits), 0::numeric) AS cost_basis_raw_price,
            sum(q.credits::numeric * q.cost_basis_service_fee) / NULLIF(sum(q.credits), 0::numeric) AS cost_basis_service_fee,
            sum(q.credits::numeric * q.cost_basis_other_fee) / NULLIF(sum(q.credits), 0::numeric) AS cost_basis_other_fee
          FROM dates d
            JOIN LATERAL ( SELECT qq.project_vintage_id,
                    qq.credits,
                    qq.cost_basis,
                    qq.cost_basis_raw_price,
                    qq.cost_basis_service_fee,
                    qq.cost_basis_other_fee
                  FROM ( SELECT hi.project_vintage_id,
                            hi.credit_flow_id,
                            rank() OVER (PARTITION BY hi.credit_inflow_id ORDER BY hi.settled_at DESC) AS rank,
                            hi.credits,
                            hi.cost_basis,
                            hi.cost_basis_raw_price,
                            hi.cost_basis_service_fee,
                            hi.cost_basis_other_fee,
                            hi.settled_at
                          FROM "${environment.db.schema.rubicon}".credit_historical_cost_basis_v1 hi
                          WHERE hi.project_vintage_id = d.project_vintage_id AND hi.settled_at <= d.settled_at) qq
                  WHERE qq.rank = 1) q USING (project_vintage_id)
          GROUP BY d.project_vintage_id, d.settled_at;`,
    );

    // project_vintage_groupings_v1
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".project_vintage_groupings_v1
        AS
        SELECT
                CASE
                    WHEN p.buffer_category_id IS NOT NULL THEN pbc.id
                    ELSE ptbc.id
                END AS buffer_category_id,
                CASE
                    WHEN p.buffer_category_id IS NOT NULL THEN pbc.name
                    ELSE ptbc.name
                END AS buffer_category_name,
            pt.id AS project_type_id,
            pt.name AS project_type_name,
            pt.category AS project_type_category,
            p.id AS project_id,
            p.name AS project_name,
            p.registry_project_id,
            p.is_science_team_approved AS project_is_science_team_approved,
            p.is_byorct_approved AS project_is_byorct_approved,
            p.suspended AS project_is_suspended,
            p.rct_standard AS project_is_rct_standard,
            p.integrity_grade_score AS project_integrity_grade_score,
            p.integrity_grade_score_risk_adjusted AS project_integrity_grade_score_risk_adjusted,
            pv.id AS project_vintage_id,
            pv.label AS project_vintage_label,
            pv."interval" AS project_vintage_interval,
            pv.risk_buffer_percentage AS project_vintage_risk_buffer_percentage,
            pv.low_buffer_percentage AS project_vintage_low_buffer_percentage,
            pv.high_buffer_percentage AS project_vintage_high_buffer_percentage
          FROM "${environment.db.schema.rubicon}".project_vintages pv
            JOIN "${environment.db.schema.rubicon}".projects p ON p.id = pv.project_id
            JOIN "${environment.db.schema.rubicon}".project_types pt ON pt.id = p.project_type_id
            LEFT JOIN "${environment.db.schema.rubicon}".buffer_categories pbc ON pbc.id = p.buffer_category_id
            LEFT JOIN "${environment.db.schema.rubicon}".buffer_categories ptbc ON ptbc.id = pt.buffer_category_id;`,
    );

    // transaction_assets_v1
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".transaction_assets_v1
        AS
        SELECT NULL::uuid AS parent_transaction_id,
            af.transaction_id,
            af.transaction_type,
            af.transaction_subtype,
            ad.id AS asset_id,
            ad.asset_type,
            ad.name AS asset_name,
            ad.registry_project_id AS asset_registry_project_id,
            count(af.*) > 1 AS has_accounting_entries,
            sum(af.amount) AS asset_quantity,
            COALESCE(sum(af.raw_price), 0::numeric) AS asset_raw_price,
            COALESCE(sum(af.service_fee), 0::numeric) AS asset_service_fee,
            COALESCE(sum(af.other_fee), 0::numeric) AS asset_other_fee
          FROM "${environment.db.schema.rubicon}".asset_flows af
            JOIN "${environment.db.schema.rubicon}".asset_details_v1 ad ON ad.id = af.asset_id
          WHERE af.transaction_type <> ALL (ARRAY['forward_line_item'::text, 'marketing_agreement_line_item'::text])
          GROUP BY af.transaction_id, af.transaction_type, af.transaction_subtype, ad.id, ad.asset_type, ad.name, ad.registry_project_id
        UNION ALL
        SELECT fli.forward_id AS parent_transaction_id,
            fli.id AS transaction_id,
            'forward_line_item'::text AS transaction_type,
            f.type AS transaction_subtype,
            ad.id AS asset_id,
            ad.asset_type,
            ad.name AS asset_name,
            ad.registry_project_id AS asset_registry_project_id,
            count(af.*) > 1 AS has_accounting_entries,
                CASE
                    WHEN fli.settled_amount IS NOT NULL THEN fli.settled_amount
                    ELSE fli.expected_amount
                END AS asset_quantity,
            COALESCE(fli.raw_price, 0::numeric) AS asset_raw_price,
            COALESCE(fli.service_fee, 0::numeric) AS asset_service_fee,
            COALESCE(fli.other_fee, 0::numeric) AS asset_other_fee
          FROM "${environment.db.schema.rubicon}".forward_line_items fli
            JOIN "${environment.db.schema.rubicon}".forwards f ON f.id = fli.forward_id
            JOIN "${environment.db.schema.rubicon}".asset_details_v1 ad ON ad.id = fli.project_vintage_id
            LEFT JOIN "${environment.db.schema.rubicon}".asset_flows af ON af.asset_id = fli.project_vintage_id
          GROUP BY f.id, fli.id, f.type, ad.id, ad.asset_type, ad.name, ad.registry_project_id
        UNION ALL
        SELECT mali.marketing_agreement_id AS parent_transaction_id,
            mali.id AS transaction_id,
            'marketing_agreement_line_item'::text AS transaction_type,
            NULL::text AS transaction_subtype,
            ad.id AS asset_id,
            ad.asset_type,
            ad.name AS asset_name,
            ad.registry_project_id AS asset_registry_project_id,
            count(af.*) > 1 AS has_accounting_entries,
            mali.amount AS asset_quantity,
            NULL::numeric AS asset_raw_price,
            NULL::numeric AS asset_service_fee,
            NULL::numeric AS asset_other_fee
          FROM "${environment.db.schema.rubicon}".marketing_agreement_line_items mali
            JOIN "${environment.db.schema.rubicon}".asset_details_v1 ad ON ad.id = mali.project_vintage_id
            LEFT JOIN "${environment.db.schema.rubicon}".asset_flows af ON af.asset_id = mali.project_vintage_id
          GROUP BY mali.marketing_agreement_id, mali.id, ad.id, ad.asset_type, ad.name, ad.registry_project_id;`,
    );

    // transactions_details_v2
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.rubicon}".transactions_details_v2
        AS
        WITH transaction_documents AS (
                SELECT DISTINCT d.related_key,
                    count(d.*) AS docs_count
                  FROM "${environment.db.schema.rubicon}".documents d
                  GROUP BY d.related_key
                  ORDER BY d.related_key
                )
        SELECT p1.id,
            p1.created_at,
            p1.updated_at,
            p1.ui_key,
            '${TransactionType.PURCHASE}'::text AS type,
            NULL::text AS subtype,
                CASE
                    WHEN p1.status = 'executed'::text AND p1.is_delivered THEN 'delivered'::text
                    WHEN p1.status = 'executed'::text AND p1.is_paid THEN 'paid'::text
                    ELSE p1.status
                END AS status,
            p1.date_finished AS settled_at,
            o1.id AS counterparty_id,
            o1.name AS counterparty_name,
            count(ta1.*) AS unique_assets_count,
            sum(ta1.asset_quantity) AS total_quantity,
            COALESCE(sum(ta1.asset_raw_price), 0::numeric) AS total_raw_price,
            COALESCE(sum(ta1.asset_service_fee), 0::numeric) AS total_service_fee,
            COALESCE(sum(ta1.asset_other_fee), 0::numeric) AS total_other_fee,
            COALESCE(d1.docs_count, 0::bigint) AS docs_count,
            NULL::uuid AS parent_id
          FROM "${environment.db.schema.rubicon}".purchases p1
            JOIN "${environment.db.schema.rubicon}".books b1 ON b1.id = p1.customer_portfolio_id
            JOIN "${environment.db.schema.rubicon}".organizations o1 ON o1.id = b1.organization_id
            LEFT JOIN "${environment.db.schema.rubicon}".transaction_assets_v1 ta1 ON ta1.transaction_id = p1.id
            LEFT JOIN transaction_documents d1 ON d1.related_key = p1.ui_key
          GROUP BY p1.id, p1.created_at, p1.updated_at, p1.ui_key, o1.id, o1.name, ta1.transaction_id, d1.related_key, d1.docs_count
        UNION ALL
        SELECT r2.id,
            r2.created_at,
            r2.updated_at,
            r2.ui_key,
            '${TransactionType.RETIREMENT}'::text AS type,
            r2.type AS subtype,
            r2.status,
            r2.date_finished AS settled_at,
            o2.id AS counterparty_id,
            o2.name AS counterparty_name,
            count(ta2.*) AS unique_assets_count,
            sum(ta2.asset_quantity) AS total_quantity,
            COALESCE(sum(ta2.asset_raw_price), 0::numeric) AS total_raw_price,
            COALESCE(sum(ta2.asset_service_fee), 0::numeric) AS total_service_fee,
            COALESCE(sum(ta2.asset_other_fee), 0::numeric) AS total_other_fee,
            COALESCE(d2.docs_count, 0::bigint) AS docs_count,
            NULL::uuid AS parent_id
          FROM "${environment.db.schema.rubicon}".retirements r2
            JOIN "${environment.db.schema.rubicon}".books b2 ON b2.id = r2.customer_portfolio_id
            JOIN "${environment.db.schema.rubicon}".organizations o2 ON o2.id = b2.organization_id
            LEFT JOIN "${environment.db.schema.rubicon}".transaction_assets_v1 ta2 ON ta2.transaction_id = r2.id
            LEFT JOIN transaction_documents d2 ON d2.related_key = r2.ui_key
          GROUP BY r2.id, r2.created_at, r2.updated_at, r2.ui_key, o2.id, o2.name, ta2.transaction_id, d2.related_key, d2.docs_count
        UNION ALL
        SELECT t3.id,
            t3.created_at,
            t3.updated_at,
            t3.ui_key,
            '${TransactionType.TRADE}'::text AS type,
            t3.type AS subtype,
                CASE
                    WHEN t3.status = 'executed'::text AND t3.is_delivered THEN 'delivered'::text
                    WHEN t3.status = 'executed'::text AND t3.is_paid THEN 'paid'::text
                    ELSE t3.status
                END AS status,
            t3.settled_at,
            c3.id AS counterparty_id,
            c3.name AS counterparty_name,
            count(ta3.*) AS unique_assets_count,
            sum(ta3.asset_quantity) AS total_quantity,
            COALESCE(sum(ta3.asset_raw_price), 0::numeric) AS total_raw_price,
            COALESCE(sum(ta3.asset_service_fee), 0::numeric) AS total_service_fee,
            COALESCE(sum(ta3.asset_other_fee), 0::numeric) AS total_other_fee,
            COALESCE(d3.docs_count, 0::bigint) AS docs_count,
            NULL::uuid AS parent_id
          FROM "${environment.db.schema.rubicon}".trades t3
            LEFT JOIN "${environment.db.schema.rubicon}".transaction_assets_v1 ta3 ON ta3.transaction_id = t3.id
            LEFT JOIN "${environment.db.schema.rubicon}".trade_counterparties tc3 ON tc3.trade_id = t3.id AND tc3.is_primary = true
            LEFT JOIN "${environment.db.schema.rubicon}".counterparties c3 ON c3.id = tc3.counterparty_id
            LEFT JOIN transaction_documents d3 ON d3.related_key = t3.ui_key
          GROUP BY t3.id, t3.created_at, t3.updated_at, t3.ui_key, c3.id, c3.name, ta3.transaction_id, d3.related_key, d3.docs_count
        UNION ALL
        SELECT t4.id,
            t4.created_at,
            t4.updated_at,
            NULL::text AS ui_key,
            '${TransactionType.INTERNAL_TRANSFER}'::text AS type,
            NULL::text AS subtype,
            'executed'::text AS status,
            t4.updated_at AS settled_at,
            NULL::uuid AS counterparty_id,
            'internal'::text AS counterparty_name,
            count(ta4.*) AS unique_assets_count,
            sum(ta4.asset_quantity) AS total_quantity,
            COALESCE(sum(ta4.asset_raw_price), 0::numeric) AS total_raw_price,
            COALESCE(sum(ta4.asset_service_fee), 0::numeric) AS total_service_fee,
            COALESCE(sum(ta4.asset_other_fee), 0::numeric) AS total_other_fee,
            0 AS docs_count,
            NULL::uuid AS parent_id
          FROM "${environment.db.schema.rubicon}".transfers t4
            LEFT JOIN "${environment.db.schema.rubicon}".transaction_assets_v1 ta4 ON ta4.transaction_id = t4.id
          GROUP BY t4.id, t4.created_at, t4.updated_at, ta4.transaction_id
        UNION ALL
        SELECT r5.id,
            r5.created_at,
            r5.updated_at,
            NULL::text AS ui_key,
            '${TransactionType.RESERVE}'::text AS type,
            NULL::text AS subtype,
                CASE
                    WHEN r5.is_deleted THEN 'deleted'::text
                    ELSE 'reserved'::text
                END AS status,
            r5.updated_at AS settled_at,
            o5.id AS counterparty_id,
            o5.name AS counterparty_name,
            count(ta5.*) AS unique_assets_count,
            r5.amount::numeric AS total_quantity,
            COALESCE(sum(ta5.asset_raw_price), 0::numeric) AS total_raw_price,
            COALESCE(sum(ta5.asset_service_fee), 0::numeric) AS total_service_fee,
            COALESCE(sum(ta5.asset_other_fee), 0::numeric) AS total_other_fee,
            0 AS docs_count,
            NULL::uuid AS parent_id
          FROM "${environment.db.schema.rubicon}".reserves r5
            LEFT JOIN "${environment.db.schema.rubicon}".organizations o5 ON o5.id = r5.organization_id
            LEFT JOIN "${environment.db.schema.rubicon}".transaction_assets_v1 ta5 ON ta5.transaction_id = r5.id
          GROUP BY r5.id, r5.created_at, r5.updated_at, o5.id, o5.name, ta5.transaction_id
        UNION ALL
        SELECT fli6.id,
            fli6.created_at,
            fli6.updated_at,
            fli6.ui_key,
            '${TransactionType.FORWARD_LINE_ITEM}'::text AS type,
            f6.type AS subtype,
            fli6.status,
            fli6.settled_at,
                CASE
                    WHEN f6.customer_portfolio_id IS NOT NULL THEN o6.id
                    ELSE NULL::uuid
                END AS counterparty_id,
                CASE
                    WHEN f6.customer_portfolio_id IS NOT NULL THEN o6.name
                    ELSE f6.counterparty
                END AS counterparty_name,
            1 AS unique_assets_count,
            sum(ta6.asset_quantity) AS total_quantity,
            COALESCE(sum(ta6.asset_raw_price), 0::numeric) AS total_raw_price,
            COALESCE(sum(ta6.asset_service_fee), 0::numeric) AS total_service_fee,
            COALESCE(sum(ta6.asset_other_fee), 0::numeric) AS total_other_fee,
            COALESCE(d6.docs_count, 0::bigint) AS docs_count,
            f6.id AS parent_id
          FROM "${environment.db.schema.rubicon}".forward_line_items fli6
            JOIN "${environment.db.schema.rubicon}".forwards f6 ON f6.id = fli6.forward_id
            LEFT JOIN "${environment.db.schema.rubicon}".books b6 ON b6.id = f6.customer_portfolio_id
            LEFT JOIN "${environment.db.schema.rubicon}".organizations o6 ON o6.id = b6.organization_id
            LEFT JOIN "${environment.db.schema.rubicon}".transaction_assets_v1 ta6 ON ta6.transaction_id = fli6.id
            LEFT JOIN transaction_documents d6 ON d6.related_key = fli6.ui_key
          GROUP BY fli6.id, fli6.created_at, fli6.updated_at, fli6.ui_key, f6.type, f6.counterparty, f6.customer_portfolio_id, o6.id, o6.name, ta6.transaction_id, d6.related_key, d6.docs_count, f6.id
        UNION ALL
        SELECT mali7.id,
            mali7.created_at,
            mali7.updated_at,
            mali7.ui_key,
            '${TransactionType.MARKETING_AGREEMENT_LINE_ITEM}'::text AS type,
            NULL::text AS subtype,
            mali7.status,
            mali7.settled_at,
            NULL::uuid AS counterparty_id,
            m7.developer AS counterparty_name,
            1 AS unique_assets_count,
            sum(ta7.asset_quantity) AS total_quantity,
            COALESCE(sum(ta7.asset_raw_price), NULL::numeric) AS total_raw_price,
            COALESCE(sum(ta7.asset_service_fee), NULL::numeric) AS total_service_fee,
            COALESCE(sum(ta7.asset_other_fee), NULL::numeric) AS total_other_fee,
            COALESCE(d7.docs_count, 0::bigint) AS docs_count,
            m7.id AS parent_id
          FROM "${environment.db.schema.rubicon}".marketing_agreement_line_items mali7
            JOIN "${environment.db.schema.rubicon}".marketing_agreements m7 ON m7.id = mali7.marketing_agreement_id
            LEFT JOIN "${environment.db.schema.rubicon}".transaction_assets_v1 ta7 ON ta7.transaction_id = mali7.id
            LEFT JOIN transaction_documents d7 ON d7.related_key = mali7.ui_key
          GROUP BY mali7.id, mali7.created_at, mali7.updated_at, mali7.ui_key, m7.developer, ta7.transaction_id, d7.related_key, d7.docs_count, m7.id;`,
    );
  }
}
