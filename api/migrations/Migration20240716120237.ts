import { Migration } from '@mikro-orm/migrations';
import { environment } from '@env/environment';
import { AssetFlowStatus, AssetType, BookType, TradeStatus } from '@rubiconcarbon/shared-types';

// create reports tables and views, must keep even during migration cleanup
// condensed reports schema 2025.01.08 by irene
export class Migration20240716120237 extends Migration {
  async up(): Promise<void> {
    this.addSql(`CREATE SCHEMA IF NOT EXISTS "${environment.db.schema.reports}";`);

    // holdings_with_sandbox_status view
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.reports}".holdings_with_sandbox_status
        AS
        WITH pending_trades AS (
                SELECT virtual_table.registry_project_id,
                    virtual_table.label,
                    sum(virtual_table.amount) FILTER (WHERE virtual_table.action = 'buy'::text) AS pending_buy,
                    sum(virtual_table.amount) FILTER (WHERE virtual_table.action = ANY (ARRAY['sell'::text, 'purchase'::text])) AS pending_sell
                  FROM ( SELECT t.ui_key AS trade_key,
                            p_1.registry_project_id,
                            p_1.name,
                            pv_1.label,
                            af.amount,
                            af.transaction_subtype AS action,
                            af.status
                          FROM "${environment.db.schema.rubicon}".asset_flows af
                            JOIN "${environment.db.schema.rubicon}".trades t ON af.transaction_id = t.id
                            LEFT JOIN "${environment.db.schema.rubicon}".project_vintages pv_1 ON pv_1.id = af.asset_id
                            LEFT JOIN "${environment.db.schema.rubicon}".projects p_1 ON p_1.id = pv_1.project_id
                          WHERE t.status <> ALL (ARRAY['${TradeStatus.SETTLED}'::text, '${TradeStatus.CANCELED}'::text, '${TradeStatus.INDICATIVE}'::text])
                        UNION
                        SELECT purchases.ui_key,
                            p_1.registry_project_id,
                            p_1.name,
                            pv_1.label,
                            af.amount,
                            af.transaction_type AS action,
                            af.status
                          FROM "${environment.db.schema.rubicon}".purchases
                            JOIN "${environment.db.schema.rubicon}".asset_flows af ON af.transaction_id = purchases.id
                            LEFT JOIN "${environment.db.schema.rubicon}".project_vintages pv_1 ON pv_1.id = af.asset_id
                            LEFT JOIN "${environment.db.schema.rubicon}".projects p_1 ON p_1.id = pv_1.project_id
                          WHERE af.status = '${AssetFlowStatus.PENDING}'::text AND af.asset_type = '${AssetType.REGISTRY_VINTAGE}'::text) virtual_table
                  GROUP BY virtual_table.registry_project_id, virtual_table.label
                ), pending_retirements AS (
                WITH average_cost AS (
                        SELECT asset_flows_1.transaction_id,
                            asset_flows_1.asset_id AS project_vintage_id,
                            sum(asset_flows_1.amount) AS amount
                          FROM "${environment.db.schema.rubicon}".asset_flows asset_flows_1
                          GROUP BY asset_flows_1.transaction_id, asset_flows_1.asset_id
                        )
                SELECT projects.registry_project_id,
                    project_vintages.label,
                    sum(average_cost.amount) AS pending_retirement
                  FROM "${environment.db.schema.rubicon}".asset_flows
                    JOIN "${environment.db.schema.rubicon}".project_vintages ON project_vintages.id = asset_flows.asset_id
                    JOIN "${environment.db.schema.rubicon}".projects ON projects.id = project_vintages.project_id
                    JOIN average_cost ON average_cost.transaction_id = asset_flows.transaction_id AND average_cost.project_vintage_id = asset_flows.asset_id
                  WHERE asset_flows.transaction_type = 'retirement'::text AND asset_flows.status = 'pending'::text
                  GROUP BY projects.registry_project_id, project_vintages.label
                ), current_allocations AS (
                SELECT ac.component_id AS project_vintage_id,
                    sum(ac.holdings) FILTER (WHERE b.type = '${BookType.RCT_PUBLIC}'::text) AS allocated_rct,
                    sum(ac.holdings) FILTER (WHERE b.type = '${BookType.RCT_CUSTOM}'::text) AS allocated_custom,
                    sum(ac.holdings) FILTER (WHERE b.type = '${BookType.PORTFOLIO_RESERVES}'::text) AS allocated_reserve,
                    sum(ac.holdings) FILTER (WHERE b.type = '${BookType.PORTFOLIO_CUSTOMER}'::text) AS allocated_customer,
                    sum(ac.holdings) FILTER (WHERE b.type <> ALL (ARRAY['${BookType.PORTFOLIO_CUSTOMER}'::text, '${BookType.PORTFOLIO_RESERVES}'::text, '${BookType.RCT_CUSTOM}'::text, '${BookType.RCT_PUBLIC}'::text])) AS allocated_other
                  FROM "${environment.db.schema.rubicon}".asset_composition_v2 ac
                    JOIN "${environment.db.schema.rubicon}".books b ON b.id = ac.parent_id
                  WHERE ac.asset_type = 'registry_vintage'::text AND ac.level = 1
                  GROUP BY ac.component_id
                ), forward_deliveries AS (
                SELECT fli.project_vintage_id,
                    sum(fli.expected_amount) FILTER (WHERE f.type = 'buy'::text) AS expected_amount_buy,
                    sum(fli.expected_amount) FILTER (WHERE f.type = 'sell'::text) AS expected_amount_sell,
                    array_agg(fli.last_updated_delivery_date::date::text) AS last_updated_delivery_date
                  FROM "${environment.db.schema.rubicon}".forwards f
                    JOIN "${environment.db.schema.rubicon}".forward_line_items fli ON fli.forward_id = f.id
                  WHERE fli.status = 'pending'::text
                  GROUP BY fli.project_vintage_id
                ), marketing_agreements AS (
                SELECT mli.project_vintage_id,
                    sum(mli.amount) AS marketing_amount,
                    array_agg(mli.last_updated_delivery_date::date::text) AS last_updated_delivery_date
                  FROM "${environment.db.schema.rubicon}".marketing_agreements m
                    JOIN "${environment.db.schema.rubicon}".marketing_agreement_line_items mli ON mli.marketing_agreement_id = m.id
                  WHERE mli.status = 'pending'::text AND m.status = 'contracted'::text
                  GROUP BY mli.project_vintage_id
                ), sandbox_status AS (
                SELECT (model_portfolio_components.project_vintage_id)::uuid AS vintage_id,
                    NULLIF(sum((model_portfolio_components.quantity)::bigint * (lower(model_portfolios.status) = 'client-reviewing'::text)::integer), 0::numeric) AS client_reviewing,
                    NULLIF(sum((model_portfolio_components.quantity)::bigint * (model_portfolios.status = 'encumber-lite'::text)::integer), 0::numeric) AS encumber_lite,
                    NULLIF(sum((model_portfolio_components.quantity)::bigint * (model_portfolios.status = 'firm-reserved'::text)::integer), 0::numeric) AS firm_reserved
                  FROM "${environment.db.schema.rubicon}".model_portfolio_components
                    JOIN "${environment.db.schema.rubicon}".model_portfolios ON model_portfolios.id = model_portfolio_components.model_portfolio_id
                  WHERE model_portfolios.status IS NOT NULL AND ((model_portfolio_components.project_vintage_id)::uuid) IS NOT NULL AND NOT model_portfolios.is_deleted
                  GROUP BY ((model_portfolio_components.project_vintage_id)::uuid)
                )
        SELECT p.registry_project_id AS "Registry Project ID",
            p.name AS "Project",
            pv.label AS "Vintage",
            c.name AS "Country",
            c.region AS "Region",
            c.sub_region AS "Sub Region",
                CASE
                    WHEN p.rct_standard THEN p.rct_standard
                    ELSE NULL::boolean
                END AS "RCT Standard",
                CASE
                    WHEN p.suspended THEN p.suspended
                    ELSE NULL::boolean
                END AS "Suspended",
                CASE
                    WHEN p.is_science_team_approved THEN p.is_science_team_approved
                    ELSE NULL::boolean
                END AS "Website Ready",
                CASE
                    WHEN p.rct_standard AND p.is_science_team_approved AND pv.risk_buffer_percentage < 1::numeric AND NOT p.suspended THEN true
                    ELSE NULL::boolean
                END AS "RCT Portfolio Eligible",
            pva.holdings AS "Holdings",
            current_allocations.allocated_rct AS "Allocated to RCT (QTY)",
            current_allocations.allocated_custom AS "Allocated to Custom (QTY)",
            current_allocations.allocated_reserve AS "Allocated to Reserves (QTY)",
            current_allocations.allocated_customer AS "Allocated to Customers (QTY)",
            current_allocations.allocated_other AS "Allocated to Other (QTY)",
            pending_trades.pending_buy AS "Pending Buy",
            '-1'::integer * pending_trades.pending_sell AS "Pending Sell",
            '-1'::integer::numeric * pending_retirements.pending_retirement AS "Pending Retirement",
            forward_deliveries.expected_amount_buy AS "Buy Pending Forward Delivery QTY",
            '-1'::integer * forward_deliveries.expected_amount_sell AS "Sell Pending Forward Delivery QTY",
            forward_deliveries.last_updated_delivery_date AS "Forward Delivery Dates",
            marketing_agreements.marketing_amount AS "Marketing Amount",
            marketing_agreements.last_updated_delivery_date AS "Marketing Delivery Dates",
            p.registry_name AS "Registry",
            pt.category AS "Project Category",
            pt.type AS "Project Type",
            pva.average_cost_basis AS "Average Cost Basis",
            asset_prices_v5.unit_value AS "MTM",
            sum(ppm.price) FILTER (WHERE ppm.source = 'viridios'::text) AS "Viridios Price",
            pv.risk_buffer_percentage AS "Risk Buffer Percentage",
            buffer_categories.name AS "Buffer Category",
            sandbox_status.client_reviewing AS "Client Reviewing",
            sandbox_status.encumber_lite AS "Encumber-Lite",
            sandbox_status.firm_reserved AS "Firm Reserved",
            pv.id AS project_vintage_id,
            pv.project_id,
            p.integrity_grade_score,
                CASE
                    WHEN p.integrity_grade_score >= 84.5 THEN 'A'::text
                    WHEN p.integrity_grade_score < 84.5 AND p.integrity_grade_score >= 74.5 THEN 'B'::text
                    WHEN p.integrity_grade_score < 74.5 AND p.integrity_grade_score >= 59.5 THEN 'C'::text
                    WHEN p.integrity_grade_score < 59.5 THEN 'D'::text
                    ELSE NULL::text
                END AS integrity_grade_letter
          FROM "${environment.db.schema.rubicon}".project_vintages pv
            JOIN "${environment.db.schema.rubicon}".projects p ON p.id = pv.project_id
            LEFT JOIN "${environment.db.schema.rubicon}".countries c ON c.alpha3::text = p.country_code::text
            LEFT JOIN "${environment.db.schema.rubicon}".project_types pt ON pt.id = p.project_type_id
            LEFT JOIN "${environment.db.schema.rubicon}".buffer_categories ON COALESCE(p.buffer_category_id, pt.buffer_category_id) = buffer_categories.id
            LEFT JOIN "${environment.db.schema.marketData}".project_price_metrics ppm ON p.registry_project_id = ppm.project_id AND pv.label = ppm.vintage
            LEFT JOIN pending_trades USING (registry_project_id, label)
            LEFT JOIN pending_retirements USING (registry_project_id, label)
            LEFT JOIN current_allocations ON pv.id = current_allocations.project_vintage_id
            LEFT JOIN "${environment.db.schema.rubicon}".asset_prices_v5 ON asset_prices_v5.owner_id = pv.id
            LEFT JOIN "${environment.db.schema.rubicon}".project_vintage_amounts_v3 pva ON pv.id = pva.asset_id
            LEFT JOIN "${environment.db.schema.rubicon}".project_flags_v2 pf ON pf.id = p.id
            LEFT JOIN forward_deliveries ON forward_deliveries.project_vintage_id = pv.id
            LEFT JOIN marketing_agreements ON marketing_agreements.project_vintage_id = pv.id
            FULL JOIN sandbox_status ON sandbox_status.vintage_id = pv.id
          WHERE pva.holdings > 0 OR pending_trades.pending_buy > 0 OR forward_deliveries.expected_amount_buy > 0 OR forward_deliveries.expected_amount_sell > 0 OR marketing_agreements.marketing_amount > 0 OR sandbox_status.vintage_id IS NOT NULL
          GROUP BY p.registry_project_id, p.name, pv.label, c.name, c.region, c.sub_region, (
                CASE
                    WHEN p.rct_standard THEN p.rct_standard
                    ELSE NULL::boolean
                END), (
                CASE
                    WHEN p.suspended THEN p.suspended
                    ELSE NULL::boolean
                END), (
                CASE
                    WHEN p.is_science_team_approved THEN p.is_science_team_approved
                    ELSE NULL::boolean
                END), (
                CASE
                    WHEN p.rct_standard AND p.is_science_team_approved AND pv.risk_buffer_percentage < 1::numeric AND NOT p.suspended THEN true
                    ELSE NULL::boolean
                END), pva.holdings, current_allocations.allocated_rct, current_allocations.allocated_custom, current_allocations.allocated_reserve, current_allocations.allocated_customer, current_allocations.allocated_other, pending_trades.pending_buy, ('-1'::integer * pending_trades.pending_sell), ('-1'::integer::numeric * pending_retirements.pending_retirement), forward_deliveries.expected_amount_buy, ('-1'::integer * forward_deliveries.expected_amount_sell), forward_deliveries.last_updated_delivery_date, marketing_agreements.marketing_amount, marketing_agreements.last_updated_delivery_date, p.registry_name, pt.category, pt.type, pva.average_cost_basis, asset_prices_v5.unit_value, pv.risk_buffer_percentage, buffer_categories.name, sandbox_status.client_reviewing, sandbox_status.encumber_lite, sandbox_status.firm_reserved, pv.id, pv.project_id, p.integrity_grade_score
          ORDER BY pva.holdings DESC;`,
    );

    // inventory_forward_purchases view
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.reports}".inventory_forward_purchases
        AS
        SELECT virtual_table.trade_id,
            virtual_table.project_id,
            virtual_table.project_vintage_id,
            virtual_table.trade_key,
            virtual_table.registry_project_id,
            virtual_table.name,
            virtual_table.label,
            virtual_table.amount,
            virtual_table.unit_cost_buy,
            virtual_table.total_cost_buy,
            virtual_table.raw_price,
            virtual_table.service_fee,
            virtual_table.other_fee,
            virtual_table.settled_at,
            virtual_table.created_at,
            virtual_table.memo,
            virtual_table.type
          FROM ( SELECT t.ui_key AS trade_key,
                    t.id AS trade_id,
                    p.id AS project_id,
                    pv.id AS project_vintage_id,
                    p.registry_project_id,
                    p.name,
                    pv.label,
                    t.amount,
                    COALESCE(a.raw_price, 0::numeric) + COALESCE(a.service_fee, 0::numeric) + COALESCE(a.other_fee, 0::numeric) AS total_cost_buy,
                    COALESCE(a.raw_price, 0::numeric) - COALESCE(a.service_fee, 0::numeric) - COALESCE(a.other_fee, 0::numeric) AS total_cost_sell,
                    (COALESCE(a.raw_price, 0::numeric) + COALESCE(a.service_fee, 0::numeric) + COALESCE(a.other_fee, 0::numeric)) / t.amount::numeric AS unit_cost_buy,
                    (COALESCE(a.raw_price, 0::numeric) - COALESCE(a.service_fee, 0::numeric) - COALESCE(a.other_fee, 0::numeric)) / t.amount::numeric AS unit_cost_sell,
                    a.raw_price,
                    a.service_fee,
                    a.other_fee,
                    t.settled_at,
                    t.created_at,
                    t.type,
                    t.status,
                    t.memo,
                    t.counterparty_name
                  FROM "${environment.db.schema.rubicon}".trades t
                    JOIN "${environment.db.schema.rubicon}".asset_flows a ON a.transaction_id = t.id
                    LEFT JOIN "${environment.db.schema.rubicon}".project_vintages pv ON pv.id = a.asset_id
                    LEFT JOIN "${environment.db.schema.rubicon}".projects p ON p.id = pv.project_id) virtual_table
          WHERE virtual_table.type = 'buy'::text AND (virtual_table.status <> ALL (ARRAY['settled'::text, 'canceled'::text]))
          ORDER BY virtual_table.settled_at DESC;`,
    );

    // inventory_waterfall view
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.reports}".inventory_waterfall
        AS
        WITH line_items AS (
                SELECT asset_flows.transaction_id,
                    json_object_agg(t.ui_key, abs(cf_o.amount)) AS line_items,
                    cf_i.project_vintage_id,
                    sum(abs(cf_o.amount)) AS credits,
                    sum(cf_o.amount::numeric * cb.cost_basis) / NULLIF(sum(cf_o.amount), 0)::numeric AS cost_basis,
                    sum(cf_o.amount::numeric * cb.cost_basis_raw_price) / NULLIF(sum(cf_o.amount), 0)::numeric AS cost_basis_raw_price,
                    sum(cf_o.amount::numeric * cb.cost_basis_service_fee) / NULLIF(sum(cf_o.amount), 0)::numeric AS cost_basis_service_fee,
                    sum(cf_o.amount::numeric * cb.cost_basis_other_fee) / NULLIF(sum(cf_o.amount), 0)::numeric AS cost_basis_other_fee
                  FROM "${environment.db.schema.rubicon}".asset_flows
                    JOIN "${environment.db.schema.rubicon}".credit_flows cf_o ON cf_o.asset_flow_id = asset_flows.id
                    JOIN "${environment.db.schema.rubicon}".credit_flows cf_i ON cf_i.id = cf_o.linked_credit_flow_id
                    JOIN "${environment.db.schema.rubicon}".asset_flows af_i ON af_i.id = cf_i.asset_flow_id
                    JOIN "${environment.db.schema.rubicon}".trades t ON t.id = af_i.transaction_id
                    LEFT JOIN LATERAL ( SELECT cf_i.id,
                            credit_historical_cost_basis_v1.cost_basis,
                            credit_historical_cost_basis_v1.cost_basis_raw_price,
                            credit_historical_cost_basis_v1.cost_basis_service_fee,
                            credit_historical_cost_basis_v1.cost_basis_other_fee
                          FROM "${environment.db.schema.rubicon}".credit_historical_cost_basis_v1
                          WHERE credit_historical_cost_basis_v1.credit_inflow_id = cf_i.id AND credit_historical_cost_basis_v1.settled_at <= asset_flows.settled_at
                          ORDER BY credit_historical_cost_basis_v1.settled_at DESC
                        LIMIT 1) cb ON cb.id = cf_i.id
                  GROUP BY asset_flows.id, cf_i.project_vintage_id
                ), retirements AS (
                SELECT organizations.salesforce_identifier AS salesforce_id,
                    organizations.id::text AS internal_id,
                    organizations.name AS customer_name,
                    retirements.ui_key AS transaction_key,
                    books.name AS basket,
                    books.type AS basket_type,
                    p.name,
                    p.registry_project_id,
                    pv.label,
                    line_items.credits AS retirement_amount,
                    line_items.cost_basis AS value,
                    line_items.cost_basis_raw_price AS average_unit_cost_raw_material,
                    line_items.cost_basis_service_fee AS average_unit_cost_service_fees,
                    line_items.cost_basis_other_fee AS average_unit_cost_other_fees,
                    retirements.date_started,
                    retirements.date_finished,
                    retirements.created_at,
                    line_items.line_items,
                    retirements.memo,
                    retirements.beneficiary
                  FROM "${environment.db.schema.rubicon}".retirements
                    LEFT JOIN "${environment.db.schema.rubicon}".books ON books.id = retirements.customer_portfolio_id
                    LEFT JOIN "${environment.db.schema.rubicon}".organizations ON organizations.id = books.organization_id
                    LEFT JOIN "${environment.db.schema.rubicon}".asset_flows ON asset_flows.transaction_id = retirements.id
                    LEFT JOIN "${environment.db.schema.rubicon}".project_vintages pv ON pv.id = asset_flows.asset_id
                    LEFT JOIN "${environment.db.schema.rubicon}".projects p ON p.id = pv.project_id
                    JOIN line_items ON line_items.project_vintage_id = pv.id AND line_items.transaction_id = asset_flows.transaction_id
                  WHERE retirements.type = 'retirement'::text AND retirements.status = 'completed'::text
                ), trades AS (
                SELECT t.ui_key AS trade_key,
                    p.registry_project_id,
                    p.name,
                    pv.label,
                        CASE
                            WHEN t.type = 'buy'::text THEN t.amount
                            ELSE t.amount * '-1'::integer
                        END AS amount,
                        CASE
                            WHEN t.type = 'buy'::text THEN sum(asset_flows.raw_price + COALESCE(asset_flows.service_fee, 0::numeric) + COALESCE(asset_flows.other_fee, 0::numeric)) FILTER (WHERE asset_flows.amount > 0)
                            WHEN t.type = 'sell'::text THEN '-1'::integer::numeric * sum(cf_o.amount::numeric * cb.cost_basis * '-1'::integer::numeric)
                            ELSE NULL::numeric
                        END AS value,
                    t.settled_at,
                    t.created_at,
                    t.type,
                    t.status,
                    t.memo,
                    t.counterparty_name,
                    books.name AS book_name
                  FROM "${environment.db.schema.rubicon}".trades t
                    LEFT JOIN "${environment.db.schema.rubicon}".books ON books.id = t.book_id
                    LEFT JOIN "${environment.db.schema.rubicon}".asset_flows ON asset_flows.transaction_id = t.id
                    LEFT JOIN "${environment.db.schema.rubicon}".credit_flows cf_o ON cf_o.asset_flow_id = asset_flows.id
                    LEFT JOIN "${environment.db.schema.rubicon}".credit_flows cf_i ON cf_i.id = cf_o.linked_credit_flow_id
                    LEFT JOIN "${environment.db.schema.rubicon}".asset_flows li_vf ON li_vf.id = cf_i.asset_flow_id
                    LEFT JOIN "${environment.db.schema.rubicon}".trades li_t ON li_t.id = li_vf.transaction_id
                    LEFT JOIN LATERAL ( SELECT cf_i.id,
                            credit_historical_cost_basis_v1.cost_basis,
                            credit_historical_cost_basis_v1.cost_basis_raw_price,
                            credit_historical_cost_basis_v1.cost_basis_service_fee,
                            credit_historical_cost_basis_v1.cost_basis_other_fee
                          FROM "${environment.db.schema.rubicon}".credit_historical_cost_basis_v1
                          WHERE credit_historical_cost_basis_v1.credit_inflow_id = cf_i.id AND credit_historical_cost_basis_v1.settled_at <= asset_flows.settled_at
                          ORDER BY credit_historical_cost_basis_v1.settled_at DESC
                        LIMIT 1) cb ON cb.id = cf_i.id
                    LEFT JOIN "${environment.db.schema.rubicon}".project_vintages pv ON pv.id = asset_flows.asset_id
                    LEFT JOIN "${environment.db.schema.rubicon}".projects p ON p.id = pv.project_id
                  GROUP BY t.ui_key, p.registry_project_id, p.name, pv.label, (
                        CASE
                            WHEN t.type = 'buy'::text THEN t.amount
                            ELSE t.amount * '-1'::integer
                        END), t.settled_at, t.created_at, t.type, t.status, t.memo, t.counterparty_name, books.name
                ), transfers AS (
                SELECT retirements.ui_key AS trade_key,
                    p.name,
                    p.registry_project_id,
                    pv.label,
                    asset_flows.amount,
                    retirements.date_started AS create_date,
                    retirements.date_finished AS settled_at,
                    retirements.created_at,
                    json_object_agg(t.ui_key, abs(cf_o.amount)) AS line_items,
                    retirements.memo,
                    retirements.status,
                    retirements.type,
                    retirements.beneficiary AS counterparty_name,
                    sum(cf_o.amount::numeric * cb.cost_basis) / NULLIF(sum(cf_o.amount), 0)::numeric AS value
                  FROM "${environment.db.schema.rubicon}".retirements
                    LEFT JOIN "${environment.db.schema.rubicon}".asset_flows ON asset_flows.transaction_id = retirements.id
                    JOIN "${environment.db.schema.rubicon}".credit_flows cf_o ON cf_o.asset_flow_id = asset_flows.id
                    JOIN "${environment.db.schema.rubicon}".credit_flows cf_i ON cf_i.id = cf_o.linked_credit_flow_id
                    JOIN "${environment.db.schema.rubicon}".asset_flows af_i ON af_i.id = cf_i.asset_flow_id
                    JOIN "${environment.db.schema.rubicon}".trades t ON t.id = af_i.transaction_id
                    LEFT JOIN LATERAL ( SELECT cf_i.id,
                            credit_historical_cost_basis_v1.cost_basis,
                            credit_historical_cost_basis_v1.cost_basis_raw_price,
                            credit_historical_cost_basis_v1.cost_basis_service_fee,
                            credit_historical_cost_basis_v1.cost_basis_other_fee
                          FROM "${environment.db.schema.rubicon}".credit_historical_cost_basis_v1
                          WHERE credit_historical_cost_basis_v1.credit_inflow_id = cf_i.id AND credit_historical_cost_basis_v1.settled_at <= asset_flows.settled_at
                          ORDER BY credit_historical_cost_basis_v1.settled_at DESC
                        LIMIT 1) cb ON cb.id = cf_i.id
                    LEFT JOIN "${environment.db.schema.rubicon}".project_vintages pv ON pv.id = asset_flows.asset_id
                    LEFT JOIN "${environment.db.schema.rubicon}".projects p ON p.id = pv.project_id
                  WHERE retirements.type = 'transfer_outflow'::text AND asset_flows.asset_type = 'registry_vintage'::text
                  GROUP BY retirements.ui_key, p.name, p.registry_project_id, pv.label, asset_flows.amount, retirements.date_started, retirements.date_finished, retirements.created_at, retirements.memo, retirements.status, retirements.type, retirements.beneficiary
                ), accounting AS (
                SELECT p.registry_project_id,
                    p.name,
                    pv.label,
                    af.settled_at,
                    (ch.cost_basis - cbp.cost_basis) * ch.credits::numeric AS raw_price,
                    cf.project_vintage_id,
                    cf.type
                  FROM "${environment.db.schema.rubicon}".credit_flows cf
                    JOIN "${environment.db.schema.rubicon}".project_vintages pv ON pv.id = cf.project_vintage_id
                    JOIN "${environment.db.schema.rubicon}".projects p ON p.id = pv.project_id
                    JOIN "${environment.db.schema.rubicon}".asset_flows af ON cf.asset_flow_id = af.id
                    JOIN "${environment.db.schema.rubicon}".credit_historical_cost_basis_v1 ch ON ch.credit_flow_id = cf.id
                    JOIN LATERAL ( SELECT chp.credit_flow_id,
                            chp.credit_inflow_id,
                            chp.project_vintage_id,
                            chp.type,
                            chp.settled_at,
                            chp.credits,
                            chp.cost_basis,
                            chp.cost_basis_raw_price,
                            chp.cost_basis_service_fee,
                            chp.cost_basis_other_fee
                          FROM "${environment.db.schema.rubicon}".credit_historical_cost_basis_v1 chp
                          WHERE chp.settled_at < af.settled_at AND (chp.type = ANY (ARRAY['inflow'::text, 'accounting'::text])) AND chp.project_vintage_id = cf.project_vintage_id AND chp.credit_inflow_id = ch.credit_inflow_id
                          ORDER BY chp.settled_at DESC
                        LIMIT 1) cbp ON true
                  WHERE cf.type = 'accounting'::text
                ), beginning_inventory AS (
                SELECT retirements.registry_project_id,
                    retirements.name,
                    retirements.label,
                    retirements.retirement_amount * '-1'::integer AS amount,
                    retirements.value * retirements.retirement_amount::numeric * '-1'::integer::numeric AS value,
                    retirements.transaction_key,
                    retirements.date_started,
                    retirements.date_finished,
                    retirements.basket_type,
                    NULL::text AS status,
                    retirements.memo,
                    retirements.beneficiary
                  FROM retirements
                  WHERE date_trunc('day'::text, (retirements.date_finished AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) < current_setting('"${environment.db.schema.reports}".start_date'::text)::date
                UNION ALL
                SELECT accounting.registry_project_id,
                    accounting.name,
                    accounting.label,
                    NULL::bigint AS amount,
                    accounting.raw_price AS value,
                    NULL::text AS trade_key,
                    NULL::timestamp with time zone AS created_at,
                    accounting.settled_at,
                    accounting.type,
                    NULL::text AS status,
                    NULL::text AS memo,
                    NULL::text AS counterparty_name
                  FROM accounting
                  WHERE date_trunc('day'::text, (accounting.settled_at AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) < current_setting('"${environment.db.schema.reports}".start_date'::text)::date
                UNION ALL
                SELECT trades.registry_project_id,
                    trades.name,
                    trades.label,
                    trades.amount,
                    trades.value,
                    trades.trade_key,
                    trades.created_at,
                    trades.settled_at,
                    trades.type,
                    trades.status,
                    trades.memo,
                    trades.counterparty_name
                  FROM trades
                  WHERE date_trunc('day'::text, (trades.settled_at AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) < current_setting('"${environment.db.schema.reports}".start_date'::text)::date
                UNION ALL
                SELECT transfers.registry_project_id,
                    transfers.name,
                    transfers.label,
                    transfers.amount * '-1'::integer AS amount,
                    transfers.value * transfers.amount::numeric * '-1'::integer::numeric AS value,
                    transfers.trade_key,
                    transfers.created_at,
                    transfers.settled_at,
                    transfers.type,
                    transfers.status,
                    transfers.memo,
                    transfers.counterparty_name
                  FROM transfers
                  WHERE date_trunc('day'::text, (transfers.settled_at AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) < current_setting('"${environment.db.schema.reports}".start_date'::text)::date
                )
        SELECT trades.registry_project_id,
            trades.name,
            trades.label,
            trades.amount,
            trades.value,
            trades.trade_key,
            trades.created_at,
            trades.settled_at,
            trades.type,
            trades.status,
            trades.memo,
            trades.counterparty_name,
            'Purchases (trades)'::text AS transaction_type
          FROM trades
          WHERE trades.status = 'settled'::text AND trades.type = 'buy'::text AND date_trunc('day'::text, (trades.settled_at AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) >= current_setting('"${environment.db.schema.reports}".start_date'::text)::date AND date_trunc('day'::text, (trades.settled_at AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) <= current_setting('"${environment.db.schema.reports}".end_date'::text)::date
        UNION ALL
        SELECT trades.registry_project_id,
            trades.name,
            trades.label,
            trades.amount,
            trades.value,
            trades.trade_key,
            trades.created_at,
            trades.settled_at,
            trades.type,
            trades.status,
            trades.memo,
            trades.counterparty_name,
            'Sales (trades)'::text AS transaction_type
          FROM trades
          WHERE trades.status = 'settled'::text AND trades.type = 'sell'::text AND date_trunc('day'::text, (trades.settled_at AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) >= current_setting('"${environment.db.schema.reports}".start_date'::text)::date AND date_trunc('day'::text, (trades.settled_at AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) <= current_setting('"${environment.db.schema.reports}".end_date'::text)::date
        UNION ALL
        SELECT retirements.registry_project_id,
            retirements.name,
            retirements.label,
            retirements.retirement_amount * '-1'::integer AS amount,
            retirements.value * retirements.retirement_amount::numeric * '-1'::integer::numeric AS value,
            retirements.transaction_key AS trade_key,
            retirements.date_started AS created_at,
            retirements.date_finished AS settled_at,
            retirements.basket_type AS type,
            NULL::text AS status,
            retirements.memo,
            retirements.beneficiary AS counterparty_name,
            'Retirements (buffer)'::text AS transaction_type
          FROM retirements
          WHERE retirements.salesforce_id = 'Buffer'::text AND date_trunc('day'::text, (retirements.date_finished AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) >= current_setting('"${environment.db.schema.reports}".start_date'::text)::date AND date_trunc('day'::text, (retirements.date_finished AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) <= current_setting('"${environment.db.schema.reports}".end_date'::text)::date
        UNION ALL
        SELECT retirements.registry_project_id,
            retirements.name,
            retirements.label,
            retirements.retirement_amount * '-1'::integer AS amount,
            retirements.value * retirements.retirement_amount::numeric * '-1'::integer::numeric AS value,
            retirements.transaction_key AS trade_key,
            retirements.date_started AS created_at,
            retirements.date_finished AS settled_at,
            retirements.basket_type AS type,
            NULL::text AS status,
            retirements.memo,
            retirements.beneficiary AS counterparty_name,
            'Retirements (customer)'::text AS transaction_type
          FROM retirements
          WHERE retirements.salesforce_id <> 'Buffer'::text AND date_trunc('day'::text, (retirements.date_finished AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) >= current_setting('"${environment.db.schema.reports}".start_date'::text)::date AND date_trunc('day'::text, (retirements.date_finished AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) <= current_setting('"${environment.db.schema.reports}".end_date'::text)::date
        UNION ALL
        SELECT transfers.registry_project_id,
            transfers.name,
            transfers.label,
            transfers.amount * '-1'::integer AS amount,
            transfers.value * transfers.amount::numeric * '-1'::integer::numeric AS value,
            transfers.trade_key,
            transfers.created_at,
            transfers.settled_at,
            transfers.type,
            NULL::text AS status,
            transfers.memo,
            transfers.counterparty_name,
            'Transfers (customer)'::text AS transaction_type
          FROM transfers
          WHERE date_trunc('day'::text, (transfers.settled_at AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) >= current_setting('"${environment.db.schema.reports}".start_date'::text)::date AND date_trunc('day'::text, (transfers.settled_at AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) <= current_setting('"${environment.db.schema.reports}".end_date'::text)::date
        UNION ALL
        SELECT accounting.registry_project_id,
            accounting.name,
            accounting.label,
            NULL::bigint AS amount,
            accounting.raw_price AS value,
            NULL::text AS trade_key,
            NULL::timestamp with time zone AS created_at,
            accounting.settled_at,
            accounting.type,
            NULL::text AS status,
            NULL::text AS memo,
            NULL::text AS counterparty_name,
            'Offsets'::text AS transaction_type
          FROM accounting
          WHERE date_trunc('day'::text, (accounting.settled_at AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) >= current_setting('"${environment.db.schema.reports}".start_date'::text)::date AND date_trunc('day'::text, (accounting.settled_at AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) <= current_setting('"${environment.db.schema.reports}".end_date'::text)::date
        UNION ALL
        SELECT beginning_inventory.registry_project_id,
            beginning_inventory.name,
            beginning_inventory.label,
            beginning_inventory.amount,
            beginning_inventory.value,
            beginning_inventory.transaction_key AS trade_key,
            beginning_inventory.date_started AS created_at,
            beginning_inventory.date_finished AS settled_at,
            beginning_inventory.basket_type AS type,
            beginning_inventory.status,
            beginning_inventory.memo,
            beginning_inventory.beneficiary AS counterparty_name,
            'Beginning of Period Inventory'::text AS transaction_type
          FROM beginning_inventory;`,
    );

    // superset_holdings view
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.reports}".superset_holdings
        AS
        WITH internal_prices AS (
                ( SELECT DISTINCT ON (internal_prices.project_id, internal_prices.vintage, (date_trunc('day'::text, (internal_prices."timestamp" AT TIME ZONE 'AMERICA/LOS_ANGELES'::text))))
                        CASE
                            WHEN internal_prices.source = ANY (ARRAY['trader_override'::text, 'index_calculated'::text]) THEN 'rubicon_trader'::text
                            ELSE internal_prices.source
                        END AS source,
                    internal_prices.project_id,
                    internal_prices.vintage,
                    date_trunc('day'::text, (internal_prices."timestamp" AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) AS "timestamp",
                    internal_prices.price
                  FROM "${environment.db.schema.marketData}".internal_prices
                  ORDER BY internal_prices.project_id, internal_prices.vintage, (date_trunc('day'::text, (internal_prices."timestamp" AT TIME ZONE 'AMERICA/LOS_ANGELES'::text))))
                UNION
                SELECT 'rubicon_trader'::text AS source,
                    p_1.registry_project_id AS project_id,
                    pv_1.label AS vintage,
                    '2024-04-25 00:00:00'::timestamp without time zone AS "timestamp",
                    pvc.average_cost_basis AS price
                  FROM "${environment.db.schema.rubicon}".project_vintage_cost_basis_v1 pvc
                    JOIN "${environment.db.schema.rubicon}".project_vintages pv_1 ON pv_1.id = pvc.project_vintage_id
                    JOIN "${environment.db.schema.rubicon}".projects p_1 ON p_1.id = pv_1.project_id
                ), prices_no_gaps AS (
                SELECT dates."timestamp",
                    vintages.source,
                    vintages.project_id,
                    vintages.vintage,
                    p_1.price,
                    (jsonb_agg(p_1.price) FILTER (WHERE p_1.price IS NOT NULL) OVER (PARTITION BY vintages.source, vintages.project_id, vintages.vintage ORDER BY dates."timestamp" ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) ->> '-1'::integer)::numeric AS last_price
                  FROM generate_series('2024-04-25 00:00:00+00'::timestamp with time zone, now()::date::timestamp with time zone, '1 day'::interval) dates("timestamp")
                    CROSS JOIN ( SELECT DISTINCT internal_prices.source,
                            internal_prices.project_id,
                            internal_prices.vintage
                          FROM internal_prices) vintages
                    LEFT JOIN internal_prices p_1 ON p_1."timestamp" = dates."timestamp" AND p_1.source = vintages.source AND p_1.project_id = vintages.project_id AND p_1.vintage = vintages.vintage
                ), price_summary AS (
                SELECT prices_no_gaps.project_id,
                    prices_no_gaps.vintage,
                    sum(prices_no_gaps.last_price) FILTER (WHERE prices_no_gaps."timestamp" = current_setting('"${environment.db.schema.reports}".start_date'::text)::date) AS lagging_price,
                    sum(prices_no_gaps.last_price) FILTER (WHERE prices_no_gaps."timestamp" = current_setting('"${environment.db.schema.reports}".end_date'::text)::date) AS leading_price
                  FROM prices_no_gaps
                  GROUP BY prices_no_gaps.project_id, prices_no_gaps.vintage
                )
        SELECT dh.holding_date,
            dh.owner_type,
            dh.asset_id,
            p.name,
            p.registry_project_id,
            pv.label,
            pt.category,
            pt.type,
            dh.asset_type,
            ps.lagging_price,
            ps.leading_price,
            sum(dh.balance) AS holdings,
            sum(dh.balance) * ps.lagging_price::double precision AS lagging_value,
            sum(dh.balance) * ps.leading_price::double precision AS leading_value
          FROM "${environment.db.schema.marketData}".daily_holdings dh
            LEFT JOIN "${environment.db.schema.rubicon}".project_vintages pv ON pv.id = dh.asset_id::uuid
            LEFT JOIN "${environment.db.schema.rubicon}".projects p ON p.id = pv.project_id
            LEFT JOIN "${environment.db.schema.rubicon}".project_types pt ON pt.id = p.project_type_id
            LEFT JOIN price_summary ps ON ps.vintage = pv.label AND ps.project_id = p.registry_project_id
          WHERE (dh.owner_type <> ALL (ARRAY['registry'::text, 'external'::text, 'offsets'::text])) AND dh.asset_type = 'registry_vintage'::text AND (dh.holding_date = ANY (ARRAY[current_setting('"${environment.db.schema.reports}".start_date'::text)::date::timestamp without time zone, current_setting('"${environment.db.schema.reports}".end_date'::text)::date::timestamp without time zone]))
          GROUP BY dh.holding_date, dh.owner_type, dh.asset_id, p.name, p.registry_project_id, pv.label, pt.category, pt.type, dh.asset_type, ps.lagging_price, ps.leading_price
          ORDER BY dh.holding_date DESC, (sum(dh.balance)) DESC;`,
    );

    // superset_holdings_settled view
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.reports}".superset_holdings_settled
        AS
        WITH internal_prices AS (
                ( SELECT DISTINCT ON (internal_prices.project_id, internal_prices.vintage, (date_trunc('day'::text, (internal_prices."timestamp" AT TIME ZONE 'AMERICA/LOS_ANGELES'::text))))
                        CASE
                            WHEN internal_prices.source = ANY (ARRAY['trader_override'::text, 'index_calculated'::text]) THEN 'rubicon_trader'::text
                            ELSE internal_prices.source
                        END AS source,
                    internal_prices.project_id,
                    internal_prices.vintage,
                    date_trunc('day'::text, (internal_prices."timestamp" AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) AS "timestamp",
                    internal_prices.price
                  FROM "${environment.db.schema.marketData}".internal_prices
                  ORDER BY internal_prices.project_id, internal_prices.vintage, (date_trunc('day'::text, (internal_prices."timestamp" AT TIME ZONE 'AMERICA/LOS_ANGELES'::text))))
                UNION
                SELECT 'rubicon_trader'::text AS source,
                    p_1.registry_project_id AS project_id,
                    pv_1.label AS vintage,
                    '2024-04-25 00:00:00'::timestamp without time zone AS "timestamp",
                    pvc.average_cost_basis AS price
                  FROM "${environment.db.schema.rubicon}".project_vintage_cost_basis_v1 pvc
                    JOIN "${environment.db.schema.rubicon}".project_vintages pv_1 ON pv_1.id = pvc.project_vintage_id
                    JOIN "${environment.db.schema.rubicon}".projects p_1 ON p_1.id = pv_1.project_id
                ), prices_no_gaps AS (
                SELECT dates."timestamp",
                    vintages.source,
                    vintages.project_id,
                    vintages.vintage,
                    p_1.price,
                    (jsonb_agg(p_1.price) FILTER (WHERE p_1.price IS NOT NULL) OVER (PARTITION BY vintages.source, vintages.project_id, vintages.vintage ORDER BY dates."timestamp" ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) ->> '-1'::integer)::numeric AS last_price
                  FROM generate_series('2024-04-25 00:00:00+00'::timestamp with time zone, now()::date::timestamp with time zone, '1 day'::interval) dates("timestamp")
                    CROSS JOIN ( SELECT DISTINCT internal_prices.source,
                            internal_prices.project_id,
                            internal_prices.vintage
                          FROM internal_prices) vintages
                    LEFT JOIN internal_prices p_1 ON p_1."timestamp" = dates."timestamp" AND p_1.source = vintages.source AND p_1.project_id = vintages.project_id AND p_1.vintage = vintages.vintage
                ), settled_trades AS (
                SELECT p_1.registry_project_id,
                    pv_1.label,
                    date_trunc('day'::text, (t.settled_at AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) AS settled_at,
                    pn.last_price,
                    sum(t.amount) FILTER (WHERE t.type = 'buy'::text) AS amount_bought,
                    sum(t.amount) FILTER (WHERE t.type = 'sell'::text) AS amount_sold
                  FROM "${environment.db.schema.rubicon}".trades t
                    JOIN "${environment.db.schema.rubicon}".asset_flows vf ON vf.transaction_id = t.id
                    JOIN "${environment.db.schema.rubicon}".project_vintages pv_1 ON pv_1.id = vf.asset_id
                    JOIN "${environment.db.schema.rubicon}".projects p_1 ON p_1.id = pv_1.project_id
                    LEFT JOIN prices_no_gaps pn ON pn.project_id = p_1.registry_project_id AND pn.vintage = pv_1.label AND pn."timestamp" = date_trunc('day'::text, (t.settled_at AT TIME ZONE 'AMERICA/LOS_ANGELES'::text))
                  WHERE t.status = 'settled'::text AND date_trunc('day'::text, (t.settled_at AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) >= current_setting('"${environment.db.schema.reports}".start_date'::text)::date AND date_trunc('day'::text, (t.settled_at AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) <= current_setting('"${environment.db.schema.reports}".end_date'::text)::date
                  GROUP BY p_1.registry_project_id, pv_1.label, (date_trunc('day'::text, (t.settled_at AT TIME ZONE 'AMERICA/LOS_ANGELES'::text))), pn.last_price
                ), settled_retirements AS (
                SELECT p_1.registry_project_id,
                    pv_1.label,
                    date_trunc('day'::text, t.date_finished) AS settled_at,
                    pn.last_price,
                    sum(vf.amount) AS amount_retired
                  FROM "${environment.db.schema.rubicon}".retirements t
                    JOIN "${environment.db.schema.rubicon}".asset_flows vf ON vf.transaction_id = t.id
                    JOIN "${environment.db.schema.rubicon}".project_vintages pv_1 ON pv_1.id = vf.asset_id
                    JOIN "${environment.db.schema.rubicon}".projects p_1 ON p_1.id = pv_1.project_id
                    LEFT JOIN prices_no_gaps pn ON pn.project_id = p_1.registry_project_id AND pn.vintage = pv_1.label AND pn."timestamp" = date_trunc('day'::text, t.date_finished)
                  WHERE t.type = 'retirement'::text AND t.status = 'completed'::text AND date_trunc('day'::text, (t.date_finished AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) >= current_setting('"${environment.db.schema.reports}".start_date'::text)::date AND date_trunc('day'::text, (t.date_finished AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) <= current_setting('"${environment.db.schema.reports}".end_date'::text)::date
                  GROUP BY p_1.registry_project_id, pv_1.label, (date_trunc('day'::text, t.date_finished)), pn.last_price
                ), settled_transfers AS (
                SELECT p_1.registry_project_id,
                    pv_1.label,
                    date_trunc('day'::text, t.date_finished) AS settled_at,
                    pn.last_price,
                    sum(vf.amount) AS amount_transfered
                  FROM "${environment.db.schema.rubicon}".retirements t
                    JOIN "${environment.db.schema.rubicon}".asset_flows vf ON vf.transaction_id = t.id
                    JOIN "${environment.db.schema.rubicon}".project_vintages pv_1 ON pv_1.id = vf.asset_id
                    JOIN "${environment.db.schema.rubicon}".projects p_1 ON p_1.id = pv_1.project_id
                    LEFT JOIN prices_no_gaps pn ON pn.project_id = p_1.registry_project_id AND pn.vintage = pv_1.label AND pn."timestamp" = date_trunc('day'::text, t.date_finished)
                  WHERE t.type = 'transfer_outflow'::text AND t.status = 'completed'::text AND date_trunc('day'::text, (t.date_finished AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) >= current_setting('"${environment.db.schema.reports}".start_date'::text)::date AND date_trunc('day'::text, (t.date_finished AT TIME ZONE 'AMERICA/LOS_ANGELES'::text)) <= current_setting('"${environment.db.schema.reports}".end_date'::text)::date
                  GROUP BY p_1.registry_project_id, pv_1.label, (date_trunc('day'::text, t.date_finished)), pn.last_price
                )
        SELECT pt.category,
            pt.type,
            sum(st.amount_bought) AS amount_bought,
            sum(st.amount_bought::numeric * st.last_price) AS value_bought,
            sum(st.amount_sold) AS amount_sold,
            sum(st.amount_sold::numeric * st.last_price) AS value_sold,
            sum(sr.amount_retired) AS amount_retired,
            sum(sr.amount_retired::numeric * sr.last_price) AS value_retired,
            sum(str.amount_transfered) AS amount_transfered,
            sum(str.amount_transfered::numeric * str.last_price) AS value_transfered
          FROM "${environment.db.schema.rubicon}".projects p
            JOIN "${environment.db.schema.rubicon}".project_vintages pv ON pv.project_id = p.id
            JOIN "${environment.db.schema.rubicon}".project_types pt ON pt.id = p.project_type_id
            LEFT JOIN settled_trades st ON st.registry_project_id = p.registry_project_id AND st.label = pv.label
            LEFT JOIN settled_retirements sr ON sr.registry_project_id = p.registry_project_id AND sr.label = pv.label
            LEFT JOIN settled_transfers str ON str.registry_project_id = p.registry_project_id AND str.label = pv.label
          GROUP BY pt.category, pt.type;`,
    );

    // finance_confirmed_sales view
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.reports}".finance_confirmed_sales
        AS
        SELECT organizations.salesforce_identifier AS salesforce_id,
            organizations.id::text AS internal_id,
            organizations.name AS customer_name,
            purchases.ui_key AS transaction_id,
            purchases.flow_type AS sale_type,
            source_books.name AS basket,
            purchases.amount AS rcts,
            purchases.date_started AS create_date,
            purchases.date_finished AS complete_date,
            sum(af.raw_price) AS purchase_price,
            purchases.created_at,
            purchases.needs_risk_adjustment
          FROM "${environment.db.schema.rubicon}".purchases
            LEFT JOIN "${environment.db.schema.rubicon}".books ON books.id = purchases.customer_portfolio_id
            LEFT JOIN "${environment.db.schema.rubicon}".organizations ON organizations.id = books.organization_id
            LEFT JOIN "${environment.db.schema.rubicon}".asset_flows af ON af.transaction_id = purchases.id
            LEFT JOIN "${environment.db.schema.rubicon}".books source_books ON source_books.id = af.source_id
          WHERE purchases.status = 'settled'::text AND (organizations.id <> ALL (ARRAY['ce9895ff-41bc-4a66-8d4d-6a1102553836'::uuid, 'c9d79655-209e-4c6b-ac04-91dffac23a8a'::uuid]))
          GROUP BY organizations.salesforce_identifier, (organizations.id::text), organizations.name, purchases.ui_key, purchases.flow_type, source_books.name, purchases.amount, purchases.date_started, purchases.date_finished, purchases.created_at, purchases.needs_risk_adjustment;`,
    );

    // finance_trades view
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.reports}".finance_trades
        AS
        SELECT t.ui_key AS trade_key,
            p.registry_project_id,
            p.name,
            pv.label,
            t.amount,
            sum(COALESCE(asset_flows.raw_price, 0::numeric) + COALESCE(asset_flows.service_fee, 0::numeric) + COALESCE(asset_flows.other_fee, 0::numeric)) FILTER (WHERE cf_o.type <> 'accounting'::text) / count(*)::numeric AS total_cost_buy,
            sum(COALESCE(asset_flows.raw_price, 0::numeric) - COALESCE(asset_flows.service_fee, 0::numeric) - COALESCE(asset_flows.other_fee, 0::numeric)) FILTER (WHERE cf_o.type <> 'accounting'::text) / count(*)::numeric AS total_cost_sell,
            sum(COALESCE(asset_flows.raw_price, 0::numeric) + COALESCE(asset_flows.service_fee, 0::numeric) + COALESCE(asset_flows.other_fee, 0::numeric)) FILTER (WHERE cf_o.type <> 'accounting'::text) / t.amount::numeric / count(*)::numeric AS unit_cost_buy,
            sum(COALESCE(asset_flows.raw_price, 0::numeric) - COALESCE(asset_flows.service_fee, 0::numeric) - COALESCE(asset_flows.other_fee, 0::numeric)) FILTER (WHERE cf_o.type <> 'accounting'::text) / t.amount::numeric / count(*)::numeric AS unit_cost_sell,
            sum(asset_flows.raw_price) FILTER (WHERE cf_o.type <> 'accounting'::text) / count(*)::numeric AS raw_price,
            sum(asset_flows.service_fee) FILTER (WHERE cf_o.type <> 'accounting'::text) / count(*)::numeric AS service_fee,
            sum(asset_flows.other_fee) FILTER (WHERE cf_o.type <> 'accounting'::text) / count(*)::numeric AS other_fee,
            t.settled_at,
            t.created_at,
            t.type,
            t.status,
            t.memo,
            t.counterparty_name,
            books.name AS book_name,
            pt.category AS project_category,
            pt.type AS project_type,
            sum(cf_o.amount::numeric * cb.cost_basis * '-1'::integer::numeric) AS cost_basis
          FROM "${environment.db.schema.rubicon}".trades t
            LEFT JOIN "${environment.db.schema.rubicon}".books ON books.id = t.book_id
            LEFT JOIN "${environment.db.schema.rubicon}".asset_flows ON asset_flows.transaction_id = t.id
            LEFT JOIN "${environment.db.schema.rubicon}".credit_flows cf_o ON cf_o.asset_flow_id = asset_flows.id
            LEFT JOIN "${environment.db.schema.rubicon}".credit_flows cf_i ON cf_i.id = cf_o.linked_credit_flow_id
            LEFT JOIN "${environment.db.schema.rubicon}".asset_flows li_vf ON li_vf.id = cf_i.asset_flow_id
            LEFT JOIN "${environment.db.schema.rubicon}".trades li_t ON li_t.id = li_vf.transaction_id
            LEFT JOIN LATERAL ( SELECT cf_i.id,
                    credit_historical_cost_basis_v1.cost_basis,
                    credit_historical_cost_basis_v1.cost_basis_raw_price,
                    credit_historical_cost_basis_v1.cost_basis_service_fee,
                    credit_historical_cost_basis_v1.cost_basis_other_fee
                  FROM "${environment.db.schema.rubicon}".credit_historical_cost_basis_v1
                  WHERE credit_historical_cost_basis_v1.credit_inflow_id = cf_i.id AND credit_historical_cost_basis_v1.settled_at <= asset_flows.settled_at
                  ORDER BY credit_historical_cost_basis_v1.settled_at DESC
                LIMIT 1) cb ON cb.id = cf_i.id
            LEFT JOIN "${environment.db.schema.rubicon}".project_vintages pv ON pv.id = asset_flows.asset_id
            LEFT JOIN "${environment.db.schema.rubicon}".projects p ON p.id = pv.project_id
            LEFT JOIN "${environment.db.schema.rubicon}".project_types pt ON pt.id = p.project_type_id
          GROUP BY t.ui_key, p.registry_project_id, p.name, pv.label, t.amount, t.settled_at, t.created_at, t.type, t.status, t.memo, t.counterparty_name, books.name, pt.category, pt.type
        UNION
        SELECT forwards.forward_key AS trade_key,
            p.registry_project_id,
            p.name,
            pv.label,
            fli.expected_amount AS amount,
            COALESCE(fli.raw_price, 0::numeric) + COALESCE(fli.service_fee, 0::numeric) + COALESCE(fli.other_fee, 0::numeric) AS total_cost_buy,
            NULL::double precision AS total_cost_sell,
            (COALESCE(fli.raw_price, 0::numeric) + COALESCE(fli.service_fee, 0::numeric) + COALESCE(fli.other_fee, 0::numeric)) / fli.expected_amount::numeric AS unit_cost_buy,
            NULL::double precision AS unit_cost_sell,
            fli.raw_price,
            fli.service_fee,
            fli.other_fee,
            fli.last_updated_delivery_date AS settled_at,
            fli.created_at,
            concat('forward_', forwards.type) AS type,
            fli.status,
            forwards.counterparty AS memo,
            forwards.counterparty AS counterparty_name,
            NULL::text AS book_name,
            pt.category AS project_category,
            pt.type AS project_type,
            NULL::double precision AS cost_basis
          FROM "${environment.db.schema.rubicon}".forwards
            JOIN "${environment.db.schema.rubicon}".forward_line_items fli ON fli.forward_id = forwards.id
            LEFT JOIN "${environment.db.schema.rubicon}".asset_flows af ON af.id = fli.asset_flow_id
            LEFT JOIN "${environment.db.schema.rubicon}".project_vintages pv ON pv.id = fli.project_vintage_id
            LEFT JOIN "${environment.db.schema.rubicon}".projects p ON p.id = forwards.project_id
            LEFT JOIN "${environment.db.schema.rubicon}".project_types pt ON pt.id = p.project_type_id
          WHERE fli.status = 'pending'::text;`,
    );

    // finance_transfers_retirements view
    this.addSql(
      `CREATE OR REPLACE VIEW "${environment.db.schema.reports}".finance_transfers_retirements
        AS
        WITH purchases AS (
                SELECT pr.retirement_id,
                    array_agg(DISTINCT p_1.ui_key) AS sales
                  FROM "${environment.db.schema.rubicon}".purchases_retirements pr
                    JOIN "${environment.db.schema.rubicon}".retirements retirements ON pr.retirement_id = retirements.id
                    JOIN "${environment.db.schema.rubicon}".purchases p_1 ON p_1.id = pr.purchase_id
                  GROUP BY pr.retirement_id
                ), line_items AS (
                SELECT asset_flows_1.transaction_id,
                    json_object_agg(t.ui_key, abs(cf_o.amount)) AS line_items,
                    cf_i.project_vintage_id,
                    sum(abs(cf_o.amount)) AS credits,
                    sum(cf_o.amount::numeric * cb.cost_basis) / NULLIF(sum(cf_o.amount), 0)::numeric AS cost_basis,
                    sum(cf_o.amount::numeric * cb.cost_basis_raw_price) / NULLIF(sum(cf_o.amount), 0)::numeric AS cost_basis_raw_price,
                    sum(cf_o.amount::numeric * cb.cost_basis_service_fee) / NULLIF(sum(cf_o.amount), 0)::numeric AS cost_basis_service_fee,
                    sum(cf_o.amount::numeric * cb.cost_basis_other_fee) / NULLIF(sum(cf_o.amount), 0)::numeric AS cost_basis_other_fee
                  FROM "${environment.db.schema.rubicon}".asset_flows asset_flows_1
                    JOIN "${environment.db.schema.rubicon}".credit_flows cf_o ON cf_o.asset_flow_id = asset_flows_1.id
                    JOIN "${environment.db.schema.rubicon}".credit_flows cf_i ON cf_i.id = cf_o.linked_credit_flow_id
                    JOIN "${environment.db.schema.rubicon}".asset_flows af_i ON af_i.id = cf_i.asset_flow_id
                    JOIN "${environment.db.schema.rubicon}".trades t ON t.id = af_i.transaction_id
                    LEFT JOIN LATERAL ( SELECT cf_i.id,
                            credit_historical_cost_basis_v1.cost_basis,
                            credit_historical_cost_basis_v1.cost_basis_raw_price,
                            credit_historical_cost_basis_v1.cost_basis_service_fee,
                            credit_historical_cost_basis_v1.cost_basis_other_fee
                          FROM "${environment.db.schema.rubicon}".credit_historical_cost_basis_v1
                          WHERE credit_historical_cost_basis_v1.credit_inflow_id = cf_i.id AND credit_historical_cost_basis_v1.settled_at <= asset_flows_1.settled_at
                          ORDER BY credit_historical_cost_basis_v1.settled_at DESC
                        LIMIT 1) cb ON cb.id = cf_i.id
                  GROUP BY asset_flows_1.id, cf_i.project_vintage_id
                )
        SELECT organizations.salesforce_identifier AS salesforce_id,
            organizations.id::text AS internal_id,
            organizations.name AS customer_name,
            transactions.ui_key AS transaction_id,
            books.name AS basket,
            books.type,
            p.name AS project,
            p.registry_project_id AS registry_id,
            pv.label AS vintage,
            line_items.credits,
            line_items.cost_basis AS average_unit_cost,
            line_items.cost_basis_raw_price AS average_unit_cost_raw_material,
            line_items.cost_basis_service_fee AS average_unit_cost_service_fees,
            line_items.cost_basis_other_fee AS average_unit_cost_other_fees,
            transactions.date_started AS create_date,
            transactions.date_finished AS complete_date,
            transactions.created_at,
            line_items.line_items,
            purchases.sales,
            transactions.type AS transaction_type
          FROM "${environment.db.schema.rubicon}".retirements transactions
            LEFT JOIN "${environment.db.schema.rubicon}".books ON books.id = transactions.customer_portfolio_id
            LEFT JOIN "${environment.db.schema.rubicon}".organizations ON organizations.id = books.organization_id
            LEFT JOIN "${environment.db.schema.rubicon}".asset_flows ON asset_flows.transaction_id = transactions.id
            LEFT JOIN "${environment.db.schema.rubicon}".project_vintages pv ON pv.id = asset_flows.asset_id
            LEFT JOIN "${environment.db.schema.rubicon}".projects p ON p.id = pv.project_id
            LEFT JOIN purchases ON purchases.retirement_id = transactions.id
            JOIN line_items ON line_items.project_vintage_id = pv.id AND line_items.transaction_id = asset_flows.transaction_id
          WHERE transactions.status = 'completed'::text;`,
    );
  }
}
