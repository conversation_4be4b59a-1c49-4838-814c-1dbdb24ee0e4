import Decimal from 'decimal.js';

export interface AdminPositionsReport {
  average_cost_basis: Decimal;
  buffer_category_id: string;
  buffer_category_name: string;
  change_1: Decimal;
  change_7: Decimal;
  change_30: Decimal;
  change_90: Decimal;
  change_365: Decimal;
  change_ytd: Decimal;
  country: string;
  holdings: Decimal;
  last_price_date: number;
  perc_change_1: Decimal;
  perc_change_7: Decimal;
  perc_change_30: Decimal;
  perc_change_90: Decimal;
  perc_change_365: Decimal;
  perc_change_ytd: Decimal;
  price: Decimal;
  project_name: string;
  project_type: string;
  rct_standard: boolean;
  registry_project_id: string;
  risk_buffer_percentage: Decimal;
  suspended: boolean;
  vintage: string;
}
