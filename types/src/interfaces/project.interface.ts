import { CountryRegions } from '../enums/country.enum';
import { PriceRange } from '../enums/pricing.enum';
import { ProjectTypeCategory } from '../enums/project-type.enum';
import {
  MobileProjectRelations,
  PortalProjectRelations,
  ProjectBeZeroRating,
  ProjectEligibilityAccreditation,
  ProjectEmissionsImpactType,
  ProjectOrderByOption,
  ProjectRelations,
  ProjectRiskScore,
} from '../enums/project.enum';
import { json } from '../types/json.type';
import { uuid } from '../types/uuid.type';
import { AdminBookTypeGroupedAllocationResponse, AdminGroupedAllocationResponse } from './allocation.interface';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { AdminBufferCategoryResponse } from './buffer-category.interface';
import { TrimmedCountryResponse } from './country.interface';
import { AdminProjectTypeResponse, TrimmedProjectTypeResponse } from './project-type.interface';
import {
  AdminProjectVintageResponse,
  PortalProjectVintageResponse as MobileProjectVintageResponse,
  PortalProjectVintageResponse,
} from './project-vintage.interface';
import { TrimmedRegistryResponse } from './registry.interface';
import { TrimmedUserResponse } from './user.interface';

export interface TrimmedKmlResponse {
  latitude?: number;
  longitude?: number;
  url?: string;
}

export interface AdminProjectCreateRequest {
  additionalityBlurb?: string;
  additionalityScore?: number;
  analystInsightsBlurb?: string;
  analystName?: string;
  analystRole?: string;
  beZeroRating?: ProjectBeZeroRating;
  beZeroUpdatedDate?: Date;
  bookChartDisplayGroup?: string;
  bufferCategoryId?: uuid;
  carbonAccountingBlurb?: string;
  categoryRiskScore?: ProjectRiskScore;
  certificationBlurb?: string;
  certificationScore?: number;
  climateImpact?: number;
  climateImpactBlurb?: string;
  climateImpactRiskAdjusted?: number;
  countryCode?: string;
  countryRegion?: string;
  dateOfLatestVerification?: Date;
  durabilityScore?: number;
  eligibilityAccreditations?: ProjectEligibilityAccreditation[];
  emissionsImpactType?: ProjectEmissionsImpactType;
  endDate?: Date;
  futureDeliveryBlurb?: string;
  futureDeliveryRisk?: number;
  illustrationImageUrl?: string;
  independentVerifierName?: string;
  integrityGradeScore?: number;
  integrityGradeScoreRiskAdjusted?: number;
  isByorctApproved?: boolean;
  isScienceTeamApproved?: boolean;
  kmlLatitude?: number;
  kmlLongitude?: number;
  kmlUrl?: string;
  lastReviewDate?: Date;
  mapImageUrl?: string;
  maxPercentage?: number;
  maxQuantity?: number;
  methodologyBlurb?: string;
  minPercentage?: number;
  minQuantity?: number;
  name: string;
  otherCoBenefitsBlurb?: string;
  overallRiskScore?: ProjectRiskScore;
  pddReportLink?: string;
  permanenceBlurb?: string;
  previewImageUrl?: string;
  projectDescription?: string;
  projectDeveloperName?: string;
  projectTypeId: number;
  rctStandard?: boolean;
  registryCreditsIssued?: number;
  registryCreditsRetired?: number;
  registryLink?: string;
  registryName?: string;
  registryProjectId: string;
  sdgIds?: number[];
  squareImage1Url?: string;
  squareImage2Url?: string;
  startDate?: Date;
  suspended?: boolean;
  vvbReportLink?: string;
  waterBlurb?: string;
}

export type AdminProjectUpdateRequest = Partial<AdminProjectCreateRequest>;

export interface AdminBulkProjectUpdateRequest extends AdminProjectUpdateRequest {
  bufferCategory?: string;
  id: uuid;
  memo: string;
}

export interface AdminProjectUpdateLogResponseDTO extends BaseResponse {
  data: Record<string, json>;
  memo: string;
  user: TrimmedUserResponse;
}

export interface BaseSdgTypeResponse {
  id: number;
  title: string;
  iconImagePath: string;
}

export interface TrimmedProjectSdgResponse extends BaseResponse {
  projectId: uuid;
  sdgDescriptionBlurb?: string;
  sdgType: BaseSdgTypeResponse;
  sdgTypeId: number;
}

export interface MobileProjectRelationsQuery {
  includeRelations?: MobileProjectRelations[];
}

export interface PortalProjectRelationsQuery {
  includeRelations?: PortalProjectRelations[];
}

export interface AdminProjectRelationsQuery {
  includeRelations?: ProjectRelations[];
}

export interface MobileProjectQuery extends BaseQuery, MobileProjectRelationsQuery {
  /* filters */
  byoBufferEligible?: boolean;
  hasAmount?: boolean;
  hasTrades?: boolean;
  // includeBYOMinimum?: boolean; // todo : deprecate and use includeRelations param
  ids?: uuid[];
  isByorctEligible?: boolean;
  /* base */
  orderBy?: ProjectOrderByOption;
}

export interface PortalProjectQuery extends BaseQuery, PortalProjectRelationsQuery {
  /* filters */
  byoBufferEligible?: boolean;
  hasAmount?: boolean;
  hasTrades?: boolean;
  ids?: uuid[];
  // includeBYOMinimum?: boolean; // todo : deprecate and use includeRelations param
  isByorctEligible?: boolean;
  projectTypeIds?: number[];
  /* base */
  orderBy?: ProjectOrderByOption;
}

export interface AdminProjectQuery extends BaseQuery, AdminProjectRelationsQuery {
  /* filters */
  byoBufferEligible?: boolean;
  hasAmount?: boolean;
  hasTrades?: boolean;
  ids?: uuid[];
  // includeBYOMinimum?: boolean; // todo : deprecate and use includeRelations param
  isByorctEligible?: boolean;
  projectTypeIds?: number[];
  /* base */
  orderBy?: ProjectOrderByOption;
}

export interface TrimmedProjectResponse extends BaseResponse {
  // bufferCategory?: AdminBufferCategoryResponse;
  beZeroRating?: ProjectBeZeroRating;
  beZeroUpdatedDate?: Date;
  country?: TrimmedCountryResponse;
  eligibilityAccreditations: ProjectEligibilityAccreditation[];
  emissionsImpactType?: ProjectEmissionsImpactType;
  hasBalance: boolean;
  integrityGradeScore?: number;
  isScienceTeamApproved: boolean;
  name: string;
  overallRiskScore?: ProjectRiskScore;
  projectDescription?: string;
  projectSDGs?: TrimmedProjectSdgResponse[];
  projectType: TrimmedProjectTypeResponse;
  // rctStandard?: boolean; // todo : see where FE needs this
  registry?: TrimmedRegistryResponse;
  registryName?: string;
  registryProjectId: string;
  // suspended?: boolean; // todo : see if FE needs this? can we just use isRctEligible?
  // productIds?: uuid[]; // todo : not returned from FE
}

export interface MobileProjectResponse extends TrimmedProjectResponse {
  additionalityBlurb?: string;
  additionalityScore?: number;
  analystInsightsBlurb?: string;
  analystName?: string;
  analystRole?: string;
  carbonAccountingBlurb?: string;
  certificationBlurb?: string;
  certificationScore?: number;
  climateImpact?: number;
  climateImpactBlurb?: string;
  climateImpactRiskAdjusted?: number;
  durabilityScore?: number;
  endDate?: Date;
  futureDeliveryBlurb?: string;
  futureDeliveryRisk?: number;
  illustrationImageUrl?: string;
  integrityGradeScoreRiskAdjusted?: number;
  isByorctApproved?: boolean;
  kml?: TrimmedKmlResponse;
  lastReviewDate?: Date;
  mapImageUrl?: string;
  methodologyBlurb?: string;
  minQuantityBYO?: number;
  otherCoBenefitsBlurb?: string;
  pdfReady?: boolean;
  permanenceBlurb?: string;
  previewImageUrl?: string;
  priceRange?: PriceRange;
  productIds?: uuid[];
  projectDeveloperName?: string;
  projectLocation?: string; // todo : do we need this if it wasn't here before?
  projectVintages?: MobileProjectVintageResponse[];
  proportionOfBook?: number;
  registryLink?: string;
  squareImage1Url?: string;
  squareImage2Url?: string;
  startDate?: Date;
  // suspended: boolean; BE returns this so can we just have this in trimmed?
  waterBlurb?: string;
}

export interface PortalProjectResponse extends MobileProjectResponse {
  projectVintages?: PortalProjectVintageResponse[];
}

export interface AdminProjectResponse extends TrimmedProjectResponse {
  additionalityBlurb?: string;
  additionalityScore?: number;
  assetAllocations?: AdminGroupedAllocationResponse;
  assetAllocationsByBookType?: AdminBookTypeGroupedAllocationResponse[];
  analystInsightsBlurb?: string;
  analystName?: string;
  analystRole?: string;
  bookChartDisplayGroup?: string;
  bufferCategory?: AdminBufferCategoryResponse;
  carbonAccountingBlurb?: string;
  categoryRiskScore?: ProjectRiskScore;
  certificationBlurb?: string;
  certificationScore?: number;
  climateImpact?: number;
  climateImpactBlurb?: string;
  climateImpactRiskAdjusted?: number;
  countryRegion?: string;
  dateOfLatestVerification?: Date;
  durabilityScore?: number;
  endDate?: Date;
  futureDeliveryBlurb?: string;
  futureDeliveryRisk?: number;
  illustrationImageUrl?: string;
  independentVerifierName?: string;
  integrityGradeScoreRiskAdjusted?: number;
  isByorctApproved?: boolean;
  kml?: TrimmedKmlResponse;
  lastReviewDate?: Date;
  mapImageUrl?: string;
  maxPercentage?: number; // todo : used?
  maxQuantity?: number; // todo : used?
  methodologyBlurb?: string;
  minPercentage?: number; // todo : used?
  minQuantity?: number; // todo : used?
  otherCoBenefitsBlurb?: string;
  pddReportLink?: string;
  pdfReady?: boolean;
  permanenceBlurb?: string;
  previewImageUrl?: string;
  priceRange?: PriceRange;
  productIds?: uuid[];
  projectDeveloperName?: string;
  projectVintages?: AdminProjectVintageResponse[];
  rctStandard: boolean;
  registryCreditsIssued?: number;
  registryCreditsRetired?: number;
  registryLink?: string;
  squareImage1Url?: string;
  squareImage2Url?: string;
  suspended: boolean;
  startDate?: Date;
  vvbReportLink?: string;
  waterBlurb?: string;
}

export interface MobileProjectQueryResponse extends BaseQueryResponse {
  data: MobileProjectResponse[];
}

export interface PortalProjectQueryResponse extends BaseQueryResponse {
  data: PortalProjectResponse[];
}

export interface AdminProjectQueryResponse extends BaseQueryResponse {
  data: AdminProjectResponse[];
}

export interface AdminProjectSearch {
  q: string;
  name?: boolean;
  id?: boolean;
  fuzzy?: boolean;
  hasBalance?: boolean;
  limit: number;
  eligibilityAccredidations?: ProjectEligibilityAccreditation[];
  emissionsImpactTypes?: ProjectEmissionsImpactType[];
  integrityGrades?: string[];
  projectTypeCategories?: ProjectTypeCategory[];
  regions?: CountryRegions[];
}

export interface AdminProjectSearchResponse {
  id: uuid;
  country?: TrimmedCountryResponse;
  isScienceTeamApproved: boolean;
  name: string;
  projectType?: AdminProjectTypeResponse;
  bufferCategory?: AdminBufferCategoryResponse;
  rctStandard?: boolean;
  registryProjectId: string;
  suspended?: boolean;
  integrityGradeScore?: number;
}
