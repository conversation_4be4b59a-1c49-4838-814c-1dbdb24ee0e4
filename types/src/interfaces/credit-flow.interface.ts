import { uuid } from '../types/uuid.type';
import { BaseQueryResponse, BaseResponse } from './base.interface';

export interface AdminCreditOutflowResponse extends BaseResponse {
  amount: number;
}

export interface AdminCreditInflowResponse extends BaseResponse {
  amount: number;
  amountRetired?: number;
  amountSold?: number;
  outflows?: AdminCreditOutflowResponse[];
  projectVintageId?: uuid;
}

export interface AdminCreditInflowQueryResponse extends BaseQueryResponse {
  data: AdminCreditInflowResponse[];
}

export interface AdminCreditOutflowQueryResponse extends BaseQueryResponse {
  data: AdminCreditOutflowResponse[];
}
