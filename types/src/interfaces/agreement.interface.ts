import { AgreementOrderByOption, AgreementStatus } from '../enums/agreement.enum';
import { json } from '../types/json.type';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedUserResponse } from './user.interface';

export interface PortalAgreementRequest {
  status: AgreementStatus;
  version?: string;
}

export interface PortalAgreementResponse extends BaseResponse {
  acceptedDate?: Date;
  label: string;
  status: AgreementStatus;
  metadata?: Record<string, json>;
  version: string;
}

// todo : do we even need agreements for user in admin pretty sure this can be removed
export interface AdminAgreementResponse extends PortalAgreementResponse {
  user?: TrimmedUserResponse;
}

export interface PortalAgreementQuery extends BaseQuery {
  acceptedAfterDate?: Date;
  acceptedBeforeDate?: Date;
  label?: string;
  status?: AgreementStatus;
  version?: string;
  orderBy?: AgreementOrderByOption;
}

export interface PortalAgreementQueryResponse extends BaseQueryResponse {
  data: PortalAgreementResponse[];
}
