import Decimal from 'decimal.js';
import { uuid } from '../types/uuid.type';
import { TradeOrderByOptions, TradeRelation, TradeStatus, TradeType, TradeUpdatableStatus } from '../enums/trade.enum';
import { CounterpartyRole } from '../enums/counterparty-role.enum';
import { AdminAssetTradeResponse, AdminAssetTradeRequest } from './asset.interface';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { AdminBookResponse } from './book.interface';
import { TrimmedUserResponse } from './user.interface';
import { AdminOrganizationResponse } from './organization.interface';
import { OrderByDirection } from '../enums/base.enum';

export interface AdminTradeCounterpartyRequest {
  actualServiceFee: Decimal;
  calculatedServiceFee?: Decimal;
  comments?: string;
  organizationId: uuid;
  defaultFeeId?: uuid; // the fee id used to calculate calculatedServiceFee
  isPrimary: boolean;
  role: CounterpartyRole;
}

export interface AdminTradeCounterpartyResponse {
  actualServiceFee?: Decimal; // only optional because of backfilling
  calculatedServiceFee?: Decimal;
  comments?: string;
  isPrimary: boolean;
  organization: AdminOrganizationResponse;
  role: CounterpartyRole;
}

export interface AdminTradeCreateRequest {
  bookId: uuid;
  counterparties: AdminTradeCounterpartyRequest[];
  goodUntilDate?: Date;
  memo?: string;
  otherFee?: Decimal;
  poid: string;
  projectVintages: AdminAssetTradeRequest[];
  rawPrice: Decimal;
  type: TradeType;
}

export interface AdminTradeUpdateRequest {
  counterparties?: AdminTradeCounterpartyRequest[];
  memo?: string;
  otherFee?: Decimal;
  rawPrice?: Decimal;
}

export interface AdminTradeConfirmationRequest {
  recipientEmails: string[];
  paymentTerms?: string;
  deliveryTerms?: string;
}

export interface BaseTradeConfirmationProductsResponse {
  productId: uuid;
  registryProjectId: string;
  projectName: string;
  vintageName: string;
  quantity: string;
  pricePerTonne: string;
  totalPrice: string;
}

export interface AdminTradeConfirmationResponse extends BaseResponse {
  tradeId: uuid;
  uiKey: string;
  recipientEmails: string[];
  formattedDate: string;
  seller: string;
  buyer: string;
  products: BaseTradeConfirmationProductsResponse[];
  totalQuantity: string;
  totalPrice: string;
  paymentTerms?: string;
  deliveryTerms?: string;
}

export interface AdminTradeExecuteRequest {
  updatableStatusOrder: TradeUpdatableStatus[];
}

export interface AdminTradeDeliverRequest {
  assetsDeliveredAt: Date;
}

export interface TrimmedTradeResponse extends BaseResponse {
  amount: number;
  status: TradeStatus;
  type: TradeType;
  uiKey: string;
}

export interface AdminTradeResponse extends TrimmedTradeResponse {
  assetsDeliveredAt?: Date;
  book?: AdminBookResponse;
  counterparties: AdminTradeCounterpartyResponse[];
  docsCount: number;
  goodUntilDate?: Date;
  isDelivered: boolean;
  isPaid: boolean;
  memo?: string;
  poid?: string;
  projectVintages?: AdminAssetTradeResponse[]; // todo : double check FE is looking for assets inside and if they need TrimmedPV or if TrimmedAsset suffices
  settledAt?: Date;
  updatableStatusOrder: TradeUpdatableStatus[];
  updatedBy?: TrimmedUserResponse;
}

export interface AdminTradeRelationsQuery {
  includeRelations?: TradeRelation[];
}

export interface BaseTradeOrderBy {
  orderBy: TradeOrderByOptions;
  orderByDirection: OrderByDirection;
}

export interface AdminTradeQuery extends BaseQuery, AdminTradeRelationsQuery {
  /* filters */
  bookId?: uuid;
  organizationIds?: uuid[];
  poid?: string;
  projectVintageId?: uuid;
  status?: TradeStatus;
  type?: TradeType;
  updatedById?: uuid;
  /* base */
  orderBys?: BaseTradeOrderBy[] | string[]; // FE passes in string array formatted as ['field:direction']
}

export interface AdminTradeQueryResponse extends BaseQueryResponse {
  data: AdminTradeResponse[];
}
