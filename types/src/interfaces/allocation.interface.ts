import Decimal from 'decimal.js';
import { AssetType } from '../enums/asset.enum';
import { BookType } from '../enums/book.enum';
import { TrimmedBookResponse } from './book.interface';
import { TrimmedProjectResponse } from './project.interface';
import { TrimmedProjectTypeResponse } from './project-type.interface';
import { AdminAssetResponse, MobileAssetResponse, PortalAssetResponse } from './asset.interface';

export interface MobileAllocationResponse {
  amountAllocated: number; // amount settled
  amountAvailable: number; // settled - pending_outflow
  amountCustomerTransferredOutflow: number;
  amountPendingCustomerTransferOutflow: number;
  amountPendingPurchase: number;
  amountPendingRetirement: number;
  amountRetired: number;
  asset: MobileAssetResponse;
}

export interface PortalAllocationResponse extends MobileAllocationResponse {
  asset: PortalAssetResponse;
}

export interface AdminAllocationResponse extends PortalAllocationResponse {
  amountPendingBuy: number;
  amountPendingSell: number;
  amountSold: number;
  asset: AdminAssetResponse;
  averageCostBasis?: Decimal;
  currentPrice?: Decimal;
  owner: TrimmedBookResponse;
}

export interface AdminGroupedPriceResponse {
  totalPriceAllocated: Decimal; // total settled
  totalPriceAvailable: Decimal; // settled - pending_outflow
  totalPriceCustomerTransferredOutflow: Decimal;
  totalPricePendingBuy: Decimal;
  totalPricePendingCustomerTransferOutflow: Decimal;
  totalPricePendingPurchase: Decimal;
  totalPricePendingRetirement: Decimal;
  totalPricePendingSell: Decimal;
  totalPriceRetired: Decimal;
  totalPriceSold: Decimal;
}

export interface MobileGroupedAllocationResponse {
  totalAmountAllocated: number;
  totalAmountAvailable: number;
  totalAmountCustomerTransferredOutflow: number;
  totalAmountPendingCustomerTransferOutflow: number;
  totalAmountPendingPurchase: number;
  totalAmountPendingRetirement: number;
  totalAmountRetired: number;
}

export type PortalGroupedAllocationResponse = MobileGroupedAllocationResponse;

export interface AdminGroupedAllocationResponse extends PortalGroupedAllocationResponse {
  groupedPrices?: AdminGroupedPriceResponse;
  totalAmountPendingBuy: number;
  totalAmountPendingSell: number;
  totalAmountSold: number;
}

export interface MobileGroupedAllocationWithNestedResponse extends MobileGroupedAllocationResponse {
  allocations: MobileAllocationResponse[];
}

export interface PortalGroupedAllocationWithNestedResponse extends PortalGroupedAllocationResponse {
  allocations: PortalAllocationResponse[];
}

export interface AdminGroupedAllocationWithNestedResponse extends AdminGroupedAllocationResponse {
  allocations: AdminAllocationResponse[];
}

export interface AdminAssetGroupedAllocationWithNestedResponse extends AdminGroupedAllocationWithNestedResponse {
  asset: AdminAssetResponse;
}

export interface AdminAssetTypeGroupedAllocationResponse extends AdminGroupedAllocationResponse {
  assetType: AssetType;
}

export interface AdminBookTypeGroupedAllocationResponse extends AdminGroupedAllocationResponse {
  bookType: BookType;
}

export interface MobileProjectGroupedAllocationResponse extends MobileGroupedAllocationResponse {
  project: TrimmedProjectResponse;
}

export interface PortalProjectGroupedAllocationResponse extends PortalGroupedAllocationResponse {
  project: TrimmedProjectResponse;
}

export interface AdminProjectGroupedAllocationResponse extends AdminGroupedAllocationResponse {
  project: TrimmedProjectResponse;
}

export interface AdminProjectTypeGroupedAllocationResponse extends AdminGroupedAllocationResponse {
  projectType: TrimmedProjectTypeResponse;
}
