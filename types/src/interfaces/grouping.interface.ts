import { GroupingRelations } from '../enums/grouping.enum';
import {
  AdminAssetTypeGroupedAllocationResponse,
  AdminGroupedAllocationResponse,
  AdminGroupedAllocationWithNestedResponse,
} from './allocation.interface';
import { BaseQueryResponse, BaseResponse } from './base.interface';
import { BaseBookLimit, TrimmedBookResponse } from './book.interface';

export interface AdminGroupingParentResponse extends BaseResponse {
  ownerAllocations?: AdminGroupedAllocationResponse | AdminGroupedAllocationWithNestedResponse;
  ownerAllocationsByAssetType?: AdminAssetTypeGroupedAllocationResponse[];
  books: TrimmedBookResponse[]; // note : see if FE can just use the ownerAllocations.allocations for this
  description: string;
  limit?: BaseBookLimit;
  name: string;
}

export interface AdminGroupingRelationsQuery {
  includeRelations?: GroupingRelations[];
}

export interface AdminGroupingParentQueryResponse extends BaseQueryResponse {
  data: AdminGroupingParentResponse[];
}
