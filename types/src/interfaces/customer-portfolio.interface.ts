import { uuid } from '../types/uuid.type';
import { TrimmedUserResponse } from './user.interface';

export interface AdminCustomerPortfolioCreateRequest {
  isEnabled: boolean;
  isOnboarded: boolean;
  rubiconManagerId: uuid;
  salesforceIdentifier: string;
}

export type AdminCustomerPortfolioUpdateRequest = Partial<AdminCustomerPortfolioCreateRequest>;

export interface MobileCustomerPortfolioResponse {
  isEnabled: boolean;
  isOnboarded: boolean;
  rubiconManager: TrimmedUserResponse;
}

export type PortalCustomerPortfolioResponse = MobileCustomerPortfolioResponse;

export interface AdminCustomerPortfolioResponse extends PortalCustomerPortfolioResponse {
  salesforceIdentifier?: string;
}
