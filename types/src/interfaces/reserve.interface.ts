import { ReserveOrderByOption, ReserveRelations } from '../enums/reserve.enum';
import { uuid } from '../types/uuid.type';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedOrganizationResponse } from './organization.interface';
import { TrimmedProjectVintageResponse } from './project-vintage.interface';
import { TrimmedUserResponse } from './user.interface';

export interface AdminReserveCreateRequest {
  amount: number;
  memo?: string;
  organizationId?: uuid;
  projectVintageId: uuid;
}

export interface AdminReserveUpdateRequest {
  amount?: number;
  memo?: string;
}

export interface AdminReserveResponse extends BaseResponse {
  amount: number;
  docsCount: number;
  memo?: string;
  organization?: TrimmedOrganizationResponse;
  projectVintage?: TrimmedProjectVintageResponse;
  updatedBy?: TrimmedUserResponse;
}

export interface AdminReserveRelationsQuery {
  includeRelations?: ReserveRelations[];
}

export interface AdminReserveQuery extends BaseQuery, AdminReserveRelationsQuery {
  memo?: string;
  organizationId?: uuid;
  projectVintageId?: uuid;
  updatedById?: uuid;
  orderBy?: ReserveOrderByOption;
}

export interface AdminReserveQueryResponse extends BaseQueryResponse {
  data: AdminReserveResponse[];
}
