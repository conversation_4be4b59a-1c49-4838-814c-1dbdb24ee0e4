import { uuid } from '../types/uuid.type';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { DocumentType } from '../enums/document.enum';
import { TrimmedOrganizationResponse } from './organization.interface';

export interface AdminDocumentUploadUrlRequest {
  filename: string;
  isPublic: boolean;
  organizationId?: uuid;
  type: DocumentType;
  relatedUiKey?: string;
}

export interface AdminDocumentUpdateRequest {
  filename: string;
  id: uuid;
  isPublic: boolean;
  organizationId?: uuid;
  type: DocumentType;
  relatedUiKey?: string;
}

export interface AdminDocumentUploadUrlResponse extends BaseResponse {
  organization?: TrimmedOrganizationResponse;
  uploadUrl: string;
}

export interface PortalDocumentResponse extends BaseResponse {
  downloadUrl: string;
  filename: string;
  organization?: TrimmedOrganizationResponse;
  relatedUiKey?: string;
  type: DocumentType;
  uploadedAt: Date;
}

export interface AdminDocumentResponse extends PortalDocumentResponse {
  isPublic: boolean;
  isUploaded: boolean;
  uploadedById: uuid;
}

export interface PortalDocumentQuery extends BaseQuery {
  types?: DocumentType[];
  relatedUiKey?: string;
}

export interface AdminDocumentQuery extends PortalDocumentQuery {
  organizationId?: uuid;
}

export interface PortalDocumentQueryResponse extends BaseQueryResponse {
  data: PortalDocumentResponse[];
}

export interface AdminDocumentQueryResponse extends BaseQueryResponse {
  data: AdminDocumentResponse[];
}
