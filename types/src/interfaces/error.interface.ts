// todo (TD-5) : this should be removed, not changing type name for now
export interface ErrorResponse {
  error: string;
  details?: string;
}

// todo (TD-5) : figure out proper typing and rename appropriately. is it base? is it common? why are words so hard.
// currently this is only used for UniqueConstraintViolationException
export interface ErrorDetailsResponse {
  constraint: string;
  field: string; // field of failure
  message: string;
  name: string; // exception name, e.g. UniqueConstraintViolationException
  status: number; // repeat of res.status
  value: string; // input that caused failure (if applicable), eventually might need to be any type
}
