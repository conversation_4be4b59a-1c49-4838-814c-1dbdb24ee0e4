import { AssetType } from '../enums/asset.enum';
import {
  RetirementRelations,
  RetirementType,
  RetirementStatus,
  RetirementOrderByOptions,
  PortalRetirementRelations,
} from '../enums/transaction.enum';
import { uuid } from '../types/uuid.type';
import {
  AdminRctAssetRetirementResponse,
  AdminVintageAssetRetirementResponse,
  BaseAssetRetirementRequest,
  PortalRctAssetRetirementResponse,
  PortalVintageAssetRetirementResponse,
} from './asset.interface';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedUserResponse } from './user.interface';
import { OrderByDirection } from '../enums/base.enum';
import { TrimmedOrganizationResponse } from './organization.interface';
import { TrimmedProjectVintageResponse } from './project-vintage.interface';

export interface BaseRetirementOrderBy {
  orderBy: RetirementOrderByOptions;
  orderByDirection: OrderByDirection;
}

export interface TrimmedRetirementLink {
  url: string;
  label?: string;
}

export interface AdminRetirementLinkUpdateRequest {
  links: TrimmedRetirementLink[];
  projectVintageId: uuid;
}

export interface PortalRetirementRequest {
  assets: BaseAssetRetirementRequest[];
  assetType: AssetType;
  beneficiary?: string;
  isPublic: boolean;
  memo?: string;
  registryAccount?: string;
  token?: string;
  type: RetirementType.RETIREMENT | RetirementType.TRANSFER_OUTFLOW;
}

export interface AdminRetirementRequest extends PortalRetirementRequest {
  organizationId: uuid;
}

export interface AdminSuggestedBufferRetirementRequest {
  retirementIds: uuid[];
}

export interface AdminSuggestedBufferElement {
  projectVintage: TrimmedProjectVintageResponse;
  amount: number;
}

export interface AdminSuggestedBufferComposition {
  category: string;
  suggestedBufferRetirements: AdminSuggestedBufferElement[];
}

export interface AdminSuggestedBufferRetirementResponse {
  suggestedAllocations: AdminSuggestedBufferComposition[];
}

export interface AdminUpdateRetirementAmountsRequest {
  projectVintageId: uuid;
  amountTransacted: number;
  sourceId: uuid;
}

export interface PortalRetirementResponse extends BaseResponse {
  amount: number;
  assets: (PortalRctAssetRetirementResponse | PortalVintageAssetRetirementResponse)[];
  assetType: AssetType;
  beneficiary?: string;
  // organization: TrimmedOrganizationResponse; // todo : @kofi/@adir to tell me if you need customerPortfolio specific details
  dateFinished?: Date;
  dateStarted: Date;
  docsCount: number;
  isPublic: boolean;
  memo?: string;
  registryAccount?: string;
  requestedBy?: TrimmedUserResponse;
  status: RetirementStatus;
  type: RetirementType;
  uiKey: string;
}

export interface AdminRetirementResponse extends BaseResponse {
  amount: number;
  assets: (AdminRctAssetRetirementResponse | AdminVintageAssetRetirementResponse)[];
  assetType: AssetType;
  beneficiary?: string;
  organization: TrimmedOrganizationResponse; // todo : @kofi/@adir to tell me if you need customerPortfolio specific details
  dateFinished?: Date;
  dateStarted: Date;
  docsCount: number;
  isPublic: boolean;
  memo?: string;
  registryAccount?: string;
  requestedBy?: TrimmedUserResponse;
  status: RetirementStatus;
  type: RetirementType;
  uiKey: string;
}

export interface PortalRetirementRelationsQuery {
  includeRelations?: PortalRetirementRelations[];
}

export interface AdminRetirementRelationsQuery {
  includeRelations?: RetirementRelations[];
}

export interface AdminRetirementQuery extends AdminRetirementRelationsQuery, BaseQuery {
  /* filters */
  assetId?: uuid;
  organizationId?: uuid;
  statuses?: RetirementStatus[];
  types?: RetirementType[];
  /* base */
  orderBys?: BaseRetirementOrderBy[] | string[]; // FE passes in string array formatted as ['field:direction']
}

export interface AdminRetirementQueryResponse extends BaseQueryResponse {
  data: AdminRetirementResponse[];
}
