import { OrderByDirection } from '../enums/base.enum';
import { OrganizationUserRole } from '../enums/organization.enum';
import { CookieConsentInteractionType, UserOrderByOptions, UserRelations, UserStatus } from '../enums/user.enum';
import { uuid } from '../types/uuid.type';
import { AdminAgreementResponse } from './agreement.interface';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { AdminOrganizationResponse } from './organization.interface';
import { TrimmedProjectResponse } from './project.interface';

export interface PortalUserCookieConsentRequest {
  analyticsCookies?: boolean;
  eventDate: Date;
  expirationDate: Date;
  interactionType: CookieConsentInteractionType;
}

export interface AdminUserCreateRequest {
  email: string;
  firstName: string;
  lastName: string;
  organizationId: uuid;
  organizationUserRoles?: OrganizationUserRole[];
}

export interface AdminUserUpdateRequest extends Partial<AdminUserCreateRequest> {
  status?: UserStatus;
}

export interface TrimmedUserResponse extends BaseResponse {
  email: string;
  name: string;
  status: UserStatus;
}

export interface PortalUserResponse extends TrimmedUserResponse {
  // companyName?: string; // @kofi do you even use this?
  // phone?: string; // @kofi do you even use this?
  organizationUserRoles: OrganizationUserRole[];
}

export interface AdminUserResponse extends PortalUserResponse {
  // does admin need org user roles?
  agreements?: AdminAgreementResponse[];
  lastLogin?: Date;
  organization?: AdminOrganizationResponse;
  projects?: TrimmedProjectResponse[];
  permitRoles: string[]; // permit.io roles
}

export interface AdminUserRelationsQuery {
  includeRelations?: UserRelations[];
}

export interface PortalUserQuery extends Omit<BaseQuery, 'orderBy'> {
  orderBys?: { orderBy: UserOrderByOptions; orderByDirection: OrderByDirection }[] | string[]; // FE passes in string array formatted as ['field:direction']
}

export interface AdminUserQuery extends PortalUserQuery, AdminUserRelationsQuery {
  email?: string;
  isInternal?: boolean;
  organizationId?: uuid;
  organizationUserRoles?: OrganizationUserRole[];
  status?: UserStatus;
}

export interface AdminUserQueryResponse extends BaseQueryResponse {
  data: AdminUserResponse[];
}

export interface PortalUserQueryResponse extends BaseQueryResponse {
  data: PortalUserResponse[];
}
