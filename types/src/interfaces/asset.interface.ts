import Decimal from 'decimal.js';
import { AssetType } from '../enums/asset.enum';
import { uuid } from '../types/uuid.type';
import { TrimmedBookResponse } from './book.interface';
import { TrimmedRetirementLink } from './retirement.interface';
import { BaseResponse } from './base.interface';
import { AdminProjectVintageResponse, TrimmedProjectVintageResponse } from './project-vintage.interface';
import { ProjectBeZeroRating } from '../enums/project.enum';
import { BookType } from '../enums/book.enum';

export interface BaseAssetTransactionRequest {
  amount: number;
  assetId: uuid;
}

export interface AdminAssetPurchaseRequest extends BaseAssetTransactionRequest {
  otherFee?: Decimal;
  rawPrice: Decimal;
  serviceFee?: Decimal;
  sourceId: uuid; // a purchase can be sourced from multiple books
}

export interface BaseAssetRetirementRequest extends BaseAssetTransactionRequest {
  otherFee?: Decimal;
  rawPrice: Decimal;
  serviceFee?: Decimal;
}

export type AdminAssetTradeRequest = BaseAssetTransactionRequest;

export interface AdminAssetTransferRequest extends BaseAssetTransactionRequest {
  destinationId: uuid;
  sourceId: uuid;
}

export interface AdminRrtAssetResponse extends BaseResponse {
  beZeroFactor: Decimal;
  calculatedFactor: Decimal;
  grossQuantity: number;
  netQuantity: number;
  portfolio: TrimmedBookResponse;
  portfolioNetPercentage: Decimal;
  projectVintage: TrimmedProjectVintageResponse;
}

export interface MobileRctAssetDetailsResponse {
  portfolioId: uuid;
  portfolioName: string;
  portfolioType: BookType;
}

export type PortalRctAssetDetailsResponse = MobileRctAssetDetailsResponse;

export type AdminRctAssetDetailsResponse = PortalRctAssetDetailsResponse;

// note : these will only be (possibly) defined if asset type is a vintage
export interface MobileVintageAssetDetailsResponse {
  beZeroRating?: ProjectBeZeroRating;
  beZeroUpdatedDate?: Date;
  countryAlpha3?: string;
  countryName?: string;
  isByorctApproved: boolean;
  projectId: uuid;
  projectTypeCategory: string;
  projectTypeId: number;
  projectTypeType: string;
  projectVintageName: string;
  registryName?: string;
  registryProjectId: string;
}

export type PortalVintageAssetDetailsResponse = MobileVintageAssetDetailsResponse;

export interface AdminVintageAssetDetailsResponse extends PortalVintageAssetDetailsResponse {
  bufferCategoryName?: string;
  highBufferPercentage?: Decimal;
  isRctEligible: boolean;
  isRctStandard: boolean;
  isScienceTeamApproved: boolean;
  isSuspended: boolean;
  lowBufferPercentage?: Decimal;
  riskBufferPercentage?: Decimal;
}

export interface MobileRrtAssetDetailsResponse
  extends MobileRctAssetDetailsResponse,
    MobileVintageAssetDetailsResponse {
  beZeroFactor: Decimal;
  netQuantity: number;
}

export interface PortalRrtAssetDetailsResponse
  extends PortalRctAssetDetailsResponse,
    PortalVintageAssetDetailsResponse {
  beZeroFactor: Decimal;
  netQuantity: number;
}

export interface AdminRrtAssetDetailsResponse extends AdminRctAssetDetailsResponse, AdminVintageAssetDetailsResponse {
  beZeroFactor: Decimal;
  netQuantity: number;
}

interface BaseAssetResponse extends BaseResponse {
  name: string;
  type: AssetType;
}

export interface MobileAssetResponse
  extends BaseAssetResponse,
    Partial<MobileVintageAssetDetailsResponse>,
    Partial<MobileVintageAssetDetailsResponse> {}

export interface PortalAssetResponse extends BaseAssetResponse, Partial<PortalVintageAssetDetailsResponse> {}

export interface AdminAssetResponse extends BaseAssetResponse, Partial<AdminVintageAssetDetailsResponse> {}

export interface MobileAssetPurchaseResponse extends BaseResponse {
  amount: number;
  asset: MobileAssetResponse;
  totalPrice: Decimal;
}

export interface PortalAssetPurchaseResponse extends MobileAssetPurchaseResponse {
  asset: PortalAssetResponse;
}

export interface AdminAssetPurchaseResponse extends BaseResponse {
  amount: number;
  asset: AdminAssetResponse;
  otherFee?: Decimal;
  rawPrice: Decimal;
  serviceFee?: Decimal;
  source: TrimmedBookResponse;
  totalPrice: Decimal; // unsure if this is actually needed?
}

export interface AdminAssetTradeResponse extends BaseResponse {
  amount: number;
  asset: AdminAssetResponse;
  destination?: TrimmedBookResponse;
  otherFee?: Decimal;
  rawPrice: Decimal;
  serviceFee?: Decimal;
  source?: TrimmedBookResponse;
  totalPrice: Decimal;
}

export interface AdminAssetTransferResponse extends BaseResponse {
  amount: number;
  asset: AdminAssetResponse;
  destination: TrimmedBookResponse;
  source: TrimmedBookResponse;
}

export interface PortalVintageAssetRetirementResponse extends BaseResponse {
  amount: number;
  asset: PortalAssetResponse;
  links?: TrimmedRetirementLink[];
  totalPrice: Decimal;
}

export interface AdminVintageAssetRetirementResponse extends PortalVintageAssetRetirementResponse {
  asset: AdminAssetResponse;
  otherFee?: Decimal;
  projectVintage: AdminProjectVintageResponse;
  rawPrice: Decimal;
  serviceFee?: Decimal;
  source: TrimmedBookResponse;
}

export interface PortalRctAssetRetirementResponse extends BaseResponse {
  amount: number;
  asset: PortalAssetResponse;
  associatedVintages?: PortalVintageAssetRetirementResponse[];
  totalPrice: Decimal;
}

export interface AdminRctAssetRetirementResponse extends BaseResponse {
  amount: number;
  asset: AdminAssetResponse;
  associatedVintages?: AdminVintageAssetRetirementResponse[];
  otherFee?: Decimal;
  rawPrice: Decimal;
  serviceFee?: Decimal;
  source: TrimmedBookResponse;
  totalPrice: Decimal;
}

export interface MobileAssetTypeQuery {
  assetTypes?: AssetType[];
}

export type PortalAssetTypeQuery = MobileAssetTypeQuery;

export type AdminAssetTypeQuery = PortalAssetTypeQuery;
