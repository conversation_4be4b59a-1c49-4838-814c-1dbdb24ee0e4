import { uuid } from '../types/uuid.type';

import { TrimmedUserResponse } from './user.interface';

export interface MobileByorctEmailRequest {
  modelPortfolioName?: string;
  cachedEstimateId: uuid;
}

export type PortalByorctEmailRequest = MobileByorctEmailRequest;

export interface MobileByorctEmailResponse {
  modelPortfolioId: uuid;
  rubiconManager: TrimmedUserResponse;
}

export type PortalByorctEmailResponse = MobileByorctEmailResponse;

export interface MobilePurchaseEmailRequest {
  amount: number;
  isPurchaseToRetire: boolean;
  isRiskAdjusted: boolean;
  rctId: uuid;
}

export type PortalPurchaseEmailRequest = MobilePurchaseEmailRequest;
