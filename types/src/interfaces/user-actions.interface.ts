import { json } from '../types/json.type';
import { uuid } from '../types/uuid.type';
import { TrimmedOrganizationResponse } from './organization.interface';
import { TrimmedUserResponse } from './user.interface';

export const UserActionType = [
  'login',
  'project_viewed',
  'portfolio_viewed',
  'byorct_price_estimate',
  'byorct_requested',
  'rct_quote_request',
  'rct_retirement_request',
] as const;

export type UserActionType = (typeof UserActionType)[number];

export type UserActionDataV1 = { version: 1 } & (
  | { type: 'login' }
  | { type: 'project_viewed'; data: { projectId: uuid } }
  | { type: 'portfolio_viewed'; data: { productId: uuid } }
  | {
      type: 'byorct_price_estimate';
      data: { vintageIds: uuid[]; quantities: number[]; riskAdjusted: boolean; forRetirement: boolean };
    }
  | {
      type: 'byorct_requested';
      data: {
        vintageIds: uuid[];
        quantities: number[];
        riskAdjusted: boolean;
        forRetirement: boolean;
        price: number;
        modelPortfolioId: uuid;
      };
    }
  | {
      type: 'rct_quote_request';
      data: {
        productId: uuid;
        price: number;
        quantity: number;
        projectIds: uuid[];
        percentages: number[];
        riskAdjusted: boolean;
        forRetirement: boolean;
      };
    }
  | { type: 'rct_retirement_request'; data: { retirementId: uuid } }
);

export type UserActionDataType = UserActionDataV1;

interface UserActionData {
  version: number;
  type: UserActionType;
  data: json;
}

export type PortalUserActionCreate = UserActionData;

export interface AdminUserActionRequest {
  since: Date;
  includeInternal?: boolean;
  userId?: uuid;
  organizationId?: uuid;
}

export interface AdminUserActionResponse extends UserActionData {
  createdAt: Date;
  user: TrimmedUserResponse;
  organization?: TrimmedOrganizationResponse;
}
