import { uuid } from '../types/uuid.type';
import { AdminAssetTransferRequest, AdminAssetTransferResponse } from './asset.interface';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedUserResponse } from './user.interface';
import { TransferOrderByOption, TransferRelation } from '../enums/transfer.enum';

export interface AdminTransferRequest {
  assetTransfers: AdminAssetTransferRequest[];
  memo?: string;
}

export interface AdminTransferResponse extends BaseResponse {
  assetTransfers?: AdminAssetTransferResponse[];
  docsCount: number;
  memo?: string;
  totalAmount: number;
  user: TrimmedUserResponse;
}

export interface AdminTransferRelationsQuery {
  includeRelations?: TransferRelation[]; // currently does not work todo : double check?
}

export interface AdminTransferQuery extends AdminTransferRelationsQuery, BaseQuery {
  assetId?: uuid;
  destinationId?: uuid;
  ownerId?: uuid; // maybe bookId is a beter name?
  sourceId?: uuid;
  userId?: uuid;
  orderBy?: TransferOrderByOption;
}

export interface AdminTransferQueryResponse extends BaseQueryResponse {
  data: AdminTransferResponse[];
}
