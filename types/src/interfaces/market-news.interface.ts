import { uuid } from '../types/uuid.type';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { MarketNewOrderByOptions, MarketNewsScope } from '../enums/market-news.enum';
import { TrimmedProjectResponse } from './project.interface';

export interface AdminMarketNewsCreateRequest {
  articleDate: Date;
  header: string;
  hitword?: string;
  isIrrelevant?: boolean;
  organizationId?: uuid;
  score?: number;
  scopes: MarketNewsScope[];
  source: string;
  summary: string;
  url: string;
}

export interface AdminMarketNewsTagRequest {
  projectIds: uuid[];
}

export interface AdminMarketNewsUpdateRequest {
  articleDate?: Date;
  header?: string;
  hitword?: string;
  isIrrelevant?: boolean;
  organizationId?: uuid;
  score?: number;
  scopes?: MarketNewsScope[];
  source?: string;
  summary?: string;
  url?: string;
}

export interface PortalMarketNewsResponse extends BaseResponse {
  articleDate: Date;
  header: string;
  hitword?: string;
  projects: TrimmedProjectResponse[];
  score?: number;
  source: string;
  summary: string;
  url: string;
}

export interface AdminMarketNewsResponse extends PortalMarketNewsResponse {
  isIrrelevant?: boolean;
  organizationId?: uuid;
  scopes: MarketNewsScope[];
}

export interface PortalMarketNewsQuery extends BaseQuery {
  /* filter */
  endDate?: Date;
  hitword?: string;
  projectIds?: uuid[];
  source?: string;
  startDate?: Date;
  /* base */
  orderBy?: MarketNewOrderByOptions;
}

export interface AdminMarketNewsQuery extends PortalMarketNewsQuery {
  /* filter */
  isIrrelevant?: boolean;
  organizationId?: uuid;
  scopes?: MarketNewsScope[];
}

export interface PortalMarketNewsQueryResponse extends BaseQueryResponse {
  data: PortalMarketNewsResponse[];
}

export interface AdminMarketNewsQueryResponse extends BaseQueryResponse {
  data: AdminMarketNewsResponse[];
}
