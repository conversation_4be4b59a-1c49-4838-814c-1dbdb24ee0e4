import Decimal from 'decimal.js';
import {
  MobileTransactionStatus,
  MobileTransactionType,
  PortalTransactionStatus,
  PortalTransactionType,
  TransactionOrderByOptions,
  TransactionStatus,
  TransactionSubtype,
  TransactionType,
} from '../enums/transaction.enum';
import { OrderByDirection } from '../enums/base.enum';
import { uuid } from '../types/uuid.type';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { AdminAssetFlowResponse, MobileAssetFlowResponse, PortalAssetFlowResponse } from './flow.interface';

export interface BaseTransactionOrderBy {
  orderBy: TransactionOrderByOptions;
  orderByDirection: OrderByDirection;
}

export interface TrimmedTransactionResponse extends BaseResponse {
  counterpartyId?: uuid;
  counterpartyName?: string;
  docsCount: number;
  parentId?: uuid; // forward or marketing agreement id for the given line item
  settledAt?: Date;
  status: TransactionStatus;
  subtype?: TransactionSubtype;
  totalPrice: Decimal;
  totalQuantity: number;
  type: TransactionType;
  uiKey: string;
}

export interface MobileTransactionResponse extends TrimmedTransactionResponse {
  assetFlows: MobileAssetFlowResponse[];
}

export interface PortalTransactionResponse extends MobileTransactionResponse {
  assetFlows: PortalAssetFlowResponse[];
}

export interface AdminTransactionResponse extends TrimmedTransactionResponse {
  assetFlows: AdminAssetFlowResponse[];
}

export interface MobileTransactionQuery extends BaseQuery {
  /* filters */
  assetIds?: uuid[];
  assetName?: string;
  ids?: uuid[];
  counterparty?: string;
  statuses?: MobileTransactionStatus[];
  subtypes?: TransactionSubtype[];
  types?: MobileTransactionType[];
  uiKey?: string;
  /* base */
  orderBys?: BaseTransactionOrderBy[] | string[]; // FE passes in string array formatted as ['field:direction']
}

export interface PortalTransactionQuery extends BaseQuery {
  /* filters */
  assetIds?: uuid[];
  assetName?: string;
  ids?: uuid[];
  counterparty?: string;
  statuses?: PortalTransactionStatus[];
  subtypes?: TransactionSubtype[];
  types?: PortalTransactionType[];
  uiKey?: string;
  /* base */
  orderBys?: BaseTransactionOrderBy[] | string[]; // FE passes in string array formatted as ['field:direction']
}

export interface AdminTransactionQuery extends BaseQuery {
  /* filters */
  assetIds?: uuid[];
  assetName?: string;
  ids?: uuid[];
  counterparty?: string;
  statuses?: TransactionStatus[];
  subtypes?: TransactionSubtype[];
  types?: TransactionType[];
  uiKey?: string;
  /* base */
  orderBys?: BaseTransactionOrderBy[] | string[]; // FE passes in string array formatted as ['field:direction']
}

export interface AdminTransactionSearchQuery {
  q: string; // string to search against
  fuzzy?: boolean; // fuzzy search on counterparty and/or asset?
  asset?: boolean; // include assets in search query?
  counterparty?: boolean; // include counterparty in search query?
  uiKey?: boolean; // include uiKey in search query?
  transactionStatuses?: TransactionStatus[];
  transactionTypes?: TransactionType[];
  limit: number;
}

export interface MobileTransactionQueryResponse extends BaseQueryResponse {
  data: MobileTransactionResponse[];
}

export interface PortalTransactionQueryResponse extends BaseQueryResponse {
  data: PortalTransactionResponse[];
}

export interface AdminTransactionQueryResponse extends BaseQueryResponse {
  data: AdminTransactionResponse[];
}
