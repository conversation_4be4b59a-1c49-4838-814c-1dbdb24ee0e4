import Decimal from 'decimal.js';
import { ForwardOrderByOptions, ForwardStatus, ForwardType } from '../enums/forward.enum';
import { uuid } from '../types/uuid.type';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedBookResponse } from './book.interface';
import { TrimmedProjectVintageResponse } from './project-vintage.interface';
import { TrimmedProjectResponse } from './project.interface';
import { OrderByDirection } from '../enums/base.enum';
import { TrimmedOrganizationResponse } from './organization.interface';
import { OrganizationType } from '../enums/organization.enum';

export interface AdminForwardCreateRequest {
  memo?: string;
  organizationId: uuid;
  projectId: uuid;
  type: ForwardType;
}

export interface AdminForwardUpdateRequest {
  memo?: string;
}

export interface AdminForwardLineItemCreateRequest {
  bookId?: uuid;
  expectedAmount: number;
  maxAmount: number;
  minAmount: number;
  otherFee?: Decimal;
  originalDeliveryDate: Date;
  rawPrice: Decimal;
  projectVintageId?: uuid;
  serviceFee?: Decimal;
}

export interface AdminForwardLineItemUpdateRequest {
  bookId?: uuid;
  expectedAmount?: number;
  lastUpdatedDeliveryDate?: Date;
  maxAmount?: number;
  minAmount?: number;
  otherFee?: Decimal;
  rawPrice?: Decimal;
  projectVintageId?: uuid;
  serviceFee?: Decimal;
}

export interface AdminForwardLineItemSettleRequest extends AdminForwardLineItemUpdateRequest {
  settledAmount: number;
  settledAt: Date;
}

export interface AdminForwardLineItemResponse extends BaseResponse {
  book?: TrimmedBookResponse;
  docsCount: number;
  expectedAmount: number;
  lastUpdatedDeliveryDate: Date;
  maxAmount: number;
  minAmount: number;
  otherFee?: Decimal;
  originalDeliveryDate: Date;
  rawPrice: Decimal;
  projectVintage?: TrimmedProjectVintageResponse;
  serviceFee?: Decimal;
  settledAmount?: number;
  settledAt?: Date;
  status: ForwardStatus;
  uiKey: string;
}

export interface AdminForwardOrganizationResponse extends TrimmedOrganizationResponse {
  type: OrganizationType;
}

export interface AdminForwardResponse extends BaseResponse {
  amount: number;
  docsCount: number;
  lineItems: AdminForwardLineItemResponse[];
  memo?: string;
  organization: AdminForwardOrganizationResponse;
  project: TrimmedProjectResponse;
  status: ForwardStatus;
  type: ForwardType;
  uiKey: string;
}

export interface BaseForwardOrderBy {
  orderBy: ForwardOrderByOptions;
  orderByDirection: OrderByDirection;
}

export interface AdminForwardQuery extends Omit<BaseQuery, 'orderBy'> {
  /* filters */
  organizationId?: uuid;
  projectId?: uuid;
  status?: ForwardStatus;
  type?: ForwardType;
  uiKey?: string;
  /* base */
  orderBys?: BaseForwardOrderBy[] | string[]; // FE passes in string array formatted as ['field:direction']
}

export interface AdminForwardQueryResponse extends BaseQueryResponse {
  data: AdminForwardResponse[];
}
