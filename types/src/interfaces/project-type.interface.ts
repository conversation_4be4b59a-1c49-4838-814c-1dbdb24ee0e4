import { ProjectTypeCategory, ProjectTypeOrderByOption, ProjectTypeRelations } from '../enums/project-type.enum';
import { AdminBookTypeGroupedAllocationResponse, AdminGroupedAllocationResponse } from './allocation.interface';
import { BaseQuery, BaseQueryResponse } from './base.interface';

export interface AdminProjectTypeCreateRequest {
  name: string;
  category: ProjectTypeCategory;
  type: string;
}

export interface AdminProjectTypeUpdateRequest {
  name?: string;
  category?: ProjectTypeCategory;
  type?: string;
}

export interface TrimmedProjectTypeResponse {
  category: ProjectTypeCategory;
  id: number;
  name: string;
  type: string;
}

export interface AdminProjectTypeResponse extends TrimmedProjectTypeResponse {
  assetAllocations?: AdminGroupedAllocationResponse;
  assetAllocationsByBookType?: AdminBookTypeGroupedAllocationResponse[];
}

export interface AdminProjectTypeRelationsQuery {
  includeRelations?: ProjectTypeRelations[];
}

export interface AdminProjectTypeQuery extends BaseQuery, AdminProjectTypeRelationsQuery {
  /* filters */
  category?: ProjectTypeCategory;
  name?: string;
  type?: string;
  /* base */
  orderBy?: ProjectTypeOrderByOption;
}

export interface AdminProjectTypeQueryResponse extends BaseQueryResponse {
  data: AdminProjectTypeResponse[];
}
