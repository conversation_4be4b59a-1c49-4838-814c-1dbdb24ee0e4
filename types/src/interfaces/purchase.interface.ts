import { AssetType } from '../enums/asset.enum';
import { OrderByDirection } from '../enums/base.enum';
import {
  MobilePurchaseRelations,
  PortalPurchaseRelations,
  PurchaseFlowType,
  PurchaseOrderByOptions,
  PurchaseRelations,
  PurchaseStatus,
  PurchaseUpdatableStatus,
} from '../enums/transaction.enum';
import { uuid } from '../types/uuid.type';
import {
  AdminAssetPurchaseRequest,
  AdminAssetPurchaseResponse,
  MobileAssetPurchaseResponse,
  PortalAssetPurchaseResponse,
} from './asset.interface';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedOrganizationResponse } from './organization.interface';

export interface BasePurchaseOrderBy {
  orderBy: PurchaseOrderByOptions;
  orderByDirection: OrderByDirection;
}

export interface AdminPurchaseRequest {
  assets: AdminAssetPurchaseRequest[];
  assetType: AssetType;
  organizationId: uuid;
  flowType: PurchaseFlowType;
  memo?: string;
  needsRiskAdjustment: boolean;
  paymentDueDate?: Date;
}

export interface AdminPurchaseExecuteRequest {
  updatableStatusOrder: PurchaseUpdatableStatus[];
}

export interface AdminPurchaseDeliverRequest {
  assetsDeliveredAt: Date;
}

export interface MobilePurchaseResponse extends BaseResponse {
  amount: number;
  assets: MobileAssetPurchaseResponse[]; // todo : always needed?
  assetType: AssetType;
  // organization: TrimmedOrganizationResponse; // todo : remove
  dateFinished?: Date;
  dateStarted: Date;
  docsCount: number;
  flowType: PurchaseFlowType;
  isDelivered: boolean;
  isPaid: boolean;
  memo?: string;
  needsRiskAdjustment?: boolean;
  paymentDueDate?: Date;
  status: PurchaseStatus;
  uiKey: string;
  // updatableStatusOrder: PurchaseUpdatableStatus[]; // todo : remove
}

export interface PortalPurchaseResponse extends MobilePurchaseResponse {
  assets: PortalAssetPurchaseResponse[]; // todo : always needed?
}

export interface AdminPurchaseResponse extends BaseResponse {
  amount: number;
  assets: AdminAssetPurchaseResponse[]; // todo : always needed?
  assetType: AssetType;
  dateFinished?: Date;
  dateStarted: Date;
  docsCount: number;
  flowType: PurchaseFlowType;
  isDelivered: boolean;
  isPaid: boolean;
  memo?: string;
  needsRiskAdjustment?: boolean;
  organization: TrimmedOrganizationResponse;
  paymentDueDate?: Date;
  status: PurchaseStatus;
  uiKey: string;
  updatableStatusOrder: PurchaseUpdatableStatus[];
}

export interface PortalPurchaseRelationsQuery {
  includeRelations?: PortalPurchaseRelations[];
}

export interface MobilePurchaseRelationsQuery {
  includeRelations?: MobilePurchaseRelations[];
}

export interface AdminPurchaseRelationsQuery {
  includeRelations?: PurchaseRelations[];
}

export interface AdminPurchaseQuery extends BaseQuery, AdminPurchaseRelationsQuery {
  /* filters */
  assetId?: uuid;
  organizationId?: uuid;
  statuses?: PurchaseStatus[];
  /* base */
  orderBys?: BasePurchaseOrderBy[] | string[]; // FE passes in string array formatted as ['field:direction']
}

export interface AdminPurchaseQueryResponse extends BaseQueryResponse {
  data: AdminPurchaseResponse[];
}
