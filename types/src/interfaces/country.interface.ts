import { CountryRelations, CountryOrderByOption, CountryRegions } from '../enums/country.enum';
import { BaseQuery, BaseQueryResponse } from './base.interface';
import { PortalProjectResponse } from './project.interface';

export interface AdminCountryRequest {
  alpha2: string;
  alpha3: string;
  name: string;
  region: CountryRegions;
  subRegion: string;
}

export type TrimmedCountryResponse = AdminCountryRequest;

export interface AdminCountryResponse extends TrimmedCountryResponse {
  projectCount?: number;
  projects?: PortalProjectResponse[]; // todo (TD-8) : maybe eventually this can use TrimmedProjectResponse
}

export interface AdminCountryQuery extends BaseQuery {
  countProjects?: boolean;
  region?: CountryRegions;
  subRegion?: string;
  orderBy?: CountryOrderByOption;
}

export interface AdminCountryRelationsQuery {
  includeRelations?: CountryRelations[];
}

export interface AdminCountryQueryResponse extends BaseQueryResponse {
  data: AdminCountryResponse[];
}
