import Decimal from 'decimal.js';
import {
  HistoricalBufferOrderByOption,
  ProjectVintageBufferType,
  ProjectVintageOrderByOption,
  ProjectVintageRelations,
} from '../enums/project-vintage.enum';
import { uuid } from '../types/uuid.type';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { AdminCreditInflowResponse } from './credit-flow.interface';
import { AdminProjectResponse, TrimmedProjectResponse } from './project.interface';
import { TrimmedUserResponse } from './user.interface';
import { DateRange } from './date-range.interface';
import { AdminBookTypeGroupedAllocationResponse, AdminGroupedAllocationResponse } from './allocation.interface';

export interface AdminProjectVintageRequest {
  name: string;
  interval: string | DateRange;
  projectId: uuid;
}

export interface AdminProjectVintageRiskBufferRequest {
  highBufferPercentage?: Decimal;
  lowBufferPercentage?: Decimal;
  notes: string;
  projectVintageId: uuid;
  riskBufferPercentage?: Decimal;
}

export interface TrimmedProjectVintageResponse extends BaseResponse {
  // averageCostBasis?: Decimal;
  // highBufferPercentage?: Decimal;
  interval?: string | DateRange;
  isRctEligible: boolean;
  // lowBufferPercentage?: Decimal;
  // riskBufferPercentage?: Decimal;
  name: string;
  // price?: Decimal;
  project?: TrimmedProjectResponse;
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface MobileProjectVintageResponse extends TrimmedProjectVintageResponse {
  // project: PortalProjectResponse;  // todo : if FE needs PortalProjectResponse, can bring it back
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface PortalProjectVintageResponse extends MobileProjectVintageResponse {
  // project: PortalProjectResponse;  // todo : if FE needs PortalProjectResponse, can bring it back
}

export interface AdminProjectVintageResponse extends TrimmedProjectVintageResponse {
  assetAllocations?: AdminGroupedAllocationResponse;
  assetAllocationsByBookType?: AdminBookTypeGroupedAllocationResponse[];
  averageCostBasis?: Decimal;
  creditFlows?: AdminCreditInflowResponse[]; // todo : used? wasn't here before
  highBufferPercentage?: Decimal;
  inflows?: AdminCreditInflowResponse[]; // todo : used? it's literally not called the same thing
  lowBufferPercentage?: Decimal;
  project?: AdminProjectResponse;
  riskBufferPercentage?: Decimal;
  price?: Decimal;
  markupPrice?: Decimal;
}

export interface AdminHistoricalBufferResponse {
  id: uuid;
  timestamp: Date;
  newPercentage?: Decimal;
  notes?: string;
  oldPercentage?: Decimal;
  projectVintage: AdminProjectVintageResponse;
  type: ProjectVintageBufferType;
  user: TrimmedUserResponse;
}

export interface AdminProjectVintageRelationsQuery {
  includeRelations?: ProjectVintageRelations[];
}
export interface AdminProjectVintageQuery extends BaseQuery, AdminProjectVintageRelationsQuery {
  ids?: uuid[];
  projectIds?: uuid[];
  projectTypeIds?: number[];
  orderBy: ProjectVintageOrderByOption;
}

export interface AdminProjectVintageQueryResponse extends BaseQueryResponse {
  data: AdminProjectVintageResponse[];
}

export interface AdminHistoricalBufferQuery extends BaseQuery {
  projectVintageId?: uuid;
  type?: ProjectVintageBufferType;
  userId?: uuid;
  orderBy: HistoricalBufferOrderByOption;
}

export interface AdminHistoricalBufferQueryResponse extends BaseQueryResponse {
  data: AdminHistoricalBufferResponse[];
}
