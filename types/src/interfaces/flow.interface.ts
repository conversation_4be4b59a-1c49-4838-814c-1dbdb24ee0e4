import { AssetType } from '../enums/asset.enum';
import { OrderByDirection } from '../enums/base.enum';
import { AssetFlowOrderByOptions, AssetFlowRelation, AssetFlowStatus, PortalAssetFlowStatus } from '../enums/flow.enum';
import { uuid } from '../types/uuid.type';
import { TrimmedTransactionResponse } from './transaction.interface';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedRetirementLink } from './retirement.interface';
import { TransactionSubtype, TransactionType } from '../enums/transaction.enum';
import { AdminAssetResponse, MobileAssetResponse, PortalAssetResponse } from './asset.interface';
import { TrimmedProjectVintageResponse } from './project-vintage.interface';
import { TrimmedBookResponse } from './book.interface';

export interface BaseAssetFlowOrderBy {
  orderBy: AssetFlowOrderByOptions;
  orderByDirection: OrderByDirection;
}

export interface MobileAssetFlowResponse extends BaseResponse {
  amount: number;
  asset: MobileAssetResponse;
  lastUpdatedDeliveryDate?: Date;
  settledAt?: Date;
  status: PortalAssetFlowStatus;
  transactionSubtype?: TransactionSubtype;
  transactionType: TransactionType;
}

// note : this is only used for retirements/transfer_outflows on portal for now
export interface PortalAssetFlowResponse extends MobileAssetFlowResponse {
  asset: PortalAssetResponse;
  detailedAsset?: TrimmedProjectVintageResponse | TrimmedBookResponse; // need project.registry and a few other things
  links?: TrimmedRetirementLink[];
  source?: TrimmedBookResponse; // needed to display if the vintage is part of an RCT retirement
  transaction?: TrimmedTransactionResponse;
}

export interface AdminAssetFlowResponse extends BaseResponse {
  amount: number;
  asset: AdminAssetResponse;
  lastUpdatedDeliveryDate?: Date;
  links?: TrimmedRetirementLink[];
  settledAt?: Date;
  status: AssetFlowStatus;
  transactionSubtype?: TransactionSubtype;
  transactionType: TransactionType;
}

export interface PortalAssetFlowQuery extends Omit<BaseQuery, 'orderBy'> {
  /* filters */
  assetId?: uuid;
  assetType?: AssetType;
  status?: AssetFlowStatus;
  transactionId?: uuid;
  transactionSubtypes?: TransactionSubtype[];
  transactionTypes?: TransactionType[];
  /* base */
  includeRelations?: AssetFlowRelation[];
  orderBys?: BaseAssetFlowOrderBy[] | string[]; // FE passes in string array formatted as ['field:direction']
}

export interface PortalAssetFlowQueryResponse extends BaseQueryResponse {
  data: PortalAssetFlowResponse[];
}
