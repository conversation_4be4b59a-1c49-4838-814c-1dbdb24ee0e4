import { BaseQueryResponse, BaseResponse } from './base.interface';
import { NotificationEvent, NotificationCadence, NotificationEventSummary } from '../enums/notification.enum';

interface BaseNotificationSubscription {
  cadence: NotificationCadence;
  event: NotificationEvent;
}

export interface AdminNotificationSubscriptionRequest {
  subscriptions: BaseNotificationSubscription[];
}

export interface AdminNotificationBatchRequest {
  cadence: NotificationCadence;
  eventSummaries?: NotificationEventSummary[];
}

export interface AdminNotificationBatchResponse {
  cadence: NotificationCadence;
  emailCount: number;
  eventCount: number;
  eventSummaries: NotificationEventSummary[];
}

export interface AdminNotificationSubscriptionResponse extends BaseResponse, BaseNotificationSubscription {}

export interface AdminNotificationSubscriptionQueryResponse extends BaseQueryResponse {
  subscriptions: AdminNotificationSubscriptionResponse[];
}
