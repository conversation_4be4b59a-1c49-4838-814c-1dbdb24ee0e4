import Decimal from 'decimal.js';
import { uuid } from '../types/uuid.type';
import { AllocationAdjustmentEnum, BufferComputationEnum } from '../enums/pricing.enum';

export interface MobileCustomPricingResponse {
  price?: Decimal; // if the price is undefined, the alternatives array will be populated
  id: uuid; // use for a quote of the initial attempt or a quote request without price
  alternatives: {
    id: uuid;
    allocation: {
      projectId: uuid;
      vintageId: uuid;
      quantity: number;
      delta: number;
    }[];
    price: Decimal;
    type: AllocationAdjustmentEnum;
  }[];
}

export type PortalCustomPricingResponse = MobileCustomPricingResponse;

export interface MobileCustomPricingRequest {
  allocation: { vintageId: uuid; quantity: number }[];
  riskAdjusted?: boolean;
  forRetirement?: boolean;
  buffer?: BufferComputationEnum;
}

export type PortalCustomPricingRequest = MobileCustomPricingRequest;

export interface AdminPricingEstimateRequest {
  vintageId?: uuid;
  quantity: number;
  price?: Decimal;
  cost?: Decimal;
  buffer?: number;
  bufferCategoryId?: uuid;
}

export interface AdminPricingEstimateResponse {
  priceEstimate: Decimal;
  costEstimate: Decimal;
  unitPrice: Decimal;
  unitCost: Decimal;
  totalQuantity: number;
  markup: Decimal;
  bufferPrice: Decimal;
  bufferCost: Decimal;
}

export interface AdminVintagePricingRequest {
  vintage_ids?: string[];
  project_names?: string[];
  registry_ids?: string[];
}

export interface AdminVintagePricingResponse {
  index: number;
  vintage_id: string;
  name: string;
  registry_project_id: string;
  label: string;
  price: Decimal;
  average_cost_basis: Decimal;
  change_7: Decimal;
  perc_change_7: Decimal;
  change_30: Decimal;
  perc_change_30: Decimal;
  change_180: Decimal;
  perc_change_180: Decimal;
  change_365: Decimal;
  perc_change_365: number;
  last_price_date: number;
  source: string;
}

// export interface HistoricalPricingRequest {
//   sources?: string[];
//   project_types?: number[];
//   project_countries?: string[];
//   project_vintages?: string[];
// }

// export interface HistoricalPricingResponse {
//   index: number;
//   source: string;
//   date: number;
//   purchase_value: number;
//   retired_value: number;
//   available_value: number;
//   allocated_value: number;
//   unallocated_value: number;
// }
