import Decimal from 'decimal.js';
import {
  BookAction,
  BookOrderByOption,
  BookRelations,
  BookType,
  MobileBookRelations,
  PortalBookRelations,
} from '../enums/book.enum';
import { uuid } from '../types/uuid.type';
import {
  AdminAssetTypeGroupedAllocationResponse,
  AdminBookTypeGroupedAllocationResponse,
  AdminGroupedAllocationResponse,
  AdminGroupedAllocationWithNestedResponse,
  AdminProjectGroupedAllocationResponse,
  AdminProjectTypeGroupedAllocationResponse,
  MobileGroupedAllocationResponse,
  MobileGroupedAllocationWithNestedResponse,
  MobileProjectGroupedAllocationResponse,
  PortalGroupedAllocationResponse,
  PortalGroupedAllocationWithNestedResponse,
  PortalProjectGroupedAllocationResponse,
} from './allocation.interface';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedProjectVintageResponse } from './project-vintage.interface';
import { TrimmedOrganizationResponse } from './organization.interface';
import { AdminRrtAssetResponse } from './asset.interface';

export interface BaseBookLimit {
  holdingAmountMax?: number;
  holdingAmountMin?: number;
  holdingPriceMax?: Decimal;
  holdingPriceMin?: Decimal;
}

export interface AdminRctCustomPortfolioCreateRequest {
  description?: string;
  isEnabled: boolean;
  limit?: BaseBookLimit;
  name: string;
  organizationId?: uuid;
  purchasePrice?: Decimal;
  purchasePriceWithBuffer?: Decimal;
}

export interface AdminRctPublicPortfolioCreateRequest {
  description?: string;
  isEnabled: boolean;
  limit?: BaseBookLimit;
  name: string;
  purchasePrice?: Decimal;
  purchasePriceWithBuffer?: Decimal;
}

export interface AdminRrtPortfolioCreateRequest {
  description?: string;
  isEnabled: boolean;
  name: string;
  purchasePrice: Decimal;
}

export interface AdminBookUpdateRequest {
  description?: string;
  isEnabled?: boolean;
  limit?: BaseBookLimit;
  name?: string;
  projectTypeIds?: number[];
  purchasePrice?: Decimal;
  purchasePriceWithBuffer?: Decimal;
}

export interface TrimmedBookResponse extends BaseResponse {
  allowedActions: BookAction[]; // not in portal // does admin need this? if not remove
  description?: string;
  isEnabled: boolean;
  name: string;
  organization?: TrimmedOrganizationResponse; // not in portal
  type: BookType;
}

export interface MobileBookResponse extends BaseResponse {
  description?: string;
  isEnabled: boolean;
  name: string;
  ownerAllocations?: MobileGroupedAllocationResponse | MobileGroupedAllocationWithNestedResponse;
  ownerAllocationsByProject?: MobileProjectGroupedAllocationResponse[];
  purchasePrice?: Decimal;
  purchasePriceWithBuffer?: Decimal;
  type: BookType;
}

export interface PortalBookResponse extends MobileBookResponse {
  assetAllocations?: PortalGroupedAllocationResponse | PortalGroupedAllocationWithNestedResponse; // todo : confirm FE needs this?
  // assetAllocationsByBookType?: AdminGroupedAllocationResponse | AdminGroupedAllocationWithNestedResponse;
  ownerAllocations?: PortalGroupedAllocationResponse | PortalGroupedAllocationWithNestedResponse; // todo : confirm FE needs this? might only need non-nested
  ownerAllocationsByProject?: PortalProjectGroupedAllocationResponse[]; // will only count vintages
}

export interface AdminBookResponse extends BaseResponse, TrimmedBookResponse {
  // assetAllocations are the rct allocations of the book
  // they only exist for books that generate an asset (i.e. public/custom)
  assetAllocations?: AdminGroupedAllocationResponse | AdminGroupedAllocationWithNestedResponse;
  assetAllocationsByBookType?: AdminBookTypeGroupedAllocationResponse[];
  // ownerAllocations are assets that belong to the book
  // i.e. public/custom/reserves/compliance/rehab/opportunistic = vintages, portfolioDefault/customer = vintages/rct
  ownerAllocations?: AdminGroupedAllocationResponse | AdminGroupedAllocationWithNestedResponse;
  ownerAllocationsByAssetType?: AdminAssetTypeGroupedAllocationResponse[];
  ownerAllocationsByProject?: AdminProjectGroupedAllocationResponse[]; // will only count vintages
  ownerAllocationsByProjectType?: AdminProjectTypeGroupedAllocationResponse[]; // will only count vintages
  limit: BaseBookLimit;
  priceUpdatedAt?: Date;
  portfolioScore?: Decimal;
  purchasePrice?: Decimal;
  purchasePriceWithBuffer?: Decimal;
}

export interface AdminRrtPortfolioResponse extends BaseResponse {
  assets: AdminRrtAssetResponse[];
  description?: string;
  isEnabled: boolean;
  name: string;
  priceUpdatedAt: Date;
  purchasePrice: Decimal;
}

export interface AdminBookEstimateRetirementResponse {
  amountTransacted: number;
  projectVintage: TrimmedProjectVintageResponse;
}

export interface MobileBookRelationsQuery {
  includeRelations?: MobileBookRelations[];
}

export interface PortalBookRelationsQuery {
  includeRelations?: PortalBookRelations[];
}

export interface AdminBookRelationsQuery {
  includeRelations?: BookRelations[];
}

export interface MobileBookQuery extends BaseQuery, MobileBookRelationsQuery {
  /* filters */
  ids?: uuid[];
  name?: string;
  types?: BookType[];
  /* base */
  orderBy?: BookOrderByOption;
}

export interface PortalBookQuery extends BaseQuery, PortalBookRelationsQuery {
  /* filters */
  ids?: uuid[];
  name?: string;
  types?: BookType[];
  /* base */
  orderBy?: BookOrderByOption;
}

export interface AdminBookQuery extends BaseQuery, AdminBookRelationsQuery {
  /* filters */
  allowedAction?: BookAction;
  ids?: uuid[];
  isEnabled?: boolean;
  name?: string;
  organizationId?: uuid;
  types?: BookType[];
  /* base */
  orderBy?: BookOrderByOption;
}

export interface MobileBookQueryResponse extends BaseQueryResponse {
  data: MobileBookResponse[];
}

export interface PortalBookQueryResponse extends BaseQueryResponse {
  data: PortalBookResponse[];
}

export interface AdminBookQueryResponse extends BaseQueryResponse {
  data: AdminBookResponse[];
}
