import { Permission } from '../enums/permission.enum';
import { uuid } from '../types/uuid.type';

interface UserClaimsShared {
  email: string;
  firstName?: string;
  lastName?: string;
  info?: {
    phone?: string;
    organization?: string;
  };
  name: string; // needed for FE
}
export interface UserClaims extends UserClaimsShared {
  auth: {
    azure?: { id: uuid; isVerified?: boolean; isAdmin?: boolean };
    apiKey?: { id: uuid; permissions: Permission[] };
  };
  roles?: string[];
}
