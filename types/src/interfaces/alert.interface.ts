import Decimal from 'decimal.js';
import { AlertEvent, PortfolioPolicyViolationCategory } from '../enums/alert.enum';
import { ProjectInelibleReasons } from '../enums/project.enum';
import { uuid } from '../types/uuid.type';
import { BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedCountryResponse } from './country.interface';

export interface AdminPortfolioViolationCountryConcentrationResponse {
  country: TrimmedCountryResponse;
  actualPercentage: Decimal;
  expectedMaxPercentage: Decimal;
}

export interface AdminPortfolioViolationProjectConcentrationResponse {
  project: { id: uuid; name: string; registryProjectId: string };
  actualPercentage: Decimal;
  expectedMaxPercentage: Decimal;
}

export interface AdminPortfolioViolationProjectVintageIneligibleResponse {
  projectVintage: { id: uuid; name: string; project: { id: uuid; name: string; registryProjectId: string } };
  ineligibleReasons: ProjectInelibleReasons[];
  isRctStandard: boolean;
  isScienceTeamApproved: boolean;
  isSuspended: boolean;
  riskBufferPercentage?: Decimal;
}

export interface AdminAlertPortfolioPolicyViolationResponse {
  portfolio: { id: uuid; name: string; isEnabled: boolean }; // public portfolio id aka book where type = BookType.RCT_PUBLIC
  violationCategory: PortfolioPolicyViolationCategory;
  violationDetails:
    | AdminPortfolioViolationCountryConcentrationResponse
    | AdminPortfolioViolationProjectConcentrationResponse
    | AdminPortfolioViolationProjectVintageIneligibleResponse;
}

export interface AdminAlertPortfolioStalePriceResponse {
  portfolio: { id: uuid; name: string; isEnabled: boolean }; // public portfolio id aka book where type = BookType.RCT_PUBLIC
  lastUpdatedAt: Date;
  purchasePrice?: Decimal;
  purchasePriceWithBuffer?: Decimal;
}

export interface AdminAlertSubscriptionRequest {
  subscribedEvents: AlertEvent[];
}

export interface AdminAlertResponse {
  id: uuid;
  openedAt: Date;
  event: AlertEvent;
  details: AdminAlertPortfolioPolicyViolationResponse | AdminAlertPortfolioStalePriceResponse;
}

export interface AdminAlertSubscriptionResponse extends BaseResponse {
  event: AlertEvent;
}

export interface AdminAlertQueryResponse extends BaseQueryResponse {
  data: AdminAlertResponse[];
}

export interface AdminAlertSubscriptionQueryResponse extends BaseQueryResponse {
  data: AdminAlertSubscriptionResponse[];
}
