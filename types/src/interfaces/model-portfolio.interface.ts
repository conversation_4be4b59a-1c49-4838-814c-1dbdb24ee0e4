import Decimal from 'decimal.js';
import {
  MobileModelPortfolioRelations,
  ModelPortfolioOrderByOption,
  ModelPortfolioRelations,
  ModelPortfolioStatus,
  PortalModelPortfolioRelations,
} from '../enums/model-portfolio.enum';
import { uuid } from '../types/uuid.type';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedUserResponse } from './user.interface';
import { AdminProjectVintageResponse, TrimmedProjectVintageResponse } from './project-vintage.interface';
import { AdminProjectResponse, TrimmedProjectResponse } from './project.interface';
import { AdminBufferCategoryResponse } from './buffer-category.interface';
import { TrimmedOrganizationResponse } from './organization.interface';
import { OrderByDirection } from '../enums/base.enum';
import { TrimmedBookResponse } from './book.interface';

export interface AdminModelPortfolioCreateRequest {
  groupingId?: uuid;
  includeRiskAdjustment?: boolean;
  memo?: string;
  name?: string;
  organizationId?: uuid;
  priceEstimate?: Decimal;
  showCustomer?: boolean;
  status?: ModelPortfolioStatus;
}

export type AdminModelPortfolioUpdateRequest = Partial<AdminModelPortfolioCreateRequest>;

export interface MobileModelPortfolioResponse extends BaseResponse {
  includeRiskAdjustment?: boolean;
  memo?: string;
  modelPortfolioComponents?: MobileModelPortfolioComponentResponse[];
  name: string;
  priceEstimate?: Decimal;
  status?: ModelPortfolioStatus;
  uiKey: string;
}

export interface PortalModelPortfolioResponse extends MobileModelPortfolioResponse {
  modelPortfolioComponents?: PortalModelPortfolioComponentResponse[];
}

export interface AdminModelPortfolioResponse extends PortalModelPortfolioResponse {
  createdBy: TrimmedUserResponse;
  groupedPortfolios?: AdminGroupedPortfolioResponse[]; // todo : required?
  groupingId: uuid;
  modelPortfolioComponents?: AdminModelPortfolioComponentResponse[];
  organization?: TrimmedOrganizationResponse; // todo : confirm can be optional
  showCustomer?: boolean;
  updatedBy: TrimmedUserResponse;
}

export interface AdminGroupedPortfolioResponse
  extends BaseResponse,
    Pick<
      AdminModelPortfolioResponse,
      'createdBy' | 'name' | 'organization' | 'priceEstimate' | 'showCustomer' | 'status' | 'uiKey' | 'updatedBy'
    > {
  totalAmount: number;
}

export interface ModelPortfolioComponentBase {
  amountAllocated: number;
  overrideMTM?: Decimal;
}

// For book components (RCT/RRT)
export interface ModelPortfolioBookComponentCreate extends ModelPortfolioComponentBase {
  bookId: uuid;
}

// For vintage components
export interface ModelPortfolioVintageComponentCreate extends ModelPortfolioComponentBase {
  bufferCategoryId?: uuid; // todo : actually used?
  bufferPercentage?: Decimal; // todo : actually used?
  costBasis?: Decimal;
  isBufferComponent?: boolean;
  portfolioManagerEstimate?: Decimal;
  projectId?: uuid; // todo : @erick to fix
  registryProjectId?: string; // todo : no longer actually used, remove but need to figure out emails
  vintageId?: uuid;
  vintageInterval?: string;
}

export type BaseModelPortfolioComponentCreate =
  | ModelPortfolioBookComponentCreate
  | ModelPortfolioVintageComponentCreate;

export interface ModelPortfolioBookComponentUpdate extends Partial<ModelPortfolioBookComponentCreate> {
  id: uuid;
}

export interface ModelPortfolioVintageComponentUpdate extends Partial<ModelPortfolioVintageComponentCreate> {
  id: uuid;
}

export type BaseModelPortfolioComponentUpdate =
  | ModelPortfolioBookComponentUpdate
  | ModelPortfolioVintageComponentUpdate;

export interface BaseModelPortfolioComponentDelete {
  id: uuid;
}

export type AdminModelPortfolioComponentRequest =
  | {
      type: 'create';
      create: BaseModelPortfolioComponentCreate[];
    }
  | {
      type: 'delete';
      delete: BaseModelPortfolioComponentDelete[];
    }
  | {
      type: 'update';
      update: BaseModelPortfolioComponentUpdate[];
    };

export type ModelPortfolioComponentPriceOverride = {
  overrideMTM?: Decimal;
};

export type MobileModelPortfolioComponentResponse = BaseResponse &
  ModelPortfolioComponentPriceOverride & {
    // always defined
    amountAllocated: number;
  } & (
    | ({
        // always defined for vintages, whether existing or custom
        project: TrimmedProjectResponse;
        projectPipeline: boolean;
      } & (
        | {
            type: 'vintage'; // existing vintage
            projectVintage: TrimmedProjectVintageResponse;
          }
        | {
            type: 'custom'; // custom vintage
            portfolioManagerEstimate: Decimal;
            vintageInterval: string;
          }
      ))
    | { type: 'rct'; book: TrimmedBookResponse }
    | { type: 'rrt'; book: TrimmedBookResponse }
  );

export type PortalModelPortfolioComponentResponse = MobileModelPortfolioComponentResponse;

export type AdminModelPortfolioComponentResponse = BaseResponse &
  ModelPortfolioComponentPriceOverride & {
    // always defined
    amountAllocated: number;
    createdBy: TrimmedUserResponse;
    updatedBy: TrimmedUserResponse;
  } & (
    | ({
        // always defined for vintages, whether existing or custom
        project: AdminProjectResponse;
        projectPipeline: boolean;
      } & (
        | {
            type: 'vintage'; // existing vintage
            projectVintage: AdminProjectVintageResponse;
            isBufferComponent: boolean;
          }
        | {
            type: 'custom'; // custom vintage
            bufferCategory: AdminBufferCategoryResponse;
            bufferPercentage: Decimal;
            costBasis: Decimal;
            portfolioManagerEstimate: Decimal;
            vintageInterval: string;
            isBufferComponent: boolean;
          }
      ))
    | { type: 'rct'; book: TrimmedBookResponse }
    | { type: 'rrt'; book: TrimmedBookResponse }
  );

export interface BaseModelPortfolioOrderBy {
  orderBy: ModelPortfolioOrderByOption;
  orderByDirection: OrderByDirection;
}

export interface MobileModelPortfolioRelationsQuery {
  includeRelations?: MobileModelPortfolioRelations[];
}

export interface PortalModelPortfolioRelationsQuery {
  includeRelations?: PortalModelPortfolioRelations[];
}

export interface AdminModelPortfolioRelationsQuery {
  includeRelations?: ModelPortfolioRelations[];
}

export interface MobileModelPortfolioQuery extends BaseQuery, MobileModelPortfolioRelationsQuery {
  /* filters */
  name?: string;
  projectId?: uuid;
  projectVintageId?: uuid; // todo : really?
  registryProjectId?: string;
  status?: ModelPortfolioStatus;
  uiKey?: string;
  /* base */
  orderBys?: BaseModelPortfolioOrderBy[]; // FE passes in string array formatted as ['field:direction']
}

export interface PortalModelPortfolioQuery extends BaseQuery, PortalModelPortfolioRelationsQuery {
  /* filters */
  name?: string;
  projectId?: uuid;
  projectVintageId?: uuid; // todo : really?
  registryProjectId?: string;
  status?: ModelPortfolioStatus;
  uiKey?: string;
  /* base */
  orderBys?: BaseModelPortfolioOrderBy[]; // FE passes in string array formatted as ['field:direction']
}

export interface AdminModelPortfolioQuery extends BaseQuery, AdminModelPortfolioRelationsQuery {
  /* filters */
  createdById?: uuid;
  name?: string;
  organizationId?: uuid;
  projectId?: uuid;
  projectVintageId?: uuid; // todo : really?
  registryProjectId?: string;
  showCustomer?: boolean;
  status?: ModelPortfolioStatus;
  uiKey?: string;
  updatedById?: uuid;
  /* base */
  orderBys?: BaseModelPortfolioOrderBy[]; // FE passes in string array formatted as ['field:direction']
}

export interface MobileModelPortfolioQueryResponse extends BaseQueryResponse {
  data: MobileModelPortfolioResponse[];
}

export interface PortalModelPortfolioQueryResponse extends BaseQueryResponse {
  data: PortalModelPortfolioResponse[];
}

export interface AdminModelPortfolioQueryResponse extends BaseQueryResponse {
  data: AdminModelPortfolioResponse[];
}
