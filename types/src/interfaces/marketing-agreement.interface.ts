import Decimal from 'decimal.js';
import { uuid } from '../types/uuid.type';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedProjectVintageResponse } from './project-vintage.interface';
import {
  MarketingAgreementOrderByOptions,
  MarketingAgreementStatus,
  MarketingAgreementType,
} from '../enums/marketing-agreement.enum';
import { TrimmedOrganizationResponse } from './organization.interface';
import { TrimmedProjectResponse } from './project.interface';
import { OrderByDirection } from '../enums/base.enum';

export interface AdminMarketingAgreementCreateRequest {
  feeCalculatorUrl?: string;
  floorPrice: Decimal;
  memo?: string;
  organizationId: uuid;
  projectId: uuid;
  status?: MarketingAgreementStatus;
  type?: MarketingAgreementType;
}

export interface AdminMarketingAgreementUpdateRequest {
  feeCalculatorUrl?: string;
  floorPrice?: Decimal;
  memo?: string;
  organizationId?: uuid;
  status?: MarketingAgreementStatus;
  type?: MarketingAgreementType;
}

export interface AdminMarketingAreementLineItemCreateRequest {
  amount: number;
  amountIssued?: number;
  expirationDate?: Date;
  deliveryDate: Date;
  projectVintageId?: uuid;
  status?: MarketingAgreementStatus;
}

export interface AdminMarketingAgreementLineItemUpdateRequest {
  amount?: number;
  amountIssued?: number;
  expirationDate?: Date;
  deliveryDate?: Date;
  projectVintageId?: uuid;
  status?: MarketingAgreementStatus;
}

export interface AdminMarketingAgreementLineItemResponse extends BaseResponse {
  amount: number;
  amountIssued?: number;
  docsCount: number;
  expirationDate?: Date;
  lastUpdatedDeliveryDate: Date;
  originalDeliveryDate: Date;
  projectVintage?: TrimmedProjectVintageResponse;
  status: MarketingAgreementStatus;
  uiKey: string;
}

export interface AdminMarketingAgreementResponse extends BaseResponse {
  docsCount: number;
  feeCalculatorUrl?: string;
  floorPrice: Decimal;
  lineItems: AdminMarketingAgreementLineItemResponse[];
  memo?: string;
  organization: TrimmedOrganizationResponse;
  project: TrimmedProjectResponse;
  status: MarketingAgreementStatus;
  type?: MarketingAgreementType;
  uiKey: string;
}

export interface BaseMarketingAgreementOrderBy {
  orderBy: MarketingAgreementOrderByOptions;
  orderByDirection: OrderByDirection;
}

export interface AdminMarketingAgreementQuery extends Omit<BaseQuery, 'orderBy'> {
  /* base */
  orderBys?: BaseMarketingAgreementOrderBy[] | string[]; // FE passes in string array formatted as ['field:direction']
  /* filters */
  organizationId?: uuid;
  projectId?: uuid;
  status?: MarketingAgreementStatus;
  uiKey?: string;
}

export interface AdminMarketingAgreementQueryResponse extends BaseQueryResponse {
  data: AdminMarketingAgreementResponse[];
}
