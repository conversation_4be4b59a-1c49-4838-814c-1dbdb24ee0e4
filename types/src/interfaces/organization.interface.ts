import { OrderByDirection } from '../enums/base.enum';
import { OrganizationOrderByOptions, OrganizationUserRole } from '../enums/organization.enum';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import {
  AdminCounterpartyCreateRequest,
  AdminCounterpartyResponse,
  AdminCounterpartyUpdateRequest,
} from './counterparty.interface';
import {
  AdminCustomerPortfolioCreateRequest,
  AdminCustomerPortfolioResponse,
  AdminCustomerPortfolioUpdateRequest,
  MobileCustomerPortfolioResponse,
  PortalCustomerPortfolioResponse,
} from './customer-portfolio.interface';

export interface AdminOrganizationCreateRequest {
  counterparty?: AdminCounterpartyCreateRequest;
  customerPortfolio?: AdminCustomerPortfolioCreateRequest;
  externalReference?: string;
  isEnabled: boolean;
  memo?: string;
  name: string;
}

export interface AdminOrganizationUpdateRequest {
  counterparty?: AdminCounterpartyUpdateRequest;
  customerPortfolio?: AdminCustomerPortfolioUpdateRequest;
  externalReference?: string;
  isEnabled?: boolean;
  memo?: string;
  name?: string;
}

export interface AdminOrganizationUserRequest {
  organizationUserRoles: OrganizationUserRole[];
}

export interface TrimmedOrganizationResponse extends BaseResponse {
  isEnabled: boolean;
  name: string;
}

// mobile user's organization with their roles for that organization
export interface MobileUserOrganizationResponse extends TrimmedOrganizationResponse {
  customerPortfolio: MobileCustomerPortfolioResponse; // currently, portal/mobile user will always have customer portfolio
  organizationUserRoles: OrganizationUserRole[];
}

// portal user's organization with their roles for that organization
export interface PortalUserOrganizationResponse extends TrimmedOrganizationResponse {
  customerPortfolio: PortalCustomerPortfolioResponse; // currently, portal/mobile user will always have customer portfolio
  organizationUserRoles: OrganizationUserRole[];
}

export interface AdminOrganizationResponse extends TrimmedOrganizationResponse {
  counterparty?: AdminCounterpartyResponse;
  customerPortfolio?: AdminCustomerPortfolioResponse;
  externalReference?: string;
  memo?: string;
}

export interface AdminOrganizationQuery extends Omit<BaseQuery, 'orderBy'> {
  /*  filters */
  isCounterparty?: boolean;
  isCustomerPortfolio?: boolean;
  isEnabled?: boolean;
  name?: string;
  /* base */
  orderBys?: { orderBy: OrganizationOrderByOptions; orderByDirection: OrderByDirection }[] | string[]; // FE passes in string array formatted as ['field:direction']
}

export interface MobileUserOrganizationQueryResponse extends BaseQueryResponse {
  data: MobileUserOrganizationResponse[];
}

export interface PortalUserOrganizationQueryResponse extends BaseQueryResponse {
  data: PortalUserOrganizationResponse[];
}

export interface AdminOrganizationQueryResponse extends BaseQueryResponse {
  data: AdminOrganizationResponse[];
}
