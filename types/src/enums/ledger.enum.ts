export enum LedgerBalanceType {
  PENDING = 'pending',
  SETTLED = 'settled',
}

export enum OwnerType {
  /* books */
  BOOK_AGED_DEFAULT = 'book:aged-default',
  BOOK_COMPLIANCE_DEFAULT = 'book:compliance-default', // book for compliance vintages
  BOOK_OPPORTUNISTIC_DEFAULT = 'book:opportunistic-default', // book for opportunistic vintages (mainly vintages to be sold)
  BOOK_PORTFOLIO_DEFAULT = 'book:portfolio-default', // rct-eligible vintages portfolio
  BOOK_REHABILITATION_DEFAULT = 'book:rehabilitation-default', // default book for all inflow vintages
  /* portfolios */
  PORTFOLIO_CUSTOMER = 'portfolio:customer', // customer portfolio
  PORTFOLIO_RCT_CUSTOM = 'portfolio:rct-custom', // custom (bespoke) rct portfolio
  PORTFOLIO_RCT_PUBLIC = 'portfolio:rct-public', // public rct portfolio
  PORTFOLIO_RESERVES = 'portfolio:reserves', // reserve vintages for customers
  PORTFOLIO_RRT_PUBLIC = 'portfolio:rrt-public', // rrt public portfolio
  /* internal */
  OFFSETS = 'offsets', // offsetting inflow/outflow credits
}

export enum LedgerTransactionType {
  PENDING = 'pending',
  RELEASE = 'release',
  REVERT = 'revert',
  SETTLED = 'settled',
}

export enum LedgerTransactionAction {
  OTHER = 'other',
  FORWARDS_SETTLE = 'forwards_settle',
  PURCHASE_CANCEL = 'purchase_cancel',
  PURCHASE_COMPLETE = 'purchase_complete',
  PURCHASE_CREATE = 'purchase_create',
  RESERVES_CREATE = 'reserves_create',
  RESERVES_DELETE = 'reserves_delete',
  RESERVES_UPDATE = 'reserves_update',
  RETIREMENT_AMOUNT_UPDATE = 'retirement_amount_update',
  RETIREMENT_CANCEL = 'retirement_cancel',
  RETIREMENT_COMPLETE = 'retirement_complete',
  RETIREMENT_CREATE = 'retirement_create',
  REVERT = 'revert',
  TRADES_CANCEL = 'trades_cancel',
  TRADES_PLACE = 'trades_place',
  TRADES_SETTLE = 'trades_settle',
  TRANSFERS_EXECUTE = 'transfers_execute',

  // deprecated
  DEPRECATED_BASKET_COMPONENT_CREATE = 'basket_component_create',
  DEPRECATED_BASKET_COMPONENT_DELETE = 'basket_component_delete',
  DEPRECATED_BASKET_COMPONENT_UPDATE = 'basket_component_update',
  DEPRECATED_LINE_ITEM_CREATE = 'line_item_create',
  DEPRECATED_LINE_ITEM_DELETE = 'line_item_delete',
  DEPRECATED_PROJECT_CHANGE_REGISTRY = 'project_change_registry',
}

export enum LedgerBalanceOrderByOption {
  ASSET_ID = 'assetId',
  CREATED_AT = 'createdAt',
  OWNER_ID = 'ownerId',
  TYPE = 'type',
  UPDATED_AT = 'updatedAt',
}
