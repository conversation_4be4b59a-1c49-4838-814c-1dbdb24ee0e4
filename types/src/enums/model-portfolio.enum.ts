export enum ModelPortfolioOrderByOption {
  CREATED_AT = 'createdAt',
  NAME = 'name',
  STATUS = 'status',
  UPDATED_AT = 'updatedAt',
}

export enum ModelPortfolioStatus {
  QUOTE_REQUESTED = 'quote-requested',
  QUOTE_READY = 'quote-ready',
  CLIENT_REVIEWING = 'client-reviewing',
  ENCUMBER_LITE = 'encumber-lite',
  ORDER_CREATED = 'order-created',
  LOST = 'lost',
  EXPIRED_REPLACED = 'expired-replaced',
}

export enum ModelPortfolioRelations {
  MODEL_PORTFOLIO_COMPONENTS = 'model_portfolio_components',
  GROUPED_PORTFOLIOS = 'grouped_portfolios',
  PROJECT = 'project',
  PROJECT_VINTAGE = 'vintage',
  PORTFOLIO = 'portfolio', // RCT/RRT Assets
}

export type MobileModelPortfolioRelations =
  | ModelPortfolioRelations.MODEL_PORTFOLIO_COMPONENTS
  | ModelPortfolioRelations.PROJECT
  | ModelPortfolioRelations.PROJECT_VINTAGE
  | ModelPortfolioRelations.PORTFOLIO;
export const AllMobileModelPortfolioRelations = [
  ModelPortfolioRelations.MODEL_PORTFOLIO_COMPONENTS,
  ModelPortfolioRelations.PROJECT,
  ModelPortfolioRelations.PROJECT_VINTAGE,
  ModelPortfolioRelations.PORTFOLIO,
];

export type PortalModelPortfolioRelations = MobileModelPortfolioRelations;
export const AllPortalModelPortfolioRelations = AllMobileModelPortfolioRelations;
