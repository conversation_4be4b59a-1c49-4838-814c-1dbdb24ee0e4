export enum BookAction {
  BUY = 'buy',
  SELL = 'sell',
  PURCHASE = 'purchase',
  RETIRE = 'retire',
  TRANSFER_OUTFLOW = 'transfer_outflow',
  INTERNAL_TRANSFER = 'transfer',
  RESERVE = 'reserve',
}

export enum BookOrderByOption {
  CREATED_AT = 'createdAt',
  IS_ENABLED = 'isEnabled',
  NAME = 'name',
  SUBTYPE = 'subtype',
  TYPE = 'type',
  UPDATED_AT = 'updatedAt',
}

export enum BookRelations {
  ASSET_ALLOCATIONS = 'asset_allocations',
  ASSET_ALLOCATIONS_NESTED = 'asset_allocations_with_nested',
  ASSET_ALLOCATIONS_BY_BOOK_TYPE = 'asset_allocations_by_book_type',
  PRICES = 'prices',
  ORGANIZATION = 'organization',
  OWNER_ALLOCATIONS = 'owner_allocations',
  OWNER_ALLOCATIONS_NESTED = 'owner_allocations_with_nested',
  OWNER_ALLOCATIONS_BY_ASSET_TYPE = 'owner_allocations_by_asset_type',
  OWNER_ALLOCATIONS_BY_PROJECT = 'owner_allocations_by_project',
  OWNER_ALLOCATIONS_BY_PROJECT_TYPE = 'owner_allocations_by_project_type',
}

export const AllMobileBookRelations = [
  BookRelations.ASSET_ALLOCATIONS,
  BookRelations.ASSET_ALLOCATIONS_NESTED,
  BookRelations.OWNER_ALLOCATIONS,
  BookRelations.OWNER_ALLOCATIONS_BY_PROJECT,
  BookRelations.OWNER_ALLOCATIONS_NESTED,
];
export type MobileBookRelations = (typeof AllMobileBookRelations)[number];

export const AllPortalBookRelations = [
  BookRelations.ASSET_ALLOCATIONS,
  BookRelations.ASSET_ALLOCATIONS_NESTED,
  BookRelations.OWNER_ALLOCATIONS,
  BookRelations.OWNER_ALLOCATIONS_BY_PROJECT,
  BookRelations.OWNER_ALLOCATIONS_NESTED,
];
export type PortalBookRelations = (typeof AllPortalBookRelations)[number];

export enum BookType {
  AGED_DEFAULT = 'aged:default',
  COMPLIANCE_DEFAULT = 'compliance:default',
  OFFSETS = 'offsets', // hidden from portal, mobile, and admin
  OPPORTUNISTIC_DEFAULT = 'opportunistic:default',
  PORTFOLIO_CUSTOMER = 'portfolio:customer',
  PORTFOLIO_DEFAULT = 'portfolio:default',
  PORTFOLIO_RESERVES = 'portfolio:reserves',
  RCT_CUSTOM = 'rct:custom',
  RCT_PUBLIC = 'rct:public',
  REHABILITATION_DEFAULT = 'rehabilitation:default',
  RRT_PUBLIC = 'rrt:public',
}

export const AllMobileBookTypes: BookType[] = [BookType.RCT_CUSTOM, BookType.RCT_PUBLIC];
export const AllPortalBookTypes: BookType[] = [BookType.RCT_CUSTOM, BookType.RCT_PUBLIC, BookType.RRT_PUBLIC];
export const AllAdminBookTypes: BookType[] = [
  BookType.AGED_DEFAULT,
  BookType.COMPLIANCE_DEFAULT,
  BookType.OPPORTUNISTIC_DEFAULT,
  BookType.PORTFOLIO_CUSTOMER,
  BookType.PORTFOLIO_DEFAULT,
  BookType.PORTFOLIO_RESERVES,
  BookType.RCT_CUSTOM,
  BookType.RCT_PUBLIC,
  BookType.REHABILITATION_DEFAULT,
  BookType.RRT_PUBLIC,
];

// aka all non-customer owner types
export const AllRubiconHoldingBookTypes: BookType[] = [
  BookType.AGED_DEFAULT,
  BookType.COMPLIANCE_DEFAULT,
  BookType.OPPORTUNISTIC_DEFAULT,
  BookType.PORTFOLIO_DEFAULT,
  BookType.PORTFOLIO_RESERVES,
  BookType.RCT_CUSTOM,
  BookType.RCT_PUBLIC,
  BookType.REHABILITATION_DEFAULT,
  BookType.RRT_PUBLIC,
];
