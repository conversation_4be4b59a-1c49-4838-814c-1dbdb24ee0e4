export enum PermissionEnum {
  LOGIN = 'login', // users that can login (should be everyone)
  VERIFIED = 'verified', // verified users
  ADMIN = 'admin', // full admins

  // Granular Admin Permissions

  ALERTS_CREATE_PORTFOLIO_POLICY_VIOLATION_ENTRY = 'alerts:create-portfolio-policy-violation-entry',
  ALERTS_CREATE_PORTFOLIO_STALE_PRICE_ENTRY = 'alerts:create-portfolio-stale-price-entry',
  ALERTS_SUBSCRIBE_PORTFOLIO_ALERTS = 'alerts:subscribe-portfolio-alerts',
  BOOKS_CALCULATE_RETIREMENT = 'books:calculate-retirement',
  BOOKS_CREATE_RCT_PORTFOLIOS = 'books:create-rct-portfolios',
  BOOKS_CREATE_RRT_PORTFOLIOS = 'books:create-rrt-portfolios',
  BOOKS_READ = 'books:read',
  BOOKS_UPDATE = 'books:update',
  BOOKS_UPDATE_PRICE = 'books:update-price',
  BOOKS_UPDATE_PROJECT_TYPES = 'books:update-project-types',
  BOOKS_UPDATE_RRT_COMPOSITION = 'books:update-rrt-composition',
  CHATBOT_INSPECT = 'chatbot:inspect',
  CHATBOT_PRICING = 'chatbot:pricing',
  CHATBOT_PRODUCTS = 'chatbot:products',
  CHATBOT_USAGE = 'chatbot:usage',
  CHATBOT_VINTAGES = 'chatbot:vintages',
  CUSTOMER_SALES_CANCEL = 'customer-sales:cancel',
  CUSTOMER_SALES_CREATE = 'customer-sales:create',
  CUSTOMER_SALES_READ = 'customer-sales:read',
  CUSTOMER_SALES_SET_EXECUTED = 'customer-sales:set-executed',
  CUSTOMER_SALES_SET_BINDING = 'customer-sales:set-binding',
  CUSTOMER_SALES_SET_DELIVERED = 'customer-sales:set-delivered',
  CUSTOMER_SALES_UPDATE_PAYMENT = 'customer-sales:update-payment',
  DATA_MANAGEMENT_READ_SCIENCE = 'data-management:read-science',
  DATA_MANAGEMENT_READ_TRADING = 'data-management:read-trading',
  DATA_MANAGEMENT_WRITE_SCIENCE = 'data-management:write-science',
  DATA_MANAGEMENT_WRITE_TRADING = 'data-management:write-trading',
  DOCUMENTS_CREATE = 'documents:create',
  DOCUMENTS_READ = 'documents:read',
  DOCUMENTS_UPDATE = 'documents:update',
  DOCUMENTS_DELETE = 'documents:delete',
  FEATURE_FLAGS_WRITE = 'feature-flags:write',
  FORWARDS_CANCEL = 'forwards:cancel',
  FORWARDS_CREATE = 'forwards:create',
  FORWARDS_READ = 'forwards:read',
  FORWARDS_UPDATE = 'forwards:update',
  FORWARDS_CANCEL_LINE_ITEMS = 'forwards:cancel-line-items',
  FORWARDS_CREATE_LINE_ITEMS = 'forwards:create-line-items',
  FORWARDS_SETTLE_LINE_ITEMS = 'forwards:settle-line-items',
  FORWARDS_UPDATE_LINE_ITEMS = 'forwards:update-line-items',
  LEDGER_DIRECT_ACCESS = 'ledger:direct-access', // this gives access to ledger through the api
  LEDGER_SYNC = 'ledger:sync', // this gives access to sync data on the api with ledger values
  MARKET_NEWS_CREATE = 'market-news:create',
  MARKET_NEWS_DELETE = 'market-news:delete',
  MARKET_NEWS_TAG = 'market-news:tag',
  MARKET_NEWS_UPDATE = 'market-news:update',
  MARKETING_AGREEMENTS_CANCEL = 'marketing-agreements:cancel',
  MARKETING_AGREEMENTS_CREATE = 'marketing-agreements:create',
  MARKETING_AGREEMENTS_READ = 'marketing-agreements:read',
  MARKETING_AGREEMENTS_UPDATE = 'marketing-agreements:update',
  MARKETING_AGREEMENTS_CANCEL_LINE_ITEMS = 'marketing-agreements:cancel-line-items',
  MARKETING_AGREEMENTS_CREATE_LINE_ITEMS = 'marketing-agreements:create-line-items',
  MARKETING_AGREEMENTS_UPDATE_LINE_ITEMS = 'marketing-agreements:update-line-items',
  MODEL_PORTFOLIOS_ADVANCED_VIEW = 'model-portfolios:advanced-view',
  MODEL_PORTFOLIOS_COMPONENTS_WRITE = 'model-portfolios:components-write',
  MODEL_PORTFOLIOS_READ = 'model-portfolios:read',
  MODEL_PORTFOLIOS_CREATE = 'model-portfolios:create',
  MODEL_PORTFOLIOS_COMMENT = 'model-portfolios:comment',
  MODEL_PORTFOLIOS_SHARE = 'model-portfolios:share',
  MODEL_PORTFOLIOS_UPDATE = 'model-portfolios:update',
  MODEL_PORTFOLIOS_DELETE = 'model-portfolios:delete',
  MODEL_PORTFOLIOS_PRICE = 'model-portfolios:price',
  NOTIFICATIONS_TRIGGER_PERIODIC = 'notifications:trigger-periodic', // this gives access to trigger notification summary batches
  ORGANIZATIONS_CREATE = 'organizations:create',
  ORGANIZATIONS_MANAGE_USERS = 'organizations:manage-users',
  ORGANIZATIONS_READ = 'organizations:read',
  ORGANIZATIONS_UPDATE = 'organizations:update',
  PROJECT_TYPES_CREATE = 'project-types:create',
  PROJECT_TYPES_READ = 'project-types:read',
  PROJECTS_EXPORT_CSV = 'projects:export-csv',
  PROJECTS_READ = 'projects:read',
  PROJECTS_WRITE = 'projects:write',
  QUOTES_READ = 'quotes:read',
  QUOTES_WRITE = 'quotes:write',
  REGISTRIES_CREATE = 'registries:create',
  REGISTRIES_UPDATE = 'registries:update',
  REPORTING_DATA_MANAGEMENT = 'reporting:data-management',
  REPORTING_INVENTORY = 'reporting:inventory',
  REPORTING_MARKET_DATA = 'reporting:market-data',
  REPORTING_RECON = 'reporting:recon', // users that can use reporting recon resources down to the api level
  RETIREMENTS_CLEAR_ADMIN_REVIEW = 'retirements:clear-admin-review',
  RETIREMENTS_CLEAR_PM_REVIEW = 'retirements:clear-pm-review',
  RETIREMENTS_CLEAR_PROCESSING = 'retirements:clear-processing',
  RETIREMENTS_CREATE = 'retirements:create',
  RETIREMENTS_READ = 'retirements:read',
  RETIREMENTS_UPDATE = 'retirements:update',
  RETIREMENTS_UPDATE_AMOUNTS = 'retirements:update-amounts', // ops and traders
  RESERVES_READ = 'reserves:read',
  RESERVES_WRITE = 'reserves:write',
  TRADES_AUTO_CANCEL = 'trades:auto-cancel',
  TRADES_CANCEL = 'trades:cancel',
  TRADES_CONFIRM_BINDING = 'trades:confirm-binding',
  TRADES_CONFIRM_DELIVERY = 'trades:confirm-delivery',
  TRADES_CONFIRM_EXECUTED = 'trades:confirm-executed',
  TRADES_CONFIRM_FIRM = 'trades:confirm-firm',
  TRADES_CONFIRM_INDICATIVE = 'trades:confirm-indicative',
  TRADES_CONFIRM_PAYMENT = 'trades:confirm-payment',
  TRADES_READ = 'trades:read',
  TRADES_UPDATE = 'trades:update',
  TRADES_UPDATE_AMOUNTS = 'trades:update-amounts',
  TRANSACTIONS_READ = 'transactions:read',
  TRANSFERS_EXECUTE = 'transfers:execute',
  TRANSFERS_READ = 'transfers:read',
  USER_ACTIONS_VIEW_ALL = 'user-actions:view-all',
  USER_ACTIONS_VIEW_CUSTOMER = 'user-actions:view-customer',
  USERS_CREATE = 'users:create',
  USERS_READ = 'users:read',
  USERS_UPDATE = 'users:update',
  VINTAGES_MANAGE_RISK_BUFFER = 'vintages:manage-risk-buffer',
  VINTAGES_READ = 'vintages:read',
  VINTAGES_WRITE = 'vintages:write',
}

export type Permission = `${PermissionEnum}`;
