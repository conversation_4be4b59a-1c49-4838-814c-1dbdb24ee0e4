{"name": "@rubiconcarbon/shared-types", "license": "ISC", "version": "3.1.0", "main": "dist/index.js", "typings": "dist/index.d.ts", "files": ["dist", "src"], "engines": {"node": ">=18", "npm": "use-yarn", "yarn": ">=1.22.0"}, "scripts": {"start": "tsdx watch", "build": "tsdx build", "test": "tsdx test", "lint": "eslint \"src/**/*.ts\" ", "prepare": "tsdx build", "size": "size-limit", "analyze": "size-limit --why"}, "peerDependencies": {}, "husky": {"hooks": {"pre-commit": "tsdx lint"}}, "prettier": {"printWidth": 120, "semi": true, "singleQuote": true, "trailingComma": "es5"}, "author": "", "module": "dist/shared-types.esm.js", "size-limit": [{"path": "dist/shared-types.cjs.production.min.js", "limit": "10 KB"}, {"path": "dist/shared-types.esm.js", "limit": "10 KB"}], "dependencies": {"decimal.js": "^10.4.3", "uuid": "^9.0.0"}, "devDependencies": {"@size-limit/preset-small-lib": "^8.1.2", "@types/uuid": "^9.0.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-import-resolver-typescript": "^3.5.3", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.0.0", "husky": "^8.0.3", "prettier": "^2.3.2", "size-limit": "^8.1.2", "tsdx": "^0.14.1", "tslib": "^2.4.1", "typescript": "^4.9.4"}, "resolutions": {"**/@typescript-eslint/eslint-plugin": "^5.0.0", "**/@typescript-eslint/parser": "^5.0.0", "**/typescript": "^4.9.4"}, "repository": {"type": "git", "url": "git+https://github.com/rubiconcarbon/rubicon-shared-types.git"}, "bugs": {"url": "https://github.com/rubiconcarbon/rubicon-shared-types/issues"}, "publishConfig": {"registry": "https://npm.pkg.github.com/"}, "homepage": "https://github.com/rubiconcarbon/rubicon-shared-types#readme"}