import {
  AdminDocumentQueryResponse,
  AdminDocumentResponse,
  AdminDocumentQuery,
  AdminDocumentUpdateRequest,
  AdminDocumentUploadUrlRequest,
  AdminDocumentUploadUrlResponse,
} from "@rubiconcarbon/shared-types";
import { useTriggerRequest, TriggerArgs } from "@rubiconcarbon/frontend-shared";
import { NO_OP, px } from "@rubiconcarbon/frontend-shared";
import { useMemo } from "react";

type UseDocumentsApiProps = {
  id?: string;
  query?: AdminDocumentQuery;
  uploadLinkPayload?: AdminDocumentUploadUrlRequest;

  onUploadLinkRetrievalSuccess?: (data: AdminDocumentUploadUrlResponse) => void;
  onUploadLinkRetrievalError?: (error: any) => void;
  onFetchSuccess?: (data: AdminDocumentResponse | AdminDocumentQueryResponse) => void;
  onFetchError?: (error: any) => void;
  onUpdateSuccess?: (data: AdminDocumentResponse) => void;
  onUpdateError?: (error: any) => void;
  onRemoveSuccess?: () => void;
  onRemoveError?: (error: any) => void;
};

type UseDocumentsApiReturn = {
  uploadLink: AdminDocumentUploadUrlResponse;
  documents: AdminDocumentResponse[];

  retrievingUploadLink: boolean;
  fetching: boolean;
  updating: boolean;
  removing: boolean;

  retrieveUploadLinkError: any;
  fetchError: any;
  updateError: any;
  removeError: any;

  retrieveUploadLink: (
    args?: TriggerArgs<AdminDocumentUploadUrlRequest, object, object>,
  ) => Promise<AdminDocumentUploadUrlResponse>;
  fetch: (
    args?: TriggerArgs<object, object, AdminDocumentQuery>,
  ) => Promise<AdminDocumentResponse | AdminDocumentQueryResponse>;
  update: (args?: TriggerArgs<AdminDocumentUpdateRequest, object, object>) => Promise<AdminDocumentResponse>;
  remove: (args?: TriggerArgs<any, any, any>) => Promise<any>;
};

const useDocumentsApi = ({
  id,
  query: queryParams,
  uploadLinkPayload,

  onUploadLinkRetrievalSuccess = NO_OP,
  onUploadLinkRetrievalError = NO_OP,
  onFetchSuccess = NO_OP,
  onFetchError = NO_OP,
  onUpdateSuccess = NO_OP,
  onUpdateError = NO_OP,
  onRemoveSuccess = NO_OP,
  onRemoveError = NO_OP,
}: UseDocumentsApiProps): UseDocumentsApiReturn => {
  const {
    data: uploadLink,
    isMutating: retrievingUploadLink,
    error: retrieveUploadLinkError,
    trigger: retrieveUploadLink,
  } = useTriggerRequest<AdminDocumentUploadUrlResponse, AdminDocumentUploadUrlRequest, object, object>({
    url: "admin/documents",
    method: "post",
    requestBody: uploadLinkPayload,
    swrOptions: {
      onSuccess: onUploadLinkRetrievalSuccess,
      onError: onUploadLinkRetrievalError,
    },
  });

  const {
    data: response,
    isMutating: fetching,
    error: fetchError,
    trigger: fetch,
  } = useTriggerRequest<AdminDocumentResponse | AdminDocumentQueryResponse, object, object, AdminDocumentQuery>({
    url: `admin/documents${id ? "/{id}" : ""}`,
    ...px({ pathParams: !!id && { id } }, [null, undefined, false]),
    queryParams,
    swrOptions: {
      onSuccess: onFetchSuccess,
      onError: onFetchError,
    },
  });

  const {
    isMutating: updating,
    error: updateError,
    trigger: update,
  } = useTriggerRequest<AdminDocumentResponse, AdminDocumentUpdateRequest, object, object>({
    url: "admin/documents",
    method: "put",
    swrOptions: {
      onSuccess: onUpdateSuccess,
      onError: onUpdateError,
    },
  });

  const {
    isMutating: removing,
    error: removeError,
    trigger: remove,
  } = useTriggerRequest({
    url: "admin/documents/{id}",
    pathParams: {
      id,
    },
    method: "delete",
    swrOptions: {
      onSuccess: onRemoveSuccess,
      onError: onRemoveError,
    },
  });

  const documents = useMemo(
    () => (response ? (response as AdminDocumentQueryResponse)?.data || [response] : []),
    [response],
  ) as AdminDocumentResponse[];

  return {
    uploadLink,
    documents: documents.filter(({ isUploaded }) => isUploaded).sort((a, b) => (a.createdAt > b.createdAt ? -1 : 1)),

    retrievingUploadLink,
    fetching,
    updating,
    removing,

    retrieveUploadLinkError,
    fetchError,
    updateError,
    removeError,

    retrieveUploadLink,
    fetch,
    update,
    remove,
  };
};

export default useDocumentsApi;
