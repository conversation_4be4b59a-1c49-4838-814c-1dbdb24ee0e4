import Decimal from "decimal.js";
import { uuid, AdminProjectResponse, BookType, AdminAssetResponse } from "@rubiconcarbon/shared-types";

export interface Project {
  id: uuid;
  name: string;
  registry?: string;
  type?: string;
  location?: string;
  suspended?: boolean;
  rctStandard?: boolean;
  isScienceTeamApproved?: boolean;
}

export function mapProject(inputData: AdminAssetResponse[] = []): Project[] {
  return inputData.map((row: AdminAssetResponse) => ({
    id: row.projectId,
    name: row?.name,
    registry: row.registryProjectId,
    type: row?.projectTypeType,
    location: row?.countryName,
    suspended: row?.isSuspended,
    rctStandard: row?.isRctStandard,
    isScienceTeamApproved: row?.isScienceTeamApproved,
  }));
}

export function getAmountAllocated(projectResponse: AdminProjectResponse): number {
  return Decimal.sum(
    ...(projectResponse.assetAllocationsByBookType ?? [])
      .filter((f) => [BookType.RCT_CUSTOM, BookType.RCT_PUBLIC].includes(f.bookType))
      .map((m) => m.totalAmountAllocated ?? 0),
    0,
  ).toNumber();
}

export function getAmountUnallocated(projectResponse: AdminProjectResponse): number {
  // todo @kofi maybe add a shared const for "unallocated" book types that can be used everywhere so we only have to update it in one place when we add new types. also is this supposed to have portfolio_default?
  return Decimal.sum(
    ...(projectResponse.assetAllocationsByBookType ?? [])
      .filter((f) =>
        [
          BookType.AGED_DEFAULT,
          BookType.COMPLIANCE_DEFAULT,
          BookType.OPPORTUNISTIC_DEFAULT,
          BookType.REHABILITATION_DEFAULT,
        ].includes(f.bookType),
      )
      .map((m) => m.totalAmountAllocated ?? 0),
    0,
  ).toNumber();
}
