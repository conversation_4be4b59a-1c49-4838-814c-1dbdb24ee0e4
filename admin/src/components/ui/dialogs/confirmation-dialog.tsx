import { PropsWithChildren, type JSX } from "react";
import { Button, Tooltip, SxProps, Box } from "@mui/material";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import { BaseDialogProps } from "../../../models/dialogs";
import COLORS from "@components/ui/theme/colors";
import DialogTheme, { dialogThemes } from "./dialog-themes";

export type DialogVariant = "contained" | "text" | "outlined";
export type ButtonColor = "inherit" | "error" | "primary" | "secondary" | "success" | "info" | "warning";

export interface ButtonDef {
  label: string;
  variant?: DialogVariant;
  onClickHandler: (event: object) => void;
  isDisabled?: boolean;
  tooltip?: string;
  style?: SxProps;
  color?: ButtonColor;
}

const buttonBaseStyle = {
  px: 2.5,
  color: "#FFFFFF",
  borderRadius: "4px",
};

const cancelButtonStyle = {
  fontWeight: 600,
  marginRight: 1,
  borderRadius: "4px",
  textTransform: "capitalize",
};

interface ConfirmationModalProps extends BaseDialogProps {
  title: string;
  dialogButtons?: ButtonDef[];
  dialogTitleStyle?: SxProps;
  dialogTheme?: DialogTheme;
  onCloseButtonLabel?: string;
}

export default function ConfirmationModal({
  children,
  title,
  dialogButtons = [],
  isOpen,
  onClose,
  dialogTitleStyle = {},
  dialogTheme,
  onCloseButtonLabel = "cancel",
}: PropsWithChildren<ConfirmationModalProps>): JSX.Element | null {
  const theme = dialogTheme ? dialogThemes[dialogTheme] : dialogThemes[DialogTheme.DEFAULT];
  return (
    <Dialog open={isOpen} onClose={onClose} maxWidth={"lg"}>
      <DialogTitle
        sx={{
          backgroundColor: COLORS.modalMargins,
          ...dialogTitleStyle,
          ...theme,
        }}
      >
        {title}
      </DialogTitle>
      <DialogContent
        sx={{
          borderTop: "1px solid #B2B6BB",
          borderBottom: "1px solid #B2B6BB",
        }}
      >
        {children}
      </DialogContent>
      <DialogActions
        sx={{
          height: 60,
          backgroundColor: COLORS.modalMargins,
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-start",
            marginRight: -2,
          }}
        >
          <Button variant="text" onClick={onClose} sx={cancelButtonStyle}>
            {onCloseButtonLabel}
          </Button>
          {dialogButtons?.map((buttonDef, idx) => (
            <Tooltip key={`${buttonDef.label}-${idx}`} title={buttonDef.tooltip} placement="top">
              <Button
                autoFocus
                disabled={buttonDef.isDisabled}
                variant={buttonDef.variant}
                onClick={buttonDef.onClickHandler}
                sx={{ textTransform: "capitalize", ...buttonBaseStyle, ...buttonDef.style }}
                color={buttonDef.color}
              >
                {buttonDef.label}
              </Button>
            </Tooltip>
          ))}
        </Box>
      </DialogActions>
    </Dialog>
  );
}
