import GenericDialog from "@components/ui/generic-dialog/generic-dialog";
import UploaderWidget from "@components/ui/uploader/components/UploaderWidget";
import { OnFileUploadSuccessMetaData } from "@components/ui/uploader/types/hook";
import { DocumentTypeUILabel } from "@constants/documents";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { FileUploadHandlers } from "@uitypes/headless-downloader";
import { CloseRounded } from "@mui/icons-material";
import { Typography, Stack, Box, Select, InputLabel, FormControl, SelectChangeEvent, MenuItem } from "@mui/material";
import {
  DocumentType,
  uuid,
  AdminDocumentUploadUrlRequest,
} from "@rubiconcarbon/shared-types";
import { useState, useMemo, useCallback, MouseEvent, useEffect, PropsWithChildren, type JSX } from "react";
import { useGetSetState, useToggle } from "react-use";
import { useLogger } from "@providers/logging";
import useDocumentsApi from "@hooks/use-documents-api";
import Attachments from "./attachments";
import { Maybe, Nullable, px } from "@rubiconcarbon/frontend-shared";

import dialogClasses from "./dialog.module.scss";

export interface FileType {
  label: string;
  value: DocumentType;
}

type DocumentsModalProps = {
  open: boolean;
  relatedUiKey: string;
  organizationId?: uuid;

  getTypeOptions?: FileType[];
  saveTypeOptions: FileType[];
  canUpload?: boolean;
  onUploadSuccess?: () => void;
  onUploadError?: () => void;
  onPositiveClick?: (callback: () => void) => void;
  onNegativeClick?: (callback: () => void) => void;
  onClose: (callback: () => void, amended?: boolean) => Promise<void>;
};

const DocumentsModal = ({
  open,
  relatedUiKey,
  organizationId,
  getTypeOptions = undefined,
  saveTypeOptions,
  canUpload = true,
  children,
  onUploadSuccess,
  onUploadError,
  onPositiveClick,
  onNegativeClick,
  onClose,
}: PropsWithChildren<DocumentsModalProps>): JSX.Element => {
  const { logger } = useLogger();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const [getUploadLinkPayload, setUploadLinkPayload] = useGetSetState<AdminDocumentUploadUrlRequest>();

  const [uploadHandlers, setUploadHandlers] = useState<FileUploadHandlers>({});
  const [uploading, setUploading] = useToggle(false);
  const [refreshAttachments, setRefreshAttachments] = useToggle(false);
  const [fileType, setFileType] = useState<DocumentType | "">("");
  const [amended, setAmended] = useToggle(false);

  useEffect(() => {
    setFileType("");
  }, []);

  const { retrieveUploadLink, update } = useDocumentsApi({
    uploadLinkPayload: getUploadLinkPayload(),
    onUploadLinkRetrievalError: (error: any) => {
      enqueueError("Unable to get upload link");
      logger.error(`Unable to get upload link: ${error?.message}`, {});
    },
    onUpdateSuccess: async () => {
      enqueueSuccess("Successfully uploaded file");
      setTimeout(() => onUploadSuccess?.());
    },
    onUpdateError: (error: any) => {
      enqueueError("Successfully uploaded file but was unable to update file details");
      logger.error(`Successfully uploaded file but was unable to update file details: ${error?.message}`, {});
      onUploadError?.();
    },
  });

  const getS3UploadApiLink = async (file: File): Promise<string> => {
    setUploadLinkPayload({
      relatedUiKey,
      filename: file.name,
      type: fileType === "" ? undefined : fileType,
      isPublic: true,
      ...px({ organizationId }, [undefined, null]),
    });

    const { uploadUrl } = await retrieveUploadLink();

    return uploadUrl;
  };

  const close = useCallback((): void => {
    setUploadHandlers({});
    setUploading(false);
  }, [setUploading]);

  const onFileUploadSuccess = async (file: File, metadata: OnFileUploadSuccessMetaData): Promise<void> => {
    setAmended(true);

    setTimeout(async () => {
      await update({
        requestBody: {
          id: uuid(metadata?.s3FileId),
          relatedUiKey,
          filename: file.name,
          type: fileType === "" ? undefined : fileType,
          isPublic: true,
          ...px({ organizationId }, [undefined, null]),
        },
      });
      setRefreshAttachments(!refreshAttachments);
      setFileType("");
      close();
    });
  };

  const onFileUploadError = useCallback((): void => {
    enqueueError("Unable to upload file");
  }, [enqueueError]);

  const hasFileToUpload = useMemo(() => Object.values(uploadHandlers).some((fn) => !!fn), [uploadHandlers]);

  const handleExposeUploadHandler = (
    inputId: string,
    handler: Nullable<(event: MouseEvent<HTMLButtonElement>) => Promise<void>>,
  ): void => {
    setUploadHandlers({ [inputId]: handler });
  };

  const handleFileUpload = async (): Promise<void> => {
    const [[inputId, handler]] = Object.entries(uploadHandlers) as [
      string,
      (event: MouseEvent<HTMLButtonElement>) => Promise<void>,
    ][];
    await handler({ preventDefault: () => {} } as MouseEvent<HTMLButtonElement>);
    setUploadHandlers({ [inputId]: null });
  };

  const handleFileTypeChange = (event: SelectChangeEvent<DocumentType>): void => {
    const value = event.target.value as DocumentType;
    setFileType(value);
  };

  const onRemoveSuccess = (): void => {
    setAmended(true);
    enqueueSuccess(`Successfully deleted document for ${relatedUiKey}`);
    setRefreshAttachments(!refreshAttachments);
  };

  return (
    <GenericDialog
      open={open}
      dismissIcon={<CloseRounded />}
      title={
        <Typography fontWeight={500} sx={{ fontSize: "20px" }}>
          Documents
        </Typography>
      }
      positiveAction={{
        buttonText: "Upload",
        disabled: !fileType || !hasFileToUpload || uploading,
      }}
      negativeAction={{
        buttonText: "Cancel",
      }}
      onClose={() => onClose(close, amended)}
      onPositiveClick={async () => {
        if (fileType) {
          await handleFileUpload();
        } else if (onPositiveClick) {
          onPositiveClick(close);
        }
      }}
      onNegativeClick={() => onNegativeClick?.(close)}
      classes={{
        root: dialogClasses.Dialog,
        title: dialogClasses.StatusTitle,
        content: dialogClasses.Content,
        actions: dialogClasses.StatusActions,
      }}
    >
      <Stack rowGap={2} padding={2}>
        <Maybe condition={canUpload}>
          {() => (
            <>
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                Please first select the document type that you want to upload
              </Typography>
              <Box>
                <FormControl sx={{ width: "100%" }} disabled={saveTypeOptions.length === 0}>
                  <InputLabel id="file-type-label">Select document type</InputLabel>
                  <Select
                    labelId="File type"
                    id="file-type"
                    sx={{ maxWidth: "340px" }}
                    label="Select document type"
                    value={fileType}
                    disabled={!canUpload}
                    onChange={(event: SelectChangeEvent<DocumentType>) => handleFileTypeChange(event)}
                  >
                    <MenuItem value="" sx={{ height: "35px" }}>
                      {""}
                    </MenuItem>
                    {saveTypeOptions.map(({ label, value }) => (
                      <MenuItem key={value} value={value}>
                        {label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
              <Stack rowGap={2}>
                <Maybe condition={!!fileType}>
                  <Typography variant="body2" fontWeight={100}>
                    Please upload{" "}
                    <Typography component="span" variant="body2">
                      {fileType ? DocumentTypeUILabel[fileType] : ""}
                    </Typography>
                    .
                  </Typography>
                </Maybe>
                <UploaderWidget
                  inputId="document-uploads"
                  cancelButtonText="back"
                  uploadLink={(file?: File) => (file ? getS3UploadApiLink(file) : Promise.reject("No file provided"))}
                  allowedExtensions={["image/jpeg", "image/png", "application/pdf"]}
                  canDragAndDrop={!!fileType}
                  canUpload={!!fileType}
                  externallyUpload
                  onExposeUploadHandler={handleExposeUploadHandler}
                  onFileUploadSuccess={(file?: File, metadata?: OnFileUploadSuccessMetaData) => {
                    if (file && metadata) {
                      return onFileUploadSuccess(file, metadata);
                    }
                  }}
                  onFileUploadError={onFileUploadError}
                  onUploadingStatusChange={(status: boolean) => setUploading(status)}
                  onFileRemoval={async () => {
                    setUploadHandlers({});
                    return true;
                  }}
                  clearOnFileUploadSuccess
                  maxFiles={5}
                />
              </Stack>
            </>
          )}
        </Maybe>
        <Attachments
          refresh={refreshAttachments}
          relatedUiKey={relatedUiKey}
          types={(getTypeOptions || saveTypeOptions).map(({ value }) => value)}
          onRemoveAttachSuccess={onRemoveSuccess}
        />
        {children}
      </Stack>
    </GenericDialog>
  );
};

export default DocumentsModal;
