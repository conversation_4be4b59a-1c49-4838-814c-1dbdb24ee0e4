import { BookType } from "@rubiconcarbon/shared-types";

export const NO_STATUS = "None";

export const BookTypeMapping = {
  [BookType.AGED_DEFAULT]: "Aged Inventory",
  [BookType.COMPLIANCE_DEFAULT]: "Compliance",
  [BookType.OPPORTUNISTIC_DEFAULT]: "Opportunistic",
  [BookType.RCT_CUSTOM]: "Custom Portfolio",
  [BookType.PORTFOLIO_DEFAULT]: "RCT Unallocated",
  [BookType.REHABILITATION_DEFAULT]: "Ineligible RCT",
  [BookType.RCT_PUBLIC]: "RCT portfolio",
  [BookType.PORTFOLIO_RESERVES]: "Reserves",
  [BookType.PORTFOLIO_CUSTOMER]: "Customer Holding",
};
