import { DEFAULT_BASKET_ID } from "@constants/constants";
import { AppStoreProvider, StoreContextType, createStoreContext } from "@rubiconcarbon/frontend-shared";
import { PropsWithChildren, useContext, type JSX } from "react";
import { AssetTypeToAlertGrouped } from "@providers/alert-provider/types/alert";

type LocalState = {
  navigation: {
    pinned: boolean;
    expanded: boolean;
    forceCollapse: boolean;
  };
};

type SessionState = object;

type EphemeralState = {
  transactions: {
    viewing?: "transactions" | "sales" | "trades";
    searchTerm: string;
    pageNumber: number;
    rowsPerPage: number;
  };
  customer: {
    viewing: "holdings" | "transactions" | "quotes" | "documents" | "users";
  };
  subscriptions: {
    viewing: "alerts" | "subscription-settings";
  };
  alerts: {
    count: number;
    groups: AssetTypeToAlertGrouped;
  };
  portfolio: {
    selected: string;
    settings: {
      viewing: "types" | "alerts";
    };
  };
};

const InitialLocalState: LocalState = {
  navigation: {
    pinned: false,
    expanded: false,
    forceCollapse: false,
  },
};

const InitialSessionState: SessionState = {};

const InitialEphemeralState: EphemeralState = {
  transactions: {
    searchTerm: "",
    pageNumber: 0,
    rowsPerPage: 100,
  },
  customer: {
    viewing: "holdings",
  },
  subscriptions: {
    viewing: "alerts",
  },
  alerts: {
    count: 0,
    groups: {
      portfolio: [],
      project: [],
      vintage: [],
    },
  },
  portfolio: {
    selected: DEFAULT_BASKET_ID,
    settings: {
      viewing: "types",
    },
  },
};

const StoreContext = createStoreContext<LocalState, SessionState, EphemeralState>(
  InitialLocalState,
  InitialSessionState,
  InitialEphemeralState,
);

const StoreProvider = ({ children }: PropsWithChildren): JSX.Element => {
  return (
    <AppStoreProvider
      persistentKey={`${window.location.hostname.replaceAll(".", "-")}-admin-app-state`}
      initialState={{
        local: InitialLocalState,
        session: InitialSessionState,
        ephemeral: InitialEphemeralState,
      }}
      StoreContext={StoreContext}
    >
      {children}
    </AppStoreProvider>
  );
};

export const useStoreProvider = (): StoreContextType<LocalState, SessionState, EphemeralState> =>
  useContext(StoreContext);

export default StoreProvider;
