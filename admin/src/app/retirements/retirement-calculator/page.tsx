import { AuthorizeServer } from "@app/authorize-server";
import { AdminBookQueryResponse, BookRelations, BookType, PermissionEnum } from "@rubiconcarbon/shared-types";
import RetirementCalculator from "./components";
import { isValidElement, type JSX } from "react";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";

/**
 * Retirement Calculator Page
 *
 * This is a server component that renders the Retirement Calculator page
 */
export default async function RetirementCalculatorPage(): Promise<JSX.Element> {
  const booksResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminBookQueryResponse>(
      `admin/books?${generateQueryParams({
        limit: SERVER_PAGINATION_LIMIT,
        isEnabled: true,
        types: [BookType.RCT_PUBLIC, BookType.RCT_CUSTOM],
        includeRelations: [BookRelations.ASSET_ALLOCATIONS],
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(booksResponse)) return booksResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.BOOKS_CALCULATE_RETIREMENT]}>
      <RetirementCalculator booksResponse={booksResponse as AdminBookQueryResponse} />
    </AuthorizeServer>
  );
}
