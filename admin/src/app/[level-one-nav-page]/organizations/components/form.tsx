import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  Box,
  Button,
  Container,
  Divider,
  FormControl,
  FormControlLabel,
  IconButton,
  MenuItem,
  Radio,
  RadioGroup,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { Controller, useFieldArray } from "react-hook-form";
import useNavigation from "@hooks/use-navigation";
import {
  calculator,
  capitalize,
  classcat,
  deepEqual,
  isArray,
  Maybe,
  Nullable,
  pickFromRecord,
  px,
  toBoolean,
  useTriggerRequest,
} from "@rubiconcarbon/frontend-shared";
import {
  AdminOrganizationCreateRequest,
  AdminOrganizationResponse,
  AdminOrganizationUpdateRequest,
  AdminUserResponse,
  CounterpartyFeeType,
  DocumentType,
  AdminDocumentUploadUrlRequest,
  TradeType,
  uuid,
} from "@rubiconcarbon/shared-types";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { useLogger } from "@providers/logging";
import useAutoCompleteOptions, { UseAutoCompleteOptionsReturnEntry } from "@hooks/use-auto-complete-options";
import COLORS from "@components/ui/theme/colors";
import InlineUploader from "@components/ui/uploader/components/InlineUploaderWidget";
import useDocumentsApi from "@hooks/use-documents-api";
import usePerformantEffect from "@hooks/use-performant-effect";
import { useGetSetState, useToggle } from "react-use";
import { Fragment, MouseEvent, SyntheticEvent, useCallback, useMemo, useState, type JSX } from "react";
import { FileUploadHandlers } from "@uitypes/headless-downloader";
import { OnFileUploadSuccessMetaData } from "@components/ui/uploader/types/hook";
import { toOrganizationModel } from "../utilities/to-model";
import usePerformantState from "@hooks/use-perfomant-state";
import { LoadingButton } from "@mui/lab";
import AttachmentList from "@components/attachment-list/attachment-list";
import { DocumentTypeUILabel } from "@constants/documents";
import { DefaultFee, Email, OrganizationModel } from "@models/organization";
import Toggle from "@components/toggle/toggle";
import MatIcon from "@components/ui/mat-icon/mat-icon";
import Grid2 from "@mui/material/Unstable_Grid2";
import { FeeTypeOptions } from "@constants/fee-type";
import { NumericFormat } from "react-number-format";
import { useEnhancedForm } from "@hooks/use-enhanced-form";
import { isEmpty } from "lodash";

import classes from "../styles/form.module.scss";

const OrganizationModelResolver = classValidatorResolver(OrganizationModel);

const OrganizationForm = ({
  organizationResponse: savedModel,
  managers,
}: {
  organizationResponse?: AdminOrganizationResponse;
  managers: AdminUserResponse[];
}): JSX.Element => {
  const { logger } = useLogger();
  const { popFromPath } = useNavigation();
  const { enqueueSuccess, enqueueError, enqueueWarning, closeSnackbar } = useSnackbarVariants();

  const [uploading, toggleUploading] = useToggle(false);
  const [hasDocUploadError, toggleHasDocUploadError] = useToggle(false);

  const [uploadHandlers, setFileUploadHandlers] = useState<FileUploadHandlers>({});
  const [organizationPayload, setOrganizationPayload] = useState<
    AdminOrganizationCreateRequest | AdminOrganizationUpdateRequest
  >();
  const [idFromCreation, setIdFromCreation] = useGetSetState<{ id: uuid }>({ id: undefined });
  const [getUploadLinkPayload, setUploadLinkPayload] = useGetSetState<AdminDocumentUploadUrlRequest>();
  const [localModel, setLocalModel] = usePerformantState<OrganizationModel>(null);

  const organizationId = useMemo(() => idFromCreation()?.id || savedModel?.id, [idFromCreation, savedModel?.id]);

  const inEdit = useMemo(() => !!localModel?.id || !!savedModel?.id, [localModel?.id, savedModel?.id]);
  const hasFileToUpload = useMemo(() => Object.keys(uploadHandlers)?.length > 0, [uploadHandlers]);

  const model = useMemo(
    () => (inEdit ? localModel || toOrganizationModel(savedModel) : new OrganizationModel()),
    [inEdit, localModel, savedModel],
  );

  const {
    control,
    formState: { errors },
    watch,
    reset,
    smartSetValue,
    handleSubmit,
  } = useEnhancedForm<OrganizationModel>({
    resolver: OrganizationModelResolver,
    defaultValues: model,
    mode: "onSubmit",
  });

  const { insert: insertFee, remove: deleteFee } = useFieldArray<OrganizationModel, "counterparty.defaultFees">({
    control,
    name: "counterparty.defaultFees",
  });

  const {
    fields: emails,
    append,
    remove,
  } = useFieldArray<OrganizationModel, "counterparty.tradeConfirmEmails">({
    control,
    name: "counterparty.tradeConfirmEmails",
  });

  const data = watch();
  const hasCounterparty = data?.counterparty?.hasCounterparty;
  const hasCustomerPortfolio = data?.customerPortfolio?.hasCustomerPortfolio;
  const watchedFees = data?.counterparty?.defaultFees;

  const hasEditableCounterparty = useMemo(
    () => inEdit && !!model?.counterparty?.hasCounterparty,
    [inEdit, model?.counterparty?.hasCounterparty],
  );
  const hasEditableCustomerPortfolio = useMemo(
    () => inEdit && !!model?.customerPortfolio?.hasCustomerPortfolio,
    [inEdit, model?.customerPortfolio?.hasCustomerPortfolio],
  );

  const {
    documents = [],
    fetching,
    fetch,
    update,
    retrieveUploadLink,
  } = useDocumentsApi({
    query: {
      organizationId: organizationId as uuid,
      types: [DocumentType.MASTER_AGREEMENT],
    },
    uploadLinkPayload: getUploadLinkPayload(),

    onFetchError: (error: any) => {
      enqueueError(`Unable to fetch documents for ${data?.name}.`);
      logger.error(`Unable to fetch documents for ${data?.name}: ${error?.message}`, {});
    },
    onUploadLinkRetrievalError: (error: any) => {
      enqueueError("Unable to get upload link");
      logger.error(`Unable to get upload link: ${error.message}`, {});
      toggleHasDocUploadError(true);
    },
    onUpdateSuccess: () => {
      enqueueSuccess(
        `Successfully ${!!organizationId ? "updated" : "created"} orgaization${!!organizationId ? " details" : "."}`,
      );
      popFromPath(!!organizationId ? 2 : 1);
    },
    onUpdateError: (error: any) => {
      enqueueWarning("Partial upload; unable to update document association.");
      logger.error(`Partial upload; unable to update document association: ${error.message}`, {});
    },
  });

  const pure = useMemo(
    () =>
      pickFromRecord(model, [
        "name",
        "externalReference",
        "memo",
        "customerPortfolio.salesforceIdentifier",
        "customerPortfolio.rubiconManager.name",
        "customerPortfolio.isOnboarded",
        "customerPortfolio.isEnabled",
        "counterparty.isOnboarded",
        "counterparty.tradeConfirmEmails",
        "counterparty.defaultFees",
      ]),
    [model],
  );

  const impure = useMemo(
    () =>
      pickFromRecord(data, [
        "name",
        "externalReference",
        "memo",
        "customerPortfolio.salesforceIdentifier",
        "customerPortfolio.rubiconManager.name",
        "customerPortfolio.isOnboarded",
        "customerPortfolio.isEnabled",
        "counterparty.isOnboarded",
        "counterparty.tradeConfirmEmails",
        "counterparty.defaultFees",
      ]),
    [data],
  );

  const isDirty = useMemo(
    () =>
      !deepEqual(pure, impure, {
        transform: {
          "customerPortfolio.isOnboarded": (value) => toBoolean(value),
          "customerPortfolio.isEnabled": (value) => toBoolean(value),
          "counterparty.isOnboarded": (value) => toBoolean(value),
        },
      }),
    [impure, pure],
  );

  const hasMultipleEmails = useMemo(() => emails.length > 1, [emails]);

  const submittable = useMemo(() => ({ model: isDirty, document: hasFileToUpload }), [hasFileToUpload, isDirty]);

  const { trigger: commitOrganization, isMutating: committingOrganization } = useTriggerRequest<
    AdminOrganizationResponse,
    AdminOrganizationCreateRequest | AdminOrganizationUpdateRequest
  >({
    url: `/admin/organizations${inEdit ? `/${organizationId}` : ""}`,
    method: inEdit ? "patch" : "post",
    requestBody: organizationPayload,
    swrOptions: {
      onSuccess: async (data): Promise<void> => {
        setLocalModel(toOrganizationModel(data));
        setIdFromCreation({ id: data?.id });

        if (hasFileToUpload) await submitDocument();
        else {
          enqueueSuccess(`Successfully ${inEdit ? "updated" : "created"} orgaization${inEdit ? " details" : "."}`);
          popFromPath(!!organizationId ? 2 : 1);
        }
      },
      onError: (error: { data: { message: string | string[] } }): void => {
        enqueueError(
          <Stack gap={1}>
            <Stack>
              <Typography>Unable to {inEdit ? "update" : "create"} organization details.</Typography>
              {isArray(error?.data?.message) ? (
                (error?.data?.message as string[])?.map((error) => <Typography key={error}>{error}</Typography>)
              ) : (
                <Typography key={error?.data?.message as string}>{error?.data?.message}</Typography>
              )}
            </Stack>
          </Stack>,
          { persist: true, className: classes.ErrorAlert },
        );
        logger.error(`Unable to ${inEdit ? "update" : "create"} organization details: ${error?.data?.message}`, {});
      },
    },
  });

  const managerOptions = useAutoCompleteOptions<AdminUserResponse, AdminUserResponse>({
    data: managers || [],
    keys: ["id", "name"],
    label: (entry: Partial<AdminUserResponse>) => entry?.name ?? "",
    value: (entry: Partial<AdminUserResponse>) => entry as AdminUserResponse,
    postTransform: (data: UseAutoCompleteOptionsReturnEntry[]) =>
      data?.sort((a, b) => a?.label?.localeCompare(b?.label)),
  });

  const disableForm = committingOrganization || uploading;

  usePerformantEffect(
    () => setExpanded((prevState) => ({ ...prevState, counterparty: hasCounterparty })),
    [hasCounterparty],
  );

  usePerformantEffect(
    () => setExpanded((prevState) => ({ ...prevState, customerPortfolio: hasCustomerPortfolio })),
    [hasCustomerPortfolio],
  );

  usePerformantEffect(() => {
    if (!!organizationId && !fetching) setTimeout(async () => await fetch());
  }, [organizationId]);

  usePerformantEffect(() => {
    if (!!organizationId && !!model) reset(model);
  }, [organizationId, model]);

  usePerformantEffect(() => {
    smartSetValue("hasChildEntry", hasCounterparty || hasCustomerPortfolio, { shouldValidate: true });
  }, [hasCounterparty, hasCustomerPortfolio]);

  const hasTradeType = useCallback(
    (type: TradeType): boolean => watchedFees?.some(({ type: tradeType }) => type === tradeType),
    [watchedFees],
  );

  const [expanded, setExpanded] = useState<{ counterparty?: boolean; customerPortfolio?: boolean }>({});

  const handleAccordionChange = useCallback(
    (event: SyntheticEvent, panel: "counterparty" | "customerPortfolio"): void => {
      event?.preventDefault();

      const isExpanded = expanded[panel];
      setExpanded((prevState) => ({ ...prevState, [panel]: !isExpanded }));
      const key = panel === "counterparty" ? "counterparty.hasCounterparty" : "customerPortfolio.hasCustomerPortfolio";
      smartSetValue(key, !isExpanded);
    },
    [expanded, smartSetValue],
  );

  const getS3UploadApiLink = async (file: File): Promise<string> => {
    setUploadLinkPayload({
      organizationId: organizationId as uuid,
      filename: file.name,
      type: DocumentType.MASTER_AGREEMENT,
      isPublic: true,
    });

    const { uploadUrl } = await retrieveUploadLink();

    return uploadUrl;
  };

  const handleDocumentsSubmission = async (): Promise<void> => {
    const documentUploadPromises = Object.entries(uploadHandlers).reduce<Promise<void>[]>(
      (accum, [inputId, handler]) => [
        ...accum,
        (handler?.({ preventDefault: () => {} } as MouseEvent<HTMLButtonElement>) || Promise.resolve())?.then(() =>
          handleExposeUploadHandler(inputId, null),
        ),
      ],
      [] as Promise<void>[],
    );

    await Promise.all(documentUploadPromises);
  };

  const handleExposeUploadHandler = (
    inputId: string,
    // eslint-disable-next-line no-unused-vars
    handler: Nullable<(event: MouseEvent<HTMLButtonElement>) => Promise<void>>,
  ): void => {
    if (handler) {
      setFileUploadHandlers((previous: any) => ({
        ...previous,
        [inputId]: handler,
      }));
    } else {
      setFileUploadHandlers((previous: any) =>
        Object.entries(previous).reduce((accum, [key, value]) => {
          if (key !== inputId)
            accum = {
              ...accum,
              [key]: value,
            };
          return accum;
        }, {}),
      );
    }
  };

  const handleFileUploadSuccess = useCallback(
    (file: File, metadata?: OnFileUploadSuccessMetaData): void => {
      const id = metadata?.s3FileId as uuid;
      const filename = file?.name;

      setTimeout(async () => {
        await update({
          requestBody: {
            id,
            filename,
            organizationId: !organizationId ? idFromCreation()?.id : model?.id,
            type: DocumentType.MASTER_AGREEMENT,
            isPublic: true,
          },
        });
      });
    },
    [idFromCreation, model?.id, organizationId, update],
  );

  const handleFileUploadError = (): void => {
    enqueueError("Unable to upload document");
    toggleHasDocUploadError(true);
  };

  const handleDeletionSuccess = async (): Promise<void> => {
    enqueueSuccess(`Successfully deleted document for ${model?.name}`);
    await fetch();
  };

  const handleDeletionError = async (error: any): Promise<void> => {
    enqueueError(`Failed to delete document for ${model?.name}`);
    logger.error(`Failed to delete document for ${model?.name}: ${error?.message}`, {});
  };

  const addFee = useCallback(
    (type: TradeType): void => {
      const fee = new DefaultFee();
      fee.type = type;

      insertFee(watchedFees?.length, fee);
    },
    [watchedFees?.length, insertFee],
  );

  const removeFee = useCallback(
    (index: number): void => {
      deleteFee(index);
    },
    [deleteFee],
  );

  const addNewEmail = useCallback((): void => {
    append(new Email());
  }, [append]);

  const removeEmail = useCallback(
    (index: number): void => {
      remove(index);
    },
    [remove],
  );

  const FeeForm = useCallback(
    ({ index }: { index: 0 | 1 }): JSX.Element => (
      <Grid2 xs={12}>
        <Stack gap={2} height={120}>
          <Typography variant="body2">{capitalize(watchedFees?.at(index)?.type)}</Typography>
          <Stack direction="row" gap={1} alignItems="baseline">
            <Controller
              name={`counterparty.defaultFees.${index}.feeType`}
              control={control}
              render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                <TextField
                  select
                  label="Calculation Method"
                  value={value ?? ""}
                  InputProps={{ ref }}
                  {...otherProps}
                  error={!!errors?.counterparty?.defaultFees?.at(index)?.feeType}
                  helperText={errors?.counterparty?.defaultFees?.at(index)?.feeType?.message}
                  fullWidth
                  disabled={disableForm}
                  sx={{ minWidth: 320 }}
                >
                  {FeeTypeOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />
            <Controller
              name={`counterparty.defaultFees.${index}.fee`}
              control={control}
              render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                <NumericFormat
                  allowNegative={false}
                  thousandSeparator
                  decimalScale={2}
                  fixedDecimalScale
                  prefix={watchedFees?.at(index)?.feeType !== CounterpartyFeeType.PERCENTAGE ? "$ " : ""}
                  suffix={watchedFees?.at(index)?.feeType === CounterpartyFeeType.PERCENTAGE ? " %" : ""}
                  label="Fee"
                  value={value}
                  customInput={TextField}
                  InputProps={{ ref }}
                  error={!!errors?.counterparty?.defaultFees?.at(index)?.fee}
                  helperText={errors?.counterparty?.defaultFees?.at(index)?.fee?.message}
                  {...otherProps}
                  fullWidth
                />
              )}
            />
            <IconButton
              disabled={disableForm}
              sx={{
                position: "relative",
                top: 10,
                width: 35,
                height: 35,
              }}
              onClick={() => removeFee(index)}
            >
              <MatIcon variant="round" value="delete" color="action" />
            </IconButton>
          </Stack>
        </Stack>
      </Grid2>
    ),
    [control, disableForm, errors?.counterparty?.defaultFees, removeFee, watchedFees],
  );

  const submitDocument = async (): Promise<void> => {
    await handleDocumentsSubmission();
  };

  const onSubmit = (formData: OrganizationModel): void => {
    closeSnackbar();
    toggleHasDocUploadError(false);

    if (submittable.model) {
      const payload: AdminOrganizationCreateRequest | AdminOrganizationUpdateRequest = {
        ...(inEdit ? { id: model?.id } : {}),
        name: formData.name,
        memo: isEmpty(formData.memo) ? null : formData.memo,
        externalReference: isEmpty(formData.externalReference) ? null : formData.externalReference,
        isEnabled: toBoolean(formData.isEnabled),
        ...px({
          counterparty: formData?.counterparty?.hasCounterparty
            ? {
                defaultFees: formData?.counterparty?.defaultFees?.map(({ type, feeType, fee }) => ({
                  type,
                  feeType,
                  [`${feeType.replace(/[-_]+(.)/g, (_, c: string) => c.toUpperCase())}Fee`]: calculator(fee, {
                    parserBlacklist: ["$", ",", " %"],
                  })
                    .divide(feeType === CounterpartyFeeType.PERCENTAGE ? 100 : 1)
                    .calculate(),
                })),
                isEnabled: toBoolean(formData?.counterparty?.isEnabled),
                isOnboarded: toBoolean(formData?.counterparty?.isOnboarded),
                tradeConfirmEmails: formData?.counterparty?.tradeConfirmEmails?.map(({ value }) => value),
              }
            : undefined,
        }),
        ...px({
          customerPortfolio: formData?.customerPortfolio?.hasCustomerPortfolio
            ? {
                salesforceIdentifier: formData?.customerPortfolio?.salesforceIdentifier,
                isEnabled: toBoolean(formData?.customerPortfolio?.isEnabled),
                isOnboarded: toBoolean(formData?.customerPortfolio?.isOnboarded),
                rubiconManagerId: formData?.customerPortfolio?.rubiconManager?.id as uuid,
              }
            : undefined,
        }),
      };

      setOrganizationPayload(payload);
      setTimeout(async () => await commitOrganization());
    } else if (submittable.document) setTimeout(async () => await submitDocument());
  };

  return (
    <Container sx={{ padding: "20px 0px" }}>
      <Stack
        component="form"
        justifyContent="center"
        gap={3}
        maxWidth={1200}
        minWidth={900}
        onSubmit={handleSubmit(onSubmit)}
      >
        <Box className={classes.FieldContainer}>
          <Controller
            name="name"
            control={control}
            render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
              <TextField
                label="Organization Name"
                value={value ?? ""}
                InputProps={{ ref }}
                {...otherProps}
                error={!!errors?.name}
                helperText={errors?.name?.message}
                fullWidth
                disabled={disableForm}
              />
            )}
          />
        </Box>

        <Stack
          className={classcat([
            classes.NonInputFieldSection,
            classes.NoPadding,
            classes.NoBackground,
            { [classes.Error]: !!errors?.hasChildEntry },
          ])}
          gap={3}
        >
          <Controller
            name="counterparty.hasCounterparty"
            control={control}
            render={({ field: { value, onChange, ...otherProps } }): JSX.Element => (
              <Accordion
                className={classes.Accordion}
                elevation={0}
                disableGutters
                disabled={disableForm || hasEditableCounterparty}
                expanded={!!expanded.counterparty}
                classes={{
                  disabled: classes.AccordionDisabled,
                }}
                onChange={(event: SyntheticEvent) => handleAccordionChange(event, "counterparty")}
              >
                <AccordionSummary
                  aria-controls="CounterpartyPanel"
                  id="CounterpartyHeader"
                  sx={{ opacity: "1 !important" }}
                >
                  <FormControl className={classes.FormControl} fullWidth disabled={disableForm}>
                    <FormControlLabel
                      className={classes.FormControlLabel}
                      label={
                        <Typography color={COLORS.rubiconGreen} fontWeight="bold">
                          Counterparty
                        </Typography>
                      }
                      labelPlacement="start"
                      control={
                        <Maybe condition={!hasEditableCounterparty}>
                          <Toggle checked={value} onChange={onChange} {...otherProps} />
                        </Maybe>
                      }
                    />
                  </FormControl>
                </AccordionSummary>
                <AccordionDetails className={classes.AccordionDetails}>
                  <Stack gap={3}>
                    {/* Counterparty Onboarding */}
                    <Stack>
                      <Stack
                        className={classcat([
                          classes.NonInputFieldSection,
                          { [classes.Error]: !!errors?.counterparty?.isOnboarded },
                        ])}
                        direction="row"
                        sx={{ width: "100%" }}
                      >
                        <Typography
                          className={classes.GroupLabel}
                          color={COLORS.rubiconGreen}
                          fontSize={14}
                          fontWeight="bold"
                        >
                          Onboarding Status:
                        </Typography>
                        <Controller
                          name="counterparty.isOnboarded"
                          control={control}
                          defaultValue={data?.counterparty?.isOnboarded}
                          render={({ field: { ref, value, ...otherProps }, formState: { errors } }): JSX.Element => (
                            <FormControl error={!!errors?.counterparty?.isOnboarded}>
                              <RadioGroup
                                className={classes.RadioGroup}
                                ref={ref}
                                value={value ?? null}
                                {...otherProps}
                                row
                              >
                                <FormControlLabel
                                  label={<Typography variant="body1">Onboarded</Typography>}
                                  value={true}
                                  control={<Radio size="small" />}
                                  disabled={disableForm}
                                />
                                <FormControlLabel
                                  label={<Typography variant="body1">Not Onboarded</Typography>}
                                  value={false}
                                  control={<Radio size="small" />}
                                  disabled={disableForm}
                                />
                              </RadioGroup>
                            </FormControl>
                          )}
                        />
                      </Stack>
                      <Typography className={classes.NonInputFieldHelperText}>
                        {errors?.counterparty?.isOnboarded?.message}
                      </Typography>
                    </Stack>

                    <Divider />

                    {/* Fees */}
                    <Stack sx={{ width: "100%" }} gap={2}>
                      <Typography color={COLORS.rubiconGreen} fontSize={15} fontWeight="bold">
                        Fees:
                      </Typography>
                      <Grid2 container alignItems="center" gap={1} justifyContent="space-between">
                        <Grid2 xs={12} md={5.8}>
                          <Maybe condition={!hasTradeType(TradeType.BUY)}>
                            <Button
                              startIcon={<MatIcon variant="round" value="add" size={34} />}
                              sx={{ height: 50 }}
                              onClick={() => addFee(TradeType.BUY)}
                              disabled={disableForm}
                            >
                              <Typography variant="body2" textTransform="none">
                                Add Buy Fee
                              </Typography>
                            </Button>
                          </Maybe>
                          <Maybe condition={hasTradeType(TradeType.BUY)}>
                            <FeeForm index={watchedFees?.findIndex(({ type }) => type === TradeType.BUY) as 0 | 1} />
                          </Maybe>
                        </Grid2>

                        <Grid2 xs={12} md={5.8}>
                          <Maybe condition={!hasTradeType(TradeType.SELL)}>
                            <Button
                              startIcon={<MatIcon variant="round" value="add" size={34} />}
                              sx={{ height: 50 }}
                              onClick={() => addFee(TradeType.SELL)}
                              disabled={disableForm}
                            >
                              <Typography variant="body2" textTransform="none">
                                Add Sell Fee
                              </Typography>
                            </Button>
                          </Maybe>
                          <Maybe condition={hasTradeType(TradeType.SELL)}>
                            <FeeForm index={watchedFees?.findIndex(({ type }) => type === TradeType.SELL) as 0 | 1} />
                          </Maybe>
                        </Grid2>
                      </Grid2>
                    </Stack>

                    <Divider />

                    {/* Emails */}
                    <Stack sx={{ width: "100%" }} gap={2}>
                      <Typography color={COLORS.rubiconGreen} fontSize={15} fontWeight="bold">
                        Emails:
                      </Typography>
                      <Grid2 container alignItems="center" gap={1} rowGap={2}>
                        <Maybe condition={emails.length === 0}>
                          <Grid2 xs={12} md={6}>
                            <Button
                              startIcon={<MatIcon variant="round" value="add" size={34} />}
                              sx={{ height: 50 }}
                              onClick={addNewEmail}
                              disabled={disableForm}
                            >
                              <Typography variant="body2" textTransform="none">
                                Add Trade Confirmation Email
                              </Typography>
                            </Button>
                          </Grid2>
                        </Maybe>
                        <Maybe condition={emails.length > 0}>
                          {emails.map((email, index) => {
                            const isFirst = index === 0;
                            const isLast = index === emails?.length - 1;
                            const tackOnRemove = (hasMultipleEmails && isLast) || (!hasMultipleEmails && isFirst);
                            const hasError = !!errors?.counterparty?.tradeConfirmEmails?.at(index)?.value;

                            return (
                              <Fragment key={email.id}>
                                <Grid2 xs={12} md={6}>
                                  <Controller
                                    name={`counterparty.tradeConfirmEmails.${index}.value`}
                                    control={control}
                                    render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                                      <TextField
                                        label={`${!isFirst ? "Additional " : ""}Trade Confirmation Email`}
                                        value={value ?? ""}
                                        InputProps={{ ref }}
                                        {...otherProps}
                                        error={!!errors?.counterparty?.tradeConfirmEmails?.at(index)?.value}
                                        helperText={errors?.counterparty?.tradeConfirmEmails?.at(index)?.value?.message}
                                        fullWidth
                                        disabled={disableForm}
                                      />
                                    )}
                                  />
                                </Grid2>
                                <Grid2
                                  xs={tackOnRemove ? 5 : 12}
                                  md={tackOnRemove ? 2 : 5}
                                  sx={{ alignSelf: hasError ? "baseline" : "center" }}
                                >
                                  <Button
                                    startIcon={
                                      <MatIcon
                                        variant="round"
                                        value={!isLast ? "remove" : "add"}
                                        size={34}
                                        sx={{
                                          color: "white",
                                          backgroundColor: !isLast ? COLORS.red : COLORS.rubiconGreen,
                                          borderRadius: "50%",
                                        }}
                                      />
                                    }
                                    disabled={disableForm}
                                    sx={{
                                      height: 50,
                                    }}
                                    onClick={() => (!isLast ? removeEmail(index) : addNewEmail())}
                                  >
                                    <Typography variant="body2" textTransform="none">
                                      {!isLast ? "Remove email" : "Add more emails"}
                                    </Typography>
                                  </Button>
                                </Grid2>
                                <Maybe condition={tackOnRemove}>
                                  <Grid2
                                    xs={tackOnRemove ? 5 : 12}
                                    md={tackOnRemove ? 2 : 5}
                                    sx={{ alignSelf: hasError ? "baseline" : "center" }}
                                  >
                                    <Button
                                      startIcon={
                                        <MatIcon
                                          variant="round"
                                          value="remove"
                                          size={34}
                                          sx={{
                                            color: "white",
                                            backgroundColor: COLORS.red,
                                            borderRadius: "50%",
                                          }}
                                        />
                                      }
                                      disabled={disableForm}
                                      sx={{
                                        height: 50,
                                      }}
                                      onClick={() => removeEmail(index)}
                                    >
                                      <Typography variant="body2" textTransform="none">
                                        Remove email
                                      </Typography>
                                    </Button>
                                  </Grid2>
                                </Maybe>
                              </Fragment>
                            );
                          })}
                        </Maybe>
                      </Grid2>
                    </Stack>
                  </Stack>
                </AccordionDetails>
              </Accordion>
            )}
          />

          <Controller
            name="customerPortfolio.hasCustomerPortfolio"
            control={control}
            render={({ field: { value, onChange, ...otherProps } }): JSX.Element => (
              <Accordion
                className={classes.Accordion}
                elevation={0}
                disableGutters
                disabled={disableForm || hasEditableCustomerPortfolio}
                expanded={!!expanded.customerPortfolio}
                classes={{
                  disabled: classes.AccordionDisabled,
                }}
                onChange={(event: SyntheticEvent) => handleAccordionChange(event, "customerPortfolio")}
              >
                <AccordionSummary
                  aria-controls="CustomerPorfolioPanel"
                  id="CustomerPorfolioHeader"
                  sx={{ opacity: "1 !important" }}
                >
                  <FormControl className={classes.FormControl} fullWidth disabled={disableForm}>
                    <FormControlLabel
                      className={classes.FormControlLabel}
                      label={
                        <Typography color={COLORS.rubiconGreen} fontWeight="bold">
                          Customer
                        </Typography>
                      }
                      labelPlacement="start"
                      control={
                        <Maybe condition={!hasEditableCustomerPortfolio}>
                          <Toggle checked={value} onChange={onChange} {...otherProps} />
                        </Maybe>
                      }
                    />
                  </FormControl>
                </AccordionSummary>
                <AccordionDetails className={classes.AccordionDetails}>
                  <Stack gap={3}>
                    {/* Customer Portfolio Enabling */}
                    <Maybe condition={hasEditableCustomerPortfolio}>
                      <Stack>
                        <Stack
                          className={classcat([
                            classes.NonInputFieldSection,
                            { [classes.Error]: !!errors?.customerPortfolio?.isEnabled },
                          ])}
                          direction="row"
                          sx={{ width: "100%" }}
                        >
                          <Typography
                            className={classes.GroupLabel}
                            color={COLORS.rubiconGreen}
                            fontSize={14}
                            fontWeight="bold"
                          >
                            Customer Portal Access:
                          </Typography>
                          <Controller
                            name="customerPortfolio.isEnabled"
                            control={control}
                            defaultValue={data?.customerPortfolio?.isEnabled}
                            render={({ field: { ref, value, ...otherProps }, formState: { errors } }): JSX.Element => (
                              <FormControl error={!!errors?.customerPortfolio?.isEnabled}>
                                <RadioGroup
                                  className={classes.RadioGroup}
                                  ref={ref}
                                  value={value ?? null}
                                  {...otherProps}
                                  row
                                >
                                  <FormControlLabel
                                    label={<Typography variant="body1">Enabled</Typography>}
                                    value={true}
                                    control={<Radio size="small" />}
                                    disabled={disableForm}
                                  />
                                  <FormControlLabel
                                    label={<Typography variant="body1">Disabled</Typography>}
                                    value={false}
                                    control={<Radio size="small" />}
                                    disabled={disableForm}
                                  />
                                </RadioGroup>
                              </FormControl>
                            )}
                          />
                        </Stack>
                        <Typography className={classes.NonInputFieldHelperText}>
                          {errors?.customerPortfolio?.isEnabled?.message}
                        </Typography>
                      </Stack>

                      <Divider />
                    </Maybe>

                    {/* Customer Portfolio Onboarding */}
                    <Stack>
                      <Stack
                        className={classcat([
                          classes.NonInputFieldSection,
                          { [classes.Error]: !!errors?.customerPortfolio?.isOnboarded },
                        ])}
                        direction="row"
                        sx={{ width: "100%" }}
                      >
                        <Typography
                          className={classes.GroupLabel}
                          color={COLORS.rubiconGreen}
                          fontSize={14}
                          fontWeight="bold"
                        >
                          Onboarding Status:
                        </Typography>
                        <Controller
                          name="customerPortfolio.isOnboarded"
                          control={control}
                          defaultValue={data?.customerPortfolio?.isOnboarded}
                          render={({ field: { ref, value, ...otherProps }, formState: { errors } }): JSX.Element => (
                            <FormControl error={!!errors?.customerPortfolio?.isOnboarded}>
                              <RadioGroup
                                className={classes.RadioGroup}
                                ref={ref}
                                value={value ?? null}
                                {...otherProps}
                                row
                              >
                                <FormControlLabel
                                  label={<Typography variant="body1">Onboarded</Typography>}
                                  value={true}
                                  control={<Radio size="small" />}
                                  disabled={disableForm}
                                />
                                <FormControlLabel
                                  label={<Typography variant="body1">Not Onboarded</Typography>}
                                  value={false}
                                  control={<Radio size="small" />}
                                  disabled={disableForm}
                                />
                              </RadioGroup>
                            </FormControl>
                          )}
                        />
                      </Stack>
                      <Typography className={classes.NonInputFieldHelperText}>
                        {errors?.customerPortfolio?.isOnboarded?.message}
                      </Typography>
                    </Stack>

                    <Divider />

                    {/* Customer Portfolio Manager & Sales Force Identifier */}
                    <Stack direction="row" gap={2} justifyContent="center">
                      <Controller
                        name="customerPortfolio.rubiconManager"
                        control={control}
                        render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => {
                          const selectedOption = managerOptions.find((entry) => entry?.value?.id === value?.id) ?? null;
                          return (
                            <Autocomplete
                              options={managerOptions}
                              value={selectedOption}
                              onChange={(_, selection) => onChange(selection?.value)}
                              id="rubiconManager"
                              getOptionKey={(option: UseAutoCompleteOptionsReturnEntry<AdminUserResponse>) =>
                                option?.value?.id
                              }
                              getOptionLabel={(option: UseAutoCompleteOptionsReturnEntry<AdminUserResponse>) =>
                                option?.label
                              }
                              renderInput={({ InputProps, ...params }) => (
                                <TextField
                                  {...params}
                                  InputProps={{
                                    ref,
                                    ...InputProps,
                                  }}
                                  label="Account Manager"
                                  {...otherProps}
                                  error={!!errors?.customerPortfolio?.rubiconManager}
                                  helperText={errors?.customerPortfolio?.rubiconManager?.message}
                                  fullWidth
                                  disabled={disableForm}
                                />
                              )}
                              fullWidth
                            />
                          );
                        }}
                      />

                      <Controller
                        name="customerPortfolio.salesforceIdentifier"
                        control={control}
                        render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                          <TextField
                            label="Salesforce Account ID"
                            value={value ?? ""}
                            InputProps={{ ref }}
                            {...otherProps}
                            error={!!errors?.customerPortfolio?.salesforceIdentifier}
                            helperText={errors?.customerPortfolio?.salesforceIdentifier?.message}
                            fullWidth
                            disabled={disableForm}
                          />
                        )}
                      />
                    </Stack>
                  </Stack>
                </AccordionDetails>
              </Accordion>
            )}
          />

          <Maybe condition={!!errors?.hasChildEntry}>
            <Typography className={classes.NonInputFieldHelperText} paddingBottom={2}>
              {errors?.hasChildEntry?.message}
            </Typography>
          </Maybe>
        </Stack>

        <Stack gap={2} className={classes.FieldContainer}>
          <Typography className={classes.GroupLabel} color={COLORS.rubiconGreen} fontSize={14} fontWeight="bold">
            Additional Information (optional):
          </Typography>
          <Controller
            name="externalReference"
            control={control}
            render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
              <TextField
                label="External Reference"
                value={value ?? ""}
                InputProps={{ ref }}
                {...otherProps}
                error={!!errors?.externalReference}
                helperText={errors?.externalReference?.message}
                fullWidth
                disabled={disableForm}
              />
            )}
          />
          <Controller
            name="memo"
            control={control}
            render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
              <TextField
                label="Memo"
                value={value ?? ""}
                multiline
                rows={4}
                maxRows={4}
                InputProps={{ ref }}
                {...otherProps}
                error={!!errors?.memo}
                helperText={errors?.memo?.message}
                fullWidth
                disabled={disableForm}
              />
            )}
          />
        </Stack>

        <Stack
          className={classcat([classes.NonInputFieldSection, { [classes.Error]: hasDocUploadError }])}
          sx={{ width: "100%" }}
          gap={2}
        >
          <Typography color={COLORS.rubiconGreen} fontSize={15} fontWeight="bold">
            Signed Agreement{documents?.length > 1 ? "s" : ""}:
          </Typography>

          <Stack gap={0.5}>
            <Maybe condition={documents.length > 0}>
              <AttachmentList
                attachments={documents}
                deleteConfirmation={{
                  title: "Confirm document deletion",
                  content: ({ document }) => (
                    <span>
                      Are you sure you want to delete <strong>{DocumentTypeUILabel[document.type]}</strong> for{" "}
                      <strong>{model.name}</strong>?
                    </span>
                  ),
                }}
                onRemoveSuccess={handleDeletionSuccess}
                onRemoveError={handleDeletionError}
              />
              <Divider sx={{ marginBottom: 1.5 }} />
            </Maybe>
            <InlineUploader
              inputId={`choose ${!!documents?.length ? "additional " : ""}signed agreement`}
              uploadLink={getS3UploadApiLink}
              uploadText={`choose ${!!documents?.length ? "additional " : ""}signed agreement`}
              clearOnFileUploadSuccess
              allowedExtensions={["application/pdf"]}
              externallyUpload
              onUploadingStatusChange={(status) => toggleUploading(status)}
              onExposeUploadHandler={handleExposeUploadHandler}
              onFileUploadSuccess={handleFileUploadSuccess}
              onFileUploadError={handleFileUploadError}
              disabled={disableForm}
            />
          </Stack>

          <Typography variant="caption" fontWeight="100">
            * Accepted file type: PDF | Max file size: 15MB
          </Typography>
        </Stack>

        <Stack direction="row" justifyContent="space-between">
          <Button
            className={classes.ActionButton}
            color="error"
            disabled={committingOrganization || uploading}
            onClick={() => popFromPath(!!organizationId ? 2 : 1)}
          >
            Cancel
          </Button>
          <LoadingButton
            className={classes.ActionButton}
            type="submit"
            variant="contained"
            loading={committingOrganization || uploading}
            disabled={uploading || (inEdit && !isDirty && !hasFileToUpload)}
          >
            {!!organizationId ? "Update" : "Create"}
          </LoadingButton>
        </Stack>
      </Stack>
    </Container>
  );
};

export default OrganizationForm;
