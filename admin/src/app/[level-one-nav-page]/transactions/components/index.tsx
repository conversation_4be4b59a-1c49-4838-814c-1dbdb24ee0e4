"use client";
import {
  AdminTransactionQueryResponse,
  AdminPurchaseQueryResponse,
  AdminTradeQueryResponse,
} from "@rubiconcarbon/shared-types";
import TransactionsComponent from "./transactions";
import { TransactionKind } from "../types/transaction-kind";

import type { JSX } from "react";

export default function Transactions({
  transactionsResponse,
  purchasesResponse,
  tradesResponse,
  type,
}: {
  transactionsResponse: AdminTransactionQueryResponse;
  purchasesResponse: AdminPurchaseQueryResponse;
  tradesResponse: AdminTradeQueryResponse;
  type: TransactionKind;
}): JSX.Element {
  return (
    <TransactionsComponent
      transactionsResponse={transactionsResponse}
      purchasesResponse={purchasesResponse}
      tradesResponse={tradesResponse}
      type={type}
    />
  );
}
