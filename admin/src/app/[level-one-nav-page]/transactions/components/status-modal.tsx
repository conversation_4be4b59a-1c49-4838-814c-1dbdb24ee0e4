import GenericDialog from "@components/ui/generic-dialog/generic-dialog";
import UploaderWidget from "@components/ui/uploader/components/UploaderWidget";
import { OnFileUploadSuccessMetaData } from "@components/ui/uploader/types/hook";
import { DocumentTypeUILabel } from "@constants/documents";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { FileUploadHandlers } from "@uitypes/headless-downloader";
import { ArrowBackIosRounded, ArrowForwardIosRounded, CloseRounded, ErrorOutlineRounded } from "@mui/icons-material";
import {
  Typography,
  Stack,
  FormControl,
  FormControlLabel,
  Checkbox,
  FormLabel,
  RadioGroup,
  Radio,
  LinearProgress,
} from "@mui/material";
import { classcat, Delay, Match, Maybe, Nullable, Undefinable } from "@rubiconcarbon/frontend-shared";
import {
  DocumentType,
  uuid,
  AdminDocumentUploadUrlRequest,
  AdminTradeConfirmationRequest,
} from "@rubiconcarbon/shared-types";
import dayjs, { Dayjs } from "dayjs";
import {
  useState,
  useMemo,
  useCallback,
  ChangeEvent,
  MouseEvent,
  Dispatch,
  SetStateAction,
  useRef,
  type JSX,
} from "react";
import { useGetSetState, useToggle } from "react-use";
import { useLogger } from "@providers/logging";
import { DatePicker } from "@mui/x-date-pickers";
import useDocumentsApi from "@hooks/use-documents-api";
import {
  TransactionStatus,
  TransactionStatusToLabel,
  TransactionUpdatableStatus,
  TransactionUpdatableStatusToLabel,
  TransactionUpdateStatus,
  UpdateToCurrentTransactionStatus,
} from "@constants/transaction-status";
import usePerformantEffect from "@hooks/use-performant-effect";
import { AllTransactionType } from "@models/transaction";
import AutomatedEmailSelectionForm from "./trade/automated-email-selection-form";
import AutomatedEmailSelectionConfirmation from "./trade/automated-email-selection-confirmation";

import dialogClasses from "../styles/dialog.module.scss";

type DocumentUploadFlowProps = {
  isSale: boolean;
  setUploading: (nextValue?: any) => void;
  setUploadHandlers: Dispatch<SetStateAction<FileUploadHandlers>>;
  getS3UploadApiLink: (file: File) => Promise<string>;
  onFileUploadSuccess: (file: File, metadata?: OnFileUploadSuccessMetaData) => Promise<void>;
  onFileUploadError: () => void;
  handleExposeUploadHandler: (
    inputId: string,
    handler: Nullable<(event: MouseEvent<HTMLButtonElement>) => Promise<void>>,
  ) => void;
} & Pick<StatusModalProps, "documentType">;

type StatusModalProps = {
  open: boolean;
  id: uuid;
  uiKey: string;

  /**
   * we should always have at least one of the following added to the request for a document upload.
   * marketing agreements are the only exception for now.
   */
  organizationId?: uuid;

  updating: boolean;
  bookBreached: boolean;
  blockStatusUpdates: boolean;
  type: AllTransactionType;
  status: TransactionUpdateStatus;
  currentStatus: TransactionStatus;
  executeStep?: TransactionUpdatableStatus;
  deliveryDate?: Dayjs;
  executeStepOrder: TransactionUpdatableStatus[];
  documentType?: DocumentType.PROOF_OF_CONFIRMATION | DocumentType.CONTRACT | DocumentType.PROOF_OF_DELIVERY;
  onExecuteStepChange: (value: Nullable<TransactionUpdatableStatus>) => void;
  onDeliverDateChange: (value: Nullable<Dayjs>) => void;
  onUploadSuccess?: () => void;
  onUploadError?: () => void;
  onPositiveClick?: (
    callback: () => void,
    options: { tradeBindingPayload?: AdminTradeConfirmationRequest; noUploadRequired?: boolean },
  ) => void;
  onNegativeClick?: (callback: () => void) => void;
  onClose: (callback: () => void) => void;
};

const DocumentUploadFlow = ({
  documentType,
  isSale,
  setUploading,
  setUploadHandlers,
  getS3UploadApiLink,
  onFileUploadSuccess,
  onFileUploadError,
  handleExposeUploadHandler,
}: DocumentUploadFlowProps): JSX.Element => {
  return (
    <Stack rowGap={2}>
      <Typography variant="body2" fontWeight={100}>
        Please upload{" "}
        <Typography component="span" variant="body2">
          {DocumentTypeUILabel[documentType as DocumentType]}
        </Typography>
        .
      </Typography>
      <UploaderWidget
        inputId={`${isSale ? "customer-sale" : "trade"}-uploads`}
        cancelButtonText="back"
        uploadLink={getS3UploadApiLink}
        allowedExtensions={["image/jpeg", "image/png", "application/pdf"]}
        canDragAndDrop
        externallyUpload
        onExposeUploadHandler={handleExposeUploadHandler}
        onFileUploadSuccess={onFileUploadSuccess}
        onFileUploadError={onFileUploadError}
        onUploadingStatusChange={(status: boolean) => setUploading(status)}
        onFileRemoval={async () => {
          setUploadHandlers({});
          return true;
        }}
      />
    </Stack>
  );
};

type TradeBindingSteps = "select" | "email-selection" | "upload" | "confirm:email-selection";

type TradeBindingSelectType = Extract<TradeBindingSteps, "email-selection" | "upload">;

const StatusModal = ({
  open,
  id,
  uiKey,
  organizationId,
  updating,
  bookBreached,
  blockStatusUpdates,
  type,
  status,
  currentStatus,
  executeStepOrder,
  documentType,
  executeStep,
  deliveryDate,
  onExecuteStepChange,
  onDeliverDateChange,
  onUploadSuccess,
  onUploadError,
  onPositiveClick,
  onNegativeClick,
  onClose,
}: StatusModalProps): JSX.Element => {
  const { logger } = useLogger();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const [getUploadLinkPayload, setUploadLinkPayload] = useGetSetState<AdminDocumentUploadUrlRequest>();

  const [uploadHandlers, setUploadHandlers] = useState<FileUploadHandlers>({});

  /** trade bind related state */
  const [tradeBindingStep, setTradeBindingStep] = useState<Nullable<TradeBindingSteps>>("select");
  const [tradeBindingPendingStep, setTradeBindingPendingStep] = useState<Nullable<TradeBindingSteps>>("select");
  const [tradeBindingPayload, setTradeBindingPayload] = useState<AdminTradeConfirmationRequest>();
  const [hasEmailToTradeBind, setHasEmailToTradeBind] = useToggle(false);
  /** trade bind related state */

  const [uploading, setUploading] = useToggle(false);
  const [bookLimitApproved, setBookLimitApproved] = useToggle(false);
  const [paymentDone, setPaymentDone] = useToggle(false);
  const [saleDeliveryDone, setSaleDeliveryDone] = useToggle(false);

  const isSale = useMemo(() => type === AllTransactionType.PURCHASE, [type]);
  const noDocUpload = useMemo(() => isSale && DocumentType.PROOF_OF_DELIVERY === documentType, [documentType, isSale]);
  const docsToSearchIn = [
    documentType,
    documentType === DocumentType.CONTRACT ? DocumentType.PURCHASE_AGREEMENT : null,
  ].filter((doc) => !!doc);
  const fetchDocument = useMemo(
    () =>
      !!documentType &&
      [DocumentType.PROOF_OF_CONFIRMATION, DocumentType.CONTRACT, !isSale ? DocumentType.PROOF_OF_DELIVERY : null]
        .filter((type) => !!type)
        .includes(documentType),
    [documentType, isSale],
  );

  const { fetching, documents, fetch, retrieveUploadLink, update } = useDocumentsApi({
    query: {
      relatedUiKey: uiKey,
      organizationId,
    },
    uploadLinkPayload: getUploadLinkPayload(),
    onUploadLinkRetrievalError: (error: any) => {
      enqueueError("Unable to get upload link");
      logger.error(
        `Unable to get upload link for ${isSale ? "Customer Sale" : "Trade"} Status ${status}: ${error?.message}`,
        {},
      );
    },
    onUpdateSuccess: async () => {
      enqueueSuccess("Successfully uploaded file");
      setTimeout(() => onUploadSuccess?.());
    },
    onUpdateError: (error: any) => {
      enqueueError("Successful upload but was unable to update file details");
      logger.error(
        `Successfully upload file but was unable to update file details for ${isSale ? "Customer Sale" : "Trade"} Status ${status}: ${error?.message}`,
        {},
      );
      onUploadError?.();
    },
  });

  const hasDocument = useMemo(
    () => !fetching && documents?.some((doc) => docsToSearchIn.includes(doc?.type)),
    [docsToSearchIn, documents, fetching],
  );

  usePerformantEffect(() => {
    if (fetchDocument) setTimeout(async () => await fetch());
  }, [fetchDocument]);

  const getS3UploadApiLink = async (file: File): Promise<string> => {
    setUploadLinkPayload({
      relatedUiKey: uiKey,
      organizationId,
      filename: file.name,
      type: documentType,
      isPublic: true,
    });

    const { uploadUrl } = await retrieveUploadLink();

    return uploadUrl;
  };

  const close = useCallback((): void => {
    setUploadHandlers({});
    setUploading(false);
    setBookLimitApproved(false);
    setPaymentDone(false);
    setSaleDeliveryDone(false);
    onExecuteStepChange(null);
    onDeliverDateChange(null);
    setTradeBindingStep("select");
    setTradeBindingPendingStep(null);
  }, [
    onDeliverDateChange,
    onExecuteStepChange,
    setBookLimitApproved,
    setPaymentDone,
    setSaleDeliveryDone,
    setUploading,
    setTradeBindingStep,
    setTradeBindingPendingStep,
  ]);

  const onFileUploadSuccess = useCallback(
    async (file: File, metadata?: OnFileUploadSuccessMetaData): Promise<void> => {
      setTimeout(async () => {
        await update({
          requestBody: {
            id: uuid(metadata?.s3FileId),
            relatedUiKey: uiKey,
            organizationId,
            filename: file.name,
            type: documentType as DocumentType,
            isPublic: true,
          },
        });
        close();
      });
    },
    [close, documentType, organizationId, uiKey, update],
  );

  const onFileUploadError = useCallback((): void => {
    enqueueError("Unable to upload file");
  }, [enqueueError]);

  const hasFileToUpload = useMemo(() => Object.values(uploadHandlers)?.some((func) => !!func), [uploadHandlers]);

  const handleExposeUploadHandler = (
    inputId: string,
    handler: Nullable<(event: MouseEvent<HTMLButtonElement>) => Promise<void>>,
  ): void => {
    setUploadHandlers({ [inputId]: handler });
  };

  const handleFileUpload = async (): Promise<void> => {
    const [[inputId, handler]] = Object.entries(uploadHandlers) as [
      string,
      (event: MouseEvent<HTMLButtonElement>) => Promise<void>,
    ][];
    await handler({ preventDefault: () => {} } as MouseEvent<HTMLButtonElement>);
    setUploadHandlers({ [inputId]: null });
  };

  const inTradeBinding = useMemo(
    () =>
      [AllTransactionType.BUY, AllTransactionType.SELL].includes(type) &&
      status === TransactionUpdateStatus.BIND &&
      !noDocUpload &&
      !!documentType &&
      !blockStatusUpdates &&
      (!fetchDocument || (fetchDocument && !hasDocument)),
    [blockStatusUpdates, documentType, fetchDocument, hasDocument, noDocUpload, status, type],
  );
  const inTradeBindingUploadStep = useMemo(() => tradeBindingStep === "upload", [tradeBindingStep]);
  const inBindingFlowConfirmSend = useMemo(() => tradeBindingStep === "confirm:email-selection", [tradeBindingStep]);
  const canProceedToNextTradeBindingStep = useMemo(() => {
    switch (tradeBindingStep) {
      case "select":
        return inTradeBinding && !!tradeBindingPendingStep;
      case "email-selection":
        return inTradeBinding && hasEmailToTradeBind;
      case "upload":
        return inTradeBinding && hasFileToUpload;
      case "confirm:email-selection":
        return inTradeBinding && !!tradeBindingPayload;
      default:
        return false;
    }
  }, [
    tradeBindingPayload,
    tradeBindingPendingStep,
    tradeBindingStep,
    hasEmailToTradeBind,
    hasFileToUpload,
    inTradeBinding,
  ]);

  const handlePositiveClick = async (): Promise<void> => {
    if (inTradeBinding) {
      switch (tradeBindingStep) {
        case "select":
          setTradeBindingStep(tradeBindingPendingStep);
          break;
        case "email-selection":
          setTradeBindingStep("confirm:email-selection");
          break;
        case "upload":
          await handleFileUpload();
          break;
        case "confirm:email-selection":
          onPositiveClick?.(close, { noUploadRequired: true, tradeBindingPayload });
          break;
        default:
          break;
      }
    } else {
      if (!noDocUpload && !!documentType && !hasDocument && !blockStatusUpdates) {
        await handleFileUpload();
      } else {
        onPositiveClick?.(close, { noUploadRequired: noDocUpload || (!!documentType && hasDocument) });
      }
    }
  };

  const handleNegativeClick = (): void => {
    if (inTradeBinding) {
      switch (tradeBindingStep) {
        case "select":
          onNegativeClick?.(close);
          break;
        case "email-selection":
          setTradeBindingStep("select");
          break;
        case "upload":
          setTradeBindingStep("select");
          break;
        case "confirm:email-selection":
          setTradeBindingStep("email-selection");
          break;
        default:
          break;
      }
    } else onNegativeClick?.(close);
  };

  return (
    <GenericDialog
      open={open}
      dismissIcon={<CloseRounded />}
      title={
        <Typography variant="h6" fontWeight={500}>
          Updating {isSale ? "Customer Sale" : "Trade"} Status from{" "}
          <strong>{TransactionStatusToLabel[currentStatus]}</strong> to{" "}
          <strong>{TransactionStatusToLabel[UpdateToCurrentTransactionStatus[status]]}</strong>
        </Typography>
      }
      positiveAction={{
        buttonText: inTradeBinding
          ? inTradeBindingUploadStep
            ? "UPDATE"
            : inBindingFlowConfirmSend
              ? "SEND"
              : "NEXT"
          : "UPDATE",
        endIcon:
          inTradeBinding && !inTradeBindingUploadStep && !inBindingFlowConfirmSend ? (
            <ArrowForwardIosRounded />
          ) : undefined,
        loading: updating || (fetchDocument && fetching),
        disabled:
          blockStatusUpdates ||
          (status === TransactionUpdateStatus.FIRM && bookBreached && !bookLimitApproved) ||
          (status === TransactionUpdateStatus.EXECUTE && !executeStep) ||
          (status === TransactionUpdateStatus.PAY && !paymentDone) ||
          (status === TransactionUpdateStatus.DELIVER &&
            ((isSale && !saleDeliveryDone) || (!isSale && !deliveryDate))) ||
          (!noDocUpload && !!documentType && !hasDocument && !hasFileToUpload && !canProceedToNextTradeBindingStep) ||
          uploading ||
          updating,
      }}
      negativeAction={{
        buttonText: inTradeBinding && tradeBindingStep !== "select" ? "BACK" : "CANCEL",
        startIcon: inTradeBinding && tradeBindingStep !== "select" ? <ArrowBackIosRounded /> : undefined,
        disabled: updating,
      }}
      onClose={() => onClose(close)}
      onPositiveClick={handlePositiveClick}
      onNegativeClick={handleNegativeClick}
      classes={{
        root: classcat([dialogClasses.Dialog, { [dialogClasses.Binding]: inBindingFlowConfirmSend }]),
        title: dialogClasses.StatusTitle,
        content: dialogClasses.Content,
        actions: dialogClasses.StatusActions,
      }}
    >
      <Stack rowGap={2} padding={2}>
        <Maybe condition={bookBreached && status === TransactionUpdateStatus.FIRM}>
          <Stack className={dialogClasses.FirmApproval}>
            <Stack direction="row" gap={2} alignItems="center">
              <ErrorOutlineRounded fontSize="small" htmlColor="#1195D6" />
              <Typography>This {isSale ? "customer sale" : "trade"} is supressing the book limit.</Typography>
            </Stack>
            <FormControl className={dialogClasses.CheckboxFormControl}>
              <FormControlLabel
                label={
                  <Typography variant="body2">I have obtained {isSale ? "customer sale" : "trade"} approval</Typography>
                }
                control={
                  <Checkbox
                    size="small"
                    checked={bookLimitApproved}
                    onChange={(_: ChangeEvent<HTMLInputElement>, checked: boolean) => setBookLimitApproved(checked)}
                  />
                }
              />
            </FormControl>
          </Stack>
        </Maybe>

        <Maybe condition={[TransactionUpdateStatus.FIRM, TransactionUpdateStatus.CANCEL].includes(status)}>
          <Typography>
            This will update the status from <strong>{TransactionStatusToLabel[currentStatus]}</strong> to{" "}
            <strong>{TransactionStatusToLabel[UpdateToCurrentTransactionStatus[status]]}</strong>
          </Typography>
        </Maybe>

        <Maybe condition={status === TransactionUpdateStatus.PAY}>
          <Stack className={dialogClasses.Approval}>
            <Typography>
              Please confirm payment was{" "}
              {[AllTransactionType.SELL, AllTransactionType.PURCHASE].includes(type) ? "received" : "sent"}.
            </Typography>
            <FormControl className={dialogClasses.CheckboxFormControl}>
              <FormControlLabel
                label={
                  <Typography variant="body2">
                    Payment{" "}
                    {[AllTransactionType.SELL, AllTransactionType.PURCHASE].includes(type) ? "received" : "sent"}
                  </Typography>
                }
                control={
                  <Checkbox
                    size="small"
                    checked={paymentDone}
                    onChange={(_: ChangeEvent<HTMLInputElement>, checked: boolean) => setPaymentDone(checked)}
                  />
                }
              />
            </FormControl>
          </Stack>
        </Maybe>

        <Maybe condition={status === TransactionUpdateStatus.DELIVER}>
          <Maybe condition={isSale}>
            <Stack className={dialogClasses.Approval}>
              <Typography>Please confirm depositing the credit in the Rubicon customers&lsquo; account.</Typography>
              <FormControl className={dialogClasses.CheckboxFormControl}>
                <FormControlLabel
                  label={<Typography variant="body2">Credit deposited</Typography>}
                  control={
                    <Checkbox
                      size="small"
                      checked={saleDeliveryDone}
                      onChange={(_: ChangeEvent<HTMLInputElement>, checked: boolean) => {
                        setSaleDeliveryDone(checked);
                        onDeliverDateChange(checked ? dayjs() : null);
                      }}
                    />
                  }
                />
              </FormControl>
            </Stack>
          </Maybe>
          <Maybe condition={!isSale}>
            <Stack className={""} gap={2}>
              <Typography variant="body2" fontWeight={100}>
                Please confirm the asset transfer date.
              </Typography>
              <DatePicker
                format="MM/DD/YYYY"
                disableFuture
                onChange={(value: Nullable<Dayjs>) => onDeliverDateChange(value)}
                value={deliveryDate}
                label="Asset transfer date"
              />
            </Stack>
          </Maybe>
        </Maybe>

        <Maybe condition={!noDocUpload && !!documentType && !blockStatusUpdates}>
          <Delay ms={500} fallback={<LinearProgress variant="indeterminate" />}>
            <Maybe
              condition={!fetchDocument || (fetchDocument && !hasDocument)}
              fallback={
                <Typography>
                  <strong>{DocumentTypeUILabel[documentType as DocumentType]}</strong> has already been uploaded.
                </Typography>
              }
            >
              <Maybe condition={!inTradeBinding}>
                <DocumentUploadFlow
                  documentType={documentType}
                  isSale={isSale}
                  setUploading={setUploading}
                  setUploadHandlers={setUploadHandlers}
                  getS3UploadApiLink={getS3UploadApiLink}
                  onFileUploadSuccess={onFileUploadSuccess}
                  onFileUploadError={onFileUploadError}
                  handleExposeUploadHandler={handleExposeUploadHandler}
                />
              </Maybe>
              <Maybe condition={inTradeBinding}>
                <NoDocumentBindingContent
                  step={tradeBindingStep as TradeBindingSteps}
                  id={id}
                  organizationId={organizationId as uuid}
                  documentType={documentType}
                  isSale={isSale}
                  setUploading={setUploading}
                  setUploadHandlers={setUploadHandlers}
                  getS3UploadApiLink={getS3UploadApiLink}
                  onFileUploadSuccess={onFileUploadSuccess}
                  onFileUploadError={onFileUploadError}
                  handleExposeUploadHandler={handleExposeUploadHandler}
                  action={(args): void => {
                    switch (tradeBindingStep) {
                      case "select":
                        return setTradeBindingPendingStep(args);
                      case "email-selection":
                        return setHasEmailToTradeBind(!!args);
                      case "confirm:email-selection":
                        return setTradeBindingPayload(args);
                      default:
                        break;
                    }
                  }}
                />
              </Maybe>
            </Maybe>
          </Delay>
          <Maybe condition={status === TransactionUpdateStatus.EXECUTE}>
            <FormControl className={dialogClasses.ApprovalCheckboxFormControl}>
              <FormLabel id="execute-step" className={dialogClasses.FormLabelText}>
                <Typography variant="body2" fontWeight={100} color="#000000">
                  Select the next step for this transaction:
                </Typography>
              </FormLabel>
              <RadioGroup
                className={dialogClasses.RadioGroup}
                aria-labelledby="execute-step-radio-buttons-group-label"
                name="execute-step-buttons-group"
                value={executeStep}
                onChange={(_: ChangeEvent<HTMLInputElement>, value: string) =>
                  onExecuteStepChange(value as TransactionUpdatableStatus)
                }
              >
                <FormControlLabel
                  value={executeStepOrder?.[0]}
                  control={<Radio size="small" />}
                  label={
                    <Typography variant="body2" fontWeight={100} color="#000000">
                      {TransactionUpdatableStatusToLabel[executeStepOrder?.[0]]}
                    </Typography>
                  }
                />
                <FormControlLabel
                  value={executeStepOrder?.[1]}
                  control={<Radio size="small" />}
                  label={
                    <Typography variant="body2" fontWeight={100} color="#000000">
                      {TransactionUpdatableStatusToLabel[executeStepOrder?.[1]]}
                    </Typography>
                  }
                />
              </RadioGroup>
            </FormControl>
          </Maybe>
        </Maybe>

        <Maybe condition={status === TransactionUpdateStatus.BIND && hasDocument}>
          <Typography>
            This will update the status from <strong>{TransactionStatusToLabel[currentStatus]}</strong> to{" "}
            <strong>{TransactionStatusToLabel[UpdateToCurrentTransactionStatus[status]]}</strong>
          </Typography>
        </Maybe>

        <Maybe condition={blockStatusUpdates}>
          <Stack className={dialogClasses.NonApproval} gap={1}>
            <Stack direction="row" gap={2} alignItems="center">
              <ErrorOutlineRounded fontSize="small" htmlColor="#D32F2F" />
              <Typography>Unable to update {isSale ? "customer sale" : "trade"} status.</Typography>
            </Stack>
            <Typography variant="body2" fontWeight={100} paddingLeft={4.5}>
              The asset doesn&apos;t pass the minimum requirement of the RCT book.
            </Typography>
          </Stack>
        </Maybe>
      </Stack>
    </GenericDialog>
  );
};

type NoDocumentBindingContentProps = {
  step: TradeBindingSteps;
  id: uuid;
  organizationId: uuid;
  action: (...args: any[]) => void;
} & DocumentUploadFlowProps;

const NoDocumentBindingContent = ({
  step,
  id,
  organizationId,
  documentType,
  isSale,
  setUploading,
  setUploadHandlers,
  getS3UploadApiLink,
  onFileUploadSuccess,
  onFileUploadError,
  handleExposeUploadHandler,
  action,
}: NoDocumentBindingContentProps): JSX.Element => {
  const previouslySelectedEmails = useRef<string[]>([]);
  const [selectedType, setSelectedType] = useState<Undefinable<TradeBindingSelectType>>();
  const [selectedEmails, setSelectedEmails] = useState<string[]>();

  usePerformantEffect(() => {
    if (step === "select") {
      action(selectedType);
      previouslySelectedEmails.current = [];
    }
  }, [step, selectedType]);

  usePerformantEffect(() => {
    if (step === "email-selection") action(selectedEmails?.length);
  }, [step, selectedEmails]);

  usePerformantEffect(() => {
    if (step === "confirm:email-selection" && selectedEmails) previouslySelectedEmails.current = selectedEmails;
  }, [step, selectedEmails]);

  return (
    <Match
      value={step}
      cases={[
        {
          case: "select",
          component: (
            <FormControl>
              <FormLabel>
                <Typography variant="body2" fontWeight={100} color="black">
                  Please select how you want to upload Proof of Confirmation.
                </Typography>
              </FormLabel>
              <RadioGroup
                value={selectedType}
                onChange={(_: ChangeEvent<HTMLInputElement>, value: string) =>
                  setSelectedType(value as TradeBindingSelectType)
                }
              >
                <FormControlLabel
                  value="email-selection"
                  control={<Radio />}
                  label={
                    <Typography variant="body2">Automatically email trade confirms to the counterparty</Typography>
                  }
                />
                <FormControlLabel
                  value="upload"
                  control={<Radio />}
                  label={<Typography variant="body2">Upload trade confirm</Typography>}
                />
              </RadioGroup>
            </FormControl>
          ),
        },
        {
          case: "email-selection",
          component: (
            <AutomatedEmailSelectionForm
              organizationId={organizationId}
              previouslySelectedEmails={previouslySelectedEmails.current}
              action={(args): void => setSelectedEmails(args)}
            />
          ),
        },
        {
          case: "upload",
          component: (
            <DocumentUploadFlow
              documentType={documentType}
              isSale={isSale}
              setUploading={setUploading}
              setUploadHandlers={setUploadHandlers}
              getS3UploadApiLink={getS3UploadApiLink}
              onFileUploadSuccess={onFileUploadSuccess}
              onFileUploadError={onFileUploadError}
              handleExposeUploadHandler={handleExposeUploadHandler}
            />
          ),
        },
        {
          case: "confirm:email-selection",
          component: <AutomatedEmailSelectionConfirmation id={id} emails={selectedEmails} action={action} />,
        },
      ]}
    />
  );
};

export default StatusModal;
