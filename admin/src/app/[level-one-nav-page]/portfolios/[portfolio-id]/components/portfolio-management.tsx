import { Box } from "@mui/material";
import React, { type JSX } from "react";
import { uuid, PermissionEnum } from "@rubiconcarbon/shared-types";
import useNavigation from "@hooks/use-navigation";
import COLORS from "@components/ui/theme/colors";
import CustomButton from "@components/ui/custom-button/custom-button";

const baseBtnStyle = {
  borderRadius: 2,
  backgroundColor: COLORS.normalGray,
  fontSize: "14px",
  fontWeight: 600,
  color: COLORS.paleBlack,
  marginLeft: "20px",
  marginTop: "7px",
  linHeight: "24px",
  marginBottom: "10px",
  textTransform: "capitalize",
};

const highlightedBtnStyle = {
  backgroundColor: COLORS.rubiconGreen,
  fontWeight: 500,
  color: COLORS.white,
};

interface PortfolioManagementProps {
  portfolioId: uuid;
}

export default function PortfolioManagement(props: PortfolioManagementProps): JSX.Element {
  const { portfolioId } = props;
  const { replacePathFromSegment } = useNavigation();

  const managePortfolioHandler = (portfolioId: uuid): void => {
    replacePathFromSegment(1, `/${portfolioId}/edit-composition`);
  };

  return (
    <Box
      sx={{
        borderRadius: "4px",
        display: "flex",
        textAlign: "center",
      }}
    >
      <Box>
        <CustomButton
          onClickHandler={() => managePortfolioHandler(portfolioId)}
          requiredPermission={PermissionEnum.TRANSFERS_READ}
          style={{ ...baseBtnStyle, ...highlightedBtnStyle, width: "150px" }}
        >
          Manage Portfolio
        </CustomButton>
      </Box>
    </Box>
  );
}
