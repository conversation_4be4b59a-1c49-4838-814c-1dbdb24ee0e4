import React, { useState, Fragment, useMemo, type JS<PERSON> } from "react";
import {
  BookType,
  AdminBookResponse,
  AdminProjectQueryResponse,
  AdminBookQueryResponse,
  BookRelations,
  ProjectRelations,
  AdminGroupedAllocationWithNestedResponse,
  AdminTransactionQueryResponse,
} from "@rubiconcarbon/shared-types";
import { Maybe, useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import { Box, Divider, IconButton, Stack, Tooltip, Typography } from "@mui/material";
import { InfoRounded, MoreHorizRounded } from "@mui/icons-material";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import ProjectValidation from "@components/alerts/project-validation/project-validation";
import GenericDialog from "@components/ui/generic-dialog/generic-dialog";
import PortfolioSelector from "./portfolio-selector";
import PortfolioSummary from "./portfolio-summary";
import Holdings from "./holdings";
import TransactionsCharts from "./transactions-charts";
import { useLogger } from "@providers/logging";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import usePerformantEffect from "@hooks/use-performant-effect";

import classes from "../styles/portfolio-details.module.scss";

const basketSumaryStyle = {
  boxShadow: "0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)",
};

interface PortfolioDetailsProps {
  portfolio: AdminBookResponse;
  booksResponse: AdminBookQueryResponse;
  completedTransactionsResponse: AdminTransactionQueryResponse;
}

export default function PortfolioDetails({
  portfolio: serverPortfolio,
  booksResponse: serverBooksResponse,
  completedTransactionsResponse,
}: PortfolioDetailsProps): JSX.Element {
  const { enqueueError } = useSnackbarVariants();
  const { logger } = useLogger();

  const [allowedOrganizations, setAllowedOrganizations] = useState<string[]>([]);
  const [showMoreOrgs, setShowMoreOrgs] = useState<boolean>(false);
  const [bookType, setBookType] = useState<BookType>();

  const portfolioId = serverPortfolio.id;

  const { data: portfolio, trigger: refreshPortfolio } = useTriggerRequest<AdminBookResponse>({
    url: `/admin/books/${portfolioId}`,
    queryParams: {
      limit: SERVER_PAGINATION_LIMIT,
      includeRelations: [
        BookRelations.OWNER_ALLOCATIONS_NESTED,
        BookRelations.OWNER_ALLOCATIONS_BY_PROJECT_TYPE,
        BookRelations.ORGANIZATION,
        BookRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE,
        BookRelations.ASSET_ALLOCATIONS,
        BookRelations.OWNER_ALLOCATIONS_BY_PROJECT,
      ],
    },
    optimisticData: serverPortfolio,
    swrOptions: {
      onError: (error: any): void => {
        enqueueError("Unable to load portfolios");
        logger.error(`Unable to load portfolios: ${error?.message}`, {});
      },
    },
  });

  const { data: booksResponse, trigger: refreshBooks } = useTriggerRequest<AdminBookQueryResponse>({
    url: "/admin/books",
    queryParams: {
      limit: SERVER_PAGINATION_LIMIT,
    },
    optimisticData: serverBooksResponse,
    swrOptions: {
      onError: (error: any): void => {
        enqueueError("Unable to load portfolios");
        logger.error(`Unable to load portfolios: ${error?.message}`, {});
      },
    },
  });

  const books = useMemo(() => (booksResponse?.data ?? []).filter((p) => p.isEnabled), [booksResponse?.data]);
  const completedTransactions = useMemo(
    () => completedTransactionsResponse?.data ?? [],
    [completedTransactionsResponse?.data],
  );

  const { data: availableProjects, trigger: getAvailableProjects } = useTriggerRequest<AdminProjectQueryResponse>({
    url: "/admin/projects",
    queryParams: {
      limit: SERVER_PAGINATION_LIMIT,
      includeRelations: [ProjectRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE],
    },
    swrOptions: {
      onError: (error: any): void => {
        enqueueError("Unable to load projects");
        logger.error(`Unable to load projects: ${error?.message}`, {});
      },
    },
  });

  usePerformantEffect(() => {
    if (portfolio) {
      setBookType(portfolio.type);
      setAllowedOrganizations(portfolio?.organization?.name ? [portfolio.organization.name] : []);

      if (portfolio.ownerAllocationsByProjectType) {
        const allowedProjectTypes = portfolio.ownerAllocationsByProjectType.map((e) => e.projectType.id);

        if (allowedProjectTypes) {
          getAvailableProjects({
            queryParams: {
              projectTypeIds: allowedProjectTypes,
            },
          });
        }
      }
    }
  }, [portfolio]);

  const reloadData = async (): Promise<void> => {
    await refreshPortfolio();
    await refreshBooks();
  };

  return (
    <>
      <ProjectValidation
        extendedAllocations={
          ((portfolio?.ownerAllocations as AdminGroupedAllocationWithNestedResponse)?.allocations as any) || []
        }
      />
      <Stack direction="row" mt={4} gap={1}>
        <PortfolioSelector portfolioId={portfolioId} portfolioType={bookType} books={books} />
        <Maybe condition={bookType === BookType.RCT_CUSTOM}>
          <Divider sx={{ height: 52, m: 0.5 }} orientation="vertical" />
          <Stack alignItems="center" direction="row" gap={1}>
            <>
              {allowedOrganizations?.slice(0, 2)?.map((name, index) => (
                <Typography key={name}>
                  {name}
                  {index >= 0 && index !== allowedOrganizations?.slice(0, 2)?.length - 1 ? "," : ""}
                </Typography>
              ))}
              <Maybe condition={!!allowedOrganizations?.slice(2)?.length}>
                <IconButton color="primary" onClick={() => setShowMoreOrgs(true)}>
                  <MoreHorizRounded />
                </IconButton>
              </Maybe>
            </>
            <Tooltip title={`Allowed organization${allowedOrganizations?.length > 1 ? "s" : ""} for portfolio`}>
              <InfoRounded />
            </Tooltip>
          </Stack>
        </Maybe>
      </Stack>
      <Maybe condition={!!portfolio}>
        <Box mt={4}>
          <Typography
            variant="body2"
            component="h4"
            sx={{
              fontSize: "20px",
              fontWeight: "500",
            }}
          >
            Overview of portfolio and inventory
          </Typography>
        </Box>
        <Box mt={3} sx={basketSumaryStyle}>
          <PortfolioSummary portfolio={portfolio!} refresh={refreshPortfolio} reloadData={reloadData} />
        </Box>
      </Maybe>
      <Maybe condition={!!portfolio && !!availableProjects?.data}>
        <Holdings portfolio={portfolio!} availableProjects={availableProjects?.data ?? []} />
      </Maybe>
      <Maybe condition={completedTransactions?.length > 0}>
        <TransactionsCharts basketTransactions={completedTransactions} />
      </Maybe>
      <GenericDialog
        open={showMoreOrgs}
        title={`Allowed organization${allowedOrganizations?.length > 1 ? "s" : ""} for portfolio`}
        onClose={() => setShowMoreOrgs(false)}
        classes={{
          root: classes.AllowedOrgsDialog,
          content: classes.Content,
        }}
      >
        <Stack gap={1}>
          {allowedOrganizations?.map((name) => (
            <Fragment key={name}>
              <Typography>{name}</Typography>
              <Divider />
            </Fragment>
          ))}
        </Stack>
      </GenericDialog>
    </>
  );
}
