import { Typo<PERSON>, Autocomplete, <PERSON>Field, Stack, Button, Tooltip, Box } from "@mui/material";
import React, { useState, MouseEvent, useMemo, type JSX } from "react";
import { AdminBookResponse, BookType, PermissionEnum } from "@rubiconcarbon/shared-types";
import { styled } from "@mui/material/styles";
import useNavigation from "@hooks/use-navigation";
import { useStoreProvider } from "@providers/store-provider";
import { Maybe, Nullable } from "@rubiconcarbon/frontend-shared";
import useAuth from "@providers/auth-provider";
import SettingsIcon from "@mui/icons-material/Settings";
import COLORS from "@components/ui/theme/colors";
import usePerformantEffect from "@hooks/use-performant-effect";

import classes from "../styles/portfolio-selector.module.scss";

const BasketAutocomplete = styled(Autocomplete)({
  "& .MuiAutocomplete-popupIndicator": {
    color: "#FFFFFF",
  },
  "& .MuiAutocomplete-clearIndicator": {
    color: "#FFFFFF",
  },
  "& .MuiAutocomplete-inputRoot": {
    '&[class*="MuiOutlinedInput-root"] .MuiAutocomplete-input:first-of-type': {
      paddingLeft: 0,
    },
    "& .MuiOutlinedInput-notchedOutline": {
      border: 0,
    },
  },
});

interface IFormField {
  error: boolean;
  message: string;
}

interface IBasket extends IFormField {
  value: AdminBookResponse | null;
}

const sortPortfolio = (a: AdminBookResponse, b: AdminBookResponse): number => {
  if (a.type > b.type) {
    return -1;
  }
  if (a.type < b.type) {
    return 1;
  }
  return a.name.localeCompare(b.name);
};

interface PortfolioSelectorProps {
  portfolioId: string;
  portfolioType?: BookType;
  books: AdminBookResponse[];
}

export default function PortfolioSelector(props: PortfolioSelectorProps): JSX.Element {
  const { user } = useAuth();
  const { updateEphemeralState } = useStoreProvider();
  const { pushToPath, replacePathFromSegment } = useNavigation();

  const { portfolioId, portfolioType, books } = props;
  const isCustomPortfolio = portfolioType === BookType.RCT_CUSTOM;

  const [availableBaskets, setAvailableBaskets] = useState<AdminBookResponse[]>();

  const canCreateCustomPortfolio = useMemo(
    () => user?.hasPermissions([PermissionEnum.TRADES_READ, PermissionEnum.BOOKS_CREATE_RCT_PORTFOLIOS]),
    [user],
  );

  const onBasketSelection = (basket: Nullable<AdminBookResponse>): void => {
    if (basket) {
      updateEphemeralState(`portfolio.selected`, basket.id);
      replacePathFromSegment(2, `/portfolios/${basket.id}`);
    }
  };

  const [selectedBasket, setSelectedBasket] = useState<IBasket>({
    value: null,
    error: false,
    message: "",
  });

  usePerformantEffect(() => {
    if (books?.length > 0) {
      setAvailableBaskets(
        books
          ?.filter((e) => (e.type === BookType.RCT_CUSTOM || e.type === BookType.RCT_PUBLIC) && e.isEnabled)
          .sort(sortPortfolio),
      );
      const selection = books?.find((b) => b.id === portfolioId) ?? null;
      setSelectedBasket({
        value: selection,
        error: false,
        message: "",
      });

      onBasketSelection(selection);
    }
  }, [books, portfolioId]);

  const basketsDefaultProps = {
    options: availableBaskets?.sort(sortPortfolio) ?? [],
    getOptionLabel: (option: AdminBookResponse): string => (option?.name ? option.name : ""),
    isOptionEqualToValue: (option: AdminBookResponse, value: AdminBookResponse): boolean =>
      option?.name === value?.name,
  };

  const basketSelectionHandler = (
    event: React.ChangeEvent<HTMLInputElement>,
    newValue: AdminBookResponse | null,
  ): void => {
    setSelectedBasket({
      value: newValue,
      error: false,
      message: "",
    });
    onBasketSelection(newValue);
  };

  const gotoCustomPortfolioForm = (event: MouseEvent<HTMLButtonElement>): void => {
    event?.preventDefault();

    if (selectedBasket.value) replacePathFromSegment(1, "/new-custom-portfolio");
    else pushToPath("new-custom-portfolio");
  };

  return (
    <Stack className={classes.Container} direction="row" alignItems="center" width={540}>
      <Typography sx={{ paddingTop: isCustomPortfolio ? "6px" : "4px", color: COLORS.rubiconGreenPale }}>
        Portfolio
      </Typography>
      <BasketAutocomplete
        {...(basketsDefaultProps as any)}
        onChange={basketSelectionHandler as any}
        value={selectedBasket.value || null}
        loading
        disablePortal
        disabled={!books?.length}
        id="basket"
        sx={{ color: COLORS.white, textAlign: "right", paddingLeft: 2 }}
        renderOption={({ key, ...restProps }, option: any) => {
          const { type, name } = option;
          return (
            <span key={key} {...restProps} style={{ width: "100%", justifyContent: "space-between" }}>
              <span key={`${option.id}-${name}`}>{name}</span>
              <Maybe condition={type === BookType.RCT_CUSTOM}>
                <Tooltip title="Custom Portfolio">
                  <SettingsIcon sx={{ fontSize: "15px", marginLeft: "5px", color: "gray" }} />
                </Tooltip>
              </Maybe>
            </span>
          );
        }}
        renderInput={(params) => (
          <TextField
            error={selectedBasket.error}
            helperText={selectedBasket.message}
            placeholder={
              books?.length ? (!selectedBasket.value ? "Select Portfolio" : "") : "No Custom Portfolio Available"
            }
            required
            {...params}
            size="small"
            InputProps={{
              ...params.InputProps,
              style: {
                width: 350,
                borderColor: COLORS.white,
                color: COLORS.white,
                marginTop: "-5px",
                marginBottom: "-10px",
              },
            }}
          />
        )}
      />

      <Tooltip title={!canCreateCustomPortfolio ? "Insufficient permissions" : null}>
        <Box>
          <Button
            className={classes.Button}
            variant="contained"
            onClick={gotoCustomPortfolioForm}
            disabled={!canCreateCustomPortfolio}
            disableElevation={!canCreateCustomPortfolio}
          >
            <Typography
              variant="body1"
              sx={{ color: !canCreateCustomPortfolio ? COLORS.darkGrey : COLORS.rubiconGreen, textTransform: "none" }}
            >
              Create
            </Typography>
          </Button>
        </Box>
      </Tooltip>
    </Stack>
  );
}
