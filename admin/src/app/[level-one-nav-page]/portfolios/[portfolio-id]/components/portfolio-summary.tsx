import { Box, Typography, Grid, Tooltip, Stack } from "@mui/material";
import React, { useMemo, useState, type JSX } from "react";
import { AdminBookResponse, PermissionEnum, BookType } from "@rubiconcarbon/shared-types";
import PortfolioManagement from "./portfolio-management";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import BlockIcon from "@mui/icons-material/Block";
import PortfolioSummaryTotals from "./portfolio-summary-totals";
import CreateIcon from "@mui/icons-material/Create";
import { getPortfolioColor } from "@utils/helpers/portfolio/get-portfolio-helper";
import ActionButton from "@components/ui/action-button/action-button-enhanced";
import COLORS from "@components/ui/theme/colors";
import PortfolioMgtCharts from "./portfolio-mgt-charts";
import PortfolioRenameModal from "./portfolio-rename-modal";
import RCIFScore from "@components/ui/rcif-score/rcif-score";

const btnStyle = {
  marginLeft: "20px",
  borderColor: COLORS.rubiconGreen,
  textTransform: "none",
  width: "110px",
  color: COLORS.rubiconGreen,
  backgroundColor: COLORS.white,
  fontWeight: 500,
  "&:hover": {
    backgroundColor: COLORS.white,
    boxShadow: "rgb(0 0 0 / 10%) 0px 4px 4px",
  },
};

const blockIconStyle = {
  height: "25px",
  color: COLORS.red,
  textAlign: "left",
  marginLeft: "10px",
};

interface PortfolioSummaryProps {
  portfolio: AdminBookResponse;
  refresh: () => Promise<AdminBookResponse>;
  reloadData: () => Promise<void>;
}

export default function PortfolioSummary(props: PortfolioSummaryProps): JSX.Element {
  const { portfolio, refresh, reloadData } = props;
  const [isBookRenameDialogOpen, setIsBookRenameDialogOpen] = useState<boolean>(false);

  //Summary first row
  const title = useMemo(
    () => (
      <Box sx={{ display: "inline-flex", width: "100%", justifyContent: "space-between" }}>
        <Stack direction="column">
          <Stack direction="row">
            <Typography
              variant="body2"
              component="h4"
              fontWeight="700"
              sx={{
                fontSize: "32px",
                fontWeight: "500",
                color: COLORS.pureBlack,
                display: "inline-flex",
              }}
            >
              {portfolio?.name} Portfolio
              {!portfolio?.isEnabled && (
                <Tooltip title="the portfolio is not active" enterDelay={750} placement="top">
                  <BlockIcon sx={blockIconStyle} />
                </Tooltip>
              )}
              <Maybe condition={portfolio.type === BookType.RCT_CUSTOM}>
                <ActionButton
                  onClickHandler={() => setIsBookRenameDialogOpen(true)}
                  startIcon={<CreateIcon />}
                  style={btnStyle}
                  requiredPermission={PermissionEnum.BOOKS_UPDATE}
                >
                  Rename
                </ActionButton>
              </Maybe>
            </Typography>
          </Stack>
          <Maybe condition={!!portfolio?.portfolioScore}>
            <Box mt={1} sx={{ width: "200px" }}>
              <RCIFScore
                score={portfolio?.portfolioScore?.toString()}
                label="RCIF Score"
                style={{ fontSize: "20px" }}
              />
            </Box>
          </Maybe>
        </Stack>
        <Box sx={{ float: "right", justifyContent: "flex-end" }}>
          <PortfolioManagement portfolioId={portfolio.id} />
        </Box>
      </Box>
    ),
    [portfolio?.name, portfolio?.isEnabled, portfolio.id, portfolio.type, portfolio?.portfolioScore],
  );

  const onRenameCompleteHandler = async (): Promise<void> => {
    setIsBookRenameDialogOpen(false);
    await reloadData();
  };

  const onRenameCloseHandler = (): void => {
    setIsBookRenameDialogOpen(false);
  };

  return (
    <Maybe condition={portfolio !== null}>
      <Box
        sx={{
          backgroundColor: COLORS.white,
          borderRadius: "4px",
          display: "flex",
        }}
      >
        <Box
          sx={{
            width: "5px",
            backgroundColor: getPortfolioColor(portfolio.id),
            borderBottomLeftRadius: "4px",
            borderTopLeftRadius: "4px",
          }}
        ></Box>
        <Grid container spacing={1} sx={{ padding: "24px" }}>
          {/*First row - title and buttons */}
          <Grid item sm={12} md={12} lg={12} xl={12}>
            <Box>{title}</Box>
          </Grid>

          <PortfolioSummaryTotals portfolio={portfolio} refresh={refresh} />

          {/*last row - charts */}
          <Grid item sm={12}>
            <PortfolioMgtCharts portfolio={portfolio} />
          </Grid>
        </Grid>
      </Box>
      <PortfolioRenameModal
        portfolioId={portfolio.id}
        initialPortfolioName={portfolio.name}
        isOpen={!!isBookRenameDialogOpen}
        onSave={onRenameCompleteHandler}
        onClose={onRenameCloseHandler}
      />
    </Maybe>
  );
}
