import React, { use<PERSON>allback, useContext, useEffect, useState, type JSX } from "react";
import { Box } from "@mui/material";
import COLORS from "@components/ui/theme/colors";
import ChangeDirectionIcon from "@components/ui/change-direction-icon/change-direction-icon";
import SingleValueChart, { SingleValueChartData } from "../single-value-chart";
import { AdminPricingEstimateRequest, AdminPricingEstimateResponse } from "@rubiconcarbon/shared-types";
import Decimal from "decimal.js";
import { AxiosContext } from "@providers/axios-provider";
import currencyFormat from "@utils/formatters/currency-format";
import { MISSING_DATA } from "@constants/constants";
import ProjectVintage from "@models/project-vintage";

interface ComputedPrice {
  unitPrice?: Decimal;
  unitCost?: Decimal;
  total?: Decimal;
  quantity?: number;
}

const buildAlgoPayload = (
  projectedAllocation: ProjectVintage[],
  withRisk: boolean = false,
): AdminPricingEstimateRequest[] => {
  if (!projectedAllocation || projectedAllocation.length < 1) return null;

  return projectedAllocation
    .filter((c) => c.quantity > 0)
    .map((c) => ({
      vintageId: c?.id,
      quantity: c.quantity,
      buffer: withRisk ? undefined : 0,
    }));
};

interface PortfolioAlgoChartProps {
  projectedAllocation: ProjectVintage[];
  isWithRisk: boolean;
  mainLabel?: string;
  backColor?: string;
  tooltip?: string;
  showChange?: boolean;
  origValue?: Decimal;
  onSuccess?: (value: Decimal) => void;
}

export default function PortfolioAlgoChart(props: PortfolioAlgoChartProps): JSX.Element {
  const {
    projectedAllocation,
    isWithRisk,
    origValue,
    onSuccess,
    mainLabel = "",
    backColor = null,
    tooltip,
    showChange = false,
  } = props;
  const [computedPrices, setComputedPrices] = useState<ComputedPrice | undefined>(undefined);
  const { api } = useContext(AxiosContext);

  const getAlgorithmPrice = useCallback(
    (totalQuantity: number, payload: AdminPricingEstimateRequest[]) => {
      if (payload?.length > 0) {
        api
          .post<AdminPricingEstimateResponse>("admin/pricing/estimate", payload)
          .then((data) => {
            if (!!data?.data?.priceEstimate && !!data?.data?.unitPrice) {
              const unitPrice =
                totalQuantity && !!data.data.priceEstimate
                  ? new Decimal(data.data.priceEstimate).dividedBy(totalQuantity)
                  : undefined;
              const unitCost =
                totalQuantity && !!data.data.costEstimate
                  ? new Decimal(data.data.costEstimate).dividedBy(totalQuantity)
                  : undefined;
              const total = data.data.priceEstimate;
              setComputedPrices({ unitPrice, unitCost, total, quantity: totalQuantity });
              if (onSuccess) onSuccess(unitPrice);
            } else {
              setComputedPrices(undefined);
            }
          })
          .catch((e) => {
            console.error(e);
            setComputedPrices(undefined);
          });
      }
    },
    [api, setComputedPrices, onSuccess],
  );

  useEffect(() => {
    const totalPortfolioVolume = projectedAllocation.reduce((acc, currentValue) => {
      return acc + currentValue.quantity;
    }, 0);
    const payload = buildAlgoPayload(projectedAllocation, isWithRisk);

    if (payload == null) {
      setComputedPrices(undefined);
    } else {
      getAlgorithmPrice(totalPortfolioVolume, payload);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectedAllocation]);

  const algoPriceData: SingleValueChartData = {
    mainLabel: mainLabel,
    value: computedPrices?.unitPrice ? currencyFormat(+computedPrices?.unitPrice) : MISSING_DATA,
    backColor: backColor ?? COLORS.paleBlue,
    tooltip: tooltip,
    icon:
      showChange && computedPrices?.unitPrice !== null && origValue ? (
        <ChangeDirectionIcon currentValue={+origValue.toFixed(2)} newValue={+computedPrices?.unitPrice?.toFixed(2)} />
      ) : (
        <></>
      ),
  };

  return (
    <Box sx={{ paddingLeft: "10px", paddingRight: "10px" }}>
      <SingleValueChart singleValueChartData={algoPriceData} />
    </Box>
  );
}
