import React, { type JSX } from "react";
import { Box, Grid, Typography } from "@mui/material";
import COLORS from "@components/ui/theme/colors";
import CustomButton from "@components/ui/custom-button/custom-button";

export enum FilterBtnsEnum {
  ALL = "all",
  PRICE = "price",
  PROJECT = "project",
  COUNTRY = "country",
  TYPE = "type",
  VINTAGE = "vintage",
  ALGORITHMIC_PRICE = "algo",
}

export interface FilterBtns {
  label: string;
  filter: FilterBtnsEnum;
  selected: boolean;
}

const unselectedBtnStyle = {
  backgroundColor: COLORS.paleGray,
  height: "40px",
  marginLeft: "10px",
  fontSize: "13px",
  lineHeight: "18px",
  color: COLORS.paleBlack,
  "&:hover": {
    color: COLORS.whiteGrey,
  },
};

const selectedBtnStyle = {
  backgroundColor: COLORS.rubiconGreen,
  height: "40px",
  fontSize: "13px",
  marginLeft: "10px",
  lineHeight: "18px",
};

const sectionStyle = {
  fontWeight: 500,
  color: COLORS.pureBlack,
  fontSize: "22px",
  lineHeight: "60px",
};

interface ChartsBtnGroupProps {
  filterBtns: FilterBtns[];
  filterBtnsHandler: (id: string) => void;
}

export default function ChartsBtnGroup(props: ChartsBtnGroupProps): JSX.Element {
  const { filterBtns, filterBtnsHandler } = props;
  return (
    <Grid container item xs={12} mt={4} sx={{ display: "flex" }}>
      <Grid item xs={6}>
        <Typography variant="body1" component="span" sx={sectionStyle} paddingLeft="30px">
          Interactive Charts
        </Typography>
      </Grid>
      <Grid item xs={6}>
        <Box
          mt={2}
          mr={2}
          sx={{
            textAlign: "right",
            display: "inline-flex",
            float: "right",
          }}
        >
          {filterBtns.map((btn) => (
            <CustomButton
              key={btn.label}
              onClickHandler={() => filterBtnsHandler(btn.filter)}
              style={btn.selected ? selectedBtnStyle : unselectedBtnStyle}
            >
              {btn.label}
            </CustomButton>
          ))}
        </Box>
      </Grid>
    </Grid>
  );
}
