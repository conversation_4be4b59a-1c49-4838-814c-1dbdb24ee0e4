import React, { useState, type JSX } from "react";
import { Grid, Typography } from "@mui/material";
import COLORS from "@components/ui/theme/colors";
import ProjectVintage from "@models/project-vintage";
import PortfolioAlgoChart from "./algo-chart";
import Decimal from "decimal.js";

const sectionStyle = {
  fontWeight: 500,
  color: COLORS.pureBlack,
  fontSize: "20px",
  lineHeight: "60px",
  paddingLeft: "30px",
};

interface PortfolioAlgoChartsProps {
  origPortfolioComposition: ProjectVintage[];
  newPortfolioComposition: ProjectVintage[];
}

export default function PortfolioAlgoCharts(props: PortfolioAlgoChartsProps): JSX.Element {
  const { origPortfolioComposition, newPortfolioComposition } = props;
  const [origAlgoPrice, setOrigAlgoPrice] = useState<Decimal>();
  const [origAlgoPriceWithRisk, setorigAlgoPriceWithRisk] = useState<Decimal>();
  return (
    <Grid
      container
      item
      xs={12}
      mt={2}
      sx={{
        display: "flex",
        border: "1px solid rgba(0, 0, 0, 0.23)",
        paddingBottom: "40px",
      }}
    >
      <Grid item xs={12}>
        <Typography variant="body1" component="span" sx={sectionStyle}>
          Algorithmic Unit Price
        </Typography>
      </Grid>

      <Grid item xs={3} mt={-1}>
        <PortfolioAlgoChart
          projectedAllocation={origPortfolioComposition}
          isWithRisk={false}
          mainLabel={"Current"}
          backColor={COLORS.paleBlue}
          onSuccess={(value: Decimal) => setOrigAlgoPrice(value)}
        />
      </Grid>
      <Grid item xs={3} mt={-1}>
        <PortfolioAlgoChart
          projectedAllocation={newPortfolioComposition}
          isWithRisk={false}
          mainLabel={"New"}
          backColor={COLORS.paleGreen}
          showChange={true}
          origValue={origAlgoPrice}
        />
      </Grid>
      <Grid item xs={3} mt={-1}>
        <PortfolioAlgoChart
          projectedAllocation={origPortfolioComposition}
          isWithRisk={true}
          mainLabel={"Current with Risk"}
          backColor={COLORS.paleBlue}
          onSuccess={(value: Decimal) => setorigAlgoPriceWithRisk(value)}
        />
      </Grid>
      <Grid item xs={3} mt={-1}>
        <PortfolioAlgoChart
          projectedAllocation={newPortfolioComposition}
          isWithRisk={true}
          mainLabel={"New with Risk"}
          backColor={COLORS.paleGreen}
          showChange={true}
          origValue={origAlgoPriceWithRisk}
        />
      </Grid>
    </Grid>
  );
}
