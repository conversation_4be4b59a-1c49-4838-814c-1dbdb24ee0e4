import { AuthorizeServer } from "@app/authorize-server";
import {
  AdminBookQueryResponse,
  AdminBookResponse,
  BookRelations,
  BookType,
  PermissionEnum,
  OrderByDirection,
  PurchaseStatus,
  RetirementStatus,
  TransactionOrderByOptions,
  AdminTransactionQueryResponse,
  TransactionType,
} from "@rubiconcarbon/shared-types";
import PortfolioDetail from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { isValidElement, type JSX } from "react";

/**
 * Portfolio Detail Page
 *
 * This is a server component that renders the Portfolio Detail page
 */
export default async function PortfolioDetailPage({
  params,
}: {
  params: Promise<{ "portfolio-id": string }>;
}): Promise<JSX.Element> {
  const { "portfolio-id": id } = await params;

  const portfolioResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminBookResponse>(
      `admin/books/${id}?${generateQueryParams({
        types: [BookType.RCT_CUSTOM, BookType.RCT_PUBLIC],
        includeRelations: [
          BookRelations.OWNER_ALLOCATIONS_NESTED,
          BookRelations.OWNER_ALLOCATIONS_BY_PROJECT_TYPE,
          BookRelations.ORGANIZATION,
          BookRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE,
          BookRelations.ASSET_ALLOCATIONS,
          BookRelations.OWNER_ALLOCATIONS_BY_PROJECT,
        ],
      })}`,
    ),
  );

  const booksResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminBookQueryResponse>(
      `admin/books?${generateQueryParams({
        limit: SERVER_PAGINATION_LIMIT,
      })}`,
    ),
  );

  const completedTransactionsResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminTransactionQueryResponse>(
      `admin/transactions?${generateQueryParams({
        limit: SERVER_PAGINATION_LIMIT,
        assetIds: [id],
        types: [TransactionType.PURCHASE, TransactionType.RETIREMENT],
        orderBys: [`${TransactionOrderByOptions.CREATED_AT}:${OrderByDirection.ASC_NULLS_LAST}`],
        statuses: [PurchaseStatus.SETTLED, RetirementStatus.COMPLETED],
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(portfolioResponse)) return portfolioResponse;
  if (isValidElement(booksResponse)) return booksResponse;
  if (isValidElement(completedTransactionsResponse)) return completedTransactionsResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.BOOKS_READ]}>
      <PortfolioDetail
        portfolio={portfolioResponse as AdminBookResponse}
        booksResponse={booksResponse as AdminBookQueryResponse}
        completedTransactionsResponse={completedTransactionsResponse as AdminTransactionQueryResponse}
      />
    </AuthorizeServer>
  );
}
