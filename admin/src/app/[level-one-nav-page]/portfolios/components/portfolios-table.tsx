import React, { useState, useEffect, use<PERSON><PERSON>back, useContext, useMemo, type JS<PERSON> } from "react";
import {
  PermissionEnum,
  BookType,
  AdminBookQueryResponse,
  AdminBookResponse,
  AdminBookUpdateRequest,
  BookRelations,
} from "@rubiconcarbon/shared-types";
import { stringComparator } from "@utils/comparators/comparator";
import { MISSING_DATA, SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { Box, IconButton, Stack, Tooltip, Typography } from "@mui/material";
import useNavigation from "@hooks/use-navigation";
import integerFormat from "@utils/formatters/integer-format";
import { currencyFormat, useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import useAuth from "@providers/auth-provider";
import { PortfolioData, mapPortfolioData } from "@models/portfolio-data";
import { isEmpty, toNumber } from "lodash";
import DeleteIcon from "@mui/icons-material/Delete";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { AxiosContext } from "@providers/axios-provider";
import { useLogger } from "@providers/logging";
import useBreadcrumbs from "@providers/breadcrumb-provider";
import { PORTFOLIOS_PAGE_LABEL } from "@constants/pages-labels";
import COLORS from "@components/ui/theme/colors";
import MoreInfoIcon from "@components/icons/more-info-icon";
import ActionButton from "@components/ui/action-button/action-button-enhanced";
import EnhancedTable, { ColDef, SortOrder } from "@components/ui/table/enhanced-table";
import ConfirmationModal from "@components/ui/dialogs/confirmation-dialog";
import DialogTheme from "@components/ui/dialogs/dialog-themes";
import TableBox from "@components/ui/table-box/table-box";
import RCIFScore from "@components/ui/rcif-score/rcif-score";

const actionsStyle = {
  borderLeft: "1px solid",
  borderColor: "rgba(224, 224, 224, 1)",
  height: "35px",
};

const disabledBtn = {
  backgroundColor: "white",
};

const enabledBtn = {
  backgroundColor: "white",
  color: COLORS.rubiconGreen,
  "&:hover": {
    backgroundColor: "white",
    color: COLORS.rubiconGreen,
  },
};

interface PortfolioDetails {
  id: string;
  name: string;
}

export default function PortfoliosTable({
  booksResponse: serverBooksResponse,
}: {
  booksResponse: AdminBookQueryResponse;
}): JSX.Element {
  const [portfolios, setPortfolios] = useState<PortfolioData[]>();
  const [showConfirmationModal, setShowConfirmationModal] = useState<boolean>(false);
  const [selectedPortfolio, setPortfolioDetails] = useState<PortfolioDetails>();
  const { replacePathFromSegment, pushToPath } = useNavigation();
  const { user } = useAuth();
  const { enqueueError, enqueueSuccess } = useSnackbarVariants();
  const { api } = useContext(AxiosContext);
  const { updateBreadcrumbName } = useBreadcrumbs();
  const { logger } = useLogger();

  useEffect(() => {
    updateBreadcrumbName?.("Portfolios", PORTFOLIOS_PAGE_LABEL);
  }, [updateBreadcrumbName]);

  const showPrice = useCallback((inputMap: Map<string, string>): string => {
    const purchasePrice = inputMap.get("purchasePrice");
    const purchasePriceWithBuffer = inputMap.get("purchasePriceWithBuffer");
    let result = purchasePrice ? currencyFormat(+purchasePrice) : MISSING_DATA;
    if (purchasePriceWithBuffer) {
      result += ` - ${currencyFormat(+purchasePriceWithBuffer)}`;
    }
    return result;
  }, []);

  const { data: booksResponse, trigger: refreshBooks } = useTriggerRequest<AdminBookQueryResponse>({
    url: "/admin/books",
    queryParams: {
      limit: SERVER_PAGINATION_LIMIT,
      types: [BookType.RCT_CUSTOM, BookType.RCT_PUBLIC],
      includeRelations: [BookRelations.OWNER_ALLOCATIONS_NESTED, BookRelations.OWNER_ALLOCATIONS_BY_PROJECT],
    },
    optimisticData: serverBooksResponse,
    swrOptions: {
      onError: (error: any): void => {
        enqueueError("Unable to load portfolios");
        logger.error(`Unable to load portfolios: ${error?.message}`, {});
      },
    },
  });

  const books = useMemo(
    () => booksResponse?.data.filter((p) => p.isEnabled).sort((a, b) => (a.name < b.name ? 1 : -1)),
    [booksResponse?.data],
  );

  const showRCIFScore = useCallback((inputMap: Map<string, string>): JSX.Element => {
    return (
      <Box sx={{ width: "60px" }}>
        <RCIFScore score={inputMap.get("portfolioScore")} />
      </Box>
    );
  }, []);

  const deletePortfolio = useCallback((): void => {
    const payload: AdminBookUpdateRequest = {
      isEnabled: false,
    };

    api
      .patch<AdminBookResponse>(`admin/books/${selectedPortfolio?.id}`, payload)
      .then(async () => {
        enqueueSuccess("Successfully deleted portfolio.");
        await refreshBooks();
      })
      .catch((e) => {
        logger.error(`Failed to delete portfolio: ${e?.message}`, {});
        enqueueError("Failed to delete portfolio.");
      })
      .finally(() => {
        setShowConfirmationModal(false);
      });
  }, [api, logger, enqueueError, enqueueSuccess, refreshBooks, selectedPortfolio?.id]);

  const deletePortfolioHandler = useCallback((id: string, name: string) => {
    setPortfolioDetails({
      id: id,
      name: name,
    });
    setShowConfirmationModal(true);
  }, []);

  const getPortfolioActions = useCallback(
    (inputMap: Map<string, any>): JSX.Element => {
      const id = inputMap.get("id");
      const type = inputMap.get("type");
      const holding = toNumber(inputMap.get("holding"));
      const name = inputMap.get("name");
      const path = `/portfolios/${id}`;
      const isDeleteEnabled = !(type === BookType.RCT_PUBLIC) && holding === 0;
      return (
        <Stack direction="row" gap={2}>
          <IconButton sx={{ color: COLORS.rubiconGreen }} onClick={() => replacePathFromSegment(1, path)}>
            <MoreInfoIcon />
          </IconButton>
          <Tooltip title={!isDeleteEnabled ? "Portfolios with positions or public portfolios can't be deleted" : ""}>
            <Box sx={actionsStyle}>
              <ActionButton
                style={isDeleteEnabled ? enabledBtn : disabledBtn}
                variant="text"
                isDisabled={!isDeleteEnabled}
                onClickHandler={() => deletePortfolioHandler(id, name)}
                requiredPermission={PermissionEnum.BOOKS_UPDATE}
              >
                <DeleteIcon sx={{ color: isDeleteEnabled ? COLORS.rubiconGreen : "lightGray" }} />
              </ActionButton>
            </Box>
          </Tooltip>
        </Stack>
      );
    },
    [replacePathFromSegment, deletePortfolioHandler],
  );
  const canCreateCustomPortfolio = useMemo(
    () => user?.hasPermissions([PermissionEnum.TRADES_READ, PermissionEnum.BOOKS_CREATE_RCT_PORTFOLIOS]),
    [user],
  );

  const getSearchBarContent = (): JSX.Element => {
    return (
      <ActionButton onClickHandler={() => pushToPath("new-custom-portfolio")} isDisabled={!canCreateCustomPortfolio}>
        Create
      </ActionButton>
    );
  };

  const formatBookType = (input: BookType): string => {
    if (isEmpty(input)) return MISSING_DATA;

    const splitted = input.split(":");
    if (splitted.length > 1) {
      return splitted[1].charAt(0).toUpperCase() + splitted[1].slice(1);
    }

    return input.charAt(0).toUpperCase() + input.slice(1);
  };

  const columnsDef: ColDef[] = [
    {
      columnName: "name",
      displayName: "Name",
      comparator: stringComparator as any,
    },
    {
      columnName: "type",
      displayName: "Type",
      formatter: {
        func: formatBookType as any,
      },
    },
    {
      columnName: "",
      displayName: "Customer Portal Price",
      sortable: false,
      formatter: {
        func: showPrice as any,
        inputFields: ["purchasePrice", "purchasePriceWithBuffer"],
      },
    },
    {
      columnName: "",
      displayName: "RCIF Score",
      sortable: false,
      formatter: {
        func: showRCIFScore,
        inputFields: ["portfolioScore"],
      },
    },
    {
      columnName: "holding",
      displayName: "Holding",
      formatter: {
        func: integerFormat as any,
      },
    },
    {
      columnName: "",
      displayName: "",
      formatter: {
        func: getPortfolioActions as any,
        inputFields: ["id", "type", "holding", "name"],
      },
      exportable: false,
      sortable: false,
    },
  ];

  useEffect(() => {
    if (books) {
      setPortfolios(mapPortfolioData(books));
    }
  }, [books]);

  const getFilteredData = (input: string): void => {
    const searchString = input.toUpperCase();
    const filteredData = books?.filter(
      (row) => row.name?.toUpperCase().includes(searchString) || row.type?.toUpperCase().includes(searchString),
    );
    setPortfolios(mapPortfolioData(filteredData));
  };

  return (
    <>
      <TableBox>
        {portfolios && (
          <EnhancedTable
            name={"portfolios_info"}
            columnsDef={columnsDef}
            data={portfolios}
            searchBarContent={getSearchBarContent}
            rowsCountPerPage={100}
            getFilteredData={getFilteredData}
            defaultSort={{ columnName: "holding", order: SortOrder.DESC }}
          />
        )}
      </TableBox>
      <ConfirmationModal
        isOpen={showConfirmationModal}
        onClose={() => setShowConfirmationModal(false)}
        dialogTheme={DialogTheme.ERROR}
        title="Please confirm"
        dialogButtons={[
          {
            label: "Confirm",
            variant: "contained",
            tooltip: "proceed with delete",
            color: "error",
            style: { borderRadius: "4px" },
            onClickHandler: deletePortfolio,
          },
        ]}
      >
        <Typography variant="body1" component="p">
          You are about to delete portfolio <strong>{selectedPortfolio?.name}</strong>.<br /> Are you sure?
        </Typography>
      </ConfirmationModal>
    </>
  );
}
