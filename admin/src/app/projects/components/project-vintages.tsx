import {
  AdminProjectVintageResponse,
  uuid,
  PermissionEnum,
  AdminProjectVintageQueryResponse,
  ProjectVintageRelations,
} from "@rubiconcarbon/shared-types";
import { useEffect, useState, useReducer, useMemo, type JSX } from "react";
import useS<PERSON> from "swr";
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  Paper,
  Grid,
  TextField,
  Stack,
} from "@mui/material";
import { SxProps } from "@mui/material/styles";
import { MISSING_DATA } from "@constants/constants";
import CreateIcon from "@mui/icons-material/Create";
import CheckIcon from "@mui/icons-material/Check";
import CancelIcon from "@mui/icons-material/Cancel";
import ActionButton from "@components/ui/action-button/action-button-enhanced";
import { NumericFormat } from "react-number-format";
import { convertPercentToNumber } from "@utils/helpers/general/general";
import Decimal from "decimal.js";
import { isEmpty, isNil } from "lodash";
import { BufferListItem } from "@models/buffer-list-item";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import dateRangeFormatter from "@utils/formatters/date-range-formatter";
import { basicComparator } from "@utils/comparators/comparator";
import { deepEqual, percentageFormat, Undefinable } from "@rubiconcarbon/frontend-shared";
import RangeSlider from "@components/ui/range-slider/range-slider";
import { SortOrder } from "@components/ui/table/enhanced-table";
import TableBox from "@components/ui/table-box/table-box";
import COLORS from "@components/ui/theme/colors";
import BuffersConfirmationModal from "./buffers-confirmation-modal";

const VALIDATION_MESSAGES = {
  GREATER_OR_EQUAL_TO_LIMIT: "Value must be greater than or equal to lower limit buffer",
  LESS_OR_EQUAL_TO_LIMIT: "Value must be less than or equal to higher limit buffer",
};

const headerStyle = {
  fontWeight: 500,
};

const bufferFieldStyle = {
  maxLength: 20,
  style: { fontSize: 14, width: "65px" },
};

interface ProjectVintagesProps {
  projectId: string;
}

interface columnHeaderDef {
  title: string;
  style?: SxProps;
}

interface IAction {
  type: VintagesAction;
  vintages?: AdminProjectVintageResponse[];
  vintageToUpdate?: { id: uuid; value: AdminProjectVintageResponse };
}

type FormField = {
  value: number;
  error: boolean;
  message: string;
};

enum VintagesAction {
  "SET",
  "UPDATE",
}

const updateChanges = (
  rowsArray: AdminProjectVintageResponse[],
  vintagesArray: AdminProjectVintageResponse[],
): BufferListItem[] => {
  const updatesList: BufferListItem[] = [];

  if (!isEmpty(rowsArray) && !isEmpty(vintagesArray)) {
    rowsArray.forEach((row) => {
      const vintage = vintagesArray.find((v) => v.id === row.id);
      if (vintage && !deepEqual(row, vintage)) {
        const buferListItem: BufferListItem = {
          projectVintageId: row.id,
          name: `${row.name} - ${dateRangeFormatter(row?.interval?.toString())}`,
          notes: "",
        };

        if (isNil(row.lowBufferPercentage) || +vintage.lowBufferPercentage! !== +row.lowBufferPercentage) {
          buferListItem.origLowBufferPercentage = row.lowBufferPercentage;
          buferListItem.lowBufferPercentage = vintage.lowBufferPercentage;
        }

        if (isNil(row.highBufferPercentage) || +vintage.highBufferPercentage! !== +row.highBufferPercentage) {
          buferListItem.origHighBufferPercentage = row.highBufferPercentage;
          buferListItem.highBufferPercentage = vintage.highBufferPercentage;
        }

        if (isNil(row.riskBufferPercentage) || +vintage.riskBufferPercentage! !== +row.riskBufferPercentage) {
          buferListItem.origRiskBufferPercentage = row.riskBufferPercentage;
          buferListItem.riskBufferPercentage = vintage.riskBufferPercentage;
        }

        updatesList.push(buferListItem);
      }
    });
  }

  return updatesList;
};

const vintagesReducer = (
  currentVinatges: AdminProjectVintageResponse[],
  action: IAction,
): AdminProjectVintageResponse[] => {
  switch (action.type) {
    case VintagesAction.SET:
      return action.vintages || [];
    case VintagesAction.UPDATE:
      return currentVinatges.map((vintage) => {
        return vintage.id === action.vintageToUpdate?.id ? action.vintageToUpdate.value : vintage;
      });
    default:
      throw new Error("invalid vintages action");
  }
};

function Row(props: {
  row: AdminProjectVintageResponse;
  idx: uuid;
  editIdx?: uuid;
  allowEditing: boolean;
  startEditing: (idx: uuid) => void;
  stopEditing: () => void;
  rowChangeHandler: (value: AdminProjectVintageResponse, id: uuid) => void;
}): JSX.Element {
  const { row, idx, editIdx, startEditing, stopEditing, rowChangeHandler, allowEditing = false } = props;

  const [lowerBuffer, setLowerBuffer] = useState<FormField>();
  const [higherBuffer, setHigherBuffer] = useState<FormField>();
  const [buffer, setBuffer] = useState<FormField>();

  const hasError = useMemo(
    () => lowerBuffer?.error || higherBuffer?.error || buffer?.error,
    [buffer?.error, higherBuffer?.error, lowerBuffer?.error],
  );

  useEffect(() => {
    setLowerBuffer({
      value: +(row?.lowBufferPercentage ?? 0) * 100,
      error: false,
      message: "",
    });

    setHigherBuffer({
      value: +(row?.highBufferPercentage ?? 0) * 100,
      error: false,
      message: "",
    });

    setBuffer({
      value: +(row?.riskBufferPercentage ?? 0) * 100,
      error: false,
      message: "",
    });
  }, [startEditing, row.lowBufferPercentage, row.highBufferPercentage, row.riskBufferPercentage]);

  const stopEditingHandler = (): void => {
    if (validateRange()) {
      const updatedRow: AdminProjectVintageResponse = {
        ...row,
        lowBufferPercentage: new Decimal((lowerBuffer?.value ?? 0) / 100),
        highBufferPercentage: new Decimal((higherBuffer?.value ?? 0) / 100),
        riskBufferPercentage: new Decimal((buffer?.value ?? 0) / 100),
      };
      rowChangeHandler(updatedRow, row.id);
      stopEditing();
    }
  };

  const cancelEditHandler = (): void => {
    stopEditing();
  };

  const lowerBufferChangeHandler = (event: React.ChangeEvent<HTMLInputElement>): void => {
    const value = convertPercentToNumber(event.target.value);
    setLowerBuffer(validateLowerLimit(value));
  };

  const higherBufferChangeHandler = (event: React.ChangeEvent<HTMLInputElement>): void => {
    const value = convertPercentToNumber(event.target.value);
    setHigherBuffer(validateHigherLimit(value));
  };

  const bufferChangeHandler = (event: React.ChangeEvent<HTMLInputElement>): void => {
    const value = convertPercentToNumber(event.target.value);
    setBuffer(validateBuffer(value));
  };

  function validateLowerLimit(input: number): FormField {
    if (higherBuffer && !isNaN(higherBuffer.value) && input > higherBuffer.value)
      return {
        value: input,
        error: true,
        message: VALIDATION_MESSAGES.LESS_OR_EQUAL_TO_LIMIT,
      };

    setHigherBuffer((prev) => ({ ...prev!, error: false, message: "" }));

    if (buffer && buffer.value >= input) setBuffer({ ...buffer, error: false, message: "" });

    return {
      value: input,
      error: false,
      message: "",
    };
  }

  function validateHigherLimit(input: number): FormField {
    if (lowerBuffer && !isNaN(lowerBuffer.value) && input < lowerBuffer.value) {
      return {
        value: input,
        error: true,
        message: VALIDATION_MESSAGES.GREATER_OR_EQUAL_TO_LIMIT,
      };
    }

    setLowerBuffer((prev) => ({ ...prev!, error: false, message: "" }));

    if (buffer && buffer.value <= input) setBuffer({ ...buffer, error: false, message: "" });

    return {
      value: input,
      error: false,
      message: "",
    };
  }

  function validateBuffer(input: number): FormField {
    if (lowerBuffer && input < lowerBuffer.value) {
      return {
        value: input,
        error: true,
        message: VALIDATION_MESSAGES.GREATER_OR_EQUAL_TO_LIMIT,
      };
    }

    if (higherBuffer && input > higherBuffer.value) {
      return {
        value: input,
        error: true,
        message: VALIDATION_MESSAGES.LESS_OR_EQUAL_TO_LIMIT,
      };
    }
    return {
      value: input,
      error: false,
      message: "",
    };
  }

  const validateRange = (): boolean => {
    let validationResult = true;

    if (lowerBuffer && lowerBuffer.value > (higherBuffer?.value ?? 0)) {
      setLowerBuffer({
        ...lowerBuffer,
        error: true,
        message: VALIDATION_MESSAGES.LESS_OR_EQUAL_TO_LIMIT,
      });
      validationResult = false;
    }

    if (higherBuffer && higherBuffer.value < (lowerBuffer?.value ?? 0)) {
      setHigherBuffer({
        ...higherBuffer,
        error: true,
        message: VALIDATION_MESSAGES.GREATER_OR_EQUAL_TO_LIMIT,
      });
      validationResult = false;
    }

    if (buffer && buffer.value < (lowerBuffer?.value ?? 0)) {
      setBuffer({
        ...buffer,
        error: true,
        message: VALIDATION_MESSAGES.GREATER_OR_EQUAL_TO_LIMIT,
      });
      validationResult = false;
    }

    if (buffer && buffer.value > (higherBuffer?.value ?? 0)) {
      setBuffer({
        ...buffer,
        error: true,
        message: VALIDATION_MESSAGES.LESS_OR_EQUAL_TO_LIMIT,
      });
      validationResult = false;
    }

    return validationResult;
  };

  const rangeContent = (bufferValue?: Decimal): string | JSX.Element => {
    if (isNil(bufferValue)) return MISSING_DATA;

    if ((lowerBuffer && isNaN(lowerBuffer.value)) || (higherBuffer && isNaN(higherBuffer.value)))
      return `${percentageFormat(+bufferValue)}`;

    return (
      <Box m="auto" height={50} sx={{ marginTop: "20px", width: "90%", minWidth: "340px" }}>
        <RangeSlider
          value={buffer?.value ?? 0}
          max={higherBuffer?.value ?? 0}
          min={lowerBuffer?.value ?? 0}
          disabled={true}
        />
      </Box>
    );
  };

  const currentlyEditing = idx === editIdx;
  return (
    <>
      <TableRow
        sx={{
          "& > *": {
            height: currentlyEditing ? "130px" : "70px",
            padding: currentlyEditing && hasError ? "40px 0" : 0,
            "& .MuiFormHelperText-root": { position: "relative", width: 65 },
          },
        }}
      >
        <TableCell scope="row" component="td">
          <Typography variant="body2">{row.name}</Typography>
        </TableCell>
        <TableCell
          scope="row"
          component="td"
          sx={{
            width: currentlyEditing ? "80%" : "70%",
            textAlign: "right",
          }}
        >
          {currentlyEditing ? (
            <Stack gap={2} direction="row">
              <Box sx={{ width: "100%" }}>
                <NumericFormat
                  label="Low limit"
                  sx={{ width: "100%" }}
                  valueIsNumericString
                  suffix="%"
                  size="small"
                  decimalScale={0}
                  inputProps={bufferFieldStyle}
                  allowNegative={false}
                  customInput={TextField}
                  type="text"
                  thousandSeparator={","}
                  value={lowerBuffer?.value ?? 0}
                  onChange={(e) => lowerBufferChangeHandler(e)}
                  helperText={lowerBuffer?.message ?? ""}
                  error={!!lowerBuffer?.error}
                />
              </Box>
              <Box sx={{ width: "100%" }}>
                <NumericFormat
                  label="High limit"
                  sx={{ width: "100%" }}
                  valueIsNumericString
                  suffix="%"
                  size="small"
                  decimalScale={0}
                  inputProps={bufferFieldStyle}
                  allowNegative={false}
                  customInput={TextField}
                  type="text"
                  thousandSeparator={","}
                  value={higherBuffer?.value ?? 0}
                  onChange={(e) => higherBufferChangeHandler(e)}
                  helperText={higherBuffer?.message ?? ""}
                  error={higherBuffer?.error}
                />
              </Box>
              <Box sx={{ width: "100%" }}>
                <NumericFormat
                  label="Buffer"
                  sx={{ width: "100%" }}
                  valueIsNumericString
                  suffix="%"
                  size="small"
                  decimalScale={0}
                  inputProps={bufferFieldStyle}
                  allowNegative={false}
                  customInput={TextField}
                  type="text"
                  thousandSeparator={","}
                  value={buffer?.value ?? 0}
                  onChange={(e) => bufferChangeHandler(e)}
                  helperText={buffer?.message ?? ""}
                  error={buffer?.error}
                />
              </Box>
            </Stack>
          ) : (
            rangeContent(row?.riskBufferPercentage)
          )}
        </TableCell>
        {allowEditing && (
          <TableCell sx={{ width: "80px" }}>
            {currentlyEditing ? (
              <Box sx={{ width: "70px", float: "right" }}>
                <Tooltip title="Confirm">
                  <IconButton
                    sx={{ color: "#094436" }}
                    disabled={!!lowerBuffer?.error || !!higherBuffer?.error || !!buffer?.error}
                    edge="start"
                    onClick={stopEditingHandler}
                  >
                    <CheckIcon />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Cancel">
                  <IconButton sx={{ marginLeft: "1px", color: "#C62828" }} edge="start" onClick={cancelEditHandler}>
                    <CancelIcon />
                  </IconButton>
                </Tooltip>
              </Box>
            ) : (
              <Tooltip title="Edit">
                <IconButton sx={{ color: "#094436", float: "right" }} edge="start" onClick={() => startEditing(idx)}>
                  <CreateIcon />
                </IconButton>
              </Tooltip>
            )}
          </TableCell>
        )}
      </TableRow>
    </>
  );
}

export default function ProjectVintages(props: ProjectVintagesProps): JSX.Element {
  const { projectId } = props;
  const [editIdx, setEditIdx] = useState<Undefinable<uuid>>();
  const [updatedBufferList, setUpdatedBufferList] = useState<BufferListItem[]>([]);
  const [vintages, dispatch] = useReducer(vintagesReducer, []);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] = useState<boolean>(false);
  const [allowEditing, setAllowEditing] = useState<boolean>(false);

  const columnsSet: columnHeaderDef[] = [
    { title: "Vintage", style: headerStyle },
    { title: "Buffer", style: { ...headerStyle, textAlign: "center" } },
  ];
  const [columns, setColumns] = useState<columnHeaderDef[]>(columnsSet);

  const { data: projectVintageResponse, mutate: refresh } = useSWR<AdminProjectVintageQueryResponse>(
    `/admin/project-vintages?includeRelations=${ProjectVintageRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE}&projectIds=${projectId}`,
  );

  useEffect(() => {
    if (projectVintageResponse?.data) resetVintages(projectVintageResponse.data);
  }, [projectVintageResponse]);

  useEffect(() => {
    if (projectVintageResponse?.data) {
      setUpdatedBufferList(updateChanges(projectVintageResponse.data, vintages));
    }
  }, [projectVintageResponse, vintages]);

  const startEditingHandler = (idx: uuid): void => {
    setEditIdx(idx);
  };

  const resetVintages = (input: AdminProjectVintageResponse[]): void => {
    if (input) {
      input.sort((a, b) => basicComparator(a.name, b.name, SortOrder.ASC));
      dispatch({ type: VintagesAction.SET, vintages: input });
    }
  };

  const stopEditingHandler = (): void => {
    setEditIdx(undefined);
  };

  const rowChangeHandler = (value: AdminProjectVintageResponse, id: uuid): void => {
    const updateData = {
      id,
      value,
    };

    dispatch({
      type: VintagesAction.UPDATE,
      vintageToUpdate: updateData,
    });
  };

  const onSubmit = (): void => {
    setIsConfirmationDialogOpen(true);
  };

  const onEditHandler = (): void => {
    setAllowEditing((prev) => !prev);

    if (!allowEditing) {
      setColumns((prev) => [...prev, { title: "" }]);
    } else {
      setColumns(columnsSet);
      resetVintages(projectVintageResponse?.data ?? []);
      setEditIdx(undefined);
    }
  };

  const refreshPage = (): void => {
    refresh();
  };

  function closeConfirmationHandler(): void {
    setIsConfirmationDialogOpen(false);
  }

  function onSuccessHandler(message: string): void {
    setUpdatedBufferList([]);
    enqueueSuccess(message);

    setAllowEditing(false);
    setColumns(columnsSet);
    refresh();
  }

  return (
    <TableBox>
      <Box sx={{ width: "100%" }}>
        <Paper sx={{ width: "100%", overflow: "hidden" }}>
          <Grid container justifyContent="center" sx={{ height: "55px", backgroundColor: "rgba(240, 240, 240, 1)" }}>
            <Grid item xs={4}></Grid>
            <Grid item xs={4}>
              <Box
                mt={2}
                sx={{
                  display: "flex",
                  justifyContent: "center",
                }}
              >
                <Typography variant="body2" component="h4" color={COLORS.black}>
                  Vintages
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={4}>
              <Box
                mt={1.2}
                sx={{
                  display: "flex",
                  justifyContent: "right",
                }}
              >
                {allowEditing && (
                  <Box>
                    <ActionButton
                      isDisabled={isEmpty(updatedBufferList)}
                      style={{ width: "100px" }}
                      onClickHandler={onSubmit}
                      requiredPermission={PermissionEnum.VINTAGES_MANAGE_RISK_BUFFER}
                    >
                      Save
                    </ActionButton>
                  </Box>
                )}
                <Box ml={1}>
                  <ActionButton
                    onClickHandler={onEditHandler}
                    style={{ width: "140px" }}
                    requiredPermission={PermissionEnum.VINTAGES_MANAGE_RISK_BUFFER}
                  >
                    {allowEditing ? "Cancel Edit" : "Edit Buffers"}
                  </ActionButton>
                </Box>
              </Box>
            </Grid>
          </Grid>
          <TableContainer sx={{ maxHeight: 500 }}>
            <Table
              aria-labelledby="portfolio-composition-table-header"
              size="small"
              sx={{ ".MuiTableCell-root": { px: "0.8rem" } }}
            >
              <TableHead>
                <TableRow sx={{ backgroundColor: "lightGray", height: "40px" }}>
                  {columns?.map((column, idx) => (
                    <TableCell key={`${column}-${idx}`}>
                      <Typography variant="body2" sx={column.style}>
                        {column.title}
                      </Typography>
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {vintages?.map((row) => (
                  <Row
                    key={row.id}
                    row={row}
                    idx={row.id}
                    editIdx={editIdx}
                    startEditing={startEditingHandler}
                    stopEditing={stopEditingHandler}
                    rowChangeHandler={rowChangeHandler}
                    allowEditing={allowEditing}
                  />
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
        <BuffersConfirmationModal
          isOpen={!!isConfirmationDialogOpen}
          onClose={closeConfirmationHandler}
          onConfirm={onSuccessHandler}
          onError={enqueueError}
          bufferList={updatedBufferList}
          refreshData={refreshPage}
        />
      </Box>
    </TableBox>
  );
}
