import { BookType } from "@rubiconcarbon/shared-types";

export const BookTypeToLabel: Partial<Record<BookType, string>> = {
  [BookType.RCT_PUBLIC]: "RCT",
  [BookType.PORTFOLIO_DEFAULT]: "RCT",
  [BookType.COMPLIANCE_DEFAULT]: "Compliance",
  [BookType.OPPORTUNISTIC_DEFAULT]: "Opportunistic",
  [BookType.REHABILITATION_DEFAULT]: "Ineligible RCT",
  [BookType.AGED_DEFAULT]: "Aged Inventory, LLC",
};
